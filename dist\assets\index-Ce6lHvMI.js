var iw=Object.defineProperty,aw=Object.defineProperties;var sw=Object.getOwnPropertyDescriptors;var co=Object.getOwnPropertySymbols;var ly=Object.prototype.hasOwnProperty,oy=Object.prototype.propertyIsEnumerable;var df=Math.pow,ry=(n,i,s)=>i in n?iw(n,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[i]=s,O=(n,i)=>{for(var s in i||(i={}))ly.call(i,s)&&ry(n,s,i[s]);if(co)for(var s of co(i))oy.call(i,s)&&ry(n,s,i[s]);return n},K=(n,i)=>aw(n,sw(i));var et=(n,i)=>{var s={};for(var l in n)ly.call(n,l)&&i.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&co)for(var l of co(n))i.indexOf(l)<0&&oy.call(n,l)&&(s[l]=n[l]);return s};var rw=(n,i)=>()=>(i||n((i={exports:{}}).exports,i),i.exports);var cn=(n,i,s)=>new Promise((l,u)=>{var d=m=>{try{h(s.next(m))}catch(p){u(p)}},f=m=>{try{h(s.throw(m))}catch(p){u(p)}},h=m=>m.done?l(m.value):Promise.resolve(m.value).then(d,f);h((s=s.apply(n,i)).next())});var a3=rw(US=>{function lw(n,i){for(var s=0;s<i.length;s++){const l=i[s];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const d=Object.getOwnPropertyDescriptor(l,u);d&&Object.defineProperty(n,u,d.get?d:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function s(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(u){if(u.ep)return;u.ep=!0;const d=s(u);fetch(u.href,d)}})();function m0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var hf={exports:{}},br={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uy;function ow(){if(uy)return br;uy=1;var n=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function s(l,u,d){var f=null;if(d!==void 0&&(f=""+d),u.key!==void 0&&(f=""+u.key),"key"in u){d={};for(var h in u)h!=="key"&&(d[h]=u[h])}else d=u;return u=d.ref,{$$typeof:n,type:l,key:f,ref:u!==void 0?u:null,props:d}}return br.Fragment=i,br.jsx=s,br.jsxs=s,br}var cy;function uw(){return cy||(cy=1,hf.exports=ow()),hf.exports}var S=uw(),mf={exports:{}},St={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy;function cw(){if(fy)return St;fy=1;var n=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function b(D){return D===null||typeof D!="object"?null:(D=v&&D[v]||D["@@iterator"],typeof D=="function"?D:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,M={};function E(D,X,tt){this.props=D,this.context=X,this.refs=M,this.updater=tt||T}E.prototype.isReactComponent={},E.prototype.setState=function(D,X){if(typeof D!="object"&&typeof D!="function"&&D!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,D,X,"setState")},E.prototype.forceUpdate=function(D){this.updater.enqueueForceUpdate(this,D,"forceUpdate")};function N(){}N.prototype=E.prototype;function z(D,X,tt){this.props=D,this.context=X,this.refs=M,this.updater=tt||T}var A=z.prototype=new N;A.constructor=z,C(A,E.prototype),A.isPureReactComponent=!0;var V=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function J(D,X,tt,$,it,vt){return tt=vt.ref,{$$typeof:n,type:D,key:X,ref:tt!==void 0?tt:null,props:vt}}function Y(D,X){return J(D.type,X,void 0,void 0,void 0,D.props)}function W(D){return typeof D=="object"&&D!==null&&D.$$typeof===n}function ft(D){var X={"=":"=0",":":"=2"};return"$"+D.replace(/[=:]/g,function(tt){return X[tt]})}var ht=/\/+/g;function lt(D,X){return typeof D=="object"&&D!==null&&D.key!=null?ft(""+D.key):X.toString(36)}function bt(){}function Et(D){switch(D.status){case"fulfilled":return D.value;case"rejected":throw D.reason;default:switch(typeof D.status=="string"?D.then(bt,bt):(D.status="pending",D.then(function(X){D.status==="pending"&&(D.status="fulfilled",D.value=X)},function(X){D.status==="pending"&&(D.status="rejected",D.reason=X)})),D.status){case"fulfilled":return D.value;case"rejected":throw D.reason}}throw D}function ut(D,X,tt,$,it){var vt=typeof D;(vt==="undefined"||vt==="boolean")&&(D=null);var dt=!1;if(D===null)dt=!0;else switch(vt){case"bigint":case"string":case"number":dt=!0;break;case"object":switch(D.$$typeof){case n:case i:dt=!0;break;case g:return dt=D._init,ut(dt(D._payload),X,tt,$,it)}}if(dt)return it=it(D),dt=$===""?"."+lt(D,0):$,V(it)?(tt="",dt!=null&&(tt=dt.replace(ht,"$&/")+"/"),ut(it,X,tt,"",function(Tt){return Tt})):it!=null&&(W(it)&&(it=Y(it,tt+(it.key==null||D&&D.key===it.key?"":(""+it.key).replace(ht,"$&/")+"/")+dt)),X.push(it)),1;dt=0;var xt=$===""?".":$+":";if(V(D))for(var nt=0;nt<D.length;nt++)$=D[nt],vt=xt+lt($,nt),dt+=ut($,X,tt,vt,it);else if(nt=b(D),typeof nt=="function")for(D=nt.call(D),nt=0;!($=D.next()).done;)$=$.value,vt=xt+lt($,nt++),dt+=ut($,X,tt,vt,it);else if(vt==="object"){if(typeof D.then=="function")return ut(Et(D),X,tt,$,it);throw X=String(D),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(D).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return dt}function B(D,X,tt){if(D==null)return D;var $=[],it=0;return ut(D,$,"","",function(vt){return X.call(tt,vt,it++)}),$}function q(D){if(D._status===-1){var X=D._result;X=X(),X.then(function(tt){(D._status===0||D._status===-1)&&(D._status=1,D._result=tt)},function(tt){(D._status===0||D._status===-1)&&(D._status=2,D._result=tt)}),D._status===-1&&(D._status=0,D._result=X)}if(D._status===1)return D._result.default;throw D._result}var P=typeof reportError=="function"?reportError:function(D){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof D=="object"&&D!==null&&typeof D.message=="string"?String(D.message):String(D),error:D});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",D);return}console.error(D)};function ct(){}return St.Children={map:B,forEach:function(D,X,tt){B(D,function(){X.apply(this,arguments)},tt)},count:function(D){var X=0;return B(D,function(){X++}),X},toArray:function(D){return B(D,function(X){return X})||[]},only:function(D){if(!W(D))throw Error("React.Children.only expected to receive a single React element child.");return D}},St.Component=E,St.Fragment=s,St.Profiler=u,St.PureComponent=z,St.StrictMode=l,St.Suspense=m,St.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,St.__COMPILER_RUNTIME={__proto__:null,c:function(D){return j.H.useMemoCache(D)}},St.cache=function(D){return function(){return D.apply(null,arguments)}},St.cloneElement=function(D,X,tt){if(D==null)throw Error("The argument must be a React element, but you passed "+D+".");var $=C({},D.props),it=D.key,vt=void 0;if(X!=null)for(dt in X.ref!==void 0&&(vt=void 0),X.key!==void 0&&(it=""+X.key),X)!I.call(X,dt)||dt==="key"||dt==="__self"||dt==="__source"||dt==="ref"&&X.ref===void 0||($[dt]=X[dt]);var dt=arguments.length-2;if(dt===1)$.children=tt;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];$.children=xt}return J(D.type,it,void 0,void 0,vt,$)},St.createContext=function(D){return D={$$typeof:f,_currentValue:D,_currentValue2:D,_threadCount:0,Provider:null,Consumer:null},D.Provider=D,D.Consumer={$$typeof:d,_context:D},D},St.createElement=function(D,X,tt){var $,it={},vt=null;if(X!=null)for($ in X.key!==void 0&&(vt=""+X.key),X)I.call(X,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(it[$]=X[$]);var dt=arguments.length-2;if(dt===1)it.children=tt;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];it.children=xt}if(D&&D.defaultProps)for($ in dt=D.defaultProps,dt)it[$]===void 0&&(it[$]=dt[$]);return J(D,vt,void 0,void 0,null,it)},St.createRef=function(){return{current:null}},St.forwardRef=function(D){return{$$typeof:h,render:D}},St.isValidElement=W,St.lazy=function(D){return{$$typeof:g,_payload:{_status:-1,_result:D},_init:q}},St.memo=function(D,X){return{$$typeof:p,type:D,compare:X===void 0?null:X}},St.startTransition=function(D){var X=j.T,tt={};j.T=tt;try{var $=D(),it=j.S;it!==null&&it(tt,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(ct,P)}catch(vt){P(vt)}finally{j.T=X}},St.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},St.use=function(D){return j.H.use(D)},St.useActionState=function(D,X,tt){return j.H.useActionState(D,X,tt)},St.useCallback=function(D,X){return j.H.useCallback(D,X)},St.useContext=function(D){return j.H.useContext(D)},St.useDebugValue=function(){},St.useDeferredValue=function(D,X){return j.H.useDeferredValue(D,X)},St.useEffect=function(D,X,tt){var $=j.H;if(typeof tt=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(D,X)},St.useId=function(){return j.H.useId()},St.useImperativeHandle=function(D,X,tt){return j.H.useImperativeHandle(D,X,tt)},St.useInsertionEffect=function(D,X){return j.H.useInsertionEffect(D,X)},St.useLayoutEffect=function(D,X){return j.H.useLayoutEffect(D,X)},St.useMemo=function(D,X){return j.H.useMemo(D,X)},St.useOptimistic=function(D,X){return j.H.useOptimistic(D,X)},St.useReducer=function(D,X,tt){return j.H.useReducer(D,X,tt)},St.useRef=function(D){return j.H.useRef(D)},St.useState=function(D){return j.H.useState(D)},St.useSyncExternalStore=function(D,X,tt){return j.H.useSyncExternalStore(D,X,tt)},St.useTransition=function(){return j.H.useTransition()},St.version="19.1.0",St}var dy;function Ad(){return dy||(dy=1,mf.exports=cw()),mf.exports}var w=Ad();const Ei=m0(w),p0=lw({__proto__:null,default:Ei},[w]);var pf={exports:{}},xr={},gf={exports:{}},yf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hy;function fw(){return hy||(hy=1,function(n){function i(B,q){var P=B.length;B.push(q);t:for(;0<P;){var ct=P-1>>>1,D=B[ct];if(0<u(D,q))B[ct]=q,B[P]=D,P=ct;else break t}}function s(B){return B.length===0?null:B[0]}function l(B){if(B.length===0)return null;var q=B[0],P=B.pop();if(P!==q){B[0]=P;t:for(var ct=0,D=B.length,X=D>>>1;ct<X;){var tt=2*(ct+1)-1,$=B[tt],it=tt+1,vt=B[it];if(0>u($,P))it<D&&0>u(vt,$)?(B[ct]=vt,B[it]=P,ct=it):(B[ct]=$,B[tt]=P,ct=tt);else if(it<D&&0>u(vt,P))B[ct]=vt,B[it]=P,ct=it;else break t}}return q}function u(B,q){var P=B.sortIndex-q.sortIndex;return P!==0?P:B.id-q.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();n.unstable_now=function(){return f.now()-h}}var m=[],p=[],g=1,v=null,b=3,T=!1,C=!1,M=!1,E=!1,N=typeof setTimeout=="function"?setTimeout:null,z=typeof clearTimeout=="function"?clearTimeout:null,A=typeof setImmediate!="undefined"?setImmediate:null;function V(B){for(var q=s(p);q!==null;){if(q.callback===null)l(p);else if(q.startTime<=B)l(p),q.sortIndex=q.expirationTime,i(m,q);else break;q=s(p)}}function j(B){if(M=!1,V(B),!C)if(s(m)!==null)C=!0,I||(I=!0,lt());else{var q=s(p);q!==null&&ut(j,q.startTime-B)}}var I=!1,J=-1,Y=5,W=-1;function ft(){return E?!0:!(n.unstable_now()-W<Y)}function ht(){if(E=!1,I){var B=n.unstable_now();W=B;var q=!0;try{t:{C=!1,M&&(M=!1,z(J),J=-1),T=!0;var P=b;try{e:{for(V(B),v=s(m);v!==null&&!(v.expirationTime>B&&ft());){var ct=v.callback;if(typeof ct=="function"){v.callback=null,b=v.priorityLevel;var D=ct(v.expirationTime<=B);if(B=n.unstable_now(),typeof D=="function"){v.callback=D,V(B),q=!0;break e}v===s(m)&&l(m),V(B)}else l(m);v=s(m)}if(v!==null)q=!0;else{var X=s(p);X!==null&&ut(j,X.startTime-B),q=!1}}break t}finally{v=null,b=P,T=!1}q=void 0}}finally{q?lt():I=!1}}}var lt;if(typeof A=="function")lt=function(){A(ht)};else if(typeof MessageChannel!="undefined"){var bt=new MessageChannel,Et=bt.port2;bt.port1.onmessage=ht,lt=function(){Et.postMessage(null)}}else lt=function(){N(ht,0)};function ut(B,q){J=N(function(){B(n.unstable_now())},q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(B){switch(b){case 1:case 2:case 3:var q=3;break;default:q=b}var P=b;b=q;try{return B()}finally{b=P}},n.unstable_requestPaint=function(){E=!0},n.unstable_runWithPriority=function(B,q){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var P=b;b=B;try{return q()}finally{b=P}},n.unstable_scheduleCallback=function(B,q,P){var ct=n.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?ct+P:ct):P=ct,B){case 1:var D=-1;break;case 2:D=250;break;case 5:D=1073741823;break;case 4:D=1e4;break;default:D=5e3}return D=P+D,B={id:g++,callback:q,priorityLevel:B,startTime:P,expirationTime:D,sortIndex:-1},P>ct?(B.sortIndex=P,i(p,B),s(m)===null&&B===s(p)&&(M?(z(J),J=-1):M=!0,ut(j,P-ct))):(B.sortIndex=D,i(m,B),C||T||(C=!0,I||(I=!0,lt()))),B},n.unstable_shouldYield=ft,n.unstable_wrapCallback=function(B){var q=b;return function(){var P=b;b=q;try{return B.apply(this,arguments)}finally{b=P}}}}(yf)),yf}var my;function dw(){return my||(my=1,gf.exports=fw()),gf.exports}var vf={exports:{}},xe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function hw(){if(py)return xe;py=1;var n=Ad();function i(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)p+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(i(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function d(m,p,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:v==null?null:""+v,children:m,containerInfo:p,implementation:g}}var f=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return xe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,xe.createPortal=function(m,p){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(i(299));return d(m,p,null,g)},xe.flushSync=function(m){var p=f.T,g=l.p;try{if(f.T=null,l.p=2,m)return m()}finally{f.T=p,l.p=g,l.d.f()}},xe.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,l.d.C(m,p))},xe.prefetchDNS=function(m){typeof m=="string"&&l.d.D(m)},xe.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,T=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;g==="style"?l.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:b,fetchPriority:T}):g==="script"&&l.d.X(m,{crossOrigin:v,integrity:b,fetchPriority:T,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},xe.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var g=h(p.as,p.crossOrigin);l.d.M(m,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&l.d.M(m)},xe.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin);l.d.L(m,g,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},xe.preloadModule=function(m,p){if(typeof m=="string")if(p){var g=h(p.as,p.crossOrigin);l.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else l.d.m(m)},xe.requestFormReset=function(m){l.d.r(m)},xe.unstable_batchedUpdates=function(m,p){return m(p)},xe.useFormState=function(m,p,g){return f.H.useFormState(m,p,g)},xe.useFormStatus=function(){return f.H.useHostTransitionStatus()},xe.version="19.1.0",xe}var gy;function g0(){if(gy)return vf.exports;gy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}return n(),vf.exports=hw(),vf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yy;function mw(){if(yy)return xr;yy=1;var n=dw(),i=Ad(),s=g0();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(d(t)!==t)throw Error(l(188))}function m(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(l(188));return e!==t?null:t}for(var a=t,r=e;;){var o=a.return;if(o===null)break;var c=o.alternate;if(c===null){if(r=o.return,r!==null){a=r;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===a)return h(o),t;if(c===r)return h(o),e;c=c.sibling}throw Error(l(188))}if(a.return!==r.return)a=o,r=c;else{for(var y=!1,x=o.child;x;){if(x===a){y=!0,a=o,r=c;break}if(x===r){y=!0,r=o,a=c;break}x=x.sibling}if(!y){for(x=c.child;x;){if(x===a){y=!0,a=c,r=o;break}if(x===r){y=!0,r=c,a=o;break}x=x.sibling}if(!y)throw Error(l(189))}}if(a.alternate!==r)throw Error(l(190))}if(a.tag!==3)throw Error(l(188));return a.stateNode.current===a?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),z=Symbol.for("react.consumer"),A=Symbol.for("react.context"),V=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),J=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),ft=Symbol.for("react.memo_cache_sentinel"),ht=Symbol.iterator;function lt(t){return t===null||typeof t!="object"?null:(t=ht&&t[ht]||t["@@iterator"],typeof t=="function"?t:null)}var bt=Symbol.for("react.client.reference");function Et(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===bt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case C:return"Fragment";case E:return"Profiler";case M:return"StrictMode";case j:return"Suspense";case I:return"SuspenseList";case W:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case T:return"Portal";case A:return(t.displayName||"Context")+".Provider";case z:return(t._context.displayName||"Context")+".Consumer";case V:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case J:return e=t.displayName||null,e!==null?e:Et(t.type)||"Memo";case Y:e=t._payload,t=t._init;try{return Et(t(e))}catch(a){}}return null}var ut=Array.isArray,B=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},ct=[],D=-1;function X(t){return{current:t}}function tt(t){0>D||(t.current=ct[D],ct[D]=null,D--)}function $(t,e){D++,ct[D]=t.current,t.current=e}var it=X(null),vt=X(null),dt=X(null),xt=X(null);function nt(t,e){switch($(dt,e),$(vt,t),$(it,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Vg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Vg(e),t=zg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}tt(it),$(it,t)}function Tt(){tt(it),tt(vt),tt(dt)}function qt(t){t.memoizedState!==null&&$(xt,t);var e=it.current,a=zg(e,t.type);e!==a&&($(vt,t),$(it,a))}function Ot(t){vt.current===t&&(tt(it),tt(vt)),xt.current===t&&(tt(xt),mr._currentValue=P)}var Ct=Object.prototype.hasOwnProperty,jt=n.unstable_scheduleCallback,de=n.unstable_cancelCallback,Ae=n.unstable_shouldYield,rn=n.unstable_requestPaint,he=n.unstable_now,Li=n.unstable_getCurrentPriorityLevel,Fr=n.unstable_ImmediatePriority,Ir=n.unstable_UserBlockingPriority,da=n.unstable_NormalPriority,ha=n.unstable_LowPriority,gh=n.unstable_IdlePriority,kS=n.log,HS=n.unstable_setDisableYieldValue,Ts=null,Ve=null;function ti(t){if(typeof kS=="function"&&HS(t),Ve&&typeof Ve.setStrictMode=="function")try{Ve.setStrictMode(Ts,t)}catch(e){}}var ze=Math.clz32?Math.clz32:YS,PS=Math.log,GS=Math.LN2;function YS(t){return t>>>=0,t===0?32:31-(PS(t)/GS|0)|0}var Wr=256,$r=4194304;function Bi(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Jr(t,e,a){var r=t.pendingLanes;if(r===0)return 0;var o=0,c=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var x=r&134217727;return x!==0?(r=x&~c,r!==0?o=Bi(r):(y&=x,y!==0?o=Bi(y):a||(a=x&~t,a!==0&&(o=Bi(a))))):(x=r&~c,x!==0?o=Bi(x):y!==0?o=Bi(y):a||(a=r&~t,a!==0&&(o=Bi(a)))),o===0?0:e!==0&&e!==o&&(e&c)===0&&(c=o&-o,a=e&-e,c>=a||c===32&&(a&4194048)!==0)?e:o}function ws(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function qS(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function yh(){var t=Wr;return Wr<<=1,(Wr&4194048)===0&&(Wr=256),t}function vh(){var t=$r;return $r<<=1,($r&62914560)===0&&($r=4194304),t}function tu(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function As(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function XS(t,e,a,r,o,c){var y=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var x=t.entanglements,R=t.expirationTimes,k=t.hiddenUpdates;for(a=y&~a;0<a;){var Z=31-ze(a),F=1<<Z;x[Z]=0,R[Z]=-1;var H=k[Z];if(H!==null)for(k[Z]=null,Z=0;Z<H.length;Z++){var G=H[Z];G!==null&&(G.lane&=-536870913)}a&=~F}r!==0&&bh(t,r,0),c!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=c&~(y&~e))}function bh(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-ze(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|a&4194090}function xh(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var r=31-ze(a),o=1<<r;o&e|t[r]&e&&(t[r]|=e),a&=~o}}function eu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function nu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Sh(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:ty(t.type))}function KS(t,e){var a=q.p;try{return q.p=t,e()}finally{q.p=a}}var ei=Math.random().toString(36).slice(2),ve="__reactFiber$"+ei,Re="__reactProps$"+ei,ma="__reactContainer$"+ei,iu="__reactEvents$"+ei,ZS="__reactListeners$"+ei,QS="__reactHandles$"+ei,Th="__reactResources$"+ei,Es="__reactMarker$"+ei;function au(t){delete t[ve],delete t[Re],delete t[iu],delete t[ZS],delete t[QS]}function pa(t){var e=t[ve];if(e)return e;for(var a=t.parentNode;a;){if(e=a[ma]||a[ve]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=kg(t);t!==null;){if(a=t[ve])return a;t=kg(t)}return e}t=a,a=t.parentNode}return null}function ga(t){if(t=t[ve]||t[ma]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ms(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function ya(t){var e=t[Th];return e||(e=t[Th]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function oe(t){t[Es]=!0}var wh=new Set,Ah={};function Ui(t,e){va(t,e),va(t+"Capture",e)}function va(t,e){for(Ah[t]=e,t=0;t<e.length;t++)wh.add(e[t])}var FS=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Eh={},Mh={};function IS(t){return Ct.call(Mh,t)?!0:Ct.call(Eh,t)?!1:FS.test(t)?Mh[t]=!0:(Eh[t]=!0,!1)}function tl(t,e,a){if(IS(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function el(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function _n(t,e,a,r){if(r===null)t.removeAttribute(a);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+r)}}var su,Ch;function ba(t){if(su===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);su=e&&e[1]||"",Ch=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+su+t+Ch}var ru=!1;function lu(t,e){if(!t||ru)return"";ru=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var F=function(){throw Error()};if(Object.defineProperty(F.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(F,[])}catch(G){var H=G}Reflect.construct(t,[],F)}else{try{F.call()}catch(G){H=G}t.call(F.prototype)}}else{try{throw Error()}catch(G){H=G}(F=t())&&typeof F.catch=="function"&&F.catch(function(){})}}catch(G){if(G&&H&&typeof G.stack=="string")return[G.stack,H.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=r.DetermineComponentFrameRoot(),y=c[0],x=c[1];if(y&&x){var R=y.split(`
`),k=x.split(`
`);for(o=r=0;r<R.length&&!R[r].includes("DetermineComponentFrameRoot");)r++;for(;o<k.length&&!k[o].includes("DetermineComponentFrameRoot");)o++;if(r===R.length||o===k.length)for(r=R.length-1,o=k.length-1;1<=r&&0<=o&&R[r]!==k[o];)o--;for(;1<=r&&0<=o;r--,o--)if(R[r]!==k[o]){if(r!==1||o!==1)do if(r--,o--,0>o||R[r]!==k[o]){var Z=`
`+R[r].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=r&&0<=o);break}}}finally{ru=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?ba(a):""}function WS(t){switch(t.tag){case 26:case 27:case 5:return ba(t.type);case 16:return ba("Lazy");case 13:return ba("Suspense");case 19:return ba("SuspenseList");case 0:case 15:return lu(t.type,!1);case 11:return lu(t.type.render,!1);case 1:return lu(t.type,!0);case 31:return ba("Activity");default:return""}}function Rh(t){try{var e="";do e+=WS(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Xe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Dh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function $S(t){var e=Dh(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof a!="undefined"&&typeof a.get=="function"&&typeof a.set=="function"){var o=a.get,c=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){r=""+y,c.call(this,y)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function nl(t){t._valueTracker||(t._valueTracker=$S(t))}function Oh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),r="";return t&&(r=Dh(t)?t.checked?"true":"false":t.value),t=r,t!==a?(e.setValue(t),!0):!1}function il(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var JS=/[\n"\\]/g;function Ke(t){return t.replace(JS,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function ou(t,e,a,r,o,c,y,x){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Xe(e)):t.value!==""+Xe(e)&&(t.value=""+Xe(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?uu(t,y,Xe(e)):a!=null?uu(t,y,Xe(a)):r!=null&&t.removeAttribute("value"),o==null&&c!=null&&(t.defaultChecked=!!c),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?t.name=""+Xe(x):t.removeAttribute("name")}function Nh(t,e,a,r,o,c,y,x){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;a=a!=null?""+Xe(a):"",e=e!=null?""+Xe(e):a,x||e===t.value||(t.value=e),t.defaultValue=e}r=r!=null?r:o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=x?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function uu(t,e,a){e==="number"&&il(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function xa(t,e,a,r){if(t=t.options,e){e={};for(var o=0;o<a.length;o++)e["$"+a[o]]=!0;for(a=0;a<t.length;a++)o=e.hasOwnProperty("$"+t[a].value),t[a].selected!==o&&(t[a].selected=o),o&&r&&(t[a].defaultSelected=!0)}else{for(a=""+Xe(a),e=null,o=0;o<t.length;o++){if(t[o].value===a){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function jh(t,e,a){if(e!=null&&(e=""+Xe(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+Xe(a):""}function _h(t,e,a,r){if(e==null){if(r!=null){if(a!=null)throw Error(l(92));if(ut(r)){if(1<r.length)throw Error(l(93));r=r[0]}a=r}a==null&&(a=""),e=a}a=Xe(e),t.defaultValue=a,r=t.textContent,r===a&&r!==""&&r!==null&&(t.value=r)}function Sa(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var t1=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Vh(t,e,a){var r=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,a):typeof a!="number"||a===0||t1.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function zh(t,e,a){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,a!=null){for(var r in a)!a.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var o in e)r=e[o],e.hasOwnProperty(o)&&a[o]!==r&&Vh(t,o,r)}else for(var c in e)e.hasOwnProperty(c)&&Vh(t,c,e[c])}function cu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var e1=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),n1=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function al(t){return n1.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var fu=null;function du(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ta=null,wa=null;function Lh(t){var e=ga(t);if(e&&(t=e.stateNode)){var a=t[Re]||null;t:switch(t=e.stateNode,e.type){case"input":if(ou(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Ke(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var r=a[e];if(r!==t&&r.form===t.form){var o=r[Re]||null;if(!o)throw Error(l(90));ou(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<a.length;e++)r=a[e],r.form===t.form&&Oh(r)}break t;case"textarea":jh(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&xa(t,!!a.multiple,e,!1)}}}var hu=!1;function Bh(t,e,a){if(hu)return t(e,a);hu=!0;try{var r=t(e);return r}finally{if(hu=!1,(Ta!==null||wa!==null)&&(Yl(),Ta&&(e=Ta,t=wa,wa=Ta=null,Lh(e),t)))for(e=0;e<t.length;e++)Lh(t[e])}}function Cs(t,e){var a=t.stateNode;if(a===null)return null;var r=a[Re]||null;if(r===null)return null;a=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(l(231,e,typeof a));return a}var Vn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),mu=!1;if(Vn)try{var Rs={};Object.defineProperty(Rs,"passive",{get:function(){mu=!0}}),window.addEventListener("test",Rs,Rs),window.removeEventListener("test",Rs,Rs)}catch(t){mu=!1}var ni=null,pu=null,sl=null;function Uh(){if(sl)return sl;var t,e=pu,a=e.length,r,o="value"in ni?ni.value:ni.textContent,c=o.length;for(t=0;t<a&&e[t]===o[t];t++);var y=a-t;for(r=1;r<=y&&e[a-r]===o[c-r];r++);return sl=o.slice(t,1<r?1-r:void 0)}function rl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ll(){return!0}function kh(){return!1}function De(t){function e(a,r,o,c,y){this._reactName=a,this._targetInst=o,this.type=r,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var x in t)t.hasOwnProperty(x)&&(a=t[x],this[x]=a?a(c):c[x]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ll:kh,this.isPropagationStopped=kh,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=ll)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=ll)},persist:function(){},isPersistent:ll}),e}var ki={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ol=De(ki),Ds=g({},ki,{view:0,detail:0}),i1=De(Ds),gu,yu,Os,ul=g({},Ds,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Os&&(Os&&t.type==="mousemove"?(gu=t.screenX-Os.screenX,yu=t.screenY-Os.screenY):yu=gu=0,Os=t),gu)},movementY:function(t){return"movementY"in t?t.movementY:yu}}),Hh=De(ul),a1=g({},ul,{dataTransfer:0}),s1=De(a1),r1=g({},Ds,{relatedTarget:0}),vu=De(r1),l1=g({},ki,{animationName:0,elapsedTime:0,pseudoElement:0}),o1=De(l1),u1=g({},ki,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),c1=De(u1),f1=g({},ki,{data:0}),Ph=De(f1),d1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},h1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},m1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function p1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=m1[t])?!!e[t]:!1}function bu(){return p1}var g1=g({},Ds,{key:function(t){if(t.key){var e=d1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=rl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?h1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bu,charCode:function(t){return t.type==="keypress"?rl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?rl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),y1=De(g1),v1=g({},ul,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gh=De(v1),b1=g({},Ds,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bu}),x1=De(b1),S1=g({},ki,{propertyName:0,elapsedTime:0,pseudoElement:0}),T1=De(S1),w1=g({},ul,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),A1=De(w1),E1=g({},ki,{newState:0,oldState:0}),M1=De(E1),C1=[9,13,27,32],xu=Vn&&"CompositionEvent"in window,Ns=null;Vn&&"documentMode"in document&&(Ns=document.documentMode);var R1=Vn&&"TextEvent"in window&&!Ns,Yh=Vn&&(!xu||Ns&&8<Ns&&11>=Ns),qh=" ",Xh=!1;function Kh(t,e){switch(t){case"keyup":return C1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Aa=!1;function D1(t,e){switch(t){case"compositionend":return Zh(e);case"keypress":return e.which!==32?null:(Xh=!0,qh);case"textInput":return t=e.data,t===qh&&Xh?null:t;default:return null}}function O1(t,e){if(Aa)return t==="compositionend"||!xu&&Kh(t,e)?(t=Uh(),sl=pu=ni=null,Aa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Yh&&e.locale!=="ko"?null:e.data;default:return null}}var N1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!N1[t.type]:e==="textarea"}function Fh(t,e,a,r){Ta?wa?wa.push(r):wa=[r]:Ta=r,e=Fl(e,"onChange"),0<e.length&&(a=new ol("onChange","change",null,a,r),t.push({event:a,listeners:e}))}var js=null,_s=null;function j1(t){Dg(t,0)}function cl(t){var e=Ms(t);if(Oh(e))return t}function Ih(t,e){if(t==="change")return e}var Wh=!1;if(Vn){var Su;if(Vn){var Tu="oninput"in document;if(!Tu){var $h=document.createElement("div");$h.setAttribute("oninput","return;"),Tu=typeof $h.oninput=="function"}Su=Tu}else Su=!1;Wh=Su&&(!document.documentMode||9<document.documentMode)}function Jh(){js&&(js.detachEvent("onpropertychange",tm),_s=js=null)}function tm(t){if(t.propertyName==="value"&&cl(_s)){var e=[];Fh(e,_s,t,du(t)),Bh(j1,e)}}function _1(t,e,a){t==="focusin"?(Jh(),js=e,_s=a,js.attachEvent("onpropertychange",tm)):t==="focusout"&&Jh()}function V1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return cl(_s)}function z1(t,e){if(t==="click")return cl(e)}function L1(t,e){if(t==="input"||t==="change")return cl(e)}function B1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Le=typeof Object.is=="function"?Object.is:B1;function Vs(t,e){if(Le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),r=Object.keys(e);if(a.length!==r.length)return!1;for(r=0;r<a.length;r++){var o=a[r];if(!Ct.call(e,o)||!Le(t[o],e[o]))return!1}return!0}function em(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function nm(t,e){var a=em(t);t=0;for(var r;a;){if(a.nodeType===3){if(r=t+a.textContent.length,t<=e&&r>=e)return{node:a,offset:e-t};t=r}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=em(a)}}function im(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?im(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function am(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=il(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch(r){a=!1}if(a)t=e.contentWindow;else break;e=il(t.document)}return e}function wu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var U1=Vn&&"documentMode"in document&&11>=document.documentMode,Ea=null,Au=null,zs=null,Eu=!1;function sm(t,e,a){var r=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Eu||Ea==null||Ea!==il(r)||(r=Ea,"selectionStart"in r&&wu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zs&&Vs(zs,r)||(zs=r,r=Fl(Au,"onSelect"),0<r.length&&(e=new ol("onSelect","select",null,e,a),t.push({event:e,listeners:r}),e.target=Ea)))}function Hi(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Ma={animationend:Hi("Animation","AnimationEnd"),animationiteration:Hi("Animation","AnimationIteration"),animationstart:Hi("Animation","AnimationStart"),transitionrun:Hi("Transition","TransitionRun"),transitionstart:Hi("Transition","TransitionStart"),transitioncancel:Hi("Transition","TransitionCancel"),transitionend:Hi("Transition","TransitionEnd")},Mu={},rm={};Vn&&(rm=document.createElement("div").style,"AnimationEvent"in window||(delete Ma.animationend.animation,delete Ma.animationiteration.animation,delete Ma.animationstart.animation),"TransitionEvent"in window||delete Ma.transitionend.transition);function Pi(t){if(Mu[t])return Mu[t];if(!Ma[t])return t;var e=Ma[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in rm)return Mu[t]=e[a];return t}var lm=Pi("animationend"),om=Pi("animationiteration"),um=Pi("animationstart"),k1=Pi("transitionrun"),H1=Pi("transitionstart"),P1=Pi("transitioncancel"),cm=Pi("transitionend"),fm=new Map,Cu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cu.push("scrollEnd");function ln(t,e){fm.set(t,e),Ui(e,[t])}var dm=new WeakMap;function Ze(t,e){if(typeof t=="object"&&t!==null){var a=dm.get(t);return a!==void 0?a:(e={value:t,source:e,stack:Rh(e)},dm.set(t,e),e)}return{value:t,source:e,stack:Rh(e)}}var Qe=[],Ca=0,Ru=0;function fl(){for(var t=Ca,e=Ru=Ca=0;e<t;){var a=Qe[e];Qe[e++]=null;var r=Qe[e];Qe[e++]=null;var o=Qe[e];Qe[e++]=null;var c=Qe[e];if(Qe[e++]=null,r!==null&&o!==null){var y=r.pending;y===null?o.next=o:(o.next=y.next,y.next=o),r.pending=o}c!==0&&hm(a,o,c)}}function dl(t,e,a,r){Qe[Ca++]=t,Qe[Ca++]=e,Qe[Ca++]=a,Qe[Ca++]=r,Ru|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function Du(t,e,a,r){return dl(t,e,a,r),hl(t)}function Ra(t,e){return dl(t,null,null,e),hl(t)}function hm(t,e,a){t.lanes|=a;var r=t.alternate;r!==null&&(r.lanes|=a);for(var o=!1,c=t.return;c!==null;)c.childLanes|=a,r=c.alternate,r!==null&&(r.childLanes|=a),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(o=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,o&&e!==null&&(o=31-ze(a),t=c.hiddenUpdates,r=t[o],r===null?t[o]=[e]:r.push(e),e.lane=a|536870912),c):null}function hl(t){if(50<rr)throw rr=0,zc=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Da={};function G1(t,e,a,r){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Be(t,e,a,r){return new G1(t,e,a,r)}function Ou(t){return t=t.prototype,!(!t||!t.isReactComponent)}function zn(t,e){var a=t.alternate;return a===null?(a=Be(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function mm(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ml(t,e,a,r,o,c){var y=0;if(r=t,typeof t=="function")Ou(t)&&(y=1);else if(typeof t=="string")y=qT(t,a,it.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case W:return t=Be(31,a,e,o),t.elementType=W,t.lanes=c,t;case C:return Gi(a.children,o,c,e);case M:y=8,o|=24;break;case E:return t=Be(12,a,e,o|2),t.elementType=E,t.lanes=c,t;case j:return t=Be(13,a,e,o),t.elementType=j,t.lanes=c,t;case I:return t=Be(19,a,e,o),t.elementType=I,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case N:case A:y=10;break t;case z:y=9;break t;case V:y=11;break t;case J:y=14;break t;case Y:y=16,r=null;break t}y=29,a=Error(l(130,t===null?"null":typeof t,"")),r=null}return e=Be(y,a,e,o),e.elementType=t,e.type=r,e.lanes=c,e}function Gi(t,e,a,r){return t=Be(7,t,r,e),t.lanes=a,t}function Nu(t,e,a){return t=Be(6,t,null,e),t.lanes=a,t}function ju(t,e,a){return e=Be(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oa=[],Na=0,pl=null,gl=0,Fe=[],Ie=0,Yi=null,Ln=1,Bn="";function qi(t,e){Oa[Na++]=gl,Oa[Na++]=pl,pl=t,gl=e}function pm(t,e,a){Fe[Ie++]=Ln,Fe[Ie++]=Bn,Fe[Ie++]=Yi,Yi=t;var r=Ln;t=Bn;var o=32-ze(r)-1;r&=~(1<<o),a+=1;var c=32-ze(e)+o;if(30<c){var y=o-o%5;c=(r&(1<<y)-1).toString(32),r>>=y,o-=y,Ln=1<<32-ze(e)+o|a<<o|r,Bn=c+t}else Ln=1<<c|a<<o|r,Bn=t}function _u(t){t.return!==null&&(qi(t,1),pm(t,1,0))}function Vu(t){for(;t===pl;)pl=Oa[--Na],Oa[Na]=null,gl=Oa[--Na],Oa[Na]=null;for(;t===Yi;)Yi=Fe[--Ie],Fe[Ie]=null,Bn=Fe[--Ie],Fe[Ie]=null,Ln=Fe[--Ie],Fe[Ie]=null}var Ee=null,It=null,Vt=!1,Xi=null,pn=!1,zu=Error(l(519));function Ki(t){var e=Error(l(418,""));throw Us(Ze(e,t)),zu}function gm(t){var e=t.stateNode,a=t.type,r=t.memoizedProps;switch(e[ve]=t,e[Re]=r,a){case"dialog":Dt("cancel",e),Dt("close",e);break;case"iframe":case"object":case"embed":Dt("load",e);break;case"video":case"audio":for(a=0;a<or.length;a++)Dt(or[a],e);break;case"source":Dt("error",e);break;case"img":case"image":case"link":Dt("error",e),Dt("load",e);break;case"details":Dt("toggle",e);break;case"input":Dt("invalid",e),Nh(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),nl(e);break;case"select":Dt("invalid",e);break;case"textarea":Dt("invalid",e),_h(e,r.value,r.defaultValue,r.children),nl(e)}a=r.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||r.suppressHydrationWarning===!0||_g(e.textContent,a)?(r.popover!=null&&(Dt("beforetoggle",e),Dt("toggle",e)),r.onScroll!=null&&Dt("scroll",e),r.onScrollEnd!=null&&Dt("scrollend",e),r.onClick!=null&&(e.onclick=Il),e=!0):e=!1,e||Ki(t)}function ym(t){for(Ee=t.return;Ee;)switch(Ee.tag){case 5:case 13:pn=!1;return;case 27:case 3:pn=!0;return;default:Ee=Ee.return}}function Ls(t){if(t!==Ee)return!1;if(!Vt)return ym(t),Vt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||Wc(t.type,t.memoizedProps)),a=!a),a&&It&&Ki(t),ym(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){It=un(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}It=null}}else e===27?(e=It,vi(t.type)?(t=ef,ef=null,It=t):It=e):It=Ee?un(t.stateNode.nextSibling):null;return!0}function Bs(){It=Ee=null,Vt=!1}function vm(){var t=Xi;return t!==null&&(je===null?je=t:je.push.apply(je,t),Xi=null),t}function Us(t){Xi===null?Xi=[t]:Xi.push(t)}var Lu=X(null),Zi=null,Un=null;function ii(t,e,a){$(Lu,e._currentValue),e._currentValue=a}function kn(t){t._currentValue=Lu.current,tt(Lu)}function Bu(t,e,a){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===a)break;t=t.return}}function Uu(t,e,a,r){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var c=o.dependencies;if(c!==null){var y=o.child;c=c.firstContext;t:for(;c!==null;){var x=c;c=o;for(var R=0;R<e.length;R++)if(x.context===e[R]){c.lanes|=a,x=c.alternate,x!==null&&(x.lanes|=a),Bu(c.return,a,t),r||(y=null);break t}c=x.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(l(341));y.lanes|=a,c=y.alternate,c!==null&&(c.lanes|=a),Bu(y,a,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function ks(t,e,a,r){t=null;for(var o=e,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var x=o.type;Le(o.pendingProps.value,y.value)||(t!==null?t.push(x):t=[x])}}else if(o===xt.current){if(y=o.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(mr):t=[mr])}o=o.return}t!==null&&Uu(e,t,a,r),e.flags|=262144}function yl(t){for(t=t.firstContext;t!==null;){if(!Le(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Qi(t){Zi=t,Un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function be(t){return bm(Zi,t)}function vl(t,e){return Zi===null&&Qi(t),bm(t,e)}function bm(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},Un===null){if(t===null)throw Error(l(308));Un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Un=Un.next=e;return a}var Y1=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},q1=n.unstable_scheduleCallback,X1=n.unstable_NormalPriority,se={$$typeof:A,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ku(){return{controller:new Y1,data:new Map,refCount:0}}function Hs(t){t.refCount--,t.refCount===0&&q1(X1,function(){t.controller.abort()})}var Ps=null,Hu=0,ja=0,_a=null;function K1(t,e){if(Ps===null){var a=Ps=[];Hu=0,ja=Gc(),_a={status:"pending",value:void 0,then:function(r){a.push(r)}}}return Hu++,e.then(xm,xm),e}function xm(){if(--Hu===0&&Ps!==null){_a!==null&&(_a.status="fulfilled");var t=Ps;Ps=null,ja=0,_a=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Z1(t,e){var a=[],r={status:"pending",value:null,reason:null,then:function(o){a.push(o)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var o=0;o<a.length;o++)(0,a[o])(e)},function(o){for(r.status="rejected",r.reason=o,o=0;o<a.length;o++)(0,a[o])(void 0)}),r}var Sm=B.S;B.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&K1(t,e),Sm!==null&&Sm(t,e)};var Fi=X(null);function Pu(){var t=Fi.current;return t!==null?t:Yt.pooledCache}function bl(t,e){e===null?$(Fi,Fi.current):$(Fi,e.pool)}function Tm(){var t=Pu();return t===null?null:{parent:se._currentValue,pool:t}}var Gs=Error(l(460)),wm=Error(l(474)),xl=Error(l(542)),Gu={then:function(){}};function Am(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Sl(){}function Em(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(Sl,Sl),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t;default:if(typeof e.status=="string")e.then(Sl,Sl);else{if(t=Yt,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=r}},function(r){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t}throw Ys=e,Gs}}var Ys=null;function Mm(){if(Ys===null)throw Error(l(459));var t=Ys;return Ys=null,t}function Cm(t){if(t===Gs||t===xl)throw Error(l(483))}var ai=!1;function Yu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function si(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ri(t,e,a){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(zt&2)!==0){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,e=hl(t),hm(t,null,a),e}return dl(t,r,e,a),hl(t)}function qs(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,a|=r,e.lanes=a,xh(t,a)}}function Xu(t,e){var a=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,a===r)){var o=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var y={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?o=c=y:c=c.next=y,a=a.next}while(a!==null);c===null?o=c=e:c=c.next=e}else o=c=e;a={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:r.shared,callbacks:r.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var Ku=!1;function Xs(){if(Ku){var t=_a;if(t!==null)throw t}}function Ks(t,e,a,r){Ku=!1;var o=t.updateQueue;ai=!1;var c=o.firstBaseUpdate,y=o.lastBaseUpdate,x=o.shared.pending;if(x!==null){o.shared.pending=null;var R=x,k=R.next;R.next=null,y===null?c=k:y.next=k,y=R;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,x=Z.lastBaseUpdate,x!==y&&(x===null?Z.firstBaseUpdate=k:x.next=k,Z.lastBaseUpdate=R))}if(c!==null){var F=o.baseState;y=0,Z=k=R=null,x=c;do{var H=x.lane&-536870913,G=H!==x.lane;if(G?(Nt&H)===H:(r&H)===H){H!==0&&H===ja&&(Ku=!0),Z!==null&&(Z=Z.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});t:{var gt=t,mt=x;H=e;var Ht=a;switch(mt.tag){case 1:if(gt=mt.payload,typeof gt=="function"){F=gt.call(Ht,F,H);break t}F=gt;break t;case 3:gt.flags=gt.flags&-65537|128;case 0:if(gt=mt.payload,H=typeof gt=="function"?gt.call(Ht,F,H):gt,H==null)break t;F=g({},F,H);break t;case 2:ai=!0}}H=x.callback,H!==null&&(t.flags|=64,G&&(t.flags|=8192),G=o.callbacks,G===null?o.callbacks=[H]:G.push(H))}else G={lane:H,tag:x.tag,payload:x.payload,callback:x.callback,next:null},Z===null?(k=Z=G,R=F):Z=Z.next=G,y|=H;if(x=x.next,x===null){if(x=o.shared.pending,x===null)break;G=x,x=G.next,G.next=null,o.lastBaseUpdate=G,o.shared.pending=null}}while(!0);Z===null&&(R=F),o.baseState=R,o.firstBaseUpdate=k,o.lastBaseUpdate=Z,c===null&&(o.shared.lanes=0),mi|=y,t.lanes=y,t.memoizedState=F}}function Rm(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Dm(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)Rm(a[t],e)}var Va=X(null),Tl=X(0);function Om(t,e){t=Kn,$(Tl,t),$(Va,e),Kn=t|e.baseLanes}function Zu(){$(Tl,Kn),$(Va,Va.current)}function Qu(){Kn=Tl.current,tt(Va),tt(Tl)}var li=0,wt=null,Ut=null,ne=null,wl=!1,za=!1,Ii=!1,Al=0,Zs=0,La=null,Q1=0;function Jt(){throw Error(l(321))}function Fu(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!Le(t[a],e[a]))return!1;return!0}function Iu(t,e,a,r,o,c){return li=c,wt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,B.H=t===null||t.memoizedState===null?hp:mp,Ii=!1,c=a(r,o),Ii=!1,za&&(c=jm(e,a,r,o)),Nm(t),c}function Nm(t){B.H=Ol;var e=Ut!==null&&Ut.next!==null;if(li=0,ne=Ut=wt=null,wl=!1,Zs=0,La=null,e)throw Error(l(300));t===null||ue||(t=t.dependencies,t!==null&&yl(t)&&(ue=!0))}function jm(t,e,a,r){wt=t;var o=0;do{if(za&&(La=null),Zs=0,za=!1,25<=o)throw Error(l(301));if(o+=1,ne=Ut=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}B.H=eT,c=e(a,r)}while(za);return c}function F1(){var t=B.H,e=t.useState()[0];return e=typeof e.then=="function"?Qs(e):e,t=t.useState()[0],(Ut!==null?Ut.memoizedState:null)!==t&&(wt.flags|=1024),e}function Wu(){var t=Al!==0;return Al=0,t}function $u(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function Ju(t){if(wl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}wl=!1}li=0,ne=Ut=wt=null,za=!1,Zs=Al=0,La=null}function Oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?wt.memoizedState=ne=t:ne=ne.next=t,ne}function ie(){if(Ut===null){var t=wt.alternate;t=t!==null?t.memoizedState:null}else t=Ut.next;var e=ne===null?wt.memoizedState:ne.next;if(e!==null)ne=e,Ut=t;else{if(t===null)throw wt.alternate===null?Error(l(467)):Error(l(310));Ut=t,t={memoizedState:Ut.memoizedState,baseState:Ut.baseState,baseQueue:Ut.baseQueue,queue:Ut.queue,next:null},ne===null?wt.memoizedState=ne=t:ne=ne.next=t}return ne}function tc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qs(t){var e=Zs;return Zs+=1,La===null&&(La=[]),t=Em(La,t,e),e=wt,(ne===null?e.memoizedState:ne.next)===null&&(e=e.alternate,B.H=e===null||e.memoizedState===null?hp:mp),t}function El(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qs(t);if(t.$$typeof===A)return be(t)}throw Error(l(438,String(t)))}function ec(t){var e=null,a=wt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var r=wt.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=tc(),wt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),r=0;r<t;r++)a[r]=ft;return e.index++,a}function Hn(t,e){return typeof e=="function"?e(t):e}function Ml(t){var e=ie();return nc(e,Ut,t)}function nc(t,e,a){var r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=a;var o=t.baseQueue,c=r.pending;if(c!==null){if(o!==null){var y=o.next;o.next=c.next,c.next=y}e.baseQueue=o=c,r.pending=null}if(c=t.baseState,o===null)t.memoizedState=c;else{e=o.next;var x=y=null,R=null,k=e,Z=!1;do{var F=k.lane&-536870913;if(F!==k.lane?(Nt&F)===F:(li&F)===F){var H=k.revertLane;if(H===0)R!==null&&(R=R.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),F===ja&&(Z=!0);else if((li&H)===H){k=k.next,H===ja&&(Z=!0);continue}else F={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=F,y=c):R=R.next=F,wt.lanes|=H,mi|=H;F=k.action,Ii&&a(c,F),c=k.hasEagerState?k.eagerState:a(c,F)}else H={lane:F,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=H,y=c):R=R.next=H,wt.lanes|=F,mi|=F;k=k.next}while(k!==null&&k!==e);if(R===null?y=c:R.next=x,!Le(c,t.memoizedState)&&(ue=!0,Z&&(a=_a,a!==null)))throw a;t.memoizedState=c,t.baseState=y,t.baseQueue=R,r.lastRenderedState=c}return o===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function ic(t){var e=ie(),a=e.queue;if(a===null)throw Error(l(311));a.lastRenderedReducer=t;var r=a.dispatch,o=a.pending,c=e.memoizedState;if(o!==null){a.pending=null;var y=o=o.next;do c=t(c,y.action),y=y.next;while(y!==o);Le(c,e.memoizedState)||(ue=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),a.lastRenderedState=c}return[c,r]}function _m(t,e,a){var r=wt,o=ie(),c=Vt;if(c){if(a===void 0)throw Error(l(407));a=a()}else a=e();var y=!Le((Ut||o).memoizedState,a);y&&(o.memoizedState=a,ue=!0),o=o.queue;var x=Lm.bind(null,r,o,t);if(Fs(2048,8,x,[t]),o.getSnapshot!==e||y||ne!==null&&ne.memoizedState.tag&1){if(r.flags|=2048,Ba(9,Cl(),zm.bind(null,r,o,a,e),null),Yt===null)throw Error(l(349));c||(li&124)!==0||Vm(r,e,a)}return a}function Vm(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=wt.updateQueue,e===null?(e=tc(),wt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function zm(t,e,a,r){e.value=a,e.getSnapshot=r,Bm(e)&&Um(t)}function Lm(t,e,a){return a(function(){Bm(e)&&Um(t)})}function Bm(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!Le(t,a)}catch(r){return!0}}function Um(t){var e=Ra(t,2);e!==null&&Ge(e,t,2)}function ac(t){var e=Oe();if(typeof t=="function"){var a=t;if(t=a(),Ii){ti(!0);try{a()}finally{ti(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:t},e}function km(t,e,a,r){return t.baseState=a,nc(t,Ut,typeof r=="function"?r:Hn)}function I1(t,e,a,r,o){if(Dl(t))throw Error(l(485));if(t=e.action,t!==null){var c={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};B.T!==null?a(!0):c.isTransition=!1,r(c),a=e.pending,a===null?(c.next=e.pending=c,Hm(e,c)):(c.next=a.next,e.pending=a.next=c)}}function Hm(t,e){var a=e.action,r=e.payload,o=t.state;if(e.isTransition){var c=B.T,y={};B.T=y;try{var x=a(o,r),R=B.S;R!==null&&R(y,x),Pm(t,e,x)}catch(k){sc(t,e,k)}finally{B.T=c}}else try{c=a(o,r),Pm(t,e,c)}catch(k){sc(t,e,k)}}function Pm(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(r){Gm(t,e,r)},function(r){return sc(t,e,r)}):Gm(t,e,a)}function Gm(t,e,a){e.status="fulfilled",e.value=a,Ym(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,Hm(t,a)))}function sc(t,e,a){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=a,Ym(e),e=e.next;while(e!==r)}t.action=null}function Ym(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function qm(t,e){return e}function Xm(t,e){if(Vt){var a=Yt.formState;if(a!==null){t:{var r=wt;if(Vt){if(It){e:{for(var o=It,c=pn;o.nodeType!==8;){if(!c){o=null;break e}if(o=un(o.nextSibling),o===null){o=null;break e}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){It=un(o.nextSibling),r=o.data==="F!";break t}}Ki(r)}r=!1}r&&(e=a[0])}}return a=Oe(),a.memoizedState=a.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qm,lastRenderedState:e},a.queue=r,a=cp.bind(null,wt,r),r.dispatch=a,r=ac(!1),c=cc.bind(null,wt,!1,r.queue),r=Oe(),o={state:e,dispatch:null,action:t,pending:null},r.queue=o,a=I1.bind(null,wt,o,c,a),o.dispatch=a,r.memoizedState=t,[e,a,!1]}function Km(t){var e=ie();return Zm(e,Ut,t)}function Zm(t,e,a){if(e=nc(t,e,qm)[0],t=Ml(Hn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Qs(e)}catch(y){throw y===Gs?xl:y}else r=e;e=ie();var o=e.queue,c=o.dispatch;return a!==e.memoizedState&&(wt.flags|=2048,Ba(9,Cl(),W1.bind(null,o,a),null)),[r,c,t]}function W1(t,e){t.action=e}function Qm(t){var e=ie(),a=Ut;if(a!==null)return Zm(e,a,t);ie(),e=e.memoizedState,a=ie();var r=a.queue.dispatch;return a.memoizedState=t,[e,r,!1]}function Ba(t,e,a,r){return t={tag:t,create:a,deps:r,inst:e,next:null},e=wt.updateQueue,e===null&&(e=tc(),wt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(r=a.next,a.next=t,t.next=r,e.lastEffect=t),t}function Cl(){return{destroy:void 0,resource:void 0}}function Fm(){return ie().memoizedState}function Rl(t,e,a,r){var o=Oe();r=r===void 0?null:r,wt.flags|=t,o.memoizedState=Ba(1|e,Cl(),a,r)}function Fs(t,e,a,r){var o=ie();r=r===void 0?null:r;var c=o.memoizedState.inst;Ut!==null&&r!==null&&Fu(r,Ut.memoizedState.deps)?o.memoizedState=Ba(e,c,a,r):(wt.flags|=t,o.memoizedState=Ba(1|e,c,a,r))}function Im(t,e){Rl(8390656,8,t,e)}function Wm(t,e){Fs(2048,8,t,e)}function $m(t,e){return Fs(4,2,t,e)}function Jm(t,e){return Fs(4,4,t,e)}function tp(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ep(t,e,a){a=a!=null?a.concat([t]):null,Fs(4,4,tp.bind(null,e,t),a)}function rc(){}function np(t,e){var a=ie();e=e===void 0?null:e;var r=a.memoizedState;return e!==null&&Fu(e,r[1])?r[0]:(a.memoizedState=[t,e],t)}function ip(t,e){var a=ie();e=e===void 0?null:e;var r=a.memoizedState;if(e!==null&&Fu(e,r[1]))return r[0];if(r=t(),Ii){ti(!0);try{t()}finally{ti(!1)}}return a.memoizedState=[r,e],r}function lc(t,e,a){return a===void 0||(li&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=rg(),wt.lanes|=t,mi|=t,a)}function ap(t,e,a,r){return Le(a,e)?a:Va.current!==null?(t=lc(t,a,r),Le(t,e)||(ue=!0),t):(li&42)===0?(ue=!0,t.memoizedState=a):(t=rg(),wt.lanes|=t,mi|=t,e)}function sp(t,e,a,r,o){var c=q.p;q.p=c!==0&&8>c?c:8;var y=B.T,x={};B.T=x,cc(t,!1,e,a);try{var R=o(),k=B.S;if(k!==null&&k(x,R),R!==null&&typeof R=="object"&&typeof R.then=="function"){var Z=Z1(R,r);Is(t,e,Z,Pe(t))}else Is(t,e,r,Pe(t))}catch(F){Is(t,e,{then:function(){},status:"rejected",reason:F},Pe())}finally{q.p=c,B.T=y}}function $1(){}function oc(t,e,a,r){if(t.tag!==5)throw Error(l(476));var o=rp(t).queue;sp(t,o,e,P,a===null?$1:function(){return lp(t),a(r)})}function rp(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:P},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function lp(t){var e=rp(t).next.queue;Is(t,e,{},Pe())}function uc(){return be(mr)}function op(){return ie().memoizedState}function up(){return ie().memoizedState}function J1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=Pe();t=si(a);var r=ri(e,t,a);r!==null&&(Ge(r,e,a),qs(r,e,a)),e={cache:ku()},t.payload=e;return}e=e.return}}function tT(t,e,a){var r=Pe();a={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Dl(t)?fp(e,a):(a=Du(t,e,a,r),a!==null&&(Ge(a,t,r),dp(a,e,r)))}function cp(t,e,a){var r=Pe();Is(t,e,a,r)}function Is(t,e,a,r){var o={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Dl(t))fp(e,o);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var y=e.lastRenderedState,x=c(y,a);if(o.hasEagerState=!0,o.eagerState=x,Le(x,y))return dl(t,e,o,0),Yt===null&&fl(),!1}catch(R){}finally{}if(a=Du(t,e,o,r),a!==null)return Ge(a,t,r),dp(a,e,r),!0}return!1}function cc(t,e,a,r){if(r={lane:2,revertLane:Gc(),action:r,hasEagerState:!1,eagerState:null,next:null},Dl(t)){if(e)throw Error(l(479))}else e=Du(t,a,r,2),e!==null&&Ge(e,t,2)}function Dl(t){var e=t.alternate;return t===wt||e!==null&&e===wt}function fp(t,e){za=wl=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function dp(t,e,a){if((a&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,a|=r,e.lanes=a,xh(t,a)}}var Ol={readContext:be,use:El,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},hp={readContext:be,use:El,useCallback:function(t,e){return Oe().memoizedState=[t,e===void 0?null:e],t},useContext:be,useEffect:Im,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,Rl(4194308,4,tp.bind(null,e,t),a)},useLayoutEffect:function(t,e){return Rl(4194308,4,t,e)},useInsertionEffect:function(t,e){Rl(4,2,t,e)},useMemo:function(t,e){var a=Oe();e=e===void 0?null:e;var r=t();if(Ii){ti(!0);try{t()}finally{ti(!1)}}return a.memoizedState=[r,e],r},useReducer:function(t,e,a){var r=Oe();if(a!==void 0){var o=a(e);if(Ii){ti(!0);try{a(e)}finally{ti(!1)}}}else o=e;return r.memoizedState=r.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},r.queue=t,t=t.dispatch=tT.bind(null,wt,t),[r.memoizedState,t]},useRef:function(t){var e=Oe();return t={current:t},e.memoizedState=t},useState:function(t){t=ac(t);var e=t.queue,a=cp.bind(null,wt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:rc,useDeferredValue:function(t,e){var a=Oe();return lc(a,t,e)},useTransition:function(){var t=ac(!1);return t=sp.bind(null,wt,t.queue,!0,!1),Oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var r=wt,o=Oe();if(Vt){if(a===void 0)throw Error(l(407));a=a()}else{if(a=e(),Yt===null)throw Error(l(349));(Nt&124)!==0||Vm(r,e,a)}o.memoizedState=a;var c={value:a,getSnapshot:e};return o.queue=c,Im(Lm.bind(null,r,c,t),[t]),r.flags|=2048,Ba(9,Cl(),zm.bind(null,r,c,a,e),null),a},useId:function(){var t=Oe(),e=Yt.identifierPrefix;if(Vt){var a=Bn,r=Ln;a=(r&~(1<<32-ze(r)-1)).toString(32)+a,e="«"+e+"R"+a,a=Al++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=Q1++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:uc,useFormState:Xm,useActionState:Xm,useOptimistic:function(t){var e=Oe();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=cc.bind(null,wt,!0,a),a.dispatch=e,[t,e]},useMemoCache:ec,useCacheRefresh:function(){return Oe().memoizedState=J1.bind(null,wt)}},mp={readContext:be,use:El,useCallback:np,useContext:be,useEffect:Wm,useImperativeHandle:ep,useInsertionEffect:$m,useLayoutEffect:Jm,useMemo:ip,useReducer:Ml,useRef:Fm,useState:function(){return Ml(Hn)},useDebugValue:rc,useDeferredValue:function(t,e){var a=ie();return ap(a,Ut.memoizedState,t,e)},useTransition:function(){var t=Ml(Hn)[0],e=ie().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:_m,useId:op,useHostTransitionStatus:uc,useFormState:Km,useActionState:Km,useOptimistic:function(t,e){var a=ie();return km(a,Ut,t,e)},useMemoCache:ec,useCacheRefresh:up},eT={readContext:be,use:El,useCallback:np,useContext:be,useEffect:Wm,useImperativeHandle:ep,useInsertionEffect:$m,useLayoutEffect:Jm,useMemo:ip,useReducer:ic,useRef:Fm,useState:function(){return ic(Hn)},useDebugValue:rc,useDeferredValue:function(t,e){var a=ie();return Ut===null?lc(a,t,e):ap(a,Ut.memoizedState,t,e)},useTransition:function(){var t=ic(Hn)[0],e=ie().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:_m,useId:op,useHostTransitionStatus:uc,useFormState:Qm,useActionState:Qm,useOptimistic:function(t,e){var a=ie();return Ut!==null?km(a,Ut,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:ec,useCacheRefresh:up},Ua=null,Ws=0;function Nl(t){var e=Ws;return Ws+=1,Ua===null&&(Ua=[]),Em(Ua,t,e)}function $s(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function jl(t,e){throw e.$$typeof===v?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function pp(t){var e=t._init;return e(t._payload)}function gp(t){function e(L,_){if(t){var U=L.deletions;U===null?(L.deletions=[_],L.flags|=16):U.push(_)}}function a(L,_){if(!t)return null;for(;_!==null;)e(L,_),_=_.sibling;return null}function r(L){for(var _=new Map;L!==null;)L.key!==null?_.set(L.key,L):_.set(L.index,L),L=L.sibling;return _}function o(L,_){return L=zn(L,_),L.index=0,L.sibling=null,L}function c(L,_,U){return L.index=U,t?(U=L.alternate,U!==null?(U=U.index,U<_?(L.flags|=67108866,_):U):(L.flags|=67108866,_)):(L.flags|=1048576,_)}function y(L){return t&&L.alternate===null&&(L.flags|=67108866),L}function x(L,_,U,Q){return _===null||_.tag!==6?(_=Nu(U,L.mode,Q),_.return=L,_):(_=o(_,U),_.return=L,_)}function R(L,_,U,Q){var at=U.type;return at===C?Z(L,_,U.props.children,Q,U.key):_!==null&&(_.elementType===at||typeof at=="object"&&at!==null&&at.$$typeof===Y&&pp(at)===_.type)?(_=o(_,U.props),$s(_,U),_.return=L,_):(_=ml(U.type,U.key,U.props,null,L.mode,Q),$s(_,U),_.return=L,_)}function k(L,_,U,Q){return _===null||_.tag!==4||_.stateNode.containerInfo!==U.containerInfo||_.stateNode.implementation!==U.implementation?(_=ju(U,L.mode,Q),_.return=L,_):(_=o(_,U.children||[]),_.return=L,_)}function Z(L,_,U,Q,at){return _===null||_.tag!==7?(_=Gi(U,L.mode,Q,at),_.return=L,_):(_=o(_,U),_.return=L,_)}function F(L,_,U){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=Nu(""+_,L.mode,U),_.return=L,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case b:return U=ml(_.type,_.key,_.props,null,L.mode,U),$s(U,_),U.return=L,U;case T:return _=ju(_,L.mode,U),_.return=L,_;case Y:var Q=_._init;return _=Q(_._payload),F(L,_,U)}if(ut(_)||lt(_))return _=Gi(_,L.mode,U,null),_.return=L,_;if(typeof _.then=="function")return F(L,Nl(_),U);if(_.$$typeof===A)return F(L,vl(L,_),U);jl(L,_)}return null}function H(L,_,U,Q){var at=_!==null?_.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return at!==null?null:x(L,_,""+U,Q);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case b:return U.key===at?R(L,_,U,Q):null;case T:return U.key===at?k(L,_,U,Q):null;case Y:return at=U._init,U=at(U._payload),H(L,_,U,Q)}if(ut(U)||lt(U))return at!==null?null:Z(L,_,U,Q,null);if(typeof U.then=="function")return H(L,_,Nl(U),Q);if(U.$$typeof===A)return H(L,_,vl(L,U),Q);jl(L,U)}return null}function G(L,_,U,Q,at){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return L=L.get(U)||null,x(_,L,""+Q,at);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case b:return L=L.get(Q.key===null?U:Q.key)||null,R(_,L,Q,at);case T:return L=L.get(Q.key===null?U:Q.key)||null,k(_,L,Q,at);case Y:var Mt=Q._init;return Q=Mt(Q._payload),G(L,_,U,Q,at)}if(ut(Q)||lt(Q))return L=L.get(U)||null,Z(_,L,Q,at,null);if(typeof Q.then=="function")return G(L,_,U,Nl(Q),at);if(Q.$$typeof===A)return G(L,_,U,vl(_,Q),at);jl(_,Q)}return null}function gt(L,_,U,Q){for(var at=null,Mt=null,ot=_,pt=_=0,fe=null;ot!==null&&pt<U.length;pt++){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var _t=H(L,ot,U[pt],Q);if(_t===null){ot===null&&(ot=fe);break}t&&ot&&_t.alternate===null&&e(L,ot),_=c(_t,_,pt),Mt===null?at=_t:Mt.sibling=_t,Mt=_t,ot=fe}if(pt===U.length)return a(L,ot),Vt&&qi(L,pt),at;if(ot===null){for(;pt<U.length;pt++)ot=F(L,U[pt],Q),ot!==null&&(_=c(ot,_,pt),Mt===null?at=ot:Mt.sibling=ot,Mt=ot);return Vt&&qi(L,pt),at}for(ot=r(ot);pt<U.length;pt++)fe=G(ot,L,pt,U[pt],Q),fe!==null&&(t&&fe.alternate!==null&&ot.delete(fe.key===null?pt:fe.key),_=c(fe,_,pt),Mt===null?at=fe:Mt.sibling=fe,Mt=fe);return t&&ot.forEach(function(wi){return e(L,wi)}),Vt&&qi(L,pt),at}function mt(L,_,U,Q){if(U==null)throw Error(l(151));for(var at=null,Mt=null,ot=_,pt=_=0,fe=null,_t=U.next();ot!==null&&!_t.done;pt++,_t=U.next()){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var wi=H(L,ot,_t.value,Q);if(wi===null){ot===null&&(ot=fe);break}t&&ot&&wi.alternate===null&&e(L,ot),_=c(wi,_,pt),Mt===null?at=wi:Mt.sibling=wi,Mt=wi,ot=fe}if(_t.done)return a(L,ot),Vt&&qi(L,pt),at;if(ot===null){for(;!_t.done;pt++,_t=U.next())_t=F(L,_t.value,Q),_t!==null&&(_=c(_t,_,pt),Mt===null?at=_t:Mt.sibling=_t,Mt=_t);return Vt&&qi(L,pt),at}for(ot=r(ot);!_t.done;pt++,_t=U.next())_t=G(ot,L,pt,_t.value,Q),_t!==null&&(t&&_t.alternate!==null&&ot.delete(_t.key===null?pt:_t.key),_=c(_t,_,pt),Mt===null?at=_t:Mt.sibling=_t,Mt=_t);return t&&ot.forEach(function(nw){return e(L,nw)}),Vt&&qi(L,pt),at}function Ht(L,_,U,Q){if(typeof U=="object"&&U!==null&&U.type===C&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case b:t:{for(var at=U.key;_!==null;){if(_.key===at){if(at=U.type,at===C){if(_.tag===7){a(L,_.sibling),Q=o(_,U.props.children),Q.return=L,L=Q;break t}}else if(_.elementType===at||typeof at=="object"&&at!==null&&at.$$typeof===Y&&pp(at)===_.type){a(L,_.sibling),Q=o(_,U.props),$s(Q,U),Q.return=L,L=Q;break t}a(L,_);break}else e(L,_);_=_.sibling}U.type===C?(Q=Gi(U.props.children,L.mode,Q,U.key),Q.return=L,L=Q):(Q=ml(U.type,U.key,U.props,null,L.mode,Q),$s(Q,U),Q.return=L,L=Q)}return y(L);case T:t:{for(at=U.key;_!==null;){if(_.key===at)if(_.tag===4&&_.stateNode.containerInfo===U.containerInfo&&_.stateNode.implementation===U.implementation){a(L,_.sibling),Q=o(_,U.children||[]),Q.return=L,L=Q;break t}else{a(L,_);break}else e(L,_);_=_.sibling}Q=ju(U,L.mode,Q),Q.return=L,L=Q}return y(L);case Y:return at=U._init,U=at(U._payload),Ht(L,_,U,Q)}if(ut(U))return gt(L,_,U,Q);if(lt(U)){if(at=lt(U),typeof at!="function")throw Error(l(150));return U=at.call(U),mt(L,_,U,Q)}if(typeof U.then=="function")return Ht(L,_,Nl(U),Q);if(U.$$typeof===A)return Ht(L,_,vl(L,U),Q);jl(L,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,_!==null&&_.tag===6?(a(L,_.sibling),Q=o(_,U),Q.return=L,L=Q):(a(L,_),Q=Nu(U,L.mode,Q),Q.return=L,L=Q),y(L)):a(L,_)}return function(L,_,U,Q){try{Ws=0;var at=Ht(L,_,U,Q);return Ua=null,at}catch(ot){if(ot===Gs||ot===xl)throw ot;var Mt=Be(29,ot,null,L.mode);return Mt.lanes=Q,Mt.return=L,Mt}finally{}}}var ka=gp(!0),yp=gp(!1),We=X(null),gn=null;function oi(t){var e=t.alternate;$(re,re.current&1),$(We,t),gn===null&&(e===null||Va.current!==null||e.memoizedState!==null)&&(gn=t)}function vp(t){if(t.tag===22){if($(re,re.current),$(We,t),gn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(gn=t)}}else ui()}function ui(){$(re,re.current),$(We,We.current)}function Pn(t){tt(We),gn===t&&(gn=null),tt(re)}var re=X(0);function _l(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||tf(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function fc(t,e,a,r){e=t.memoizedState,a=a(r,e),a=a==null?e:g({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var dc={enqueueSetState:function(t,e,a){t=t._reactInternals;var r=Pe(),o=si(r);o.payload=e,a!=null&&(o.callback=a),e=ri(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var r=Pe(),o=si(r);o.tag=1,o.payload=e,a!=null&&(o.callback=a),e=ri(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=Pe(),r=si(a);r.tag=2,e!=null&&(r.callback=e),e=ri(t,r,a),e!==null&&(Ge(e,t,a),qs(e,t,a))}};function bp(t,e,a,r,o,c,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,c,y):e.prototype&&e.prototype.isPureReactComponent?!Vs(a,r)||!Vs(o,c):!0}function xp(t,e,a,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,r),e.state!==t&&dc.enqueueReplaceState(e,e.state,null)}function Wi(t,e){var a=e;if("ref"in e){a={};for(var r in e)r!=="ref"&&(a[r]=e[r])}if(t=t.defaultProps){a===e&&(a=g({},a));for(var o in t)a[o]===void 0&&(a[o]=t[o])}return a}var Vl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Sp(t){Vl(t)}function Tp(t){console.error(t)}function wp(t){Vl(t)}function zl(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function Ap(t,e,a){try{var r=t.onCaughtError;r(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function hc(t,e,a){return a=si(a),a.tag=3,a.payload={element:null},a.callback=function(){zl(t,e)},a}function Ep(t){return t=si(t),t.tag=3,t}function Mp(t,e,a,r){var o=a.type.getDerivedStateFromError;if(typeof o=="function"){var c=r.value;t.payload=function(){return o(c)},t.callback=function(){Ap(e,a,r)}}var y=a.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Ap(e,a,r),typeof o!="function"&&(pi===null?pi=new Set([this]):pi.add(this));var x=r.stack;this.componentDidCatch(r.value,{componentStack:x!==null?x:""})})}function nT(t,e,a,r,o){if(a.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=a.alternate,e!==null&&ks(e,a,o,!0),a=We.current,a!==null){switch(a.tag){case 13:return gn===null?Bc():a.alternate===null&&Wt===0&&(Wt=3),a.flags&=-257,a.flags|=65536,a.lanes=o,r===Gu?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([r]):e.add(r),kc(t,r,o)),!1;case 22:return a.flags|=65536,r===Gu?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([r]):a.add(r)),kc(t,r,o)),!1}throw Error(l(435,a.tag))}return kc(t,r,o),Bc(),!1}if(Vt)return e=We.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,r!==zu&&(t=Error(l(422),{cause:r}),Us(Ze(t,a)))):(r!==zu&&(e=Error(l(423),{cause:r}),Us(Ze(e,a))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,r=Ze(r,a),o=hc(t.stateNode,r,o),Xu(t,o),Wt!==4&&(Wt=2)),!1;var c=Error(l(520),{cause:r});if(c=Ze(c,a),sr===null?sr=[c]:sr.push(c),Wt!==4&&(Wt=2),e===null)return!0;r=Ze(r,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=o&-o,a.lanes|=t,t=hc(a.stateNode,r,t),Xu(a,t),!1;case 1:if(e=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(pi===null||!pi.has(c))))return a.flags|=65536,o&=-o,a.lanes|=o,o=Ep(o),Mp(o,t,a,r),Xu(a,o),!1}a=a.return}while(a!==null);return!1}var Cp=Error(l(461)),ue=!1;function me(t,e,a,r){e.child=t===null?yp(e,null,a,r):ka(e,t.child,a,r)}function Rp(t,e,a,r,o){a=a.render;var c=e.ref;if("ref"in r){var y={};for(var x in r)x!=="ref"&&(y[x]=r[x])}else y=r;return Qi(e),r=Iu(t,e,a,y,c,o),x=Wu(),t!==null&&!ue?($u(t,e,o),Gn(t,e,o)):(Vt&&x&&_u(e),e.flags|=1,me(t,e,r,o),e.child)}function Dp(t,e,a,r,o){if(t===null){var c=a.type;return typeof c=="function"&&!Ou(c)&&c.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=c,Op(t,e,c,r,o)):(t=ml(a.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!Sc(t,o)){var y=c.memoizedProps;if(a=a.compare,a=a!==null?a:Vs,a(y,r)&&t.ref===e.ref)return Gn(t,e,o)}return e.flags|=1,t=zn(c,r),t.ref=e.ref,t.return=e,e.child=t}function Op(t,e,a,r,o){if(t!==null){var c=t.memoizedProps;if(Vs(c,r)&&t.ref===e.ref)if(ue=!1,e.pendingProps=r=c,Sc(t,o))(t.flags&131072)!==0&&(ue=!0);else return e.lanes=t.lanes,Gn(t,e,o)}return mc(t,e,a,r,o)}function Np(t,e,a){var r=e.pendingProps,o=r.children,c=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=c!==null?c.baseLanes|a:a,t!==null){for(o=e.child=t.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;e.childLanes=c&~r}else e.childLanes=0,e.child=null;return jp(t,e,r,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&bl(e,c!==null?c.cachePool:null),c!==null?Om(e,c):Zu(),vp(e);else return e.lanes=e.childLanes=536870912,jp(t,e,c!==null?c.baseLanes|a:a,a)}else c!==null?(bl(e,c.cachePool),Om(e,c),ui(),e.memoizedState=null):(t!==null&&bl(e,null),Zu(),ui());return me(t,e,o,a),e.child}function jp(t,e,a,r){var o=Pu();return o=o===null?null:{parent:se._currentValue,pool:o},e.memoizedState={baseLanes:a,cachePool:o},t!==null&&bl(e,null),Zu(),vp(e),t!==null&&ks(t,e,r,!0),null}function Ll(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(l(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function mc(t,e,a,r,o){return Qi(e),a=Iu(t,e,a,r,void 0,o),r=Wu(),t!==null&&!ue?($u(t,e,o),Gn(t,e,o)):(Vt&&r&&_u(e),e.flags|=1,me(t,e,a,o),e.child)}function _p(t,e,a,r,o,c){return Qi(e),e.updateQueue=null,a=jm(e,r,a,o),Nm(t),r=Wu(),t!==null&&!ue?($u(t,e,c),Gn(t,e,c)):(Vt&&r&&_u(e),e.flags|=1,me(t,e,a,c),e.child)}function Vp(t,e,a,r,o){if(Qi(e),e.stateNode===null){var c=Da,y=a.contextType;typeof y=="object"&&y!==null&&(c=be(y)),c=new a(r,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=dc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=r,c.state=e.memoizedState,c.refs={},Yu(e),y=a.contextType,c.context=typeof y=="object"&&y!==null?be(y):Da,c.state=e.memoizedState,y=a.getDerivedStateFromProps,typeof y=="function"&&(fc(e,a,y,r),c.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&dc.enqueueReplaceState(c,c.state,null),Ks(e,r,c,o),Xs(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){c=e.stateNode;var x=e.memoizedProps,R=Wi(a,x);c.props=R;var k=c.context,Z=a.contextType;y=Da,typeof Z=="object"&&Z!==null&&(y=be(Z));var F=a.getDerivedStateFromProps;Z=typeof F=="function"||typeof c.getSnapshotBeforeUpdate=="function",x=e.pendingProps!==x,Z||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(x||k!==y)&&xp(e,c,r,y),ai=!1;var H=e.memoizedState;c.state=H,Ks(e,r,c,o),Xs(),k=e.memoizedState,x||H!==k||ai?(typeof F=="function"&&(fc(e,a,F,r),k=e.memoizedState),(R=ai||bp(e,a,R,r,H,k,y))?(Z||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=k),c.props=r,c.state=k,c.context=y,r=R):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{c=e.stateNode,qu(t,e),y=e.memoizedProps,Z=Wi(a,y),c.props=Z,F=e.pendingProps,H=c.context,k=a.contextType,R=Da,typeof k=="object"&&k!==null&&(R=be(k)),x=a.getDerivedStateFromProps,(k=typeof x=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==F||H!==R)&&xp(e,c,r,R),ai=!1,H=e.memoizedState,c.state=H,Ks(e,r,c,o),Xs();var G=e.memoizedState;y!==F||H!==G||ai||t!==null&&t.dependencies!==null&&yl(t.dependencies)?(typeof x=="function"&&(fc(e,a,x,r),G=e.memoizedState),(Z=ai||bp(e,a,Z,r,H,G,R)||t!==null&&t.dependencies!==null&&yl(t.dependencies))?(k||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(r,G,R),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(r,G,R)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=G),c.props=r,c.state=G,c.context=R,r=Z):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),r=!1)}return c=r,Ll(t,e),r=(e.flags&128)!==0,c||r?(c=e.stateNode,a=r&&typeof a.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&r?(e.child=ka(e,t.child,null,o),e.child=ka(e,null,a,o)):me(t,e,a,o),e.memoizedState=c.state,t=e.child):t=Gn(t,e,o),t}function zp(t,e,a,r){return Bs(),e.flags|=256,me(t,e,a,r),e.child}var pc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function gc(t){return{baseLanes:t,cachePool:Tm()}}function yc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=$e),t}function Lp(t,e,a){var r=e.pendingProps,o=!1,c=(e.flags&128)!==0,y;if((y=c)||(y=t!==null&&t.memoizedState===null?!1:(re.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(Vt){if(o?oi(e):ui(),Vt){var x=It,R;if(R=x){t:{for(R=x,x=pn;R.nodeType!==8;){if(!x){x=null;break t}if(R=un(R.nextSibling),R===null){x=null;break t}}x=R}x!==null?(e.memoizedState={dehydrated:x,treeContext:Yi!==null?{id:Ln,overflow:Bn}:null,retryLane:536870912,hydrationErrors:null},R=Be(18,null,null,0),R.stateNode=x,R.return=e,e.child=R,Ee=e,It=null,R=!0):R=!1}R||Ki(e)}if(x=e.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return tf(x)?e.lanes=32:e.lanes=536870912,null;Pn(e)}return x=r.children,r=r.fallback,o?(ui(),o=e.mode,x=Bl({mode:"hidden",children:x},o),r=Gi(r,o,a,null),x.return=e,r.return=e,x.sibling=r,e.child=x,o=e.child,o.memoizedState=gc(a),o.childLanes=yc(t,y,a),e.memoizedState=pc,r):(oi(e),vc(e,x))}if(R=t.memoizedState,R!==null&&(x=R.dehydrated,x!==null)){if(c)e.flags&256?(oi(e),e.flags&=-257,e=bc(t,e,a)):e.memoizedState!==null?(ui(),e.child=t.child,e.flags|=128,e=null):(ui(),o=r.fallback,x=e.mode,r=Bl({mode:"visible",children:r.children},x),o=Gi(o,x,a,null),o.flags|=2,r.return=e,o.return=e,r.sibling=o,e.child=r,ka(e,t.child,null,a),r=e.child,r.memoizedState=gc(a),r.childLanes=yc(t,y,a),e.memoizedState=pc,e=o);else if(oi(e),tf(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var k=y.dgst;y=k,r=Error(l(419)),r.stack="",r.digest=y,Us({value:r,source:null,stack:null}),e=bc(t,e,a)}else if(ue||ks(t,e,a,!1),y=(a&t.childLanes)!==0,ue||y){if(y=Yt,y!==null&&(r=a&-a,r=(r&42)!==0?1:eu(r),r=(r&(y.suspendedLanes|a))!==0?0:r,r!==0&&r!==R.retryLane))throw R.retryLane=r,Ra(t,r),Ge(y,t,r),Cp;x.data==="$?"||Bc(),e=bc(t,e,a)}else x.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=R.treeContext,It=un(x.nextSibling),Ee=e,Vt=!0,Xi=null,pn=!1,t!==null&&(Fe[Ie++]=Ln,Fe[Ie++]=Bn,Fe[Ie++]=Yi,Ln=t.id,Bn=t.overflow,Yi=e),e=vc(e,r.children),e.flags|=4096);return e}return o?(ui(),o=r.fallback,x=e.mode,R=t.child,k=R.sibling,r=zn(R,{mode:"hidden",children:r.children}),r.subtreeFlags=R.subtreeFlags&65011712,k!==null?o=zn(k,o):(o=Gi(o,x,a,null),o.flags|=2),o.return=e,r.return=e,r.sibling=o,e.child=r,r=o,o=e.child,x=t.child.memoizedState,x===null?x=gc(a):(R=x.cachePool,R!==null?(k=se._currentValue,R=R.parent!==k?{parent:k,pool:k}:R):R=Tm(),x={baseLanes:x.baseLanes|a,cachePool:R}),o.memoizedState=x,o.childLanes=yc(t,y,a),e.memoizedState=pc,r):(oi(e),a=t.child,t=a.sibling,a=zn(a,{mode:"visible",children:r.children}),a.return=e,a.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=a,e.memoizedState=null,a)}function vc(t,e){return e=Bl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Bl(t,e){return t=Be(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function bc(t,e,a){return ka(e,t.child,null,a),t=vc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Bp(t,e,a){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Bu(t.return,e,a)}function xc(t,e,a,r,o){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:a,tailMode:o}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=a,c.tailMode=o)}function Up(t,e,a){var r=e.pendingProps,o=r.revealOrder,c=r.tail;if(me(t,e,r.children,a),r=re.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Bp(t,a,e);else if(t.tag===19)Bp(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch($(re,r),o){case"forwards":for(a=e.child,o=null;a!==null;)t=a.alternate,t!==null&&_l(t)===null&&(o=a),a=a.sibling;a=o,a===null?(o=e.child,e.child=null):(o=a.sibling,a.sibling=null),xc(e,!1,o,a,c);break;case"backwards":for(a=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&_l(t)===null){e.child=o;break}t=o.sibling,o.sibling=a,a=o,o=t}xc(e,!0,a,null,c);break;case"together":xc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Gn(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),mi|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(ks(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,a=zn(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=zn(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function Sc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&yl(t)))}function iT(t,e,a){switch(e.tag){case 3:nt(e,e.stateNode.containerInfo),ii(e,se,t.memoizedState.cache),Bs();break;case 27:case 5:qt(e);break;case 4:nt(e,e.stateNode.containerInfo);break;case 10:ii(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(oi(e),e.flags|=128,null):(a&e.child.childLanes)!==0?Lp(t,e,a):(oi(e),t=Gn(t,e,a),t!==null?t.sibling:null);oi(e);break;case 19:var o=(t.flags&128)!==0;if(r=(a&e.childLanes)!==0,r||(ks(t,e,a,!1),r=(a&e.childLanes)!==0),o){if(r)return Up(t,e,a);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),$(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Np(t,e,a);case 24:ii(e,se,t.memoizedState.cache)}return Gn(t,e,a)}function kp(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)ue=!0;else{if(!Sc(t,a)&&(e.flags&128)===0)return ue=!1,iT(t,e,a);ue=(t.flags&131072)!==0}else ue=!1,Vt&&(e.flags&1048576)!==0&&pm(e,gl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,o=r._init;if(r=o(r._payload),e.type=r,typeof r=="function")Ou(r)?(t=Wi(r,t),e.tag=1,e=Vp(null,e,r,t,a)):(e.tag=0,e=mc(null,e,r,t,a));else{if(r!=null){if(o=r.$$typeof,o===V){e.tag=11,e=Rp(null,e,r,t,a);break t}else if(o===J){e.tag=14,e=Dp(null,e,r,t,a);break t}}throw e=Et(r)||r,Error(l(306,e,""))}}return e;case 0:return mc(t,e,e.type,e.pendingProps,a);case 1:return r=e.type,o=Wi(r,e.pendingProps),Vp(t,e,r,o,a);case 3:t:{if(nt(e,e.stateNode.containerInfo),t===null)throw Error(l(387));r=e.pendingProps;var c=e.memoizedState;o=c.element,qu(t,e),Ks(e,r,null,a);var y=e.memoizedState;if(r=y.cache,ii(e,se,r),r!==c.cache&&Uu(e,[se],a,!0),Xs(),r=y.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=zp(t,e,r,a);break t}else if(r!==o){o=Ze(Error(l(424)),e),Us(o),e=zp(t,e,r,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(It=un(t.firstChild),Ee=e,Vt=!0,Xi=null,pn=!0,a=yp(e,null,r,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Bs(),r===o){e=Gn(t,e,a);break t}me(t,e,r,a)}e=e.child}return e;case 26:return Ll(t,e),t===null?(a=Yg(e.type,null,e.pendingProps,null))?e.memoizedState=a:Vt||(a=e.type,t=e.pendingProps,r=Wl(dt.current).createElement(a),r[ve]=e,r[Re]=t,ge(r,a,t),oe(r),e.stateNode=r):e.memoizedState=Yg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return qt(e),t===null&&Vt&&(r=e.stateNode=Hg(e.type,e.pendingProps,dt.current),Ee=e,pn=!0,o=It,vi(e.type)?(ef=o,It=un(r.firstChild)):It=o),me(t,e,e.pendingProps.children,a),Ll(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Vt&&((o=r=It)&&(r=NT(r,e.type,e.pendingProps,pn),r!==null?(e.stateNode=r,Ee=e,It=un(r.firstChild),pn=!1,o=!0):o=!1),o||Ki(e)),qt(e),o=e.type,c=e.pendingProps,y=t!==null?t.memoizedProps:null,r=c.children,Wc(o,c)?r=null:y!==null&&Wc(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Iu(t,e,F1,null,null,a),mr._currentValue=o),Ll(t,e),me(t,e,r,a),e.child;case 6:return t===null&&Vt&&((t=a=It)&&(a=jT(a,e.pendingProps,pn),a!==null?(e.stateNode=a,Ee=e,It=null,t=!0):t=!1),t||Ki(e)),null;case 13:return Lp(t,e,a);case 4:return nt(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=ka(e,null,r,a):me(t,e,r,a),e.child;case 11:return Rp(t,e,e.type,e.pendingProps,a);case 7:return me(t,e,e.pendingProps,a),e.child;case 8:return me(t,e,e.pendingProps.children,a),e.child;case 12:return me(t,e,e.pendingProps.children,a),e.child;case 10:return r=e.pendingProps,ii(e,e.type,r.value),me(t,e,r.children,a),e.child;case 9:return o=e.type._context,r=e.pendingProps.children,Qi(e),o=be(o),r=r(o),e.flags|=1,me(t,e,r,a),e.child;case 14:return Dp(t,e,e.type,e.pendingProps,a);case 15:return Op(t,e,e.type,e.pendingProps,a);case 19:return Up(t,e,a);case 31:return r=e.pendingProps,a=e.mode,r={mode:r.mode,children:r.children},t===null?(a=Bl(r,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=zn(t.child,r),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return Np(t,e,a);case 24:return Qi(e),r=be(se),t===null?(o=Pu(),o===null&&(o=Yt,c=ku(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=a),o=c),e.memoizedState={parent:r,cache:o},Yu(e),ii(e,se,o)):((t.lanes&a)!==0&&(qu(t,e),Ks(e,null,null,a),Xs()),o=t.memoizedState,c=e.memoizedState,o.parent!==r?(o={parent:r,cache:r},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),ii(e,se,r)):(r=c.cache,ii(e,se,r),r!==o.cache&&Uu(e,[se],a,!0))),me(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function Yn(t){t.flags|=4}function Hp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Qg(e)){if(e=We.current,e!==null&&((Nt&4194048)===Nt?gn!==null:(Nt&62914560)!==Nt&&(Nt&536870912)===0||e!==gn))throw Ys=Gu,wm;t.flags|=8192}}function Ul(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?vh():536870912,t.lanes|=e,Ya|=e)}function Js(t,e){if(!Vt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var r=null;a!==null;)a.alternate!==null&&(r=a),a=a.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Zt(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,r=0;if(e)for(var o=t.child;o!==null;)a|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)a|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=a,e}function aT(t,e,a){var r=e.pendingProps;switch(Vu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zt(e),null;case 1:return Zt(e),null;case 3:return a=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),kn(se),Tt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(Ls(e)?Yn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,vm())),Zt(e),null;case 26:return a=e.memoizedState,t===null?(Yn(e),a!==null?(Zt(e),Hp(e,a)):(Zt(e),e.flags&=-16777217)):a?a!==t.memoizedState?(Yn(e),Zt(e),Hp(e,a)):(Zt(e),e.flags&=-16777217):(t.memoizedProps!==r&&Yn(e),Zt(e),e.flags&=-16777217),null;case 27:Ot(e),a=dt.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}t=it.current,Ls(e)?gm(e):(t=Hg(o,r,a),e.stateNode=t,Yn(e))}return Zt(e),null;case 5:if(Ot(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}if(t=it.current,Ls(e))gm(e);else{switch(o=Wl(dt.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?o.createElement(a,{is:r.is}):o.createElement(a)}}t[ve]=e,t[Re]=r;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(ge(t,a,r),a){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Yn(e)}}return Zt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(l(166));if(t=dt.current,Ls(e)){if(t=e.stateNode,a=e.memoizedProps,r=null,o=Ee,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}t[ve]=e,t=!!(t.nodeValue===a||r!==null&&r.suppressHydrationWarning===!0||_g(t.nodeValue,a)),t||Ki(e)}else t=Wl(t).createTextNode(r),t[ve]=e,e.stateNode=t}return Zt(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ls(e),r!==null&&r.dehydrated!==null){if(t===null){if(!o)throw Error(l(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(l(317));o[ve]=e}else Bs(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Zt(e),o=!1}else o=vm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Pn(e),e):(Pn(e),null)}if(Pn(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=r!==null,t=t!==null&&t.memoizedState!==null,a){r=e.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var c=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(c=r.memoizedState.cachePool.pool),c!==o&&(r.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),Ul(e,e.updateQueue),Zt(e),null;case 4:return Tt(),t===null&&Kc(e.stateNode.containerInfo),Zt(e),null;case 10:return kn(e.type),Zt(e),null;case 19:if(tt(re),o=e.memoizedState,o===null)return Zt(e),null;if(r=(e.flags&128)!==0,c=o.rendering,c===null)if(r)Js(o,!1);else{if(Wt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=_l(t),c!==null){for(e.flags|=128,Js(o,!1),t=c.updateQueue,e.updateQueue=t,Ul(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)mm(a,t),a=a.sibling;return $(re,re.current&1|2),e.child}t=t.sibling}o.tail!==null&&he()>Pl&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304)}else{if(!r)if(t=_l(c),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Ul(e,t),Js(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!Vt)return Zt(e),null}else 2*he()-o.renderingStartTime>Pl&&a!==536870912&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304);o.isBackwards?(c.sibling=e.child,e.child=c):(t=o.last,t!==null?t.sibling=c:e.child=c,o.last=c)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=he(),e.sibling=null,t=re.current,$(re,r?t&1|2:t&1),e):(Zt(e),null);case 22:case 23:return Pn(e),Qu(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(a&536870912)!==0&&(e.flags&128)===0&&(Zt(e),e.subtreeFlags&6&&(e.flags|=8192)):Zt(e),a=e.updateQueue,a!==null&&Ul(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==a&&(e.flags|=2048),t!==null&&tt(Fi),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),kn(se),Zt(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function sT(t,e){switch(Vu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return kn(se),Tt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ot(e),null;case 13:if(Pn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));Bs()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return tt(re),null;case 4:return Tt(),null;case 10:return kn(e.type),null;case 22:case 23:return Pn(e),Qu(),t!==null&&tt(Fi),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return kn(se),null;case 25:return null;default:return null}}function Pp(t,e){switch(Vu(e),e.tag){case 3:kn(se),Tt();break;case 26:case 27:case 5:Ot(e);break;case 4:Tt();break;case 13:Pn(e);break;case 19:tt(re);break;case 10:kn(e.type);break;case 22:case 23:Pn(e),Qu(),t!==null&&tt(Fi);break;case 24:kn(se)}}function tr(t,e){try{var a=e.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var o=r.next;a=o;do{if((a.tag&t)===t){r=void 0;var c=a.create,y=a.inst;r=c(),y.destroy=r}a=a.next}while(a!==o)}}catch(x){Gt(e,e.return,x)}}function ci(t,e,a){try{var r=e.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var c=o.next;r=c;do{if((r.tag&t)===t){var y=r.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,o=e;var R=a,k=x;try{k()}catch(Z){Gt(o,R,Z)}}}r=r.next}while(r!==c)}}catch(Z){Gt(e,e.return,Z)}}function Gp(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{Dm(e,a)}catch(r){Gt(t,t.return,r)}}}function Yp(t,e,a){a.props=Wi(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(r){Gt(t,e,r)}}function er(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof a=="function"?t.refCleanup=a(r):a.current=r}}catch(o){Gt(t,e,o)}}function yn(t,e){var a=t.ref,r=t.refCleanup;if(a!==null)if(typeof r=="function")try{r()}catch(o){Gt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(o){Gt(t,e,o)}else a.current=null}function qp(t){var e=t.type,a=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break t;case"img":a.src?r.src=a.src:a.srcSet&&(r.srcset=a.srcSet)}}catch(o){Gt(t,t.return,o)}}function Tc(t,e,a){try{var r=t.stateNode;MT(r,t.type,a,e),r[Re]=e}catch(o){Gt(t,t.return,o)}}function Xp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&vi(t.type)||t.tag===4}function wc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Xp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&vi(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Ac(t,e,a){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Il));else if(r!==4&&(r===27&&vi(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(Ac(t,e,a),t=t.sibling;t!==null;)Ac(t,e,a),t=t.sibling}function kl(t,e,a){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(r!==4&&(r===27&&vi(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(kl(t,e,a),t=t.sibling;t!==null;)kl(t,e,a),t=t.sibling}function Kp(t){var e=t.stateNode,a=t.memoizedProps;try{for(var r=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);ge(e,r,a),e[ve]=t,e[Re]=a}catch(c){Gt(t,t.return,c)}}var qn=!1,te=!1,Ec=!1,Zp=typeof WeakSet=="function"?WeakSet:Set,ce=null;function rT(t,e){if(t=t.containerInfo,Fc=io,t=am(t),wu(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var r=a.getSelection&&a.getSelection();if(r&&r.rangeCount!==0){a=r.anchorNode;var o=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{a.nodeType,c.nodeType}catch(mt){a=null;break t}var y=0,x=-1,R=-1,k=0,Z=0,F=t,H=null;e:for(;;){for(var G;F!==a||o!==0&&F.nodeType!==3||(x=y+o),F!==c||r!==0&&F.nodeType!==3||(R=y+r),F.nodeType===3&&(y+=F.nodeValue.length),(G=F.firstChild)!==null;)H=F,F=G;for(;;){if(F===t)break e;if(H===a&&++k===o&&(x=y),H===c&&++Z===r&&(R=y),(G=F.nextSibling)!==null)break;F=H,H=F.parentNode}F=G}a=x===-1||R===-1?null:{start:x,end:R}}else a=null}a=a||{start:0,end:0}}else a=null;for(Ic={focusedElem:t,selectionRange:a},io=!1,ce=e;ce!==null;)if(e=ce,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ce=t;else for(;ce!==null;){switch(e=ce,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,a=e,o=c.memoizedProps,c=c.memoizedState,r=a.stateNode;try{var gt=Wi(a.type,o,a.elementType===a.type);t=r.getSnapshotBeforeUpdate(gt,c),r.__reactInternalSnapshotBeforeUpdate=t}catch(mt){Gt(a,a.return,mt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)Jc(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Jc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,ce=t;break}ce=e.return}}function Qp(t,e,a){var r=a.flags;switch(a.tag){case 0:case 11:case 15:fi(t,a),r&4&&tr(5,a);break;case 1:if(fi(t,a),r&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(y){Gt(a,a.return,y)}else{var o=Wi(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Gt(a,a.return,y)}}r&64&&Gp(a),r&512&&er(a,a.return);break;case 3:if(fi(t,a),r&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{Dm(t,e)}catch(y){Gt(a,a.return,y)}}break;case 27:e===null&&r&4&&Kp(a);case 26:case 5:fi(t,a),e===null&&r&4&&qp(a),r&512&&er(a,a.return);break;case 12:fi(t,a);break;case 13:fi(t,a),r&4&&Wp(t,a),r&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=pT.bind(null,a),_T(t,a))));break;case 22:if(r=a.memoizedState!==null||qn,!r){e=e!==null&&e.memoizedState!==null||te,o=qn;var c=te;qn=r,(te=e)&&!c?di(t,a,(a.subtreeFlags&8772)!==0):fi(t,a),qn=o,te=c}break;case 30:break;default:fi(t,a)}}function Fp(t){var e=t.alternate;e!==null&&(t.alternate=null,Fp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&au(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ne=!1;function Xn(t,e,a){for(a=a.child;a!==null;)Ip(t,e,a),a=a.sibling}function Ip(t,e,a){if(Ve&&typeof Ve.onCommitFiberUnmount=="function")try{Ve.onCommitFiberUnmount(Ts,a)}catch(c){}switch(a.tag){case 26:te||yn(a,e),Xn(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:te||yn(a,e);var r=Xt,o=Ne;vi(a.type)&&(Xt=a.stateNode,Ne=!1),Xn(t,e,a),cr(a.stateNode),Xt=r,Ne=o;break;case 5:te||yn(a,e);case 6:if(r=Xt,o=Ne,Xt=null,Xn(t,e,a),Xt=r,Ne=o,Xt!==null)if(Ne)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(a.stateNode)}catch(c){Gt(a,e,c)}else try{Xt.removeChild(a.stateNode)}catch(c){Gt(a,e,c)}break;case 18:Xt!==null&&(Ne?(t=Xt,Ug(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),vr(t)):Ug(Xt,a.stateNode));break;case 4:r=Xt,o=Ne,Xt=a.stateNode.containerInfo,Ne=!0,Xn(t,e,a),Xt=r,Ne=o;break;case 0:case 11:case 14:case 15:te||ci(2,a,e),te||ci(4,a,e),Xn(t,e,a);break;case 1:te||(yn(a,e),r=a.stateNode,typeof r.componentWillUnmount=="function"&&Yp(a,e,r)),Xn(t,e,a);break;case 21:Xn(t,e,a);break;case 22:te=(r=te)||a.memoizedState!==null,Xn(t,e,a),te=r;break;default:Xn(t,e,a)}}function Wp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{vr(t)}catch(a){Gt(e,e.return,a)}}function lT(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Zp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Zp),e;default:throw Error(l(435,t.tag))}}function Mc(t,e){var a=lT(t);e.forEach(function(r){var o=gT.bind(null,t,r);a.has(r)||(a.add(r),r.then(o,o))})}function Ue(t,e){var a=e.deletions;if(a!==null)for(var r=0;r<a.length;r++){var o=a[r],c=t,y=e,x=y;t:for(;x!==null;){switch(x.tag){case 27:if(vi(x.type)){Xt=x.stateNode,Ne=!1;break t}break;case 5:Xt=x.stateNode,Ne=!1;break t;case 3:case 4:Xt=x.stateNode.containerInfo,Ne=!0;break t}x=x.return}if(Xt===null)throw Error(l(160));Ip(c,y,o),Xt=null,Ne=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)$p(e,t),e=e.sibling}var on=null;function $p(t,e){var a=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ue(e,t),ke(t),r&4&&(ci(3,t,t.return),tr(3,t),ci(5,t,t.return));break;case 1:Ue(e,t),ke(t),r&512&&(te||a===null||yn(a,a.return)),r&64&&qn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?r:a.concat(r))));break;case 26:var o=on;if(Ue(e,t),ke(t),r&512&&(te||a===null||yn(a,a.return)),r&4){var c=a!==null?a.memoizedState:null;if(r=t.memoizedState,a===null)if(r===null)if(t.stateNode===null){t:{r=t.type,a=t.memoizedProps,o=o.ownerDocument||o;e:switch(r){case"title":c=o.getElementsByTagName("title")[0],(!c||c[Es]||c[ve]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(r),o.head.insertBefore(c,o.querySelector("head > title"))),ge(c,r,a),c[ve]=t,oe(c),r=c;break t;case"link":var y=Kg("link","href",o).get(r+(a.href||""));if(y){for(var x=0;x<y.length;x++)if(c=y[x],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){y.splice(x,1);break e}}c=o.createElement(r),ge(c,r,a),o.head.appendChild(c);break;case"meta":if(y=Kg("meta","content",o).get(r+(a.content||""))){for(x=0;x<y.length;x++)if(c=y[x],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){y.splice(x,1);break e}}c=o.createElement(r),ge(c,r,a),o.head.appendChild(c);break;default:throw Error(l(468,r))}c[ve]=t,oe(c),r=c}t.stateNode=r}else Zg(o,t.type,t.stateNode);else t.stateNode=Xg(o,r,t.memoizedProps);else c!==r?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,r===null?Zg(o,t.type,t.stateNode):Xg(o,r,t.memoizedProps)):r===null&&t.stateNode!==null&&Tc(t,t.memoizedProps,a.memoizedProps)}break;case 27:Ue(e,t),ke(t),r&512&&(te||a===null||yn(a,a.return)),a!==null&&r&4&&Tc(t,t.memoizedProps,a.memoizedProps);break;case 5:if(Ue(e,t),ke(t),r&512&&(te||a===null||yn(a,a.return)),t.flags&32){o=t.stateNode;try{Sa(o,"")}catch(G){Gt(t,t.return,G)}}r&4&&t.stateNode!=null&&(o=t.memoizedProps,Tc(t,o,a!==null?a.memoizedProps:o)),r&1024&&(Ec=!0);break;case 6:if(Ue(e,t),ke(t),r&4){if(t.stateNode===null)throw Error(l(162));r=t.memoizedProps,a=t.stateNode;try{a.nodeValue=r}catch(G){Gt(t,t.return,G)}}break;case 3:if(to=null,o=on,on=$l(e.containerInfo),Ue(e,t),on=o,ke(t),r&4&&a!==null&&a.memoizedState.isDehydrated)try{vr(e.containerInfo)}catch(G){Gt(t,t.return,G)}Ec&&(Ec=!1,Jp(t));break;case 4:r=on,on=$l(t.stateNode.containerInfo),Ue(e,t),ke(t),on=r;break;case 12:Ue(e,t),ke(t);break;case 13:Ue(e,t),ke(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(jc=he()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mc(t,r)));break;case 22:o=t.memoizedState!==null;var R=a!==null&&a.memoizedState!==null,k=qn,Z=te;if(qn=k||o,te=Z||R,Ue(e,t),te=Z,qn=k,ke(t),r&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(a===null||R||qn||te||$i(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){R=a=e;try{if(c=R.stateNode,o)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=R.stateNode;var F=R.memoizedProps.style,H=F!=null&&F.hasOwnProperty("display")?F.display:null;x.style.display=H==null||typeof H=="boolean"?"":(""+H).trim()}}catch(G){Gt(R,R.return,G)}}}else if(e.tag===6){if(a===null){R=e;try{R.stateNode.nodeValue=o?"":R.memoizedProps}catch(G){Gt(R,R.return,G)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(a=r.retryQueue,a!==null&&(r.retryQueue=null,Mc(t,a))));break;case 19:Ue(e,t),ke(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mc(t,r)));break;case 30:break;case 21:break;default:Ue(e,t),ke(t)}}function ke(t){var e=t.flags;if(e&2){try{for(var a,r=t.return;r!==null;){if(Xp(r)){a=r;break}r=r.return}if(a==null)throw Error(l(160));switch(a.tag){case 27:var o=a.stateNode,c=wc(t);kl(t,c,o);break;case 5:var y=a.stateNode;a.flags&32&&(Sa(y,""),a.flags&=-33);var x=wc(t);kl(t,x,y);break;case 3:case 4:var R=a.stateNode.containerInfo,k=wc(t);Ac(t,k,R);break;default:throw Error(l(161))}}catch(Z){Gt(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function fi(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Qp(t,e.alternate,e),e=e.sibling}function $i(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ci(4,e,e.return),$i(e);break;case 1:yn(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&Yp(e,e.return,a),$i(e);break;case 27:cr(e.stateNode);case 26:case 5:yn(e,e.return),$i(e);break;case 22:e.memoizedState===null&&$i(e);break;case 30:$i(e);break;default:$i(e)}t=t.sibling}}function di(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,o=t,c=e,y=c.flags;switch(c.tag){case 0:case 11:case 15:di(o,c,a),tr(4,c);break;case 1:if(di(o,c,a),r=c,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(k){Gt(r,r.return,k)}if(r=c,o=r.updateQueue,o!==null){var x=r.stateNode;try{var R=o.shared.hiddenCallbacks;if(R!==null)for(o.shared.hiddenCallbacks=null,o=0;o<R.length;o++)Rm(R[o],x)}catch(k){Gt(r,r.return,k)}}a&&y&64&&Gp(c),er(c,c.return);break;case 27:Kp(c);case 26:case 5:di(o,c,a),a&&r===null&&y&4&&qp(c),er(c,c.return);break;case 12:di(o,c,a);break;case 13:di(o,c,a),a&&y&4&&Wp(o,c);break;case 22:c.memoizedState===null&&di(o,c,a),er(c,c.return);break;case 30:break;default:di(o,c,a)}e=e.sibling}}function Cc(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&Hs(a))}function Rc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t))}function vn(t,e,a,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)tg(t,e,a,r),e=e.sibling}function tg(t,e,a,r){var o=e.flags;switch(e.tag){case 0:case 11:case 15:vn(t,e,a,r),o&2048&&tr(9,e);break;case 1:vn(t,e,a,r);break;case 3:vn(t,e,a,r),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t)));break;case 12:if(o&2048){vn(t,e,a,r),t=e.stateNode;try{var c=e.memoizedProps,y=c.id,x=c.onPostCommit;typeof x=="function"&&x(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(R){Gt(e,e.return,R)}}else vn(t,e,a,r);break;case 13:vn(t,e,a,r);break;case 23:break;case 22:c=e.stateNode,y=e.alternate,e.memoizedState!==null?c._visibility&2?vn(t,e,a,r):nr(t,e):c._visibility&2?vn(t,e,a,r):(c._visibility|=2,Ha(t,e,a,r,(e.subtreeFlags&10256)!==0)),o&2048&&Cc(y,e);break;case 24:vn(t,e,a,r),o&2048&&Rc(e.alternate,e);break;default:vn(t,e,a,r)}}function Ha(t,e,a,r,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,y=e,x=a,R=r,k=y.flags;switch(y.tag){case 0:case 11:case 15:Ha(c,y,x,R,o),tr(8,y);break;case 23:break;case 22:var Z=y.stateNode;y.memoizedState!==null?Z._visibility&2?Ha(c,y,x,R,o):nr(c,y):(Z._visibility|=2,Ha(c,y,x,R,o)),o&&k&2048&&Cc(y.alternate,y);break;case 24:Ha(c,y,x,R,o),o&&k&2048&&Rc(y.alternate,y);break;default:Ha(c,y,x,R,o)}e=e.sibling}}function nr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,r=e,o=r.flags;switch(r.tag){case 22:nr(a,r),o&2048&&Cc(r.alternate,r);break;case 24:nr(a,r),o&2048&&Rc(r.alternate,r);break;default:nr(a,r)}e=e.sibling}}var ir=8192;function Pa(t){if(t.subtreeFlags&ir)for(t=t.child;t!==null;)eg(t),t=t.sibling}function eg(t){switch(t.tag){case 26:Pa(t),t.flags&ir&&t.memoizedState!==null&&KT(on,t.memoizedState,t.memoizedProps);break;case 5:Pa(t);break;case 3:case 4:var e=on;on=$l(t.stateNode.containerInfo),Pa(t),on=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ir,ir=16777216,Pa(t),ir=e):Pa(t));break;default:Pa(t)}}function ng(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ar(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var r=e[a];ce=r,ag(r,t)}ng(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ig(t),t=t.sibling}function ig(t){switch(t.tag){case 0:case 11:case 15:ar(t),t.flags&2048&&ci(9,t,t.return);break;case 3:ar(t);break;case 12:ar(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Hl(t)):ar(t);break;default:ar(t)}}function Hl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var r=e[a];ce=r,ag(r,t)}ng(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ci(8,e,e.return),Hl(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,Hl(e));break;default:Hl(e)}t=t.sibling}}function ag(t,e){for(;ce!==null;){var a=ce;switch(a.tag){case 0:case 11:case 15:ci(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var r=a.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Hs(a.memoizedState.cache)}if(r=a.child,r!==null)r.return=a,ce=r;else t:for(a=t;ce!==null;){r=ce;var o=r.sibling,c=r.return;if(Fp(r),r===a){ce=null;break t}if(o!==null){o.return=c,ce=o;break t}ce=c}}}var oT={getCacheForType:function(t){var e=be(se),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},uT=typeof WeakMap=="function"?WeakMap:Map,zt=0,Yt=null,Rt=null,Nt=0,Lt=0,He=null,hi=!1,Ga=!1,Dc=!1,Kn=0,Wt=0,mi=0,Ji=0,Oc=0,$e=0,Ya=0,sr=null,je=null,Nc=!1,jc=0,Pl=1/0,Gl=null,pi=null,pe=0,gi=null,qa=null,Xa=0,_c=0,Vc=null,sg=null,rr=0,zc=null;function Pe(){if((zt&2)!==0&&Nt!==0)return Nt&-Nt;if(B.T!==null){var t=ja;return t!==0?t:Gc()}return Sh()}function rg(){$e===0&&($e=(Nt&536870912)===0||Vt?yh():536870912);var t=We.current;return t!==null&&(t.flags|=32),$e}function Ge(t,e,a){(t===Yt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ka(t,0),yi(t,Nt,$e,!1)),As(t,a),((zt&2)===0||t!==Yt)&&(t===Yt&&((zt&2)===0&&(Ji|=a),Wt===4&&yi(t,Nt,$e,!1)),bn(t))}function lg(t,e,a){if((zt&6)!==0)throw Error(l(327));var r=!a&&(e&124)===0&&(e&t.expiredLanes)===0||ws(t,e),o=r?dT(t,e):Uc(t,e,!0),c=r;do{if(o===0){Ga&&!r&&yi(t,e,0,!1);break}else{if(a=t.current.alternate,c&&!cT(a)){o=Uc(t,e,!1),c=!1;continue}if(o===2){if(c=e,t.errorRecoveryDisabledLanes&c)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var x=t;o=sr;var R=x.current.memoizedState.isDehydrated;if(R&&(Ka(x,y).flags|=256),y=Uc(x,y,!1),y!==2){if(Dc&&!R){x.errorRecoveryDisabledLanes|=c,Ji|=c,o=4;break t}c=je,je=o,c!==null&&(je===null?je=c:je.push.apply(je,c))}o=y}if(c=!1,o!==2)continue}}if(o===1){Ka(t,0),yi(t,e,0,!0);break}t:{switch(r=t,c=o,c){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:yi(r,e,$e,!hi);break t;case 2:je=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(o=jc+300-he(),10<o)){if(yi(r,e,$e,!hi),Jr(r,0,!0)!==0)break t;r.timeoutHandle=Lg(og.bind(null,r,a,je,Gl,Nc,e,$e,Ji,Ya,hi,c,2,-0,0),o);break t}og(r,a,je,Gl,Nc,e,$e,Ji,Ya,hi,c,0,-0,0)}}break}while(!0);bn(t)}function og(t,e,a,r,o,c,y,x,R,k,Z,F,H,G){if(t.timeoutHandle=-1,F=e.subtreeFlags,(F&8192||(F&16785408)===16785408)&&(hr={stylesheets:null,count:0,unsuspend:XT},eg(e),F=ZT(),F!==null)){t.cancelPendingCommit=F(pg.bind(null,t,e,c,a,r,o,y,x,R,Z,1,H,G)),yi(t,c,y,!k);return}pg(t,e,c,a,r,o,y,x,R)}function cT(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var r=0;r<a.length;r++){var o=a[r],c=o.getSnapshot;o=o.value;try{if(!Le(c(),o))return!1}catch(y){return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function yi(t,e,a,r){e&=~Oc,e&=~Ji,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var o=e;0<o;){var c=31-ze(o),y=1<<c;r[c]=-1,o&=~y}a!==0&&bh(t,a,e)}function Yl(){return(zt&6)===0?(lr(0),!1):!0}function Lc(){if(Rt!==null){if(Lt===0)var t=Rt.return;else t=Rt,Un=Zi=null,Ju(t),Ua=null,Ws=0,t=Rt;for(;t!==null;)Pp(t.alternate,t),t=t.return;Rt=null}}function Ka(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,RT(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),Lc(),Yt=t,Rt=a=zn(t.current,null),Nt=e,Lt=0,He=null,hi=!1,Ga=ws(t,e),Dc=!1,Ya=$e=Oc=Ji=mi=Wt=0,je=sr=null,Nc=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var o=31-ze(r),c=1<<o;e|=t[o],r&=~c}return Kn=e,fl(),a}function ug(t,e){wt=null,B.H=Ol,e===Gs||e===xl?(e=Mm(),Lt=3):e===wm?(e=Mm(),Lt=4):Lt=e===Cp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,He=e,Rt===null&&(Wt=1,zl(t,Ze(e,t.current)))}function cg(){var t=B.H;return B.H=Ol,t===null?Ol:t}function fg(){var t=B.A;return B.A=oT,t}function Bc(){Wt=4,hi||(Nt&4194048)!==Nt&&We.current!==null||(Ga=!0),(mi&134217727)===0&&(Ji&134217727)===0||Yt===null||yi(Yt,Nt,$e,!1)}function Uc(t,e,a){var r=zt;zt|=2;var o=cg(),c=fg();(Yt!==t||Nt!==e)&&(Gl=null,Ka(t,e)),e=!1;var y=Wt;t:do try{if(Lt!==0&&Rt!==null){var x=Rt,R=He;switch(Lt){case 8:Lc(),y=6;break t;case 3:case 2:case 9:case 6:We.current===null&&(e=!0);var k=Lt;if(Lt=0,He=null,Za(t,x,R,k),a&&Ga){y=0;break t}break;default:k=Lt,Lt=0,He=null,Za(t,x,R,k)}}fT(),y=Wt;break}catch(Z){ug(t,Z)}while(!0);return e&&t.shellSuspendCounter++,Un=Zi=null,zt=r,B.H=o,B.A=c,Rt===null&&(Yt=null,Nt=0,fl()),y}function fT(){for(;Rt!==null;)dg(Rt)}function dT(t,e){var a=zt;zt|=2;var r=cg(),o=fg();Yt!==t||Nt!==e?(Gl=null,Pl=he()+500,Ka(t,e)):Ga=ws(t,e);t:do try{if(Lt!==0&&Rt!==null){e=Rt;var c=He;e:switch(Lt){case 1:Lt=0,He=null,Za(t,e,c,1);break;case 2:case 9:if(Am(c)){Lt=0,He=null,hg(e);break}e=function(){Lt!==2&&Lt!==9||Yt!==t||(Lt=7),bn(t)},c.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:Am(c)?(Lt=0,He=null,hg(e)):(Lt=0,He=null,Za(t,e,c,7));break;case 5:var y=null;switch(Rt.tag){case 26:y=Rt.memoizedState;case 5:case 27:var x=Rt;if(!y||Qg(y)){Lt=0,He=null;var R=x.sibling;if(R!==null)Rt=R;else{var k=x.return;k!==null?(Rt=k,ql(k)):Rt=null}break e}}Lt=0,He=null,Za(t,e,c,5);break;case 6:Lt=0,He=null,Za(t,e,c,6);break;case 8:Lc(),Wt=6;break t;default:throw Error(l(462))}}hT();break}catch(Z){ug(t,Z)}while(!0);return Un=Zi=null,B.H=r,B.A=o,zt=a,Rt!==null?0:(Yt=null,Nt=0,fl(),Wt)}function hT(){for(;Rt!==null&&!Ae();)dg(Rt)}function dg(t){var e=kp(t.alternate,t,Kn);t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function hg(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=_p(a,e,e.pendingProps,e.type,void 0,Nt);break;case 11:e=_p(a,e,e.pendingProps,e.type.render,e.ref,Nt);break;case 5:Ju(e);default:Pp(a,e),e=Rt=mm(e,Kn),e=kp(a,e,Kn)}t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function Za(t,e,a,r){Un=Zi=null,Ju(e),Ua=null,Ws=0;var o=e.return;try{if(nT(t,o,e,a,Nt)){Wt=1,zl(t,Ze(a,t.current)),Rt=null;return}}catch(c){if(o!==null)throw Rt=o,c;Wt=1,zl(t,Ze(a,t.current)),Rt=null;return}e.flags&32768?(Vt||r===1?t=!0:Ga||(Nt&536870912)!==0?t=!1:(hi=t=!0,(r===2||r===9||r===3||r===6)&&(r=We.current,r!==null&&r.tag===13&&(r.flags|=16384))),mg(e,t)):ql(e)}function ql(t){var e=t;do{if((e.flags&32768)!==0){mg(e,hi);return}t=e.return;var a=aT(e.alternate,e,Kn);if(a!==null){Rt=a;return}if(e=e.sibling,e!==null){Rt=e;return}Rt=e=t}while(e!==null);Wt===0&&(Wt=5)}function mg(t,e){do{var a=sT(t.alternate,t);if(a!==null){a.flags&=32767,Rt=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){Rt=t;return}Rt=t=a}while(t!==null);Wt=6,Rt=null}function pg(t,e,a,r,o,c,y,x,R){t.cancelPendingCommit=null;do Xl();while(pe!==0);if((zt&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(c=e.lanes|e.childLanes,c|=Ru,XS(t,a,c,y,x,R),t===Yt&&(Rt=Yt=null,Nt=0),qa=e,gi=t,Xa=a,_c=c,Vc=o,sg=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,yT(da,function(){return xg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=B.T,B.T=null,o=q.p,q.p=2,y=zt,zt|=4;try{rT(t,e,a)}finally{zt=y,q.p=o,B.T=r}}pe=1,gg(),yg(),vg()}}function gg(){if(pe===1){pe=0;var t=gi,e=qa,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{$p(e,t);var c=Ic,y=am(t.containerInfo),x=c.focusedElem,R=c.selectionRange;if(y!==x&&x&&x.ownerDocument&&im(x.ownerDocument.documentElement,x)){if(R!==null&&wu(x)){var k=R.start,Z=R.end;if(Z===void 0&&(Z=k),"selectionStart"in x)x.selectionStart=k,x.selectionEnd=Math.min(Z,x.value.length);else{var F=x.ownerDocument||document,H=F&&F.defaultView||window;if(H.getSelection){var G=H.getSelection(),gt=x.textContent.length,mt=Math.min(R.start,gt),Ht=R.end===void 0?mt:Math.min(R.end,gt);!G.extend&&mt>Ht&&(y=Ht,Ht=mt,mt=y);var L=nm(x,mt),_=nm(x,Ht);if(L&&_&&(G.rangeCount!==1||G.anchorNode!==L.node||G.anchorOffset!==L.offset||G.focusNode!==_.node||G.focusOffset!==_.offset)){var U=F.createRange();U.setStart(L.node,L.offset),G.removeAllRanges(),mt>Ht?(G.addRange(U),G.extend(_.node,_.offset)):(U.setEnd(_.node,_.offset),G.addRange(U))}}}}for(F=[],G=x;G=G.parentNode;)G.nodeType===1&&F.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<F.length;x++){var Q=F[x];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}io=!!Fc,Ic=Fc=null}finally{zt=o,q.p=r,B.T=a}}t.current=e,pe=2}}function yg(){if(pe===2){pe=0;var t=gi,e=qa,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{Qp(t,e.alternate,e)}finally{zt=o,q.p=r,B.T=a}}pe=3}}function vg(){if(pe===4||pe===3){pe=0,rn();var t=gi,e=qa,a=Xa,r=sg;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?pe=5:(pe=0,qa=gi=null,bg(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(pi=null),nu(a),e=e.stateNode,Ve&&typeof Ve.onCommitFiberRoot=="function")try{Ve.onCommitFiberRoot(Ts,e,void 0,(e.current.flags&128)===128)}catch(R){}if(r!==null){e=B.T,o=q.p,q.p=2,B.T=null;try{for(var c=t.onRecoverableError,y=0;y<r.length;y++){var x=r[y];c(x.value,{componentStack:x.stack})}}finally{B.T=e,q.p=o}}(Xa&3)!==0&&Xl(),bn(t),o=t.pendingLanes,(a&4194090)!==0&&(o&42)!==0?t===zc?rr++:(rr=0,zc=t):rr=0,lr(0)}}function bg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Hs(e)))}function Xl(t){return gg(),yg(),vg(),xg()}function xg(){if(pe!==5)return!1;var t=gi,e=_c;_c=0;var a=nu(Xa),r=B.T,o=q.p;try{q.p=32>a?32:a,B.T=null,a=Vc,Vc=null;var c=gi,y=Xa;if(pe=0,qa=gi=null,Xa=0,(zt&6)!==0)throw Error(l(331));var x=zt;if(zt|=4,ig(c.current),tg(c,c.current,y,a),zt=x,lr(0,!1),Ve&&typeof Ve.onPostCommitFiberRoot=="function")try{Ve.onPostCommitFiberRoot(Ts,c)}catch(R){}return!0}finally{q.p=o,B.T=r,bg(t,e)}}function Sg(t,e,a){e=Ze(a,e),e=hc(t.stateNode,e,2),t=ri(t,e,2),t!==null&&(As(t,2),bn(t))}function Gt(t,e,a){if(t.tag===3)Sg(t,t,a);else for(;e!==null;){if(e.tag===3){Sg(e,t,a);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(pi===null||!pi.has(r))){t=Ze(a,t),a=Ep(2),r=ri(e,a,2),r!==null&&(Mp(a,r,e,t),As(r,2),bn(r));break}}e=e.return}}function kc(t,e,a){var r=t.pingCache;if(r===null){r=t.pingCache=new uT;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(a)||(Dc=!0,o.add(a),t=mT.bind(null,t,e,a),e.then(t,t))}function mT(t,e,a){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,Yt===t&&(Nt&a)===a&&(Wt===4||Wt===3&&(Nt&62914560)===Nt&&300>he()-jc?(zt&2)===0&&Ka(t,0):Oc|=a,Ya===Nt&&(Ya=0)),bn(t)}function Tg(t,e){e===0&&(e=vh()),t=Ra(t,e),t!==null&&(As(t,e),bn(t))}function pT(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Tg(t,a)}function gT(t,e){var a=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(a=o.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(l(314))}r!==null&&r.delete(e),Tg(t,a)}function yT(t,e){return jt(t,e)}var Kl=null,Qa=null,Hc=!1,Zl=!1,Pc=!1,ta=0;function bn(t){t!==Qa&&t.next===null&&(Qa===null?Kl=Qa=t:Qa=Qa.next=t),Zl=!0,Hc||(Hc=!0,bT())}function lr(t,e){if(!Pc&&Zl){Pc=!0;do for(var a=!1,r=Kl;r!==null;){if(t!==0){var o=r.pendingLanes;if(o===0)var c=0;else{var y=r.suspendedLanes,x=r.pingedLanes;c=(1<<31-ze(42|t)+1)-1,c&=o&~(y&~x),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Mg(r,c))}else c=Nt,c=Jr(r,r===Yt?c:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(c&3)===0||ws(r,c)||(a=!0,Mg(r,c));r=r.next}while(a);Pc=!1}}function vT(){wg()}function wg(){Zl=Hc=!1;var t=0;ta!==0&&(CT()&&(t=ta),ta=0);for(var e=he(),a=null,r=Kl;r!==null;){var o=r.next,c=Ag(r,e);c===0?(r.next=null,a===null?Kl=o:a.next=o,o===null&&(Qa=a)):(a=r,(t!==0||(c&3)!==0)&&(Zl=!0)),r=o}lr(t)}function Ag(t,e){for(var a=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var y=31-ze(c),x=1<<y,R=o[y];R===-1?((x&a)===0||(x&r)!==0)&&(o[y]=qS(x,e)):R<=e&&(t.expiredLanes|=x),c&=~x}if(e=Yt,a=Nt,a=Jr(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,a===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&de(r),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||ws(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(r!==null&&de(r),nu(a)){case 2:case 8:a=Ir;break;case 32:a=da;break;case 268435456:a=gh;break;default:a=da}return r=Eg.bind(null,t),a=jt(a,r),t.callbackPriority=e,t.callbackNode=a,e}return r!==null&&r!==null&&de(r),t.callbackPriority=2,t.callbackNode=null,2}function Eg(t,e){if(pe!==0&&pe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(Xl()&&t.callbackNode!==a)return null;var r=Nt;return r=Jr(t,t===Yt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(lg(t,r,e),Ag(t,he()),t.callbackNode!=null&&t.callbackNode===a?Eg.bind(null,t):null)}function Mg(t,e){if(Xl())return null;lg(t,e,!0)}function bT(){DT(function(){(zt&6)!==0?jt(Fr,vT):wg()})}function Gc(){return ta===0&&(ta=yh()),ta}function Cg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:al(""+t)}function Rg(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function xT(t,e,a,r,o){if(e==="submit"&&a&&a.stateNode===o){var c=Cg((o[Re]||null).action),y=r.submitter;y&&(e=(e=y[Re]||null)?Cg(e.formAction):y.getAttribute("formAction"),e!==null&&(c=e,y=null));var x=new ol("action","action",null,r,o);t.push({event:x,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(ta!==0){var R=y?Rg(o,y):new FormData(o);oc(a,{pending:!0,data:R,method:o.method,action:c},null,R)}}else typeof c=="function"&&(x.preventDefault(),R=y?Rg(o,y):new FormData(o),oc(a,{pending:!0,data:R,method:o.method,action:c},c,R))},currentTarget:o}]})}}for(var Yc=0;Yc<Cu.length;Yc++){var qc=Cu[Yc],ST=qc.toLowerCase(),TT=qc[0].toUpperCase()+qc.slice(1);ln(ST,"on"+TT)}ln(lm,"onAnimationEnd"),ln(om,"onAnimationIteration"),ln(um,"onAnimationStart"),ln("dblclick","onDoubleClick"),ln("focusin","onFocus"),ln("focusout","onBlur"),ln(k1,"onTransitionRun"),ln(H1,"onTransitionStart"),ln(P1,"onTransitionCancel"),ln(cm,"onTransitionEnd"),va("onMouseEnter",["mouseout","mouseover"]),va("onMouseLeave",["mouseout","mouseover"]),va("onPointerEnter",["pointerout","pointerover"]),va("onPointerLeave",["pointerout","pointerover"]),Ui("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ui("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ui("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ui("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ui("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ui("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wT=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(or));function Dg(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var r=t[a],o=r.event;r=r.listeners;t:{var c=void 0;if(e)for(var y=r.length-1;0<=y;y--){var x=r[y],R=x.instance,k=x.currentTarget;if(x=x.listener,R!==c&&o.isPropagationStopped())break t;c=x,o.currentTarget=k;try{c(o)}catch(Z){Vl(Z)}o.currentTarget=null,c=R}else for(y=0;y<r.length;y++){if(x=r[y],R=x.instance,k=x.currentTarget,x=x.listener,R!==c&&o.isPropagationStopped())break t;c=x,o.currentTarget=k;try{c(o)}catch(Z){Vl(Z)}o.currentTarget=null,c=R}}}}function Dt(t,e){var a=e[iu];a===void 0&&(a=e[iu]=new Set);var r=t+"__bubble";a.has(r)||(Og(e,t,2,!1),a.add(r))}function Xc(t,e,a){var r=0;e&&(r|=4),Og(a,t,r,e)}var Ql="_reactListening"+Math.random().toString(36).slice(2);function Kc(t){if(!t[Ql]){t[Ql]=!0,wh.forEach(function(a){a!=="selectionchange"&&(wT.has(a)||Xc(a,!1,t),Xc(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ql]||(e[Ql]=!0,Xc("selectionchange",!1,e))}}function Og(t,e,a,r){switch(ty(e)){case 2:var o=IT;break;case 8:o=WT;break;default:o=lf}a=o.bind(null,e,a,t),o=void 0,!mu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,a,{capture:!0,passive:o}):t.addEventListener(e,a,!0):o!==void 0?t.addEventListener(e,a,{passive:o}):t.addEventListener(e,a,!1)}function Zc(t,e,a,r,o){var c=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var x=r.stateNode.containerInfo;if(x===o)break;if(y===4)for(y=r.return;y!==null;){var R=y.tag;if((R===3||R===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;x!==null;){if(y=pa(x),y===null)return;if(R=y.tag,R===5||R===6||R===26||R===27){r=c=y;continue t}x=x.parentNode}}r=r.return}Bh(function(){var k=c,Z=du(a),F=[];t:{var H=fm.get(t);if(H!==void 0){var G=ol,gt=t;switch(t){case"keypress":if(rl(a)===0)break t;case"keydown":case"keyup":G=y1;break;case"focusin":gt="focus",G=vu;break;case"focusout":gt="blur",G=vu;break;case"beforeblur":case"afterblur":G=vu;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Hh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=s1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=x1;break;case lm:case om:case um:G=o1;break;case cm:G=T1;break;case"scroll":case"scrollend":G=i1;break;case"wheel":G=A1;break;case"copy":case"cut":case"paste":G=c1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=Gh;break;case"toggle":case"beforetoggle":G=M1}var mt=(e&4)!==0,Ht=!mt&&(t==="scroll"||t==="scrollend"),L=mt?H!==null?H+"Capture":null:H;mt=[];for(var _=k,U;_!==null;){var Q=_;if(U=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||U===null||L===null||(Q=Cs(_,L),Q!=null&&mt.push(ur(_,Q,U))),Ht)break;_=_.return}0<mt.length&&(H=new G(H,gt,null,a,Z),F.push({event:H,listeners:mt}))}}if((e&7)===0){t:{if(H=t==="mouseover"||t==="pointerover",G=t==="mouseout"||t==="pointerout",H&&a!==fu&&(gt=a.relatedTarget||a.fromElement)&&(pa(gt)||gt[ma]))break t;if((G||H)&&(H=Z.window===Z?Z:(H=Z.ownerDocument)?H.defaultView||H.parentWindow:window,G?(gt=a.relatedTarget||a.toElement,G=k,gt=gt?pa(gt):null,gt!==null&&(Ht=d(gt),mt=gt.tag,gt!==Ht||mt!==5&&mt!==27&&mt!==6)&&(gt=null)):(G=null,gt=k),G!==gt)){if(mt=Hh,Q="onMouseLeave",L="onMouseEnter",_="mouse",(t==="pointerout"||t==="pointerover")&&(mt=Gh,Q="onPointerLeave",L="onPointerEnter",_="pointer"),Ht=G==null?H:Ms(G),U=gt==null?H:Ms(gt),H=new mt(Q,_+"leave",G,a,Z),H.target=Ht,H.relatedTarget=U,Q=null,pa(Z)===k&&(mt=new mt(L,_+"enter",gt,a,Z),mt.target=U,mt.relatedTarget=Ht,Q=mt),Ht=Q,G&&gt)e:{for(mt=G,L=gt,_=0,U=mt;U;U=Fa(U))_++;for(U=0,Q=L;Q;Q=Fa(Q))U++;for(;0<_-U;)mt=Fa(mt),_--;for(;0<U-_;)L=Fa(L),U--;for(;_--;){if(mt===L||L!==null&&mt===L.alternate)break e;mt=Fa(mt),L=Fa(L)}mt=null}else mt=null;G!==null&&Ng(F,H,G,mt,!1),gt!==null&&Ht!==null&&Ng(F,Ht,gt,mt,!0)}}t:{if(H=k?Ms(k):window,G=H.nodeName&&H.nodeName.toLowerCase(),G==="select"||G==="input"&&H.type==="file")var at=Ih;else if(Qh(H))if(Wh)at=L1;else{at=V1;var Mt=_1}else G=H.nodeName,!G||G.toLowerCase()!=="input"||H.type!=="checkbox"&&H.type!=="radio"?k&&cu(k.elementType)&&(at=Ih):at=z1;if(at&&(at=at(t,k))){Fh(F,at,a,Z);break t}Mt&&Mt(t,H,k),t==="focusout"&&k&&H.type==="number"&&k.memoizedProps.value!=null&&uu(H,"number",H.value)}switch(Mt=k?Ms(k):window,t){case"focusin":(Qh(Mt)||Mt.contentEditable==="true")&&(Ea=Mt,Au=k,zs=null);break;case"focusout":zs=Au=Ea=null;break;case"mousedown":Eu=!0;break;case"contextmenu":case"mouseup":case"dragend":Eu=!1,sm(F,a,Z);break;case"selectionchange":if(U1)break;case"keydown":case"keyup":sm(F,a,Z)}var ot;if(xu)t:{switch(t){case"compositionstart":var pt="onCompositionStart";break t;case"compositionend":pt="onCompositionEnd";break t;case"compositionupdate":pt="onCompositionUpdate";break t}pt=void 0}else Aa?Kh(t,a)&&(pt="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(pt="onCompositionStart");pt&&(Yh&&a.locale!=="ko"&&(Aa||pt!=="onCompositionStart"?pt==="onCompositionEnd"&&Aa&&(ot=Uh()):(ni=Z,pu="value"in ni?ni.value:ni.textContent,Aa=!0)),Mt=Fl(k,pt),0<Mt.length&&(pt=new Ph(pt,t,null,a,Z),F.push({event:pt,listeners:Mt}),ot?pt.data=ot:(ot=Zh(a),ot!==null&&(pt.data=ot)))),(ot=R1?D1(t,a):O1(t,a))&&(pt=Fl(k,"onBeforeInput"),0<pt.length&&(Mt=new Ph("onBeforeInput","beforeinput",null,a,Z),F.push({event:Mt,listeners:pt}),Mt.data=ot)),xT(F,t,k,a,Z)}Dg(F,e)})}function ur(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Fl(t,e){for(var a=e+"Capture",r=[];t!==null;){var o=t,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Cs(t,a),o!=null&&r.unshift(ur(t,o,c)),o=Cs(t,e),o!=null&&r.push(ur(t,o,c))),t.tag===3)return r;t=t.return}return[]}function Fa(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Ng(t,e,a,r,o){for(var c=e._reactName,y=[];a!==null&&a!==r;){var x=a,R=x.alternate,k=x.stateNode;if(x=x.tag,R!==null&&R===r)break;x!==5&&x!==26&&x!==27||k===null||(R=k,o?(k=Cs(a,c),k!=null&&y.unshift(ur(a,k,R))):o||(k=Cs(a,c),k!=null&&y.push(ur(a,k,R)))),a=a.return}y.length!==0&&t.push({event:e,listeners:y})}var AT=/\r\n?/g,ET=/\u0000|\uFFFD/g;function jg(t){return(typeof t=="string"?t:""+t).replace(AT,`
`).replace(ET,"")}function _g(t,e){return e=jg(e),jg(t)===e}function Il(){}function kt(t,e,a,r,o,c){switch(a){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||Sa(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&Sa(t,""+r);break;case"className":el(t,"class",r);break;case"tabIndex":el(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":el(t,a,r);break;case"style":zh(t,r,c);break;case"data":if(e!=="object"){el(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(a);break}r=al(""+r),t.setAttribute(a,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(e!=="input"&&kt(t,e,"name",o.name,o,null),kt(t,e,"formEncType",o.formEncType,o,null),kt(t,e,"formMethod",o.formMethod,o,null),kt(t,e,"formTarget",o.formTarget,o,null)):(kt(t,e,"encType",o.encType,o,null),kt(t,e,"method",o.method,o,null),kt(t,e,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(a);break}r=al(""+r),t.setAttribute(a,r);break;case"onClick":r!=null&&(t.onclick=Il);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(a=r.__html,a!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}a=al(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(a,""+r):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":r===!0?t.setAttribute(a,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(a,r):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(a,r):t.removeAttribute(a);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(a):t.setAttribute(a,r);break;case"popover":Dt("beforetoggle",t),Dt("toggle",t),tl(t,"popover",r);break;case"xlinkActuate":_n(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":_n(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":_n(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":_n(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":_n(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":_n(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":_n(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":_n(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":_n(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tl(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=e1.get(a)||a,tl(t,a,r))}}function Qc(t,e,a,r,o,c){switch(a){case"style":zh(t,r,c);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(a=r.__html,a!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"children":typeof r=="string"?Sa(t,r):(typeof r=="number"||typeof r=="bigint")&&Sa(t,""+r);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Il);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ah.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(o=a.endsWith("Capture"),e=a.slice(2,o?a.length-7:void 0),c=t[Re]||null,c=c!=null?c[a]:null,typeof c=="function"&&t.removeEventListener(e,c,o),typeof r=="function")){typeof c!="function"&&c!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,r,o);break t}a in t?t[a]=r:r===!0?t.setAttribute(a,""):tl(t,a,r)}}}function ge(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dt("error",t),Dt("load",t);var r=!1,o=!1,c;for(c in a)if(a.hasOwnProperty(c)){var y=a[c];if(y!=null)switch(c){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,c,y,a,null)}}o&&kt(t,e,"srcSet",a.srcSet,a,null),r&&kt(t,e,"src",a.src,a,null);return;case"input":Dt("invalid",t);var x=c=y=o=null,R=null,k=null;for(r in a)if(a.hasOwnProperty(r)){var Z=a[r];if(Z!=null)switch(r){case"name":o=Z;break;case"type":y=Z;break;case"checked":R=Z;break;case"defaultChecked":k=Z;break;case"value":c=Z;break;case"defaultValue":x=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(l(137,e));break;default:kt(t,e,r,Z,a,null)}}Nh(t,c,x,R,k,y,o,!1),nl(t);return;case"select":Dt("invalid",t),r=y=c=null;for(o in a)if(a.hasOwnProperty(o)&&(x=a[o],x!=null))switch(o){case"value":c=x;break;case"defaultValue":y=x;break;case"multiple":r=x;default:kt(t,e,o,x,a,null)}e=c,a=y,t.multiple=!!r,e!=null?xa(t,!!r,e,!1):a!=null&&xa(t,!!r,a,!0);return;case"textarea":Dt("invalid",t),c=o=r=null;for(y in a)if(a.hasOwnProperty(y)&&(x=a[y],x!=null))switch(y){case"value":r=x;break;case"defaultValue":o=x;break;case"children":c=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(l(91));break;default:kt(t,e,y,x,a,null)}_h(t,r,o,c),nl(t);return;case"option":for(R in a)if(a.hasOwnProperty(R)&&(r=a[R],r!=null))switch(R){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:kt(t,e,R,r,a,null)}return;case"dialog":Dt("beforetoggle",t),Dt("toggle",t),Dt("cancel",t),Dt("close",t);break;case"iframe":case"object":Dt("load",t);break;case"video":case"audio":for(r=0;r<or.length;r++)Dt(or[r],t);break;case"image":Dt("error",t),Dt("load",t);break;case"details":Dt("toggle",t);break;case"embed":case"source":case"link":Dt("error",t),Dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in a)if(a.hasOwnProperty(k)&&(r=a[k],r!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,k,r,a,null)}return;default:if(cu(e)){for(Z in a)a.hasOwnProperty(Z)&&(r=a[Z],r!==void 0&&Qc(t,e,Z,r,a,void 0));return}}for(x in a)a.hasOwnProperty(x)&&(r=a[x],r!=null&&kt(t,e,x,r,a,null))}function MT(t,e,a,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,y=null,x=null,R=null,k=null,Z=null;for(G in a){var F=a[G];if(a.hasOwnProperty(G)&&F!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":R=F;default:r.hasOwnProperty(G)||kt(t,e,G,null,r,F)}}for(var H in r){var G=r[H];if(F=a[H],r.hasOwnProperty(H)&&(G!=null||F!=null))switch(H){case"type":c=G;break;case"name":o=G;break;case"checked":k=G;break;case"defaultChecked":Z=G;break;case"value":y=G;break;case"defaultValue":x=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(l(137,e));break;default:G!==F&&kt(t,e,H,G,r,F)}}ou(t,y,x,R,k,Z,c,o);return;case"select":G=y=x=H=null;for(c in a)if(R=a[c],a.hasOwnProperty(c)&&R!=null)switch(c){case"value":break;case"multiple":G=R;default:r.hasOwnProperty(c)||kt(t,e,c,null,r,R)}for(o in r)if(c=r[o],R=a[o],r.hasOwnProperty(o)&&(c!=null||R!=null))switch(o){case"value":H=c;break;case"defaultValue":x=c;break;case"multiple":y=c;default:c!==R&&kt(t,e,o,c,r,R)}e=x,a=y,r=G,H!=null?xa(t,!!a,H,!1):!!r!=!!a&&(e!=null?xa(t,!!a,e,!0):xa(t,!!a,a?[]:"",!1));return;case"textarea":G=H=null;for(x in a)if(o=a[x],a.hasOwnProperty(x)&&o!=null&&!r.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:kt(t,e,x,null,r,o)}for(y in r)if(o=r[y],c=a[y],r.hasOwnProperty(y)&&(o!=null||c!=null))switch(y){case"value":H=o;break;case"defaultValue":G=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(l(91));break;default:o!==c&&kt(t,e,y,o,r,c)}jh(t,H,G);return;case"option":for(var gt in a)if(H=a[gt],a.hasOwnProperty(gt)&&H!=null&&!r.hasOwnProperty(gt))switch(gt){case"selected":t.selected=!1;break;default:kt(t,e,gt,null,r,H)}for(R in r)if(H=r[R],G=a[R],r.hasOwnProperty(R)&&H!==G&&(H!=null||G!=null))switch(R){case"selected":t.selected=H&&typeof H!="function"&&typeof H!="symbol";break;default:kt(t,e,R,H,r,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var mt in a)H=a[mt],a.hasOwnProperty(mt)&&H!=null&&!r.hasOwnProperty(mt)&&kt(t,e,mt,null,r,H);for(k in r)if(H=r[k],G=a[k],r.hasOwnProperty(k)&&H!==G&&(H!=null||G!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(l(137,e));break;default:kt(t,e,k,H,r,G)}return;default:if(cu(e)){for(var Ht in a)H=a[Ht],a.hasOwnProperty(Ht)&&H!==void 0&&!r.hasOwnProperty(Ht)&&Qc(t,e,Ht,void 0,r,H);for(Z in r)H=r[Z],G=a[Z],!r.hasOwnProperty(Z)||H===G||H===void 0&&G===void 0||Qc(t,e,Z,H,r,G);return}}for(var L in a)H=a[L],a.hasOwnProperty(L)&&H!=null&&!r.hasOwnProperty(L)&&kt(t,e,L,null,r,H);for(F in r)H=r[F],G=a[F],!r.hasOwnProperty(F)||H===G||H==null&&G==null||kt(t,e,F,H,r,G)}var Fc=null,Ic=null;function Wl(t){return t.nodeType===9?t:t.ownerDocument}function Vg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function zg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Wc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var $c=null;function CT(){var t=window.event;return t&&t.type==="popstate"?t===$c?!1:($c=t,!0):($c=null,!1)}var Lg=typeof setTimeout=="function"?setTimeout:void 0,RT=typeof clearTimeout=="function"?clearTimeout:void 0,Bg=typeof Promise=="function"?Promise:void 0,DT=typeof queueMicrotask=="function"?queueMicrotask:typeof Bg!="undefined"?function(t){return Bg.resolve(null).then(t).catch(OT)}:Lg;function OT(t){setTimeout(function(){throw t})}function vi(t){return t==="head"}function Ug(t,e){var a=e,r=0,o=0;do{var c=a.nextSibling;if(t.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<r&&8>r){a=r;var y=t.ownerDocument;if(a&1&&cr(y.documentElement),a&2&&cr(y.body),a&4)for(a=y.head,cr(a),y=a.firstChild;y;){var x=y.nextSibling,R=y.nodeName;y[Es]||R==="SCRIPT"||R==="STYLE"||R==="LINK"&&y.rel.toLowerCase()==="stylesheet"||a.removeChild(y),y=x}}if(o===0){t.removeChild(c),vr(e);return}o--}else a==="$"||a==="$?"||a==="$!"?o++:r=a.charCodeAt(0)-48;else r=0;a=c}while(a);vr(e)}function Jc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Jc(a),au(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function NT(t,e,a,r){for(;t.nodeType===1;){var o=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[Es])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=un(t.nextSibling),t===null)break}return null}function jT(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=un(t.nextSibling),t===null))return null;return t}function tf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function _T(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var r=function(){e(),a.removeEventListener("DOMContentLoaded",r)};a.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function un(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ef=null;function kg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function Hg(t,e,a){switch(e=Wl(a),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function cr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);au(t)}var Je=new Map,Pg=new Set;function $l(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Zn=q.d;q.d={f:VT,r:zT,D:LT,C:BT,L:UT,m:kT,X:PT,S:HT,M:GT};function VT(){var t=Zn.f(),e=Yl();return t||e}function zT(t){var e=ga(t);e!==null&&e.tag===5&&e.type==="form"?lp(e):Zn.r(t)}var Ia=typeof document=="undefined"?null:document;function Gg(t,e,a){var r=Ia;if(r&&typeof e=="string"&&e){var o=Ke(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof a=="string"&&(o+='[crossorigin="'+a+'"]'),Pg.has(o)||(Pg.add(o),t={rel:t,crossOrigin:a,href:e},r.querySelector(o)===null&&(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function LT(t){Zn.D(t),Gg("dns-prefetch",t,null)}function BT(t,e){Zn.C(t,e),Gg("preconnect",t,e)}function UT(t,e,a){Zn.L(t,e,a);var r=Ia;if(r&&t&&e){var o='link[rel="preload"][as="'+Ke(e)+'"]';e==="image"&&a&&a.imageSrcSet?(o+='[imagesrcset="'+Ke(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(o+='[imagesizes="'+Ke(a.imageSizes)+'"]')):o+='[href="'+Ke(t)+'"]';var c=o;switch(e){case"style":c=Wa(t);break;case"script":c=$a(t)}Je.has(c)||(t=g({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),Je.set(c,t),r.querySelector(o)!==null||e==="style"&&r.querySelector(fr(c))||e==="script"&&r.querySelector(dr(c))||(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function kT(t,e){Zn.m(t,e);var a=Ia;if(a&&t){var r=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ke(r)+'"][href="'+Ke(t)+'"]',c=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=$a(t)}if(!Je.has(c)&&(t=g({rel:"modulepreload",href:t},e),Je.set(c,t),a.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(dr(c)))return}r=a.createElement("link"),ge(r,"link",t),oe(r),a.head.appendChild(r)}}}function HT(t,e,a){Zn.S(t,e,a);var r=Ia;if(r&&t){var o=ya(r).hoistableStyles,c=Wa(t);e=e||"default";var y=o.get(c);if(!y){var x={loading:0,preload:null};if(y=r.querySelector(fr(c)))x.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},a),(a=Je.get(c))&&nf(t,a);var R=y=r.createElement("link");oe(R),ge(R,"link",t),R._p=new Promise(function(k,Z){R.onload=k,R.onerror=Z}),R.addEventListener("load",function(){x.loading|=1}),R.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Jl(y,e,r)}y={type:"stylesheet",instance:y,count:1,state:x},o.set(c,y)}}}function PT(t,e){Zn.X(t,e);var a=Ia;if(a&&t){var r=ya(a).hoistableScripts,o=$a(t),c=r.get(o);c||(c=a.querySelector(dr(o)),c||(t=g({src:t,async:!0},e),(e=Je.get(o))&&af(t,e),c=a.createElement("script"),oe(c),ge(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function GT(t,e){Zn.M(t,e);var a=Ia;if(a&&t){var r=ya(a).hoistableScripts,o=$a(t),c=r.get(o);c||(c=a.querySelector(dr(o)),c||(t=g({src:t,async:!0,type:"module"},e),(e=Je.get(o))&&af(t,e),c=a.createElement("script"),oe(c),ge(c,"link",t),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Yg(t,e,a,r){var o=(o=dt.current)?$l(o):null;if(!o)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=Wa(a.href),a=ya(o).hoistableStyles,r=a.get(e),r||(r={type:"style",instance:null,count:0,state:null},a.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=Wa(a.href);var c=ya(o).hoistableStyles,y=c.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,y),(c=o.querySelector(fr(t)))&&!c._p&&(y.instance=c,y.state.loading=5),Je.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Je.set(t,a),c||YT(o,t,a,y.state))),e&&r===null)throw Error(l(528,""));return y}if(e&&r!==null)throw Error(l(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$a(a),a=ya(o).hoistableScripts,r=a.get(e),r||(r={type:"script",instance:null,count:0,state:null},a.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function Wa(t){return'href="'+Ke(t)+'"'}function fr(t){return'link[rel="stylesheet"]['+t+"]"}function qg(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function YT(t,e,a,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),ge(e,"link",a),oe(e),t.head.appendChild(e))}function $a(t){return'[src="'+Ke(t)+'"]'}function dr(t){return"script[async]"+t}function Xg(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Ke(a.href)+'"]');if(r)return e.instance=r,oe(r),r;var o=g({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),oe(r),ge(r,"style",o),Jl(r,a.precedence,t),e.instance=r;case"stylesheet":o=Wa(a.href);var c=t.querySelector(fr(o));if(c)return e.state.loading|=4,e.instance=c,oe(c),c;r=qg(a),(o=Je.get(o))&&nf(r,o),c=(t.ownerDocument||t).createElement("link"),oe(c);var y=c;return y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(c,"link",r),e.state.loading|=4,Jl(c,a.precedence,t),e.instance=c;case"script":return c=$a(a.src),(o=t.querySelector(dr(c)))?(e.instance=o,oe(o),o):(r=a,(o=Je.get(c))&&(r=g({},a),af(r,o)),t=t.ownerDocument||t,o=t.createElement("script"),oe(o),ge(o,"link",r),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,Jl(r,a.precedence,t));return e.instance}function Jl(t,e,a){for(var r=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,c=o,y=0;y<r.length;y++){var x=r[y];if(x.dataset.precedence===e)c=x;else if(c!==o)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function nf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function af(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var to=null;function Kg(t,e,a){if(to===null){var r=new Map,o=to=new Map;o.set(a,r)}else o=to,r=o.get(a),r||(r=new Map,o.set(a,r));if(r.has(t))return r;for(r.set(t,null),a=a.getElementsByTagName(t),o=0;o<a.length;o++){var c=a[o];if(!(c[Es]||c[ve]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(e)||"";y=t+y;var x=r.get(y);x?x.push(c):r.set(y,[c])}}return r}function Zg(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function qT(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Qg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var hr=null;function XT(){}function KT(t,e,a){if(hr===null)throw Error(l(475));var r=hr;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Wa(a.href),c=t.querySelector(fr(o));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=eo.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=c,oe(c);return}c=t.ownerDocument||t,a=qg(a),(o=Je.get(o))&&nf(a,o),c=c.createElement("link"),oe(c);var y=c;y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(c,"link",a),e.instance=c}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=eo.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function ZT(){if(hr===null)throw Error(l(475));var t=hr;return t.stylesheets&&t.count===0&&sf(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&sf(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function eo(){if(this.count--,this.count===0){if(this.stylesheets)sf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var no=null;function sf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,no=new Map,e.forEach(QT,t),no=null,eo.call(t))}function QT(t,e){if(!(e.state.loading&4)){var a=no.get(t);if(a)var r=a.get(null);else{a=new Map,no.set(t,a);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var y=o[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(a.set(y.dataset.precedence,y),r=y)}r&&a.set(null,r)}o=e.instance,y=o.getAttribute("data-precedence"),c=a.get(y)||r,c===r&&a.set(null,o),a.set(y,o),this.count++,r=eo.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),c?c.parentNode.insertBefore(o,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var mr={$$typeof:A,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function FT(t,e,a,r,o,c,y,x){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=tu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tu(0),this.hiddenUpdates=tu(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function Fg(t,e,a,r,o,c,y,x,R,k,Z,F){return t=new FT(t,e,a,y,x,R,k,F),e=1,c===!0&&(e|=24),c=Be(3,null,null,e),t.current=c,c.stateNode=t,e=ku(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:r,isDehydrated:a,cache:e},Yu(c),t}function Ig(t){return t?(t=Da,t):Da}function Wg(t,e,a,r,o,c){o=Ig(o),r.context===null?r.context=o:r.pendingContext=o,r=si(e),r.payload={element:a},c=c===void 0?null:c,c!==null&&(r.callback=c),a=ri(t,r,e),a!==null&&(Ge(a,t,e),qs(a,t,e))}function $g(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function rf(t,e){$g(t,e),(t=t.alternate)&&$g(t,e)}function Jg(t){if(t.tag===13){var e=Ra(t,67108864);e!==null&&Ge(e,t,67108864),rf(t,67108864)}}var io=!0;function IT(t,e,a,r){var o=B.T;B.T=null;var c=q.p;try{q.p=2,lf(t,e,a,r)}finally{q.p=c,B.T=o}}function WT(t,e,a,r){var o=B.T;B.T=null;var c=q.p;try{q.p=8,lf(t,e,a,r)}finally{q.p=c,B.T=o}}function lf(t,e,a,r){if(io){var o=of(r);if(o===null)Zc(t,e,r,ao,a),ey(t,r);else if(JT(o,t,e,a,r))r.stopPropagation();else if(ey(t,r),e&4&&-1<$T.indexOf(t)){for(;o!==null;){var c=ga(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=Bi(c.pendingLanes);if(y!==0){var x=c;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var R=1<<31-ze(y);x.entanglements[1]|=R,y&=~R}bn(c),(zt&6)===0&&(Pl=he()+500,lr(0))}}break;case 13:x=Ra(c,2),x!==null&&Ge(x,c,2),Yl(),rf(c,2)}if(c=of(r),c===null&&Zc(t,e,r,ao,a),c===o)break;o=c}o!==null&&r.stopPropagation()}else Zc(t,e,r,null,a)}}function of(t){return t=du(t),uf(t)}var ao=null;function uf(t){if(ao=null,t=pa(t),t!==null){var e=d(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=f(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return ao=t,null}function ty(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Li()){case Fr:return 2;case Ir:return 8;case da:case ha:return 32;case gh:return 268435456;default:return 32}default:return 32}}var cf=!1,bi=null,xi=null,Si=null,pr=new Map,gr=new Map,Ti=[],$T="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ey(t,e){switch(t){case"focusin":case"focusout":bi=null;break;case"dragenter":case"dragleave":xi=null;break;case"mouseover":case"mouseout":Si=null;break;case"pointerover":case"pointerout":pr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(e.pointerId)}}function yr(t,e,a,r,o,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:a,eventSystemFlags:r,nativeEvent:c,targetContainers:[o]},e!==null&&(e=ga(e),e!==null&&Jg(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function JT(t,e,a,r,o){switch(e){case"focusin":return bi=yr(bi,t,e,a,r,o),!0;case"dragenter":return xi=yr(xi,t,e,a,r,o),!0;case"mouseover":return Si=yr(Si,t,e,a,r,o),!0;case"pointerover":var c=o.pointerId;return pr.set(c,yr(pr.get(c)||null,t,e,a,r,o)),!0;case"gotpointercapture":return c=o.pointerId,gr.set(c,yr(gr.get(c)||null,t,e,a,r,o)),!0}return!1}function ny(t){var e=pa(t.target);if(e!==null){var a=d(e);if(a!==null){if(e=a.tag,e===13){if(e=f(a),e!==null){t.blockedOn=e,KS(t.priority,function(){if(a.tag===13){var r=Pe();r=eu(r);var o=Ra(a,r);o!==null&&Ge(o,a,r),rf(a,r)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function so(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=of(t.nativeEvent);if(a===null){a=t.nativeEvent;var r=new a.constructor(a.type,a);fu=r,a.target.dispatchEvent(r),fu=null}else return e=ga(a),e!==null&&Jg(e),t.blockedOn=a,!1;e.shift()}return!0}function iy(t,e,a){so(t)&&a.delete(e)}function tw(){cf=!1,bi!==null&&so(bi)&&(bi=null),xi!==null&&so(xi)&&(xi=null),Si!==null&&so(Si)&&(Si=null),pr.forEach(iy),gr.forEach(iy)}function ro(t,e){t.blockedOn===e&&(t.blockedOn=null,cf||(cf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,tw)))}var lo=null;function ay(t){lo!==t&&(lo=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){lo===t&&(lo=null);for(var e=0;e<t.length;e+=3){var a=t[e],r=t[e+1],o=t[e+2];if(typeof r!="function"){if(uf(r||a)===null)continue;break}var c=ga(a);c!==null&&(t.splice(e,3),e-=3,oc(c,{pending:!0,data:o,method:a.method,action:r},r,o))}}))}function vr(t){function e(R){return ro(R,t)}bi!==null&&ro(bi,t),xi!==null&&ro(xi,t),Si!==null&&ro(Si,t),pr.forEach(e),gr.forEach(e);for(var a=0;a<Ti.length;a++){var r=Ti[a];r.blockedOn===t&&(r.blockedOn=null)}for(;0<Ti.length&&(a=Ti[0],a.blockedOn===null);)ny(a),a.blockedOn===null&&Ti.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(r=0;r<a.length;r+=3){var o=a[r],c=a[r+1],y=o[Re]||null;if(typeof c=="function")y||ay(a);else if(y){var x=null;if(c&&c.hasAttribute("formAction")){if(o=c,y=c[Re]||null)x=y.formAction;else if(uf(o)!==null)continue}else x=y.action;typeof x=="function"?a[r+1]=x:(a.splice(r,3),r-=3),ay(a)}}}function ff(t){this._internalRoot=t}oo.prototype.render=ff.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var a=e.current,r=Pe();Wg(a,r,t,e,null,null)},oo.prototype.unmount=ff.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Wg(t.current,2,null,t,null,null),Yl(),e[ma]=null}};function oo(t){this._internalRoot=t}oo.prototype.unstable_scheduleHydration=function(t){if(t){var e=Sh();t={blockedOn:null,target:t,priority:e};for(var a=0;a<Ti.length&&e!==0&&e<Ti[a].priority;a++);Ti.splice(a,0,t),a===0&&ny(t)}};var sy=i.version;if(sy!=="19.1.0")throw Error(l(527,sy,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=m(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var ew={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var uo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!uo.isDisabled&&uo.supportsFiber)try{Ts=uo.inject(ew),Ve=uo}catch(t){}}return xr.createRoot=function(t,e){if(!u(t))throw Error(l(299));var a=!1,r="",o=Sp,c=Tp,y=wp,x=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(x=e.unstable_transitionCallbacks)),e=Fg(t,1,!1,null,null,a,r,o,c,y,x,null),t[ma]=e.current,Kc(t),new ff(e)},xr.hydrateRoot=function(t,e,a){if(!u(t))throw Error(l(299));var r=!1,o="",c=Sp,y=Tp,x=wp,R=null,k=null;return a!=null&&(a.unstable_strictMode===!0&&(r=!0),a.identifierPrefix!==void 0&&(o=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(y=a.onCaughtError),a.onRecoverableError!==void 0&&(x=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(R=a.unstable_transitionCallbacks),a.formState!==void 0&&(k=a.formState)),e=Fg(t,1,!0,e,a!=null?a:null,r,o,c,y,x,R,k),e.context=Ig(null),a=e.current,r=Pe(),r=eu(r),o=si(r),o.callback=null,ri(a,o,r),a=r,e.current.lanes=a,As(e,a),bn(e),t[ma]=e.current,Kc(t),new oo(e)},xr.version="19.1.0",xr}var vy;function pw(){if(vy)return pf.exports;vy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}return n(),pf.exports=mw(),pf.exports}var gw=pw();const y0=w.createContext({});function yw(n){const i=w.useRef(null);return i.current===null&&(i.current=n()),i.current}const Ed=typeof window!="undefined",vw=Ed?w.useLayoutEffect:w.useEffect,Md=w.createContext(null);function Cd(n,i){n.indexOf(i)===-1&&n.push(i)}function Rd(n,i){const s=n.indexOf(i);s>-1&&n.splice(s,1)}const In=(n,i,s)=>s>i?i:s<n?n:s;let Dd=()=>{};const Wn={},v0=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function b0(n){return typeof n=="object"&&n!==null}const x0=n=>/^0[^.\s]+$/u.test(n);function Od(n){let i;return()=>(i===void 0&&(i=n()),i)}const sn=n=>n,bw=(n,i)=>s=>i(n(s)),Hr=(...n)=>n.reduce(bw),Or=(n,i,s)=>{const l=i-n;return l===0?1:(s-n)/l};class Nd{constructor(){this.subscriptions=[]}add(i){return Cd(this.subscriptions,i),()=>Rd(this.subscriptions,i)}notify(i,s,l){const u=this.subscriptions.length;if(u)if(u===1)this.subscriptions[0](i,s,l);else for(let d=0;d<u;d++){const f=this.subscriptions[d];f&&f(i,s,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Cn=n=>n*1e3,Rn=n=>n/1e3;function S0(n,i){return i?n*(1e3/i):0}const T0=(n,i,s)=>(((1-3*s+3*i)*n+(3*s-6*i))*n+3*i)*n,xw=1e-7,Sw=12;function Tw(n,i,s,l,u){let d,f,h=0;do f=i+(s-i)/2,d=T0(f,l,u)-n,d>0?s=f:i=f;while(Math.abs(d)>xw&&++h<Sw);return f}function Pr(n,i,s,l){if(n===i&&s===l)return sn;const u=d=>Tw(d,0,1,n,s);return d=>d===0||d===1?d:T0(u(d),i,l)}const w0=n=>i=>i<=.5?n(2*i)/2:(2-n(2*(1-i)))/2,A0=n=>i=>1-n(1-i),E0=Pr(.33,1.53,.69,.99),jd=A0(E0),M0=w0(jd),C0=n=>(n*=2)<1?.5*jd(n):.5*(2-Math.pow(2,-10*(n-1))),_d=n=>1-Math.sin(Math.acos(n)),R0=A0(_d),D0=w0(_d),ww=Pr(.42,0,1,1),Aw=Pr(0,0,.58,1),O0=Pr(.42,0,.58,1),Ew=n=>Array.isArray(n)&&typeof n[0]!="number",N0=n=>Array.isArray(n)&&typeof n[0]=="number",Mw={linear:sn,easeIn:ww,easeInOut:O0,easeOut:Aw,circIn:_d,circInOut:D0,circOut:R0,backIn:jd,backInOut:M0,backOut:E0,anticipate:C0},Cw=n=>typeof n=="string",by=n=>{if(N0(n)){Dd(n.length===4);const[i,s,l,u]=n;return Pr(i,s,l,u)}else if(Cw(n))return Mw[n];return n},fo=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],xy={value:null};function Rw(n,i){let s=new Set,l=new Set,u=!1,d=!1;const f=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},m=0;function p(v){f.has(v)&&(g.schedule(v),n()),m++,v(h)}const g={schedule:(v,b=!1,T=!1)=>{const M=T&&u?s:l;return b&&f.add(v),M.has(v)||M.add(v),v},cancel:v=>{l.delete(v),f.delete(v)},process:v=>{if(h=v,u){d=!0;return}u=!0,[s,l]=[l,s],s.forEach(p),i&&xy.value&&xy.value.frameloop[i].push(m),m=0,s.clear(),u=!1,d&&(d=!1,g.process(v))}};return g}const Dw=40;function j0(n,i){let s=!1,l=!0;const u={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=fo.reduce((A,V)=>(A[V]=Rw(d,i?V:void 0),A),{}),{setup:h,read:m,resolveKeyframes:p,preUpdate:g,update:v,preRender:b,render:T,postRender:C}=f,M=()=>{const A=Wn.useManualTiming?u.timestamp:performance.now();s=!1,Wn.useManualTiming||(u.delta=l?1e3/60:Math.max(Math.min(A-u.timestamp,Dw),1)),u.timestamp=A,u.isProcessing=!0,h.process(u),m.process(u),p.process(u),g.process(u),v.process(u),b.process(u),T.process(u),C.process(u),u.isProcessing=!1,s&&i&&(l=!1,n(M))},E=()=>{s=!0,l=!0,u.isProcessing||n(M)};return{schedule:fo.reduce((A,V)=>{const j=f[V];return A[V]=(I,J=!1,Y=!1)=>(s||E(),j.schedule(I,J,Y)),A},{}),cancel:A=>{for(let V=0;V<fo.length;V++)f[fo[V]].cancel(A)},state:u,steps:f}}const{schedule:Ft,cancel:Ri,state:ye,steps:bf}=j0(typeof requestAnimationFrame!="undefined"?requestAnimationFrame:sn,!0);let Eo;function Ow(){Eo=void 0}const _e={now:()=>(Eo===void 0&&_e.set(ye.isProcessing||Wn.useManualTiming?ye.timestamp:performance.now()),Eo),set:n=>{Eo=n,queueMicrotask(Ow)}},_0=n=>i=>typeof i=="string"&&i.startsWith(n),Vd=_0("--"),Nw=_0("var(--"),zd=n=>Nw(n)?jw.test(n.split("/*")[0].trim()):!1,jw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ms={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Nr=K(O({},ms),{transform:n=>In(0,1,n)}),ho=K(O({},ms),{default:1}),Er=n=>Math.round(n*1e5)/1e5,Ld=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function _w(n){return n==null}const Vw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Bd=(n,i)=>s=>!!(typeof s=="string"&&Vw.test(s)&&s.startsWith(n)||i&&!_w(s)&&Object.prototype.hasOwnProperty.call(s,i)),V0=(n,i,s)=>l=>{if(typeof l!="string")return l;const[u,d,f,h]=l.match(Ld);return{[n]:parseFloat(u),[i]:parseFloat(d),[s]:parseFloat(f),alpha:h!==void 0?parseFloat(h):1}},zw=n=>In(0,255,n),xf=K(O({},ms),{transform:n=>Math.round(zw(n))}),sa={test:Bd("rgb","red"),parse:V0("red","green","blue"),transform:({red:n,green:i,blue:s,alpha:l=1})=>"rgba("+xf.transform(n)+", "+xf.transform(i)+", "+xf.transform(s)+", "+Er(Nr.transform(l))+")"};function Lw(n){let i="",s="",l="",u="";return n.length>5?(i=n.substring(1,3),s=n.substring(3,5),l=n.substring(5,7),u=n.substring(7,9)):(i=n.substring(1,2),s=n.substring(2,3),l=n.substring(3,4),u=n.substring(4,5),i+=i,s+=s,l+=l,u+=u),{red:parseInt(i,16),green:parseInt(s,16),blue:parseInt(l,16),alpha:u?parseInt(u,16)/255:1}}const qf={test:Bd("#"),parse:Lw,transform:sa.transform},Gr=n=>({test:i=>typeof i=="string"&&i.endsWith(n)&&i.split(" ").length===1,parse:parseFloat,transform:i=>`${i}${n}`}),Mi=Gr("deg"),Dn=Gr("%"),yt=Gr("px"),Bw=Gr("vh"),Uw=Gr("vw"),Sy=K(O({},Dn),{parse:n=>Dn.parse(n)/100,transform:n=>Dn.transform(n*100)}),as={test:Bd("hsl","hue"),parse:V0("hue","saturation","lightness"),transform:({hue:n,saturation:i,lightness:s,alpha:l=1})=>"hsla("+Math.round(n)+", "+Dn.transform(Er(i))+", "+Dn.transform(Er(s))+", "+Er(Nr.transform(l))+")"},Se={test:n=>sa.test(n)||qf.test(n)||as.test(n),parse:n=>sa.test(n)?sa.parse(n):as.test(n)?as.parse(n):qf.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?sa.transform(n):as.transform(n)},kw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Hw(n){var i,s;return isNaN(n)&&typeof n=="string"&&(((i=n.match(Ld))==null?void 0:i.length)||0)+(((s=n.match(kw))==null?void 0:s.length)||0)>0}const z0="number",L0="color",Pw="var",Gw="var(",Ty="${}",Yw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function jr(n){const i=n.toString(),s=[],l={color:[],number:[],var:[]},u=[];let d=0;const h=i.replace(Yw,m=>(Se.test(m)?(l.color.push(d),u.push(L0),s.push(Se.parse(m))):m.startsWith(Gw)?(l.var.push(d),u.push(Pw),s.push(m)):(l.number.push(d),u.push(z0),s.push(parseFloat(m))),++d,Ty)).split(Ty);return{values:s,split:h,indexes:l,types:u}}function B0(n){return jr(n).values}function U0(n){const{split:i,types:s}=jr(n),l=i.length;return u=>{let d="";for(let f=0;f<l;f++)if(d+=i[f],u[f]!==void 0){const h=s[f];h===z0?d+=Er(u[f]):h===L0?d+=Se.transform(u[f]):d+=u[f]}return d}}const qw=n=>typeof n=="number"?0:n;function Xw(n){const i=B0(n);return U0(n)(i.map(qw))}const Di={test:Hw,parse:B0,createTransformer:U0,getAnimatableNone:Xw};function Sf(n,i,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(i-n)*6*s:s<1/2?i:s<2/3?n+(i-n)*(2/3-s)*6:n}function Kw({hue:n,saturation:i,lightness:s,alpha:l}){n/=360,i/=100,s/=100;let u=0,d=0,f=0;if(!i)u=d=f=s;else{const h=s<.5?s*(1+i):s+i-s*i,m=2*s-h;u=Sf(m,h,n+1/3),d=Sf(m,h,n),f=Sf(m,h,n-1/3)}return{red:Math.round(u*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:l}}function jo(n,i){return s=>s>0?i:n}const Qt=(n,i,s)=>n+(i-n)*s,Tf=(n,i,s)=>{const l=n*n,u=s*(i*i-l)+l;return u<0?0:Math.sqrt(u)},Zw=[qf,sa,as],Qw=n=>Zw.find(i=>i.test(n));function wy(n){const i=Qw(n);if(!i)return!1;let s=i.parse(n);return i===as&&(s=Kw(s)),s}const Ay=(n,i)=>{const s=wy(n),l=wy(i);if(!s||!l)return jo(n,i);const u=O({},s);return d=>(u.red=Tf(s.red,l.red,d),u.green=Tf(s.green,l.green,d),u.blue=Tf(s.blue,l.blue,d),u.alpha=Qt(s.alpha,l.alpha,d),sa.transform(u))},Xf=new Set(["none","hidden"]);function Fw(n,i){return Xf.has(n)?s=>s<=0?n:i:s=>s>=1?i:n}function Iw(n,i){return s=>Qt(n,i,s)}function Ud(n){return typeof n=="number"?Iw:typeof n=="string"?zd(n)?jo:Se.test(n)?Ay:Jw:Array.isArray(n)?k0:typeof n=="object"?Se.test(n)?Ay:Ww:jo}function k0(n,i){const s=[...n],l=s.length,u=n.map((d,f)=>Ud(d)(d,i[f]));return d=>{for(let f=0;f<l;f++)s[f]=u[f](d);return s}}function Ww(n,i){const s=O(O({},n),i),l={};for(const u in s)n[u]!==void 0&&i[u]!==void 0&&(l[u]=Ud(n[u])(n[u],i[u]));return u=>{for(const d in l)s[d]=l[d](u);return s}}function $w(n,i){var u;const s=[],l={color:0,var:0,number:0};for(let d=0;d<i.values.length;d++){const f=i.types[d],h=n.indexes[f][l[f]],m=(u=n.values[h])!=null?u:0;s[d]=m,l[f]++}return s}const Jw=(n,i)=>{const s=Di.createTransformer(i),l=jr(n),u=jr(i);return l.indexes.var.length===u.indexes.var.length&&l.indexes.color.length===u.indexes.color.length&&l.indexes.number.length>=u.indexes.number.length?Xf.has(n)&&!u.values.length||Xf.has(i)&&!l.values.length?Fw(n,i):Hr(k0($w(l,u),u.values),s):jo(n,i)};function H0(n,i,s){return typeof n=="number"&&typeof i=="number"&&typeof s=="number"?Qt(n,i,s):Ud(n)(n,i)}const tA=n=>{const i=({timestamp:s})=>n(s);return{start:(s=!0)=>Ft.update(i,s),stop:()=>Ri(i),now:()=>ye.isProcessing?ye.timestamp:_e.now()}},P0=(n,i,s=10)=>{let l="";const u=Math.max(Math.round(i/s),2);for(let d=0;d<u;d++)l+=n(d/(u-1))+", ";return`linear(${l.substring(0,l.length-2)})`},_o=2e4;function kd(n){let i=0;const s=50;let l=n.next(i);for(;!l.done&&i<_o;)i+=s,l=n.next(i);return i>=_o?1/0:i}function eA(n,i=100,s){const l=s(K(O({},n),{keyframes:[0,i]})),u=Math.min(kd(l),_o);return{type:"keyframes",ease:d=>l.next(u*d).value/i,duration:Rn(u)}}const nA=5;function G0(n,i,s){const l=Math.max(i-nA,0);return S0(s-n(l),i-l)}const $t={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Ey=.001;function iA({duration:n=$t.duration,bounce:i=$t.bounce,velocity:s=$t.velocity,mass:l=$t.mass}){let u,d,f=1-i;f=In($t.minDamping,$t.maxDamping,f),n=In($t.minDuration,$t.maxDuration,Rn(n)),f<1?(u=p=>{const g=p*f,v=g*n,b=g-s,T=Kf(p,f),C=Math.exp(-v);return Ey-b/T*C},d=p=>{const v=p*f*n,b=v*s+s,T=Math.pow(f,2)*Math.pow(p,2)*n,C=Math.exp(-v),M=Kf(Math.pow(p,2),f);return(-u(p)+Ey>0?-1:1)*((b-T)*C)/M}):(u=p=>{const g=Math.exp(-p*n),v=(p-s)*n+1;return-.001+g*v},d=p=>{const g=Math.exp(-p*n),v=(s-p)*(n*n);return g*v});const h=5/n,m=sA(u,d,h);if(n=Cn(n),isNaN(m))return{stiffness:$t.stiffness,damping:$t.damping,duration:n};{const p=Math.pow(m,2)*l;return{stiffness:p,damping:f*2*Math.sqrt(l*p),duration:n}}}const aA=12;function sA(n,i,s){let l=s;for(let u=1;u<aA;u++)l=l-n(l)/i(l);return l}function Kf(n,i){return n*Math.sqrt(1-i*i)}const rA=["duration","bounce"],lA=["stiffness","damping","mass"];function My(n,i){return i.some(s=>n[s]!==void 0)}function oA(n){let i=O({velocity:$t.velocity,stiffness:$t.stiffness,damping:$t.damping,mass:$t.mass,isResolvedFromDuration:!1},n);if(!My(n,lA)&&My(n,rA))if(n.visualDuration){const s=n.visualDuration,l=2*Math.PI/(s*1.2),u=l*l,d=2*In(.05,1,1-(n.bounce||0))*Math.sqrt(u);i=K(O({},i),{mass:$t.mass,stiffness:u,damping:d})}else{const s=iA(n);i=K(O(O({},i),s),{mass:$t.mass}),i.isResolvedFromDuration=!0}return i}function Vo(n=$t.visualDuration,i=$t.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:i}:n;let{restSpeed:l,restDelta:u}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:m,damping:p,mass:g,duration:v,velocity:b,isResolvedFromDuration:T}=oA(K(O({},s),{velocity:-Rn(s.velocity||0)})),C=b||0,M=p/(2*Math.sqrt(m*g)),E=f-d,N=Rn(Math.sqrt(m/g)),z=Math.abs(E)<5;l||(l=z?$t.restSpeed.granular:$t.restSpeed.default),u||(u=z?$t.restDelta.granular:$t.restDelta.default);let A;if(M<1){const j=Kf(N,M);A=I=>{const J=Math.exp(-M*N*I);return f-J*((C+M*N*E)/j*Math.sin(j*I)+E*Math.cos(j*I))}}else if(M===1)A=j=>f-Math.exp(-N*j)*(E+(C+N*E)*j);else{const j=N*Math.sqrt(M*M-1);A=I=>{const J=Math.exp(-M*N*I),Y=Math.min(j*I,300);return f-J*((C+M*N*E)*Math.sinh(Y)+j*E*Math.cosh(Y))/j}}const V={calculatedDuration:T&&v||null,next:j=>{const I=A(j);if(T)h.done=j>=v;else{let J=j===0?C:0;M<1&&(J=j===0?Cn(C):G0(A,j,I));const Y=Math.abs(J)<=l,W=Math.abs(f-I)<=u;h.done=Y&&W}return h.value=h.done?f:I,h},toString:()=>{const j=Math.min(kd(V),_o),I=P0(J=>V.next(j*J).value,j,30);return j+"ms "+I},toTransition:()=>{}};return V}Vo.applyToOptions=n=>{const i=eA(n,100,Vo);return n.ease=i.ease,n.duration=Cn(i.duration),n.type="keyframes",n};function Zf({keyframes:n,velocity:i=0,power:s=.8,timeConstant:l=325,bounceDamping:u=10,bounceStiffness:d=500,modifyTarget:f,min:h,max:m,restDelta:p=.5,restSpeed:g}){const v=n[0],b={done:!1,value:v},T=Y=>h!==void 0&&Y<h||m!==void 0&&Y>m,C=Y=>h===void 0?m:m===void 0||Math.abs(h-Y)<Math.abs(m-Y)?h:m;let M=s*i;const E=v+M,N=f===void 0?E:f(E);N!==E&&(M=N-v);const z=Y=>-M*Math.exp(-Y/l),A=Y=>N+z(Y),V=Y=>{const W=z(Y),ft=A(Y);b.done=Math.abs(W)<=p,b.value=b.done?N:ft};let j,I;const J=Y=>{T(b.value)&&(j=Y,I=Vo({keyframes:[b.value,C(b.value)],velocity:G0(A,Y,b.value),damping:u,stiffness:d,restDelta:p,restSpeed:g}))};return J(0),{calculatedDuration:null,next:Y=>{let W=!1;return!I&&j===void 0&&(W=!0,V(Y),J(Y)),j!==void 0&&Y>=j?I.next(Y-j):(!W&&V(Y),b)}}}function uA(n,i,s){const l=[],u=s||Wn.mix||H0,d=n.length-1;for(let f=0;f<d;f++){let h=u(n[f],n[f+1]);if(i){const m=Array.isArray(i)?i[f]||sn:i;h=Hr(m,h)}l.push(h)}return l}function cA(n,i,{clamp:s=!0,ease:l,mixer:u}={}){const d=n.length;if(Dd(d===i.length),d===1)return()=>i[0];if(d===2&&i[0]===i[1])return()=>i[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),i=[...i].reverse());const h=uA(i,l,u),m=h.length,p=g=>{if(f&&g<n[0])return i[0];let v=0;if(m>1)for(;v<n.length-2&&!(g<n[v+1]);v++);const b=Or(n[v],n[v+1],g);return h[v](b)};return s?g=>p(In(n[0],n[d-1],g)):p}function fA(n,i){const s=n[n.length-1];for(let l=1;l<=i;l++){const u=Or(0,i,l);n.push(Qt(s,1,u))}}function dA(n){const i=[0];return fA(i,n.length-1),i}function hA(n,i){return n.map(s=>s*i)}function mA(n,i){return n.map(()=>i||O0).splice(0,n.length-1)}function Mr({duration:n=300,keyframes:i,times:s,ease:l="easeInOut"}){const u=Ew(l)?l.map(by):by(l),d={done:!1,value:i[0]},f=hA(s&&s.length===i.length?s:dA(i),n),h=cA(f,i,{ease:Array.isArray(u)?u:mA(i,u)});return{calculatedDuration:n,next:m=>(d.value=h(m),d.done=m>=n,d)}}const pA=n=>n!==null;function Hd(n,{repeat:i,repeatType:s="loop"},l,u=1){const d=n.filter(pA),h=u<0||i&&s!=="loop"&&i%2===1?0:d.length-1;return!h||l===void 0?d[h]:l}const gA={decay:Zf,inertia:Zf,tween:Mr,keyframes:Mr,spring:Vo};function Y0(n){typeof n.type=="string"&&(n.type=gA[n.type])}class Pd{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(i=>{this.resolve=i})}notifyFinished(){this.resolve()}then(i,s){return this.finished.then(i,s)}}const yA=n=>n/100;class Gd extends Pd{constructor(i){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var l,u;const{motionValue:s}=this.options;s&&s.updatedAt!==_e.now()&&this.tick(_e.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(u=(l=this.options).onStop)==null||u.call(l))},this.options=i,this.initAnimation(),this.play(),i.autoplay===!1&&this.pause()}initAnimation(){const{options:i}=this;Y0(i);const{type:s=Mr,repeat:l=0,repeatDelay:u=0,repeatType:d,velocity:f=0}=i;let{keyframes:h}=i;const m=s||Mr;m!==Mr&&typeof h[0]!="number"&&(this.mixKeyframes=Hr(yA,H0(h[0],h[1])),h=[0,100]);const p=m(K(O({},i),{keyframes:h}));d==="mirror"&&(this.mirroredGenerator=m(K(O({},i),{keyframes:[...h].reverse(),velocity:-f}))),p.calculatedDuration===null&&(p.calculatedDuration=kd(p));const{calculatedDuration:g}=p;this.calculatedDuration=g,this.resolvedDuration=g+u,this.totalDuration=this.resolvedDuration*(l+1)-u,this.generator=p}updateTime(i){const s=Math.round(i-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(i,s=!1){const{generator:l,totalDuration:u,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:m}=this;if(this.startTime===null)return l.next(0);const{delay:p=0,keyframes:g,repeat:v,repeatType:b,repeatDelay:T,type:C,onUpdate:M,finalKeyframe:E}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,i):this.speed<0&&(this.startTime=Math.min(i-u/this.speed,this.startTime)),s?this.currentTime=i:this.updateTime(i);const N=this.currentTime-p*(this.playbackSpeed>=0?1:-1),z=this.playbackSpeed>=0?N<0:N>u;this.currentTime=Math.max(N,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let A=this.currentTime,V=l;if(v){const Y=Math.min(this.currentTime,u)/h;let W=Math.floor(Y),ft=Y%1;!ft&&Y>=1&&(ft=1),ft===1&&W--,W=Math.min(W,v+1),!!(W%2)&&(b==="reverse"?(ft=1-ft,T&&(ft-=T/h)):b==="mirror"&&(V=f)),A=In(0,1,ft)*h}const j=z?{done:!1,value:g[0]}:V.next(A);d&&(j.value=d(j.value));let{done:I}=j;!z&&m!==null&&(I=this.playbackSpeed>=0?this.currentTime>=u:this.currentTime<=0);const J=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&I);return J&&C!==Zf&&(j.value=Hd(g,this.options,E,this.speed)),M&&M(j.value),J&&this.finish(),j}then(i,s){return this.finished.then(i,s)}get duration(){return Rn(this.calculatedDuration)}get time(){return Rn(this.currentTime)}set time(i){var s;i=Cn(i),this.currentTime=i,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=i:this.driver&&(this.startTime=this.driver.now()-i/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(i){this.updateTime(_e.now());const s=this.playbackSpeed!==i;this.playbackSpeed=i,s&&(this.time=Rn(this.currentTime))}play(){var u,d;if(this.isStopped)return;const{driver:i=tA,startTime:s}=this.options;this.driver||(this.driver=i(f=>this.tick(f))),(d=(u=this.options).onPlay)==null||d.call(u);const l=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=l):this.holdTime!==null?this.startTime=l-this.holdTime:this.startTime||(this.startTime=s!=null?s:l),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(_e.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var i,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(i=this.options).onComplete)==null||s.call(i)}cancel(){var i,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(i=this.options).onCancel)==null||s.call(i)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(i){return this.startTime=0,this.tick(i,!0)}attachTimeline(i){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),i.observe(this)}}function vA(n){var i;for(let s=1;s<n.length;s++)(i=n[s])!=null||(n[s]=n[s-1])}const ra=n=>n*180/Math.PI,Qf=n=>{const i=ra(Math.atan2(n[1],n[0]));return Ff(i)},bA={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Qf,rotateZ:Qf,skewX:n=>ra(Math.atan(n[1])),skewY:n=>ra(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},Ff=n=>(n=n%360,n<0&&(n+=360),n),Cy=Qf,Ry=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),Dy=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),xA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ry,scaleY:Dy,scale:n=>(Ry(n)+Dy(n))/2,rotateX:n=>Ff(ra(Math.atan2(n[6],n[5]))),rotateY:n=>Ff(ra(Math.atan2(-n[2],n[0]))),rotateZ:Cy,rotate:Cy,skewX:n=>ra(Math.atan(n[4])),skewY:n=>ra(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function If(n){return n.includes("scale")?1:0}function Wf(n,i){if(!n||n==="none")return If(i);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let l,u;if(s)l=xA,u=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);l=bA,u=h}if(!u)return If(i);const d=l[i],f=u[1].split(",").map(TA);return typeof d=="function"?d(f):f[d]}const SA=(n,i)=>{const{transform:s="none"}=getComputedStyle(n);return Wf(s,i)};function TA(n){return parseFloat(n.trim())}const ps=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gs=new Set(ps),Oy=n=>n===ms||n===yt,wA=new Set(["x","y","z"]),AA=ps.filter(n=>!wA.has(n));function EA(n){const i=[];return AA.forEach(s=>{const l=n.getValue(s);l!==void 0&&(i.push([s,l.get()]),l.set(s.startsWith("scale")?1:0))}),i}const la={width:({x:n},{paddingLeft:i="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(i)-parseFloat(s),height:({y:n},{paddingTop:i="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(i)-parseFloat(s),top:(n,{top:i})=>parseFloat(i),left:(n,{left:i})=>parseFloat(i),bottom:({y:n},{top:i})=>parseFloat(i)+(n.max-n.min),right:({x:n},{left:i})=>parseFloat(i)+(n.max-n.min),x:(n,{transform:i})=>Wf(i,"x"),y:(n,{transform:i})=>Wf(i,"y")};la.translateX=la.x;la.translateY=la.y;const oa=new Set;let $f=!1,Jf=!1,td=!1;function q0(){if(Jf){const n=Array.from(oa).filter(l=>l.needsMeasurement),i=new Set(n.map(l=>l.element)),s=new Map;i.forEach(l=>{const u=EA(l);u.length&&(s.set(l,u),l.render())}),n.forEach(l=>l.measureInitialState()),i.forEach(l=>{l.render();const u=s.get(l);u&&u.forEach(([d,f])=>{var h;(h=l.getValue(d))==null||h.set(f)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}Jf=!1,$f=!1,oa.forEach(n=>n.complete(td)),oa.clear()}function X0(){oa.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Jf=!0)})}function MA(){td=!0,X0(),q0(),td=!1}class Yd{constructor(i,s,l,u,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...i],this.onComplete=s,this.name=l,this.motionValue=u,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(oa.add(this),$f||($f=!0,Ft.read(X0),Ft.resolveKeyframes(q0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:i,name:s,element:l,motionValue:u}=this;if(i[0]===null){const d=u==null?void 0:u.get(),f=i[i.length-1];if(d!==void 0)i[0]=d;else if(l&&s){const h=l.readValue(s,f);h!=null&&(i[0]=h)}i[0]===void 0&&(i[0]=f),u&&d===void 0&&u.set(i[0])}vA(i)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(i=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,i),oa.delete(this)}cancel(){this.state==="scheduled"&&(oa.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const CA=n=>n.startsWith("--");function RA(n,i,s){CA(i)?n.style.setProperty(i,s):n.style[i]=s}const DA=Od(()=>window.ScrollTimeline!==void 0),OA={};function NA(n,i){const s=Od(n);return()=>{var l;return(l=OA[i])!=null?l:s()}}const K0=NA(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(n){return!1}return!0},"linearEasing"),wr=([n,i,s,l])=>`cubic-bezier(${n}, ${i}, ${s}, ${l})`,Ny={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wr([0,.65,.55,1]),circOut:wr([.55,0,1,.45]),backIn:wr([.31,.01,.66,-.59]),backOut:wr([.33,1.53,.69,.99])};function Z0(n,i){if(n)return typeof n=="function"?K0()?P0(n,i):"ease-out":N0(n)?wr(n):Array.isArray(n)?n.map(s=>Z0(s,i)||Ny.easeOut):Ny[n]}function jA(n,i,s,{delay:l=0,duration:u=300,repeat:d=0,repeatType:f="loop",ease:h="easeOut",times:m}={},p=void 0){const g={[i]:s};m&&(g.offset=m);const v=Z0(h,u);Array.isArray(v)&&(g.easing=v);const b={delay:l,duration:u,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return p&&(b.pseudoElement=p),n.animate(g,b)}function Q0(n){return typeof n=="function"&&"applyToOptions"in n}function _A(s){var l=s,{type:n}=l,i=et(l,["type"]);var u,d;return Q0(n)&&K0()?n.applyToOptions(i):((u=i.duration)!=null||(i.duration=300),(d=i.ease)!=null||(i.ease="easeOut"),i)}class VA extends Pd{constructor(i){if(super(),this.finishedTime=null,this.isStopped=!1,!i)return;const{element:s,name:l,keyframes:u,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:h,onComplete:m}=i;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=i,Dd(typeof i.type!="string");const p=_A(i);this.animation=jA(s,l,u,p,d),p.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const g=Hd(u,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(g):RA(s,l,g),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var i,s;(s=(i=this.animation).finish)==null||s.call(i)}cancel(){try{this.animation.cancel()}catch(i){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:i}=this;i==="idle"||i==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var i,s;this.isPseudoElement||(s=(i=this.animation).commitStyles)==null||s.call(i)}get duration(){var s,l;const i=((l=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:l.call(s).duration)||0;return Rn(Number(i))}get time(){return Rn(Number(this.animation.currentTime)||0)}set time(i){this.finishedTime=null,this.animation.currentTime=Cn(i)}get speed(){return this.animation.playbackRate}set speed(i){i<0&&(this.finishedTime=null),this.animation.playbackRate=i}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(i){this.animation.startTime=i}attachTimeline({timeline:i,observe:s}){var l;return this.allowFlatten&&((l=this.animation.effect)==null||l.updateTiming({easing:"linear"})),this.animation.onfinish=null,i&&DA()?(this.animation.timeline=i,sn):s(this)}}const F0={anticipate:C0,backInOut:M0,circInOut:D0};function zA(n){return n in F0}function LA(n){typeof n.ease=="string"&&zA(n.ease)&&(n.ease=F0[n.ease])}const jy=10;class BA extends VA{constructor(i){LA(i),Y0(i),super(i),i.startTime&&(this.startTime=i.startTime),this.options=i}updateMotionValue(i){var g;const p=this.options,{motionValue:s,onUpdate:l,onComplete:u,element:d}=p,f=et(p,["motionValue","onUpdate","onComplete","element"]);if(!s)return;if(i!==void 0){s.set(i);return}const h=new Gd(K(O({},f),{autoplay:!1})),m=Cn((g=this.finishedTime)!=null?g:this.time);s.setWithVelocity(h.sample(m-jy).value,h.sample(m).value,jy),h.stop()}}const _y=(n,i)=>i==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Di.test(n)||n==="0")&&!n.startsWith("url("));function UA(n){const i=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==i)return!0}function kA(n,i,s,l){const u=n[0];if(u===null)return!1;if(i==="display"||i==="visibility")return!0;const d=n[n.length-1],f=_y(u,i),h=_y(d,i);return!f||!h?!1:UA(n)||(s==="spring"||Q0(s))&&l}function I0(n){return b0(n)&&"offsetHeight"in n}const HA=new Set(["opacity","clipPath","filter","transform"]),PA=Od(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function GA(n){var p;const{motionValue:i,name:s,repeatDelay:l,repeatType:u,damping:d,type:f}=n;if(!I0((p=i==null?void 0:i.owner)==null?void 0:p.current))return!1;const{onUpdate:h,transformTemplate:m}=i.owner.getProps();return PA()&&s&&HA.has(s)&&(s!=="transform"||!m)&&!h&&!l&&u!=="mirror"&&d!==0&&f!=="inertia"}const YA=40;class qA extends Pd{constructor(b){var T=b,{autoplay:i=!0,delay:s=0,type:l="keyframes",repeat:u=0,repeatDelay:d=0,repeatType:f="loop",keyframes:h,name:m,motionValue:p,element:g}=T,v=et(T,["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"]);var E;super(),this.stop=()=>{var N,z;this._animation&&(this._animation.stop(),(N=this.stopTimeline)==null||N.call(this)),(z=this.keyframeResolver)==null||z.cancel()},this.createdAt=_e.now();const C=O({autoplay:i,delay:s,type:l,repeat:u,repeatDelay:d,repeatType:f,name:m,motionValue:p,element:g},v),M=(g==null?void 0:g.KeyframeResolver)||Yd;this.keyframeResolver=new M(h,(N,z,A)=>this.onKeyframesResolved(N,z,C,!A),m,p,g),(E=this.keyframeResolver)==null||E.scheduleResolve()}onKeyframesResolved(i,s,l,u){this.keyframeResolver=void 0;const{name:d,type:f,velocity:h,delay:m,isHandoff:p,onUpdate:g}=l;this.resolvedAt=_e.now(),kA(i,d,f,h)||((Wn.instantAnimations||!m)&&(g==null||g(Hd(i,l,s))),i[0]=i[i.length-1],l.duration=0,l.repeat=0);const v=u?this.resolvedAt?this.resolvedAt-this.createdAt>YA?this.resolvedAt:this.createdAt:this.createdAt:void 0,b=K(O({startTime:v,finalKeyframe:s},l),{keyframes:i}),T=!p&&GA(b)?new BA(K(O({},b),{element:b.motionValue.owner.current})):new Gd(b);T.finished.then(()=>this.notifyFinished()).catch(sn),this.pendingTimeline&&(this.stopTimeline=T.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=T}get finished(){return this._animation?this.animation.finished:this._finished}then(i,s){return this.finished.finally(i).then(()=>{})}get animation(){var i;return this._animation||((i=this.keyframeResolver)==null||i.resume(),MA()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(i){this.animation.time=i}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(i){this.animation.speed=i}get startTime(){return this.animation.startTime}attachTimeline(i){return this._animation?this.stopTimeline=this.animation.attachTimeline(i):this.pendingTimeline=i,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var i;this._animation&&this.animation.cancel(),(i=this.keyframeResolver)==null||i.cancel()}}const XA=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function KA(n){const i=XA.exec(n);if(!i)return[,];const[,s,l,u]=i;return[`--${s!=null?s:l}`,u]}function W0(n,i,s=1){const[l,u]=KA(n);if(!l)return;const d=window.getComputedStyle(i).getPropertyValue(l);if(d){const f=d.trim();return v0(f)?parseFloat(f):f}return zd(u)?W0(u,i,s+1):u}function qd(n,i){var s,l;return(l=(s=n==null?void 0:n[i])!=null?s:n==null?void 0:n.default)!=null?l:n}const $0=new Set(["width","height","top","left","right","bottom",...ps]),ZA={test:n=>n==="auto",parse:n=>n},J0=n=>i=>i.test(n),tb=[ms,yt,Dn,Mi,Uw,Bw,ZA],Vy=n=>tb.find(J0(n));function QA(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||x0(n):!0}const FA=new Set(["brightness","contrast","saturate","opacity"]);function IA(n){const[i,s]=n.slice(0,-1).split("(");if(i==="drop-shadow")return n;const[l]=s.match(Ld)||[];if(!l)return n;const u=s.replace(l,"");let d=FA.has(i)?1:0;return l!==s&&(d*=100),i+"("+d+u+")"}const WA=/\b([a-z-]*)\(.*?\)/gu,ed=K(O({},Di),{getAnimatableNone:n=>{const i=n.match(WA);return i?i.map(IA).join(" "):n}}),zy=K(O({},ms),{transform:Math.round}),$A={rotate:Mi,rotateX:Mi,rotateY:Mi,rotateZ:Mi,scale:ho,scaleX:ho,scaleY:ho,scaleZ:ho,skew:Mi,skewX:Mi,skewY:Mi,distance:yt,translateX:yt,translateY:yt,translateZ:yt,x:yt,y:yt,z:yt,perspective:yt,transformPerspective:yt,opacity:Nr,originX:Sy,originY:Sy,originZ:yt},Xd=K(O({borderWidth:yt,borderTopWidth:yt,borderRightWidth:yt,borderBottomWidth:yt,borderLeftWidth:yt,borderRadius:yt,radius:yt,borderTopLeftRadius:yt,borderTopRightRadius:yt,borderBottomRightRadius:yt,borderBottomLeftRadius:yt,width:yt,maxWidth:yt,height:yt,maxHeight:yt,top:yt,right:yt,bottom:yt,left:yt,padding:yt,paddingTop:yt,paddingRight:yt,paddingBottom:yt,paddingLeft:yt,margin:yt,marginTop:yt,marginRight:yt,marginBottom:yt,marginLeft:yt,backgroundPositionX:yt,backgroundPositionY:yt},$A),{zIndex:zy,fillOpacity:Nr,strokeOpacity:Nr,numOctaves:zy}),JA=K(O({},Xd),{color:Se,backgroundColor:Se,outlineColor:Se,fill:Se,stroke:Se,borderColor:Se,borderTopColor:Se,borderRightColor:Se,borderBottomColor:Se,borderLeftColor:Se,filter:ed,WebkitFilter:ed}),eb=n=>JA[n];function nb(n,i){let s=eb(n);return s!==ed&&(s=Di),s.getAnimatableNone?s.getAnimatableNone(i):void 0}const tE=new Set(["auto","none","0"]);function eE(n,i,s){let l=0,u;for(;l<n.length&&!u;){const d=n[l];typeof d=="string"&&!tE.has(d)&&jr(d).values.length&&(u=n[l]),l++}if(u&&s)for(const d of i)n[d]=nb(s,u)}class nE extends Yd{constructor(i,s,l,u,d){super(i,s,l,u,d,!0)}readKeyframes(){const{unresolvedKeyframes:i,element:s,name:l}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<i.length;m++){let p=i[m];if(typeof p=="string"&&(p=p.trim(),zd(p))){const g=W0(p,s.current);g!==void 0&&(i[m]=g),m===i.length-1&&(this.finalKeyframe=p)}}if(this.resolveNoneKeyframes(),!$0.has(l)||i.length!==2)return;const[u,d]=i,f=Vy(u),h=Vy(d);if(f!==h)if(Oy(f)&&Oy(h))for(let m=0;m<i.length;m++){const p=i[m];typeof p=="string"&&(i[m]=parseFloat(p))}else la[l]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:i,name:s}=this,l=[];for(let u=0;u<i.length;u++)(i[u]===null||QA(i[u]))&&l.push(u);l.length&&eE(i,l,s)}measureInitialState(){const{element:i,unresolvedKeyframes:s,name:l}=this;if(!i||!i.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=la[l](i.measureViewportBox(),window.getComputedStyle(i.current)),s[0]=this.measuredOrigin;const u=s[s.length-1];u!==void 0&&i.getValue(l,u).jump(u,!1)}measureEndState(){var h;const{element:i,name:s,unresolvedKeyframes:l}=this;if(!i||!i.current)return;const u=i.getValue(s);u&&u.jump(this.measuredOrigin,!1);const d=l.length-1,f=l[d];l[d]=la[s](i.measureViewportBox(),window.getComputedStyle(i.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([m,p])=>{i.getValue(m).set(p)}),this.resolveNoneKeyframes()}}function iE(n,i,s){var l;if(n instanceof EventTarget)return[n];if(typeof n=="string"){let u=document;const d=(l=s==null?void 0:s[n])!=null?l:u.querySelectorAll(n);return d?Array.from(d):[]}return Array.from(n)}const ib=(n,i)=>i&&typeof n=="number"?i.transform(n):n,Ly=30,aE=n=>!isNaN(parseFloat(n));class sE{constructor(i,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,u=!0)=>{var f,h;const d=_e.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();u&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(i),this.owner=s.owner}setCurrent(i){this.current=i,this.updatedAt=_e.now(),this.canTrackVelocity===null&&i!==void 0&&(this.canTrackVelocity=aE(this.current))}setPrevFrameValue(i=this.current){this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt}onChange(i){return this.on("change",i)}on(i,s){this.events[i]||(this.events[i]=new Nd);const l=this.events[i].add(s);return i==="change"?()=>{l(),Ft.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const i in this.events)this.events[i].clear()}attach(i,s){this.passiveEffect=i,this.stopPassiveEffect=s}set(i,s=!0){!s||!this.passiveEffect?this.updateAndNotify(i,s):this.passiveEffect(i,this.updateAndNotify)}setWithVelocity(i,s,l){this.set(s),this.prev=void 0,this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt-l}jump(i,s=!0){this.updateAndNotify(i),this.prev=i,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var i;(i=this.events.change)==null||i.notify(this.current)}addDependent(i){this.dependents||(this.dependents=new Set),this.dependents.add(i)}removeDependent(i){this.dependents&&this.dependents.delete(i)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const i=_e.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||i-this.updatedAt>Ly)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Ly);return S0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(i){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=i(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var i,s;(i=this.dependents)==null||i.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function fs(n,i){return new sE(n,i)}const{schedule:Kd}=j0(queueMicrotask,!1),fn={x:!1,y:!1};function ab(){return fn.x||fn.y}function rE(n){return n==="x"||n==="y"?fn[n]?null:(fn[n]=!0,()=>{fn[n]=!1}):fn.x||fn.y?null:(fn.x=fn.y=!0,()=>{fn.x=fn.y=!1})}function sb(n,i){const s=iE(n),l=new AbortController,u=K(O({passive:!0},i),{signal:l.signal});return[s,u,()=>l.abort()]}function By(n){return!(n.pointerType==="touch"||ab())}function lE(n,i,s={}){const[l,u,d]=sb(n,s),f=h=>{if(!By(h))return;const{target:m}=h,p=i(m,h);if(typeof p!="function"||!m)return;const g=v=>{By(v)&&(p(v),m.removeEventListener("pointerleave",g))};m.addEventListener("pointerleave",g,u)};return l.forEach(h=>{h.addEventListener("pointerenter",f,u)}),d}const rb=(n,i)=>i?n===i?!0:rb(n,i.parentElement):!1,Zd=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,oE=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function uE(n){return oE.has(n.tagName)||n.tabIndex!==-1}const Mo=new WeakSet;function Uy(n){return i=>{i.key==="Enter"&&n(i)}}function wf(n,i){n.dispatchEvent(new PointerEvent("pointer"+i,{isPrimary:!0,bubbles:!0}))}const cE=(n,i)=>{const s=n.currentTarget;if(!s)return;const l=Uy(()=>{if(Mo.has(s))return;wf(s,"down");const u=Uy(()=>{wf(s,"up")}),d=()=>wf(s,"cancel");s.addEventListener("keyup",u,i),s.addEventListener("blur",d,i)});s.addEventListener("keydown",l,i),s.addEventListener("blur",()=>s.removeEventListener("keydown",l),i)};function ky(n){return Zd(n)&&!ab()}function fE(n,i,s={}){const[l,u,d]=sb(n,s),f=h=>{const m=h.currentTarget;if(!ky(h))return;Mo.add(m);const p=i(m,h),g=(T,C)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",b),Mo.has(m)&&Mo.delete(m),ky(T)&&typeof p=="function"&&p(T,{success:C})},v=T=>{g(T,m===window||m===document||s.useGlobalTarget||rb(m,T.target))},b=T=>{g(T,!1)};window.addEventListener("pointerup",v,u),window.addEventListener("pointercancel",b,u)};return l.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",f,u),I0(h)&&(h.addEventListener("focus",p=>cE(p,u)),!uE(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function lb(n){return b0(n)&&"ownerSVGElement"in n}function dE(n){return lb(n)&&n.tagName==="svg"}const Te=n=>!!(n&&n.getVelocity),hE=[...tb,Se,Di],mE=n=>hE.find(J0(n)),ob=w.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function pE(n=!0){const i=w.useContext(Md);if(i===null)return[!0,null];const{isPresent:s,onExitComplete:l,register:u}=i,d=w.useId();w.useEffect(()=>{if(n)return u(d)},[n]);const f=w.useCallback(()=>n&&l&&l(d),[d,l,n]);return!s&&l?[!1,f]:[!0]}const ub=w.createContext({strict:!1}),Hy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ds={};for(const n in Hy)ds[n]={isEnabled:i=>Hy[n].some(s=>!!i[s])};function gE(n){for(const i in n)ds[i]=O(O({},ds[i]),n[i])}const yE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function zo(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||yE.has(n)}let cb=n=>!zo(n);function vE(n){n&&(cb=i=>i.startsWith("on")?!zo(i):n(i))}try{vE(require("@emotion/is-prop-valid").default)}catch(n){}function bE(n,i,s){const l={};for(const u in n)u==="values"&&typeof n.values=="object"||(cb(u)||s===!0&&zo(u)||!i&&!zo(u)||n.draggable&&u.startsWith("onDrag"))&&(l[u]=n[u]);return l}function xE(n){if(typeof Proxy=="undefined")return n;const i=new Map,s=(...l)=>n(...l);return new Proxy(s,{get:(l,u)=>u==="create"?n:(i.has(u)||i.set(u,n(u)),i.get(u))})}const Yo=w.createContext({});function qo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function _r(n){return typeof n=="string"||Array.isArray(n)}const Qd=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Fd=["initial",...Qd];function Xo(n){return qo(n.animate)||Fd.some(i=>_r(n[i]))}function fb(n){return!!(Xo(n)||n.variants)}function SE(n,i){if(Xo(n)){const{initial:s,animate:l}=n;return{initial:s===!1||_r(s)?s:void 0,animate:_r(l)?l:void 0}}return n.inherit!==!1?i:{}}function TE(n){const{initial:i,animate:s}=SE(n,w.useContext(Yo));return w.useMemo(()=>({initial:i,animate:s}),[Py(i),Py(s)])}function Py(n){return Array.isArray(n)?n.join(" "):n}const wE=Symbol.for("motionComponentSymbol");function ss(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function AE(n,i,s){return w.useCallback(l=>{l&&n.onMount&&n.onMount(l),i&&(l?i.mount(l):i.unmount()),s&&(typeof s=="function"?s(l):ss(s)&&(s.current=l))},[i])}const Id=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),EE="framerAppearId",db="data-"+Id(EE),hb=w.createContext({});function ME(n,i,s,l,u){var M,E;const{visualElement:d}=w.useContext(Yo),f=w.useContext(ub),h=w.useContext(Md),m=w.useContext(ob).reducedMotion,p=w.useRef(null);l=l||f.renderer,!p.current&&l&&(p.current=l(n,{visualState:i,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:m}));const g=p.current,v=w.useContext(hb);g&&!g.projection&&u&&(g.type==="html"||g.type==="svg")&&CE(p.current,s,u,v);const b=w.useRef(!1);w.useInsertionEffect(()=>{g&&b.current&&g.update(s,h)});const T=s[db],C=w.useRef(!!T&&!((M=window.MotionHandoffIsComplete)!=null&&M.call(window,T))&&((E=window.MotionHasOptimisedAnimation)==null?void 0:E.call(window,T)));return vw(()=>{g&&(b.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),Kd.render(g.render),C.current&&g.animationState&&g.animationState.animateChanges())}),w.useEffect(()=>{g&&(!C.current&&g.animationState&&g.animationState.animateChanges(),C.current&&(queueMicrotask(()=>{var N;(N=window.MotionHandoffMarkAsComplete)==null||N.call(window,T)}),C.current=!1))}),g}function CE(n,i,s,l){const{layoutId:u,layout:d,drag:f,dragConstraints:h,layoutScroll:m,layoutRoot:p,layoutCrossfade:g}=i;n.projection=new s(n.latestValues,i["data-framer-portal-id"]?void 0:mb(n.parent)),n.projection.setOptions({layoutId:u,layout:d,alwaysMeasureLayout:!!f||h&&ss(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,crossfade:g,layoutScroll:m,layoutRoot:p})}function mb(n){if(n)return n.options.allowProjection!==!1?n.projection:mb(n.parent)}function RE({preloadedFeatures:n,createVisualElement:i,useRender:s,useVisualState:l,Component:u}){var h,m;n&&gE(n);function d(p,g){let v;const b=K(O(O({},w.useContext(ob)),p),{layoutId:DE(p)}),{isStatic:T}=b,C=TE(p),M=l(p,T);if(!T&&Ed){OE();const E=NE(b);v=E.MeasureLayout,C.visualElement=ME(u,M,b,i,E.ProjectionNode)}return S.jsxs(Yo.Provider,{value:C,children:[v&&C.visualElement?S.jsx(v,O({visualElement:C.visualElement},b)):null,s(u,p,AE(M,C.visualElement,g),M,T,C.visualElement)]})}d.displayName=`motion.${typeof u=="string"?u:`create(${(m=(h=u.displayName)!=null?h:u.name)!=null?m:""})`}`;const f=w.forwardRef(d);return f[wE]=u,f}function DE({layoutId:n}){const i=w.useContext(y0).id;return i&&n!==void 0?i+"-"+n:n}function OE(n,i){w.useContext(ub).strict}function NE(n){const{drag:i,layout:s}=ds;if(!i&&!s)return{};const l=O(O({},i),s);return{MeasureLayout:i!=null&&i.isEnabled(n)||s!=null&&s.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const Vr={};function jE(n){for(const i in n)Vr[i]=n[i],Vd(i)&&(Vr[i].isCSSVariable=!0)}function pb(n,{layout:i,layoutId:s}){return gs.has(n)||n.startsWith("origin")||(i||s!==void 0)&&(!!Vr[n]||n==="opacity")}const _E={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},VE=ps.length;function zE(n,i,s){let l="",u=!0;for(let d=0;d<VE;d++){const f=ps[d],h=n[f];if(h===void 0)continue;let m=!0;if(typeof h=="number"?m=h===(f.startsWith("scale")?1:0):m=parseFloat(h)===0,!m||s){const p=ib(h,Xd[f]);if(!m){u=!1;const g=_E[f]||f;l+=`${g}(${p}) `}s&&(i[f]=p)}}return l=l.trim(),s?l=s(i,u?"":l):u&&(l="none"),l}function Wd(n,i,s){const{style:l,vars:u,transformOrigin:d}=n;let f=!1,h=!1;for(const m in i){const p=i[m];if(gs.has(m)){f=!0;continue}else if(Vd(m)){u[m]=p;continue}else{const g=ib(p,Xd[m]);m.startsWith("origin")?(h=!0,d[m]=g):l[m]=g}}if(i.transform||(f||s?l.transform=zE(i,n.transform,s):l.transform&&(l.transform="none")),h){const{originX:m="50%",originY:p="50%",originZ:g=0}=d;l.transformOrigin=`${m} ${p} ${g}`}}const $d=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function gb(n,i,s){for(const l in i)!Te(i[l])&&!pb(l,s)&&(n[l]=i[l])}function LE({transformTemplate:n},i){return w.useMemo(()=>{const s=$d();return Wd(s,i,n),Object.assign({},s.vars,s.style)},[i])}function BE(n,i){const s=n.style||{},l={};return gb(l,s,n),Object.assign(l,LE(n,i)),l}function UE(n,i){const s={},l=BE(n,i);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=l,s}const kE={offset:"stroke-dashoffset",array:"stroke-dasharray"},HE={offset:"strokeDashoffset",array:"strokeDasharray"};function PE(n,i,s=1,l=0,u=!0){n.pathLength=1;const d=u?kE:HE;n[d.offset]=yt.transform(-l);const f=yt.transform(i),h=yt.transform(s);n[d.array]=`${f} ${h}`}function yb(n,v,m,p,g){var b=v,{attrX:i,attrY:s,attrScale:l,pathLength:u,pathSpacing:d=1,pathOffset:f=0}=b,h=et(b,["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"]);var M,E;if(Wd(n,h,p),m){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:T,style:C}=n;T.transform&&(C.transform=T.transform,delete T.transform),(C.transform||T.transformOrigin)&&(C.transformOrigin=(M=T.transformOrigin)!=null?M:"50% 50%",delete T.transformOrigin),C.transform&&(C.transformBox=(E=g==null?void 0:g.transformBox)!=null?E:"fill-box",delete T.transformBox),i!==void 0&&(T.x=i),s!==void 0&&(T.y=s),l!==void 0&&(T.scale=l),u!==void 0&&PE(T,u,d,f,!1)}const vb=()=>K(O({},$d()),{attrs:{}}),bb=n=>typeof n=="string"&&n.toLowerCase()==="svg";function GE(n,i,s,l){const u=w.useMemo(()=>{const d=vb();return yb(d,i,bb(l),n.transformTemplate,n.style),K(O({},d.attrs),{style:O({},d.style)})},[i]);if(n.style){const d={};gb(d,n.style,n),u.style=O(O({},d),u.style)}return u}const YE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Jd(n){return typeof n!="string"||n.includes("-")?!1:!!(YE.indexOf(n)>-1||/[A-Z]/u.test(n))}function qE(n=!1){return(s,l,u,{latestValues:d},f)=>{const m=(Jd(s)?GE:UE)(l,d,f,s),p=bE(l,typeof s=="string",n),g=s!==w.Fragment?K(O(O({},p),m),{ref:u}):{},{children:v}=l,b=w.useMemo(()=>Te(v)?v.get():v,[v]);return w.createElement(s,K(O({},g),{children:b}))}}function Gy(n){const i=[{},{}];return n==null||n.values.forEach((s,l)=>{i[0][l]=s.get(),i[1][l]=s.getVelocity()}),i}function th(n,i,s,l){if(typeof i=="function"){const[u,d]=Gy(l);i=i(s!==void 0?s:n.custom,u,d)}if(typeof i=="string"&&(i=n.variants&&n.variants[i]),typeof i=="function"){const[u,d]=Gy(l);i=i(s!==void 0?s:n.custom,u,d)}return i}function Co(n){return Te(n)?n.get():n}function XE({scrapeMotionValuesFromProps:n,createRenderState:i},s,l,u){return{latestValues:KE(s,l,u,n),renderState:i()}}const xb=n=>(i,s)=>{const l=w.useContext(Yo),u=w.useContext(Md),d=()=>XE(n,i,l,u);return s?d():yw(d)};function KE(n,i,s,l){const u={},d=l(n,{});for(const T in d)u[T]=Co(d[T]);let{initial:f,animate:h}=n;const m=Xo(n),p=fb(n);i&&p&&!m&&n.inherit!==!1&&(f===void 0&&(f=i.initial),h===void 0&&(h=i.animate));let g=s?s.initial===!1:!1;g=g||f===!1;const v=g?h:f;if(v&&typeof v!="boolean"&&!qo(v)){const T=Array.isArray(v)?v:[v];for(let C=0;C<T.length;C++){const M=th(n,T[C]);if(M){const b=M,{transitionEnd:E,transition:N}=b,z=et(b,["transitionEnd","transition"]);for(const A in z){let V=z[A];if(Array.isArray(V)){const j=g?V.length-1:0;V=V[j]}V!==null&&(u[A]=V)}for(const A in E)u[A]=E[A]}}}return u}function eh(n,i,s){var d;const{style:l}=n,u={};for(const f in l)(Te(l[f])||i.style&&Te(i.style[f])||pb(f,n)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(u[f]=l[f]);return u}const ZE={useVisualState:xb({scrapeMotionValuesFromProps:eh,createRenderState:$d})};function Sb(n,i,s){const l=eh(n,i,s);for(const u in n)if(Te(n[u])||Te(i[u])){const d=ps.indexOf(u)!==-1?"attr"+u.charAt(0).toUpperCase()+u.substring(1):u;l[d]=n[u]}return l}const QE={useVisualState:xb({scrapeMotionValuesFromProps:Sb,createRenderState:vb})};function FE(n,i){return function(l,{forwardMotionProps:u}={forwardMotionProps:!1}){const d=Jd(l)?QE:ZE,f=K(O({},d),{preloadedFeatures:n,useRender:qE(u),createVisualElement:i,Component:l});return RE(f)}}function zr(n,i,s){const l=n.getProps();return th(l,i,s!==void 0?s:l.custom,n)}const nd=n=>Array.isArray(n);function IE(n,i,s){n.hasValue(i)?n.getValue(i).set(s):n.addValue(i,fs(s))}function WE(n){return nd(n)?n[n.length-1]||0:n}function $E(n,i){let f=zr(n,i)||{},{transitionEnd:l={},transition:u={}}=f,d=et(f,["transitionEnd","transition"]);d=O(O({},d),l);for(const h in d){const m=WE(d[h]);IE(n,h,m)}}function JE(n){return!!(Te(n)&&n.add)}function id(n,i){const s=n.getValue("willChange");if(JE(s))return s.add(i);if(!s&&Wn.WillChange){const l=new Wn.WillChange("auto");n.addValue("willChange",l),l.add(i)}}function Tb(n){return n.props[db]}const t2=n=>n!==null;function e2(n,{repeat:i,repeatType:s="loop"},l){const u=n.filter(t2),d=i&&s!=="loop"&&i%2===1?0:u.length-1;return u[d]}const n2={type:"spring",stiffness:500,damping:25,restSpeed:10},i2=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),a2={type:"keyframes",duration:.8},s2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},r2=(n,{keyframes:i})=>i.length>2?a2:gs.has(n)?n.startsWith("scale")?i2(i[1]):n2:s2;function l2(v){var b=v,{when:n,delay:i,delayChildren:s,staggerChildren:l,staggerDirection:u,repeat:d,repeatType:f,repeatDelay:h,from:m,elapsed:p}=b,g=et(b,["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"]);return!!Object.keys(g).length}const nh=(n,i,s,l={},u,d)=>f=>{const h=qd(l,n)||{},m=h.delay||l.delay||0;let{elapsed:p=0}=l;p=p-Cn(m);const g=K(O({keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:i.getVelocity()},h),{delay:-p,onUpdate:b=>{i.set(b),h.onUpdate&&h.onUpdate(b)},onComplete:()=>{f(),h.onComplete&&h.onComplete()},name:n,motionValue:i,element:d?void 0:u});l2(h)||Object.assign(g,r2(n,g)),g.duration&&(g.duration=Cn(g.duration)),g.repeatDelay&&(g.repeatDelay=Cn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let v=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(v=!0)),(Wn.instantAnimations||Wn.skipAnimations)&&(v=!0,g.duration=0,g.delay=0),g.allowFlatten=!h.type&&!h.ease,v&&!d&&i.get()!==void 0){const b=e2(g.keyframes,h);if(b!==void 0){Ft.update(()=>{g.onUpdate(b),g.onComplete()});return}}return h.isSync?new Gd(g):new qA(g)};function o2({protectedKeys:n,needsAnimating:i},s){const l=n.hasOwnProperty(s)&&i[s]!==!0;return i[s]=!1,l}function wb(n,i,{delay:s=0,transitionOverride:l,type:u}={}){var v;let g=i,{transition:d=n.getDefaultTransition(),transitionEnd:f}=g,h=et(g,["transition","transitionEnd"]);l&&(d=l);const m=[],p=u&&n.animationState&&n.animationState.getState()[u];for(const b in h){const T=n.getValue(b,(v=n.latestValues[b])!=null?v:null),C=h[b];if(C===void 0||p&&o2(p,b))continue;const M=O({delay:s},qd(d||{},b)),E=T.get();if(E!==void 0&&!T.isAnimating&&!Array.isArray(C)&&C===E&&!M.velocity)continue;let N=!1;if(window.MotionHandoffAnimation){const A=Tb(n);if(A){const V=window.MotionHandoffAnimation(A,b,Ft);V!==null&&(M.startTime=V,N=!0)}}id(n,b),T.start(nh(b,T,C,n.shouldReduceMotion&&$0.has(b)?{type:!1}:M,n,N));const z=T.animation;z&&m.push(z)}return f&&Promise.all(m).then(()=>{Ft.update(()=>{f&&$E(n,f)})}),m}function ad(n,i,s={}){var m;const l=zr(n,i,s.type==="exit"?(m=n.presenceContext)==null?void 0:m.custom:void 0);let{transition:u=n.getDefaultTransition()||{}}=l||{};s.transitionOverride&&(u=s.transitionOverride);const d=l?()=>Promise.all(wb(n,l,s)):()=>Promise.resolve(),f=n.variantChildren&&n.variantChildren.size?(p=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:b}=u;return u2(n,i,g+p,v,b,s)}:()=>Promise.resolve(),{when:h}=u;if(h){const[p,g]=h==="beforeChildren"?[d,f]:[f,d];return p().then(()=>g())}else return Promise.all([d(),f(s.delay)])}function u2(n,i,s=0,l=0,u=1,d){const f=[],h=(n.variantChildren.size-1)*l,m=u===1?(p=0)=>p*l:(p=0)=>h-p*l;return Array.from(n.variantChildren).sort(c2).forEach((p,g)=>{p.notify("AnimationStart",i),f.push(ad(p,i,K(O({},d),{delay:s+m(g)})).then(()=>p.notify("AnimationComplete",i)))}),Promise.all(f)}function c2(n,i){return n.sortNodePosition(i)}function f2(n,i,s={}){n.notify("AnimationStart",i);let l;if(Array.isArray(i)){const u=i.map(d=>ad(n,d,s));l=Promise.all(u)}else if(typeof i=="string")l=ad(n,i,s);else{const u=typeof i=="function"?zr(n,i,s.custom):i;l=Promise.all(wb(n,u,s))}return l.then(()=>{n.notify("AnimationComplete",i)})}function Ab(n,i){if(!Array.isArray(i))return!1;const s=i.length;if(s!==n.length)return!1;for(let l=0;l<s;l++)if(i[l]!==n[l])return!1;return!0}const d2=Fd.length;function Eb(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?Eb(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const i={};for(let s=0;s<d2;s++){const l=Fd[s],u=n.props[l];(_r(u)||u===!1)&&(i[l]=u)}return i}const h2=[...Qd].reverse(),m2=Qd.length;function p2(n){return i=>Promise.all(i.map(({animation:s,options:l})=>f2(n,s,l)))}function g2(n){let i=p2(n),s=Yy(),l=!0;const u=m=>(p,g)=>{var b;const v=zr(n,g,m==="exit"?(b=n.presenceContext)==null?void 0:b.custom:void 0);if(v){const T=v,{transition:C,transitionEnd:M}=T,E=et(T,["transition","transitionEnd"]);p=O(O(O({},p),E),M)}return p};function d(m){i=m(n)}function f(m){const{props:p}=n,g=Eb(n.parent)||{},v=[],b=new Set;let T={},C=1/0;for(let E=0;E<m2;E++){const N=h2[E],z=s[N],A=p[N]!==void 0?p[N]:g[N],V=_r(A),j=N===m?z.isActive:null;j===!1&&(C=E);let I=A===g[N]&&A!==p[N]&&V;if(I&&l&&n.manuallyAnimateOnMount&&(I=!1),z.protectedKeys=O({},T),!z.isActive&&j===null||!A&&!z.prevProp||qo(A)||typeof A=="boolean")continue;const J=y2(z.prevProp,A);let Y=J||N===m&&z.isActive&&!I&&V||E>C&&V,W=!1;const ft=Array.isArray(A)?A:[A];let ht=ft.reduce(u(N),{});j===!1&&(ht={});const{prevResolvedValues:lt={}}=z,bt=O(O({},lt),ht),Et=q=>{Y=!0,b.has(q)&&(W=!0,b.delete(q)),z.needsAnimating[q]=!0;const P=n.getValue(q);P&&(P.liveStyle=!1)};for(const q in bt){const P=ht[q],ct=lt[q];if(T.hasOwnProperty(q))continue;let D=!1;nd(P)&&nd(ct)?D=!Ab(P,ct):D=P!==ct,D?P!=null?Et(q):b.add(q):P!==void 0&&b.has(q)?Et(q):z.protectedKeys[q]=!0}z.prevProp=A,z.prevResolvedValues=ht,z.isActive&&(T=O(O({},T),ht)),l&&n.blockInitialAnimation&&(Y=!1),Y&&(!(I&&J)||W)&&v.push(...ft.map(q=>({animation:q,options:{type:N}})))}if(b.size){const E={};if(typeof p.initial!="boolean"){const N=zr(n,Array.isArray(p.initial)?p.initial[0]:p.initial);N&&N.transition&&(E.transition=N.transition)}b.forEach(N=>{const z=n.getBaseTarget(N),A=n.getValue(N);A&&(A.liveStyle=!0),E[N]=z!=null?z:null}),v.push({animation:E})}let M=!!v.length;return l&&(p.initial===!1||p.initial===p.animate)&&!n.manuallyAnimateOnMount&&(M=!1),l=!1,M?i(v):Promise.resolve()}function h(m,p){var v;if(s[m].isActive===p)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(b=>{var T;return(T=b.animationState)==null?void 0:T.setActive(m,p)}),s[m].isActive=p;const g=f(m);for(const b in s)s[b].protectedKeys={};return g}return{animateChanges:f,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=Yy(),l=!0}}}function y2(n,i){return typeof i=="string"?i!==n:Array.isArray(i)?!Ab(i,n):!1}function ea(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Yy(){return{animate:ea(!0),whileInView:ea(),whileHover:ea(),whileTap:ea(),whileDrag:ea(),whileFocus:ea(),exit:ea()}}class _i{constructor(i){this.isMounted=!1,this.node=i}update(){}}class v2 extends _i{constructor(i){super(i),i.animationState||(i.animationState=g2(i))}updateAnimationControlsSubscription(){const{animate:i}=this.node.getProps();qo(i)&&(this.unmountControls=i.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:i}=this.node.getProps(),{animate:s}=this.node.prevProps||{};i!==s&&this.updateAnimationControlsSubscription()}unmount(){var i;this.node.animationState.reset(),(i=this.unmountControls)==null||i.call(this)}}let b2=0;class x2 extends _i{constructor(){super(...arguments),this.id=b2++}update(){if(!this.node.presenceContext)return;const{isPresent:i,onExitComplete:s}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||i===l)return;const u=this.node.animationState.setActive("exit",!i);s&&!i&&u.then(()=>{s(this.id)})}mount(){const{register:i,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),i&&(this.unmount=i(this.id))}unmount(){}}const S2={animation:{Feature:v2},exit:{Feature:x2}};function Lr(n,i,s,l={passive:!0}){return n.addEventListener(i,s,l),()=>n.removeEventListener(i,s)}function Yr(n){return{point:{x:n.pageX,y:n.pageY}}}const T2=n=>i=>Zd(i)&&n(i,Yr(i));function Cr(n,i,s,l){return Lr(n,i,T2(s),l)}function Mb({top:n,left:i,right:s,bottom:l}){return{x:{min:i,max:s},y:{min:n,max:l}}}function w2({x:n,y:i}){return{top:i.min,right:n.max,bottom:i.max,left:n.min}}function A2(n,i){if(!i)return n;const s=i({x:n.left,y:n.top}),l=i({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:l.y,right:l.x}}const Cb=1e-4,E2=1-Cb,M2=1+Cb,Rb=.01,C2=0-Rb,R2=0+Rb;function Me(n){return n.max-n.min}function D2(n,i,s){return Math.abs(n-i)<=s}function qy(n,i,s,l=.5){n.origin=l,n.originPoint=Qt(i.min,i.max,n.origin),n.scale=Me(s)/Me(i),n.translate=Qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=E2&&n.scale<=M2||isNaN(n.scale))&&(n.scale=1),(n.translate>=C2&&n.translate<=R2||isNaN(n.translate))&&(n.translate=0)}function Rr(n,i,s,l){qy(n.x,i.x,s.x,l?l.originX:void 0),qy(n.y,i.y,s.y,l?l.originY:void 0)}function Xy(n,i,s){n.min=s.min+i.min,n.max=n.min+Me(i)}function O2(n,i,s){Xy(n.x,i.x,s.x),Xy(n.y,i.y,s.y)}function Ky(n,i,s){n.min=i.min-s.min,n.max=n.min+Me(i)}function Dr(n,i,s){Ky(n.x,i.x,s.x),Ky(n.y,i.y,s.y)}const Zy=()=>({translate:0,scale:1,origin:0,originPoint:0}),rs=()=>({x:Zy(),y:Zy()}),Qy=()=>({min:0,max:0}),ee=()=>({x:Qy(),y:Qy()});function an(n){return[n("x"),n("y")]}function Af(n){return n===void 0||n===1}function sd({scale:n,scaleX:i,scaleY:s}){return!Af(n)||!Af(i)||!Af(s)}function aa(n){return sd(n)||Db(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Db(n){return Fy(n.x)||Fy(n.y)}function Fy(n){return n&&n!=="0%"}function Lo(n,i,s){const l=n-s,u=i*l;return s+u}function Iy(n,i,s,l,u){return u!==void 0&&(n=Lo(n,u,l)),Lo(n,s,l)+i}function rd(n,i=0,s=1,l,u){n.min=Iy(n.min,i,s,l,u),n.max=Iy(n.max,i,s,l,u)}function Ob(n,{x:i,y:s}){rd(n.x,i.translate,i.scale,i.originPoint),rd(n.y,s.translate,s.scale,s.originPoint)}const Wy=.999999999999,$y=1.0000000000001;function N2(n,i,s,l=!1){const u=s.length;if(!u)return;i.x=i.y=1;let d,f;for(let h=0;h<u;h++){d=s[h],f=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&os(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(i.x*=f.x.scale,i.y*=f.y.scale,Ob(n,f)),l&&aa(d.latestValues)&&os(n,d.latestValues))}i.x<$y&&i.x>Wy&&(i.x=1),i.y<$y&&i.y>Wy&&(i.y=1)}function ls(n,i){n.min=n.min+i,n.max=n.max+i}function Jy(n,i,s,l,u=.5){const d=Qt(n.min,n.max,u);rd(n,i,s,d,l)}function os(n,i){Jy(n.x,i.x,i.scaleX,i.scale,i.originX),Jy(n.y,i.y,i.scaleY,i.scale,i.originY)}function Nb(n,i){return Mb(A2(n.getBoundingClientRect(),i))}function j2(n,i,s){const l=Nb(n,s),{scroll:u}=i;return u&&(ls(l.x,u.offset.x),ls(l.y,u.offset.y)),l}const jb=({current:n})=>n?n.ownerDocument.defaultView:null,tv=(n,i)=>Math.abs(n-i);function _2(n,i){const s=tv(n.x,i.x),l=tv(n.y,i.y);return Math.sqrt(df(s,2)+df(l,2))}class _b{constructor(i,s,{transformPagePoint:l,contextWindow:u,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=Mf(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,T=_2(v.offset,{x:0,y:0})>=3;if(!b&&!T)return;const{point:C}=v,{timestamp:M}=ye;this.history.push(K(O({},C),{timestamp:M}));const{onStart:E,onMove:N}=this.handlers;b||(E&&E(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),N&&N(this.lastMoveEvent,v)},this.handlePointerMove=(v,b)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=Ef(b,this.transformPagePoint),Ft.update(this.updatePoint,!0)},this.handlePointerUp=(v,b)=>{this.end();const{onEnd:T,onSessionEnd:C,resumeAnimation:M}=this.handlers;if(this.dragSnapToOrigin&&M&&M(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const E=Mf(v.type==="pointercancel"?this.lastMoveEventInfo:Ef(b,this.transformPagePoint),this.history);this.startEvent&&T&&T(v,E),C&&C(v,E)},!Zd(i))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=l,this.contextWindow=u||window;const f=Yr(i),h=Ef(f,this.transformPagePoint),{point:m}=h,{timestamp:p}=ye;this.history=[K(O({},m),{timestamp:p})];const{onSessionStart:g}=s;g&&g(i,Mf(h,this.history)),this.removeListeners=Hr(Cr(this.contextWindow,"pointermove",this.handlePointerMove),Cr(this.contextWindow,"pointerup",this.handlePointerUp),Cr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(i){this.handlers=i}end(){this.removeListeners&&this.removeListeners(),Ri(this.updatePoint)}}function Ef(n,i){return i?{point:i(n.point)}:n}function ev(n,i){return{x:n.x-i.x,y:n.y-i.y}}function Mf({point:n},i){return{point:n,delta:ev(n,Vb(i)),offset:ev(n,V2(i)),velocity:z2(i,.1)}}function V2(n){return n[0]}function Vb(n){return n[n.length-1]}function z2(n,i){if(n.length<2)return{x:0,y:0};let s=n.length-1,l=null;const u=Vb(n);for(;s>=0&&(l=n[s],!(u.timestamp-l.timestamp>Cn(i)));)s--;if(!l)return{x:0,y:0};const d=Rn(u.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const f={x:(u.x-l.x)/d,y:(u.y-l.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function L2(n,{min:i,max:s},l){return i!==void 0&&n<i?n=l?Qt(i,n,l.min):Math.max(n,i):s!==void 0&&n>s&&(n=l?Qt(s,n,l.max):Math.min(n,s)),n}function nv(n,i,s){return{min:i!==void 0?n.min+i:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function B2(n,{top:i,left:s,bottom:l,right:u}){return{x:nv(n.x,s,u),y:nv(n.y,i,l)}}function iv(n,i){let s=i.min-n.min,l=i.max-n.max;return i.max-i.min<n.max-n.min&&([s,l]=[l,s]),{min:s,max:l}}function U2(n,i){return{x:iv(n.x,i.x),y:iv(n.y,i.y)}}function k2(n,i){let s=.5;const l=Me(n),u=Me(i);return u>l?s=Or(i.min,i.max-l,n.min):l>u&&(s=Or(n.min,n.max-u,i.min)),In(0,1,s)}function H2(n,i){const s={};return i.min!==void 0&&(s.min=i.min-n.min),i.max!==void 0&&(s.max=i.max-n.min),s}const ld=.35;function P2(n=ld){return n===!1?n=0:n===!0&&(n=ld),{x:av(n,"left","right"),y:av(n,"top","bottom")}}function av(n,i,s){return{min:sv(n,i),max:sv(n,s)}}function sv(n,i){return typeof n=="number"?n:n[i]||0}const G2=new WeakMap;class Y2{constructor(i){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=i}start(i,{snapToCursor:s=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const u=g=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Yr(g).point)},d=(g,v)=>{const{drag:b,dragPropagation:T,onDragStart:C}=this.getProps();if(b&&!T&&(this.openDragLock&&this.openDragLock(),this.openDragLock=rE(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),an(E=>{let N=this.getAxisMotionValue(E).get()||0;if(Dn.test(N)){const{projection:z}=this.visualElement;if(z&&z.layout){const A=z.layout.layoutBox[E];A&&(N=Me(A)*(parseFloat(N)/100))}}this.originPoint[E]=N}),C&&Ft.postRender(()=>C(g,v)),id(this.visualElement,"transform");const{animationState:M}=this.visualElement;M&&M.setActive("whileDrag",!0)},f=(g,v)=>{const{dragPropagation:b,dragDirectionLock:T,onDirectionLock:C,onDrag:M}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:E}=v;if(T&&this.currentDirection===null){this.currentDirection=q2(E),this.currentDirection!==null&&C&&C(this.currentDirection);return}this.updateAxis("x",v.point,E),this.updateAxis("y",v.point,E),this.visualElement.render(),M&&M(g,v)},h=(g,v)=>this.stop(g,v),m=()=>an(g=>{var v;return this.getAnimationState(g)==="paused"&&((v=this.getAxisMotionValue(g).animation)==null?void 0:v.play())}),{dragSnapToOrigin:p}=this.getProps();this.panSession=new _b(i,{onSessionStart:u,onStart:d,onMove:f,onSessionEnd:h,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:p,contextWindow:jb(this.visualElement)})}stop(i,s){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:u}=s;this.startAnimation(u);const{onDragEnd:d}=this.getProps();d&&Ft.postRender(()=>d(i,s))}cancel(){this.isDragging=!1;const{projection:i,animationState:s}=this.visualElement;i&&(i.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(i,s,l){const{drag:u}=this.getProps();if(!l||!mo(i,u,this.currentDirection))return;const d=this.getAxisMotionValue(i);let f=this.originPoint[i]+l[i];this.constraints&&this.constraints[i]&&(f=L2(f,this.constraints[i],this.elastic[i])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:i,dragElastic:s}=this.getProps(),l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,u=this.constraints;i&&ss(i)?this.constraints||(this.constraints=this.resolveRefConstraints()):i&&l?this.constraints=B2(l.layoutBox,i):this.constraints=!1,this.elastic=P2(s),u!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&an(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=H2(l.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:i,onMeasureDragConstraints:s}=this.getProps();if(!i||!ss(i))return!1;const l=i.current,{projection:u}=this.visualElement;if(!u||!u.layout)return!1;const d=j2(l,u.root,this.visualElement.getTransformPagePoint());let f=U2(u.layout.layoutBox,d);if(s){const h=s(w2(f));this.hasMutatedConstraints=!!h,h&&(f=Mb(h))}return f}startAnimation(i){const{drag:s,dragMomentum:l,dragElastic:u,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:h}=this.getProps(),m=this.constraints||{},p=an(g=>{if(!mo(g,s,this.currentDirection))return;let v=m&&m[g]||{};f&&(v={min:0,max:0});const b=u?200:1e6,T=u?40:1e7,C=O(O({type:"inertia",velocity:l?i[g]:0,bounceStiffness:b,bounceDamping:T,timeConstant:750,restDelta:1,restSpeed:10},d),v);return this.startAxisValueAnimation(g,C)});return Promise.all(p).then(h)}startAxisValueAnimation(i,s){const l=this.getAxisMotionValue(i);return id(this.visualElement,i),l.start(nh(i,l,0,s,this.visualElement,!1))}stopAnimation(){an(i=>this.getAxisMotionValue(i).stop())}pauseAnimation(){an(i=>{var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.pause()})}getAnimationState(i){var s;return(s=this.getAxisMotionValue(i).animation)==null?void 0:s.state}getAxisMotionValue(i){const s=`_drag${i.toUpperCase()}`,l=this.visualElement.getProps(),u=l[s];return u||this.visualElement.getValue(i,(l.initial?l.initial[i]:void 0)||0)}snapToCursor(i){an(s=>{const{drag:l}=this.getProps();if(!mo(s,l,this.currentDirection))return;const{projection:u}=this.visualElement,d=this.getAxisMotionValue(s);if(u&&u.layout){const{min:f,max:h}=u.layout.layoutBox[s];d.set(i[s]-Qt(f,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:i,dragConstraints:s}=this.getProps(),{projection:l}=this.visualElement;if(!ss(s)||!l||!this.constraints)return;this.stopAnimation();const u={x:0,y:0};an(f=>{const h=this.getAxisMotionValue(f);if(h&&this.constraints!==!1){const m=h.get();u[f]=k2({min:m,max:m},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),an(f=>{if(!mo(f,i,null))return;const h=this.getAxisMotionValue(f),{min:m,max:p}=this.constraints[f];h.set(Qt(m,p,u[f]))})}addListeners(){if(!this.visualElement.current)return;G2.set(this.visualElement,this);const i=this.visualElement.current,s=Cr(i,"pointerdown",m=>{const{drag:p,dragListener:g=!0}=this.getProps();p&&g&&this.start(m)}),l=()=>{const{dragConstraints:m}=this.getProps();ss(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:u}=this.visualElement,d=u.addEventListener("measure",l);u&&!u.layout&&(u.root&&u.root.updateScroll(),u.updateLayout()),Ft.read(l);const f=Lr(window,"resize",()=>this.scalePositionWithinConstraints()),h=u.addEventListener("didUpdate",({delta:m,hasLayoutChanged:p})=>{this.isDragging&&p&&(an(g=>{const v=this.getAxisMotionValue(g);v&&(this.originPoint[g]+=m[g].translate,v.set(v.get()+m[g].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),h&&h()}}getProps(){const i=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:l=!1,dragPropagation:u=!1,dragConstraints:d=!1,dragElastic:f=ld,dragMomentum:h=!0}=i;return K(O({},i),{drag:s,dragDirectionLock:l,dragPropagation:u,dragConstraints:d,dragElastic:f,dragMomentum:h})}}function mo(n,i,s){return(i===!0||i===n)&&(s===null||s===n)}function q2(n,i=10){let s=null;return Math.abs(n.y)>i?s="y":Math.abs(n.x)>i&&(s="x"),s}class X2 extends _i{constructor(i){super(i),this.removeGroupControls=sn,this.removeListeners=sn,this.controls=new Y2(i)}mount(){const{dragControls:i}=this.node.getProps();i&&(this.removeGroupControls=i.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||sn}unmount(){this.removeGroupControls(),this.removeListeners()}}const rv=n=>(i,s)=>{n&&Ft.postRender(()=>n(i,s))};class K2 extends _i{constructor(){super(...arguments),this.removePointerDownListener=sn}onPointerDown(i){this.session=new _b(i,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:jb(this.node)})}createPanHandlers(){const{onPanSessionStart:i,onPanStart:s,onPan:l,onPanEnd:u}=this.node.getProps();return{onSessionStart:rv(i),onStart:rv(s),onMove:l,onEnd:(d,f)=>{delete this.session,u&&Ft.postRender(()=>u(d,f))}}}mount(){this.removePointerDownListener=Cr(this.node.current,"pointerdown",i=>this.onPointerDown(i))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function lv(n,i){return i.max===i.min?0:n/(i.max-i.min)*100}const Sr={correct:(n,i)=>{if(!i.target)return n;if(typeof n=="string")if(yt.test(n))n=parseFloat(n);else return n;const s=lv(n,i.target.x),l=lv(n,i.target.y);return`${s}% ${l}%`}},Z2={correct:(n,{treeScale:i,projectionDelta:s})=>{const l=n,u=Di.parse(n);if(u.length>5)return l;const d=Di.createTransformer(n),f=typeof u[0]!="number"?1:0,h=s.x.scale*i.x,m=s.y.scale*i.y;u[0+f]/=h,u[1+f]/=m;const p=Qt(h,m,.5);return typeof u[2+f]=="number"&&(u[2+f]/=p),typeof u[3+f]=="number"&&(u[3+f]/=p),d(u)}};class Q2 extends w.Component{componentDidMount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:l,layoutId:u}=this.props,{projection:d}=i;jE(F2),d&&(s.group&&s.group.add(d),l&&l.register&&u&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions(K(O({},d.options),{onExitComplete:()=>this.safeToRemove()}))),Ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(i){const{layoutDependency:s,visualElement:l,drag:u,isPresent:d}=this.props,{projection:f}=l;return f&&(f.isPresent=d,u||i.layoutDependency!==s||s===void 0||i.isPresent!==d?f.willUpdate():this.safeToRemove(),i.isPresent!==d&&(d?f.promote():f.relegate()||Ft.postRender(()=>{const h=f.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:i}=this.props.visualElement;i&&(i.root.didUpdate(),Kd.postRender(()=>{!i.currentAnimation&&i.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:l}=this.props,{projection:u}=i;u&&(u.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(u),l&&l.deregister&&l.deregister(u))}safeToRemove(){const{safeToRemove:i}=this.props;i&&i()}render(){return null}}function zb(n){const[i,s]=pE(),l=w.useContext(y0);return S.jsx(Q2,K(O({},n),{layoutGroup:l,switchLayoutGroup:w.useContext(hb),isPresent:i,safeToRemove:s}))}const F2={borderRadius:K(O({},Sr),{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Sr,borderTopRightRadius:Sr,borderBottomLeftRadius:Sr,borderBottomRightRadius:Sr,boxShadow:Z2};function I2(n,i,s){const l=Te(n)?n:fs(n);return l.start(nh("",l,i,s)),l.animation}const W2=(n,i)=>n.depth-i.depth;class $2{constructor(){this.children=[],this.isDirty=!1}add(i){Cd(this.children,i),this.isDirty=!0}remove(i){Rd(this.children,i),this.isDirty=!0}forEach(i){this.isDirty&&this.children.sort(W2),this.isDirty=!1,this.children.forEach(i)}}function J2(n,i){const s=_e.now(),l=({timestamp:u})=>{const d=u-s;d>=i&&(Ri(l),n(d-i))};return Ft.setup(l,!0),()=>Ri(l)}const Lb=["TopLeft","TopRight","BottomLeft","BottomRight"],tM=Lb.length,ov=n=>typeof n=="string"?parseFloat(n):n,uv=n=>typeof n=="number"||yt.test(n);function eM(n,i,s,l,u,d){var f,h,m,p;u?(n.opacity=Qt(0,(f=s.opacity)!=null?f:1,nM(l)),n.opacityExit=Qt((h=i.opacity)!=null?h:1,0,iM(l))):d&&(n.opacity=Qt((m=i.opacity)!=null?m:1,(p=s.opacity)!=null?p:1,l));for(let g=0;g<tM;g++){const v=`border${Lb[g]}Radius`;let b=cv(i,v),T=cv(s,v);if(b===void 0&&T===void 0)continue;b||(b=0),T||(T=0),b===0||T===0||uv(b)===uv(T)?(n[v]=Math.max(Qt(ov(b),ov(T),l),0),(Dn.test(T)||Dn.test(b))&&(n[v]+="%")):n[v]=T}(i.rotate||s.rotate)&&(n.rotate=Qt(i.rotate||0,s.rotate||0,l))}function cv(n,i){return n[i]!==void 0?n[i]:n.borderRadius}const nM=Bb(0,.5,R0),iM=Bb(.5,.95,sn);function Bb(n,i,s){return l=>l<n?0:l>i?1:s(Or(n,i,l))}function fv(n,i){n.min=i.min,n.max=i.max}function tn(n,i){fv(n.x,i.x),fv(n.y,i.y)}function dv(n,i){n.translate=i.translate,n.scale=i.scale,n.originPoint=i.originPoint,n.origin=i.origin}function hv(n,i,s,l,u){return n-=i,n=Lo(n,1/s,l),u!==void 0&&(n=Lo(n,1/u,l)),n}function aM(n,i=0,s=1,l=.5,u,d=n,f=n){if(Dn.test(i)&&(i=parseFloat(i),i=Qt(f.min,f.max,i/100)-f.min),typeof i!="number")return;let h=Qt(d.min,d.max,l);n===d&&(h-=i),n.min=hv(n.min,i,s,h,u),n.max=hv(n.max,i,s,h,u)}function mv(n,i,[s,l,u],d,f){aM(n,i[s],i[l],i[u],i.scale,d,f)}const sM=["x","scaleX","originX"],rM=["y","scaleY","originY"];function pv(n,i,s,l){mv(n.x,i,sM,s?s.x:void 0,l?l.x:void 0),mv(n.y,i,rM,s?s.y:void 0,l?l.y:void 0)}function gv(n){return n.translate===0&&n.scale===1}function Ub(n){return gv(n.x)&&gv(n.y)}function yv(n,i){return n.min===i.min&&n.max===i.max}function lM(n,i){return yv(n.x,i.x)&&yv(n.y,i.y)}function vv(n,i){return Math.round(n.min)===Math.round(i.min)&&Math.round(n.max)===Math.round(i.max)}function kb(n,i){return vv(n.x,i.x)&&vv(n.y,i.y)}function bv(n){return Me(n.x)/Me(n.y)}function xv(n,i){return n.translate===i.translate&&n.scale===i.scale&&n.originPoint===i.originPoint}class oM{constructor(){this.members=[]}add(i){Cd(this.members,i),i.scheduleRender()}remove(i){if(Rd(this.members,i),i===this.prevLead&&(this.prevLead=void 0),i===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(i){const s=this.members.findIndex(u=>i===u);if(s===0)return!1;let l;for(let u=s;u>=0;u--){const d=this.members[u];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(i,s){const l=this.lead;if(i!==l&&(this.prevLead=l,this.lead=i,i.show(),l)){l.instance&&l.scheduleRender(),i.scheduleRender(),i.resumeFrom=l,s&&(i.resumeFrom.preserveOpacity=!0),l.snapshot&&(i.snapshot=l.snapshot,i.snapshot.latestValues=l.animationValues||l.latestValues),i.root&&i.root.isUpdating&&(i.isLayoutDirty=!0);const{crossfade:u}=i.options;u===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(i=>{const{options:s,resumingFrom:l}=i;s.onExitComplete&&s.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(i=>{i.instance&&i.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function uM(n,i,s){let l="";const u=n.x.translate/i.x,d=n.y.translate/i.y,f=(s==null?void 0:s.z)||0;if((u||d||f)&&(l=`translate3d(${u}px, ${d}px, ${f}px) `),(i.x!==1||i.y!==1)&&(l+=`scale(${1/i.x}, ${1/i.y}) `),s){const{transformPerspective:p,rotate:g,rotateX:v,rotateY:b,skewX:T,skewY:C}=s;p&&(l=`perspective(${p}px) ${l}`),g&&(l+=`rotate(${g}deg) `),v&&(l+=`rotateX(${v}deg) `),b&&(l+=`rotateY(${b}deg) `),T&&(l+=`skewX(${T}deg) `),C&&(l+=`skewY(${C}deg) `)}const h=n.x.scale*i.x,m=n.y.scale*i.y;return(h!==1||m!==1)&&(l+=`scale(${h}, ${m})`),l||"none"}const Cf=["","X","Y","Z"],cM={visibility:"hidden"},fM=1e3;let dM=0;function Rf(n,i,s,l){const{latestValues:u}=i;u[n]&&(s[n]=u[n],i.setStaticValue(n,0),l&&(l[n]=0))}function Hb(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:i}=n.options;if(!i)return;const s=Tb(i);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:u,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Ft,!(u||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&Hb(l)}function Pb({attachResizeListener:n,defaultParent:i,measureScroll:s,checkIsScrollRoot:l,resetTransform:u}){return class{constructor(f={},h=i==null?void 0:i()){this.id=dM++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(pM),this.nodes.forEach(xM),this.nodes.forEach(SM),this.nodes.forEach(gM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new $2)}addEventListener(f,h){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new Nd),this.eventHandlers.get(f).add(h)}notifyListeners(f,...h){const m=this.eventHandlers.get(f);m&&m.notify(...h)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=lb(f)&&!dE(f),this.instance=f;const{layoutId:h,layout:m,visualElement:p}=this.options;if(p&&!p.current&&p.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||h)&&(this.isLayoutDirty=!0),n){let g;const v=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=J2(v,250),Ro.hasAnimatedSinceResize&&(Ro.hasAnimatedSinceResize=!1,this.nodes.forEach(Tv))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&p&&(h||m)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:v,hasRelativeLayoutChanged:b,layout:T})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const C=this.options.transition||p.getDefaultTransition()||MM,{onLayoutAnimationStart:M,onLayoutAnimationComplete:E}=p.getProps(),N=!this.targetLayout||!kb(this.targetLayout,T),z=!v&&b;if(this.options.layoutRoot||this.resumeFrom||z||v&&(N||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const A=K(O({},qd(C,"layout")),{onPlay:M,onComplete:E});(p.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A),this.setAnimationOrigin(g,z)}else v||Tv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=T})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ri(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(TM),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Hb(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const v=this.path[g];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:m}=this.options;if(h===void 0&&!m)return;const p=this.getTransformTemplate();this.prevTransformTemplateValue=p?p(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Sv);return}this.isUpdating||this.nodes.forEach(vM),this.isUpdating=!1,this.nodes.forEach(bM),this.nodes.forEach(hM),this.nodes.forEach(mM),this.clearAllSnapshots();const h=_e.now();ye.delta=In(0,1e3/60,h-ye.timestamp),ye.timestamp=h,ye.isProcessing=!0,bf.update.process(ye),bf.preRender.process(ye),bf.render.process(ye),ye.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Kd.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yM),this.sharedNodes.forEach(wM)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ft.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ft.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Me(this.snapshot.measuredBox.x)&&!Me(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(h=!1),h&&this.instance){const m=l(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!u)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!Ub(this.projectionDelta),m=this.getTransformTemplate(),p=m?m(this.latestValues,""):void 0,g=p!==this.prevTransformTemplateValue;f&&this.instance&&(h||aa(this.latestValues)||g)&&(u(this.instance,p),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const h=this.measurePageBox();let m=this.removeElementScroll(h);return f&&(m=this.removeTransform(m)),CM(m),{animationId:this.root.animationId,measuredBox:h,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var p;const{visualElement:f}=this.options;if(!f)return ee();const h=f.measureViewportBox();if(!(((p=this.scroll)==null?void 0:p.wasRoot)||this.path.some(RM))){const{scroll:g}=this.root;g&&(ls(h.x,g.offset.x),ls(h.y,g.offset.y))}return h}removeElementScroll(f){var m;const h=ee();if(tn(h,f),(m=this.scroll)!=null&&m.wasRoot)return h;for(let p=0;p<this.path.length;p++){const g=this.path[p],{scroll:v,options:b}=g;g!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&tn(h,f),ls(h.x,v.offset.x),ls(h.y,v.offset.y))}return h}applyTransform(f,h=!1){const m=ee();tn(m,f);for(let p=0;p<this.path.length;p++){const g=this.path[p];!h&&g.options.layoutScroll&&g.scroll&&g!==g.root&&os(m,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),aa(g.latestValues)&&os(m,g.latestValues)}return aa(this.latestValues)&&os(m,this.latestValues),m}removeTransform(f){const h=ee();tn(h,f);for(let m=0;m<this.path.length;m++){const p=this.path[m];if(!p.instance||!aa(p.latestValues))continue;sd(p.latestValues)&&p.updateSnapshot();const g=ee(),v=p.measurePageBox();tn(g,v),pv(h,p.latestValues,p.snapshot?p.snapshot.layoutBox:void 0,g)}return aa(this.latestValues)&&pv(h,this.latestValues),h}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options=K(O(O({},this.options),f),{crossfade:f.crossfade!==void 0?f.crossfade:!0})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ye.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var b;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==h;if(!(f||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:v}=this.options;if(!(!this.layout||!(g||v))){if(this.resolvedRelativeTargetAt=ye.timestamp,!this.targetDelta&&!this.relativeTarget){const T=this.getClosestProjectingParent();T&&T.layout&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.layout.layoutBox,T.layout.layoutBox),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),O2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tn(this.target,this.layout.layoutBox),Ob(this.target,this.targetDelta)):tn(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const T=this.getClosestProjectingParent();T&&!!T.resumingFrom==!!this.resumingFrom&&!T.options.layoutScroll&&T.target&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.target,T.target),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||sd(this.parent.latestValues)||Db(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var C;const f=this.getLead(),h=!!this.resumingFrom||this!==f;let m=!0;if((this.isProjectionDirty||(C=this.parent)!=null&&C.isProjectionDirty)&&(m=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===ye.timestamp&&(m=!1),m)return;const{layout:p,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(p||g))return;tn(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,b=this.treeScale.y;N2(this.layoutCorrected,this.treeScale,this.path,h),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=ee());const{target:T}=f;if(!T){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(dv(this.prevProjectionDelta.x,this.projectionDelta.x),dv(this.prevProjectionDelta.y,this.projectionDelta.y)),Rr(this.projectionDelta,this.layoutCorrected,T,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==b||!xv(this.projectionDelta.x,this.prevProjectionDelta.x)||!xv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",T))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rs(),this.projectionDelta=rs(),this.projectionDeltaWithTransform=rs()}setAnimationOrigin(f,h=!1){const m=this.snapshot,p=m?m.latestValues:{},g=O({},this.latestValues),v=rs();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const b=ee(),T=m?m.source:void 0,C=this.layout?this.layout.source:void 0,M=T!==C,E=this.getStack(),N=!E||E.members.length<=1,z=!!(M&&!N&&this.options.crossfade===!0&&!this.path.some(EM));this.animationProgress=0;let A;this.mixTargetDelta=V=>{const j=V/1e3;wv(v.x,f.x,j),wv(v.y,f.y,j),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dr(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),AM(this.relativeTarget,this.relativeTargetOrigin,b,j),A&&lM(this.relativeTarget,A)&&(this.isProjectionDirty=!1),A||(A=ee()),tn(A,this.relativeTarget)),M&&(this.animationValues=g,eM(g,p,this.latestValues,j,z,N)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var h,m,p;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(p=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||p.stop(),this.pendingAnimation&&(Ri(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ft.update(()=>{Ro.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=fs(0)),this.currentAnimation=I2(this.motionValue,[0,1e3],K(O({},f),{isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),f.onUpdate&&f.onUpdate(g)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fM),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:h,target:m,layout:p,latestValues:g}=f;if(!(!h||!m||!p)){if(this!==f&&this.layout&&p&&Gb(this.options.animationType,this.layout.layoutBox,p.layoutBox)){m=this.target||ee();const v=Me(this.layout.layoutBox.x);m.x.min=f.target.x.min,m.x.max=m.x.min+v;const b=Me(this.layout.layoutBox.y);m.y.min=f.target.y.min,m.y.max=m.y.min+b}tn(h,m),os(h,g),Rr(this.projectionDeltaWithTransform,this.layoutCorrected,h,g)}}registerSharedNode(f,h){this.sharedNodes.has(f)||this.sharedNodes.set(f,new oM),this.sharedNodes.get(f).add(h);const p=h.options.initialPromotionConfig;h.promote({transition:p?p.transition:void 0,preserveFollowOpacity:p&&p.shouldPreserveFollowOpacity?p.shouldPreserveFollowOpacity(h):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var h;const{layoutId:f}=this.options;return f?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:f}=this.options;return f?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:h,preserveFollowOpacity:m}={}){const p=this.getStack();p&&p.promote(this,m),f&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let h=!1;const{latestValues:m}=f;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(h=!0),!h)return;const p={};m.z&&Rf("z",f,p,this.animationValues);for(let g=0;g<Cf.length;g++)Rf(`rotate${Cf[g]}`,f,p,this.animationValues),Rf(`skew${Cf[g]}`,f,p,this.animationValues);f.render();for(const g in p)f.setStaticValue(g,p[g]),this.animationValues&&(this.animationValues[g]=p[g]);f.scheduleRender()}getProjectionStyles(f){var T,C;if(!this.instance||this.isSVG)return;if(!this.isVisible)return cM;const h={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=Co(f==null?void 0:f.pointerEvents)||"",h.transform=m?m(this.latestValues,""):"none",h;const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){const M={};return this.options.layoutId&&(M.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,M.pointerEvents=Co(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!aa(this.latestValues)&&(M.transform=m?m({},""):"none",this.hasProjected=!1),M}const g=p.animationValues||p.latestValues;this.applyTransformsToTarget(),h.transform=uM(this.projectionDeltaWithTransform,this.treeScale,g),m&&(h.transform=m(g,h.transform));const{x:v,y:b}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${b.origin*100}% 0`,p.animationValues?h.opacity=p===this?(C=(T=g.opacity)!=null?T:this.latestValues.opacity)!=null?C:1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:h.opacity=p===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const M in Vr){if(g[M]===void 0)continue;const{correct:E,applyTo:N,isCSSVariable:z}=Vr[M],A=h.transform==="none"?g[M]:E(g[M],p);if(N){const V=N.length;for(let j=0;j<V;j++)h[N[j]]=A}else z?this.options.visualElement.renderState.vars[M]=A:h[M]=A}return this.options.layoutId&&(h.pointerEvents=p===this?Co(f==null?void 0:f.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var h;return(h=f.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(Sv),this.root.sharedNodes.clear()}}}function hM(n){n.updateLayout()}function mM(n){var s;const i=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&i&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:u}=n.layout,{animationType:d}=n.options,f=i.source!==n.layout.source;d==="size"?an(v=>{const b=f?i.measuredBox[v]:i.layoutBox[v],T=Me(b);b.min=l[v].min,b.max=b.min+T}):Gb(d,i.layoutBox,l)&&an(v=>{const b=f?i.measuredBox[v]:i.layoutBox[v],T=Me(l[v]);b.max=b.min+T,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+T)});const h=rs();Rr(h,l,i.layoutBox);const m=rs();f?Rr(m,n.applyTransform(u,!0),i.measuredBox):Rr(m,l,i.layoutBox);const p=!Ub(h);let g=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:T}=v;if(b&&T){const C=ee();Dr(C,i.layoutBox,b.layoutBox);const M=ee();Dr(M,l,T.layoutBox),kb(C,M)||(g=!0),v.options.layoutRoot&&(n.relativeTarget=M,n.relativeTargetOrigin=C,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:i,delta:m,layoutDelta:h,hasLayoutChanged:p,hasRelativeLayoutChanged:g})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function pM(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function gM(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function yM(n){n.clearSnapshot()}function Sv(n){n.clearMeasurements()}function vM(n){n.isLayoutDirty=!1}function bM(n){const{visualElement:i}=n.options;i&&i.getProps().onBeforeLayoutMeasure&&i.notify("BeforeLayoutMeasure"),n.resetTransform()}function Tv(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function xM(n){n.resolveTargetDelta()}function SM(n){n.calcProjection()}function TM(n){n.resetSkewAndRotation()}function wM(n){n.removeLeadSnapshot()}function wv(n,i,s){n.translate=Qt(i.translate,0,s),n.scale=Qt(i.scale,1,s),n.origin=i.origin,n.originPoint=i.originPoint}function Av(n,i,s,l){n.min=Qt(i.min,s.min,l),n.max=Qt(i.max,s.max,l)}function AM(n,i,s,l){Av(n.x,i.x,s.x,l),Av(n.y,i.y,s.y,l)}function EM(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const MM={duration:.45,ease:[.4,0,.1,1]},Ev=n=>typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Mv=Ev("applewebkit/")&&!Ev("chrome/")?Math.round:sn;function Cv(n){n.min=Mv(n.min),n.max=Mv(n.max)}function CM(n){Cv(n.x),Cv(n.y)}function Gb(n,i,s){return n==="position"||n==="preserve-aspect"&&!D2(bv(i),bv(s),.2)}function RM(n){var i;return n!==n.root&&((i=n.scroll)==null?void 0:i.wasRoot)}const DM=Pb({attachResizeListener:(n,i)=>Lr(n,"resize",i),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Df={current:void 0},Yb=Pb({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Df.current){const n=new DM({});n.mount(window),n.setOptions({layoutScroll:!0}),Df.current=n}return Df.current},resetTransform:(n,i)=>{n.style.transform=i!==void 0?i:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),OM={pan:{Feature:K2},drag:{Feature:X2,ProjectionNode:Yb,MeasureLayout:zb}};function Rv(n,i,s){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",s==="Start");const u="onHover"+s,d=l[u];d&&Ft.postRender(()=>d(i,Yr(i)))}class NM extends _i{mount(){const{current:i}=this.node;i&&(this.unmount=lE(i,(s,l)=>(Rv(this.node,l,"Start"),u=>Rv(this.node,u,"End"))))}unmount(){}}class jM extends _i{constructor(){super(...arguments),this.isActive=!1}onFocus(){let i=!1;try{i=this.node.current.matches(":focus-visible")}catch(s){i=!0}!i||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Hr(Lr(this.node.current,"focus",()=>this.onFocus()),Lr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Dv(n,i,s){const{props:l}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",s==="Start");const u="onTap"+(s==="End"?"":s),d=l[u];d&&Ft.postRender(()=>d(i,Yr(i)))}class _M extends _i{mount(){const{current:i}=this.node;i&&(this.unmount=fE(i,(s,l)=>(Dv(this.node,l,"Start"),(u,{success:d})=>Dv(this.node,u,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const od=new WeakMap,Of=new WeakMap,VM=n=>{const i=od.get(n.target);i&&i(n)},zM=n=>{n.forEach(VM)};function LM(s){var l=s,{root:n}=l,i=et(l,["root"]);const u=n||document;Of.has(u)||Of.set(u,{});const d=Of.get(u),f=JSON.stringify(i);return d[f]||(d[f]=new IntersectionObserver(zM,O({root:n},i))),d[f]}function BM(n,i,s){const l=LM(i);return od.set(n,s),l.observe(n),()=>{od.delete(n),l.unobserve(n)}}const UM={some:0,all:1};class kM extends _i{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:i={}}=this.node.getProps(),{root:s,margin:l,amount:u="some",once:d}=i,f={root:s?s.current:void 0,rootMargin:l,threshold:typeof u=="number"?u:UM[u]},h=m=>{const{isIntersecting:p}=m;if(this.isInView===p||(this.isInView=p,d&&!p&&this.hasEnteredView))return;p&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",p);const{onViewportEnter:g,onViewportLeave:v}=this.node.getProps(),b=p?g:v;b&&b(m)};return BM(this.node.current,f,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver=="undefined")return;const{props:i,prevProps:s}=this.node;["amount","margin","root"].some(HM(i,s))&&this.startObserver()}unmount(){}}function HM({viewport:n={}},{viewport:i={}}={}){return s=>n[s]!==i[s]}const PM={inView:{Feature:kM},tap:{Feature:_M},focus:{Feature:jM},hover:{Feature:NM}},GM={layout:{ProjectionNode:Yb,MeasureLayout:zb}},ud={current:null},qb={current:!1};function YM(){if(qb.current=!0,!!Ed)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),i=()=>ud.current=n.matches;n.addListener(i),i()}else ud.current=!1}const qM=new WeakMap;function XM(n,i,s){for(const l in i){const u=i[l],d=s[l];if(Te(u))n.addValue(l,u);else if(Te(d))n.addValue(l,fs(u,{owner:n}));else if(d!==u)if(n.hasValue(l)){const f=n.getValue(l);f.liveStyle===!0?f.jump(u):f.hasAnimated||f.set(u)}else{const f=n.getStaticValue(l);n.addValue(l,fs(f!==void 0?f:u,{owner:n}))}}for(const l in s)i[l]===void 0&&n.removeValue(l);return i}const Ov=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class KM{scrapeMotionValuesFromProps(i,s,l){return{}}constructor({parent:i,props:s,presenceContext:l,reducedMotionConfig:u,blockInitialAnimation:d,visualState:f},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Yd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const T=_e.now();this.renderScheduledAt<T&&(this.renderScheduledAt=T,Ft.render(this.render,!1,!0))};const{latestValues:m,renderState:p}=f;this.latestValues=m,this.baseTarget=O({},m),this.initialValues=s.initial?O({},m):{},this.renderState=p,this.parent=i,this.props=s,this.presenceContext=l,this.depth=i?i.depth+1:0,this.reducedMotionConfig=u,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=Xo(s),this.isVariantNode=fb(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(i&&i.current);const b=this.scrapeMotionValuesFromProps(s,{},this),{willChange:g}=b,v=et(b,["willChange"]);for(const T in v){const C=v[T];m[T]!==void 0&&Te(C)&&C.set(m[T],!1)}}mount(i){this.current=i,qM.set(i,this),this.projection&&!this.projection.instance&&this.projection.mount(i),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,l)=>this.bindToMotionValue(l,s)),qb.current||YM(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ud.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ri(this.notifyUpdate),Ri(this.render),this.valueSubscriptions.forEach(i=>i()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const i in this.events)this.events[i].clear();for(const i in this.features){const s=this.features[i];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(i,s){this.valueSubscriptions.has(i)&&this.valueSubscriptions.get(i)();const l=gs.has(i);l&&this.onBindTransform&&this.onBindTransform();const u=s.on("change",h=>{this.latestValues[i]=h,this.props.onUpdate&&Ft.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,i,s)),this.valueSubscriptions.set(i,()=>{u(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(i){return!this.current||!this.sortInstanceNodePosition||this.type!==i.type?0:this.sortInstanceNodePosition(this.current,i.current)}updateFeatures(){let i="animation";for(i in ds){const s=ds[i];if(!s)continue;const{isEnabled:l,Feature:u}=s;if(!this.features[i]&&u&&l(this.props)&&(this.features[i]=new u(this)),this.features[i]){const d=this.features[i];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(i){return this.latestValues[i]}setStaticValue(i,s){this.latestValues[i]=s}update(i,s){(i.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=i,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let l=0;l<Ov.length;l++){const u=Ov[l];this.propEventSubscriptions[u]&&(this.propEventSubscriptions[u](),delete this.propEventSubscriptions[u]);const d="on"+u,f=i[d];f&&(this.propEventSubscriptions[u]=this.on(u,f))}this.prevMotionValues=XM(this,this.scrapeMotionValuesFromProps(i,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(i){return this.props.variants?this.props.variants[i]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(i){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(i),()=>s.variantChildren.delete(i)}addValue(i,s){const l=this.values.get(i);s!==l&&(l&&this.removeValue(i),this.bindToMotionValue(i,s),this.values.set(i,s),this.latestValues[i]=s.get())}removeValue(i){this.values.delete(i);const s=this.valueSubscriptions.get(i);s&&(s(),this.valueSubscriptions.delete(i)),delete this.latestValues[i],this.removeValueFromRenderState(i,this.renderState)}hasValue(i){return this.values.has(i)}getValue(i,s){if(this.props.values&&this.props.values[i])return this.props.values[i];let l=this.values.get(i);return l===void 0&&s!==void 0&&(l=fs(s===null?void 0:s,{owner:this}),this.addValue(i,l)),l}readValue(i,s){var u;let l=this.latestValues[i]!==void 0||!this.current?this.latestValues[i]:(u=this.getBaseTargetFromProps(this.props,i))!=null?u:this.readValueFromInstance(this.current,i,this.options);return l!=null&&(typeof l=="string"&&(v0(l)||x0(l))?l=parseFloat(l):!mE(l)&&Di.test(s)&&(l=nb(i,s)),this.setBaseTarget(i,Te(l)?l.get():l)),Te(l)?l.get():l}setBaseTarget(i,s){this.baseTarget[i]=s}getBaseTarget(i){var d;const{initial:s}=this.props;let l;if(typeof s=="string"||typeof s=="object"){const f=th(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(l=f[i])}if(s&&l!==void 0)return l;const u=this.getBaseTargetFromProps(this.props,i);return u!==void 0&&!Te(u)?u:this.initialValues[i]!==void 0&&l===void 0?void 0:this.baseTarget[i]}on(i,s){return this.events[i]||(this.events[i]=new Nd),this.events[i].add(s)}notify(i,...s){this.events[i]&&this.events[i].notify(...s)}}class Xb extends KM{constructor(){super(...arguments),this.KeyframeResolver=nE}sortInstanceNodePosition(i,s){return i.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(i,s){return i.style?i.style[s]:void 0}removeValueFromRenderState(i,{vars:s,style:l}){delete s[i],delete l[i]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:i}=this.props;Te(i)&&(this.childSubscription=i.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Kb(n,{style:i,vars:s},l,u){Object.assign(n.style,i,u&&u.getProjectionStyles(l));for(const d in s)n.style.setProperty(d,s[d])}function ZM(n){return window.getComputedStyle(n)}class QM extends Xb{constructor(){super(...arguments),this.type="html",this.renderInstance=Kb}readValueFromInstance(i,s){var l;if(gs.has(s))return(l=this.projection)!=null&&l.isProjecting?If(s):SA(i,s);{const u=ZM(i),d=(Vd(s)?u.getPropertyValue(s):u[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(i,{transformPagePoint:s}){return Nb(i,s)}build(i,s,l){Wd(i,s,l.transformTemplate)}scrapeMotionValuesFromProps(i,s,l){return eh(i,s,l)}}const Zb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function FM(n,i,s,l){Kb(n,i,void 0,l);for(const u in i.attrs)n.setAttribute(Zb.has(u)?u:Id(u),i.attrs[u])}class IM extends Xb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ee}getBaseTargetFromProps(i,s){return i[s]}readValueFromInstance(i,s){if(gs.has(s)){const l=eb(s);return l&&l.default||0}return s=Zb.has(s)?s:Id(s),i.getAttribute(s)}scrapeMotionValuesFromProps(i,s,l){return Sb(i,s,l)}build(i,s,l){yb(i,s,this.isSVGTag,l.transformTemplate,l.style)}renderInstance(i,s,l,u){FM(i,s,l,u)}mount(i){this.isSVGTag=bb(i.tagName),super.mount(i)}}const WM=(n,i)=>Jd(n)?new IM(i):new QM(i,{allowProjection:n!==w.Fragment}),$M=FE(O(O(O(O({},S2),PM),OM),GM),WM),xn=xE($M);function Nv(n,i){if(typeof n=="function")return n(i);n!=null&&(n.current=i)}function JM(...n){return i=>{let s=!1;const l=n.map(u=>{const d=Nv(u,i);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let u=0;u<l.length;u++){const d=l[u];typeof d=="function"?d():Nv(n[u],null)}}}}function ae(...n){return w.useCallback(JM(...n),n)}function Br(n){const i=tC(n),s=w.forwardRef((l,u)=>{const p=l,{children:d}=p,f=et(p,["children"]),h=w.Children.toArray(d),m=h.find(nC);if(m){const g=m.props.children,v=h.map(b=>b===m?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:b);return S.jsx(i,K(O({},f),{ref:u,children:w.isValidElement(g)?w.cloneElement(g,void 0,v):null}))}return S.jsx(i,K(O({},f),{ref:u,children:d}))});return s.displayName=`${n}.Slot`,s}var Qb=Br("Slot");function tC(n){const i=w.forwardRef((s,l)=>{const m=s,{children:u}=m,d=et(m,["children"]),f=w.isValidElement(u)?aC(u):void 0,h=ae(f,l);if(w.isValidElement(u)){const p=iC(d,u.props);return u.type!==w.Fragment&&(p.ref=h),w.cloneElement(u,p)}return w.Children.count(u)>1?w.Children.only(null):null});return i.displayName=`${n}.SlotClone`,i}var eC=Symbol("radix.slottable");function nC(n){return w.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===eC}function iC(n,i){const s=O({},i);for(const l in i){const u=n[l],d=i[l];/^on[A-Z]/.test(l)?u&&d?s[l]=(...h)=>{const m=d(...h);return u(...h),m}:u&&(s[l]=u):l==="style"?s[l]=O(O({},u),d):l==="className"&&(s[l]=[u,d].filter(Boolean).join(" "))}return O(O({},n),s)}function aC(n){var l,u;let i=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=i&&"isReactWarning"in i&&i.isReactWarning;return s?n.ref:(i=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=i&&"isReactWarning"in i&&i.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Fb(n){var i,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(i=0;i<u;i++)n[i]&&(s=Fb(n[i]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function Ib(){for(var n,i,s=0,l="",u=arguments.length;s<u;s++)(n=arguments[s])&&(i=Fb(n))&&(l&&(l+=" "),l+=i);return l}const jv=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,_v=Ib,Wb=(n,i)=>s=>{var l;if((i==null?void 0:i.variants)==null)return _v(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:u,defaultVariants:d}=i,f=Object.keys(u).map(p=>{const g=s==null?void 0:s[p],v=d==null?void 0:d[p];if(g===null)return null;const b=jv(g)||jv(v);return u[p][b]}),h=s&&Object.entries(s).reduce((p,g)=>{let[v,b]=g;return b===void 0||(p[v]=b),p},{}),m=i==null||(l=i.compoundVariants)===null||l===void 0?void 0:l.reduce((p,g)=>{let C=g,{class:v,className:b}=C,T=et(C,["class","className"]);return Object.entries(T).every(M=>{let[E,N]=M;return Array.isArray(N)?N.includes(O(O({},d),h)[E]):O(O({},d),h)[E]===N})?[...p,v,b]:p},[]);return _v(n,f,m,s==null?void 0:s.class,s==null?void 0:s.className)},ih="-",sC=n=>{const i=lC(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const h=f.split(ih);return h[0]===""&&h.length!==1&&h.shift(),$b(h,i)||rC(f)},getConflictingClassGroupIds:(f,h)=>{const m=s[f]||[];return h&&l[f]?[...m,...l[f]]:m}}},$b=(n,i)=>{var f;if(n.length===0)return i.classGroupId;const s=n[0],l=i.nextPart.get(s),u=l?$b(n.slice(1),l):void 0;if(u)return u;if(i.validators.length===0)return;const d=n.join(ih);return(f=i.validators.find(({validator:h})=>h(d)))==null?void 0:f.classGroupId},Vv=/^\[(.+)\]$/,rC=n=>{if(Vv.test(n)){const i=Vv.exec(n)[1],s=i==null?void 0:i.substring(0,i.indexOf(":"));if(s)return"arbitrary.."+s}},lC=n=>{const{theme:i,classGroups:s}=n,l={nextPart:new Map,validators:[]};for(const u in s)cd(s[u],l,u,i);return l},cd=(n,i,s,l)=>{n.forEach(u=>{if(typeof u=="string"){const d=u===""?i:zv(i,u);d.classGroupId=s;return}if(typeof u=="function"){if(oC(u)){cd(u(l),i,s,l);return}i.validators.push({validator:u,classGroupId:s});return}Object.entries(u).forEach(([d,f])=>{cd(f,zv(i,d),s,l)})})},zv=(n,i)=>{let s=n;return i.split(ih).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},oC=n=>n.isThemeGetter,uC=n=>{if(n<1)return{get:()=>{},set:()=>{}};let i=0,s=new Map,l=new Map;const u=(d,f)=>{s.set(d,f),i++,i>n&&(i=0,l=s,s=new Map)};return{get(d){let f=s.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return u(d,f),f},set(d,f){s.has(d)?s.set(d,f):u(d,f)}}},fd="!",dd=":",cC=dd.length,fC=n=>{const{prefix:i,experimentalParseClassName:s}=n;let l=u=>{const d=[];let f=0,h=0,m=0,p;for(let C=0;C<u.length;C++){let M=u[C];if(f===0&&h===0){if(M===dd){d.push(u.slice(m,C)),m=C+cC;continue}if(M==="/"){p=C;continue}}M==="["?f++:M==="]"?f--:M==="("?h++:M===")"&&h--}const g=d.length===0?u:u.substring(m),v=dC(g),b=v!==g,T=p&&p>m?p-m:void 0;return{modifiers:d,hasImportantModifier:b,baseClassName:v,maybePostfixModifierPosition:T}};if(i){const u=i+dd,d=l;l=f=>f.startsWith(u)?d(f.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:f,maybePostfixModifierPosition:void 0}}if(s){const u=l;l=d=>s({className:d,parseClassName:u})}return l},dC=n=>n.endsWith(fd)?n.substring(0,n.length-1):n.startsWith(fd)?n.substring(1):n,hC=n=>{const i=Object.fromEntries(n.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;const u=[];let d=[];return l.forEach(f=>{f[0]==="["||i[f]?(u.push(...d.sort(),f),d=[]):d.push(f)}),u.push(...d.sort()),u}},mC=n=>O({cache:uC(n.cacheSize),parseClassName:fC(n),sortModifiers:hC(n)},sC(n)),pC=/\s+/,gC=(n,i)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:u,sortModifiers:d}=i,f=[],h=n.trim().split(pC);let m="";for(let p=h.length-1;p>=0;p-=1){const g=h[p],{isExternal:v,modifiers:b,hasImportantModifier:T,baseClassName:C,maybePostfixModifierPosition:M}=s(g);if(v){m=g+(m.length>0?" "+m:m);continue}let E=!!M,N=l(E?C.substring(0,M):C);if(!N){if(!E){m=g+(m.length>0?" "+m:m);continue}if(N=l(C),!N){m=g+(m.length>0?" "+m:m);continue}E=!1}const z=d(b).join(":"),A=T?z+fd:z,V=A+N;if(f.includes(V))continue;f.push(V);const j=u(N,E);for(let I=0;I<j.length;++I){const J=j[I];f.push(A+J)}m=g+(m.length>0?" "+m:m)}return m};function yC(){let n=0,i,s,l="";for(;n<arguments.length;)(i=arguments[n++])&&(s=Jb(i))&&(l&&(l+=" "),l+=s);return l}const Jb=n=>{if(typeof n=="string")return n;let i,s="";for(let l=0;l<n.length;l++)n[l]&&(i=Jb(n[l]))&&(s&&(s+=" "),s+=i);return s};function vC(n,...i){let s,l,u,d=f;function f(m){const p=i.reduce((g,v)=>v(g),n());return s=mC(p),l=s.cache.get,u=s.cache.set,d=h,h(m)}function h(m){const p=l(m);if(p)return p;const g=gC(m,s);return u(m,g),g}return function(){return d(yC.apply(null,arguments))}}const le=n=>{const i=s=>s[n]||[];return i.isThemeGetter=!0,i},tx=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ex=/^\((?:(\w[\w-]*):)?(.+)\)$/i,bC=/^\d+\/\d+$/,xC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,SC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,TC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,wC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,AC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ja=n=>bC.test(n),At=n=>!!n&&!Number.isNaN(Number(n)),Ai=n=>!!n&&Number.isInteger(Number(n)),Nf=n=>n.endsWith("%")&&At(n.slice(0,-1)),Qn=n=>xC.test(n),EC=()=>!0,MC=n=>SC.test(n)&&!TC.test(n),nx=()=>!1,CC=n=>wC.test(n),RC=n=>AC.test(n),DC=n=>!st(n)&&!rt(n),OC=n=>ys(n,sx,nx),st=n=>tx.test(n),na=n=>ys(n,rx,MC),jf=n=>ys(n,zC,At),Lv=n=>ys(n,ix,nx),NC=n=>ys(n,ax,RC),po=n=>ys(n,lx,CC),rt=n=>ex.test(n),Tr=n=>vs(n,rx),jC=n=>vs(n,LC),Bv=n=>vs(n,ix),_C=n=>vs(n,sx),VC=n=>vs(n,ax),go=n=>vs(n,lx,!0),ys=(n,i,s)=>{const l=tx.exec(n);return l?l[1]?i(l[1]):s(l[2]):!1},vs=(n,i,s=!1)=>{const l=ex.exec(n);return l?l[1]?i(l[1]):s:!1},ix=n=>n==="position"||n==="percentage",ax=n=>n==="image"||n==="url",sx=n=>n==="length"||n==="size"||n==="bg-size",rx=n=>n==="length",zC=n=>n==="number",LC=n=>n==="family-name",lx=n=>n==="shadow",BC=()=>{const n=le("color"),i=le("font"),s=le("text"),l=le("font-weight"),u=le("tracking"),d=le("leading"),f=le("breakpoint"),h=le("container"),m=le("spacing"),p=le("radius"),g=le("shadow"),v=le("inset-shadow"),b=le("text-shadow"),T=le("drop-shadow"),C=le("blur"),M=le("perspective"),E=le("aspect"),N=le("ease"),z=le("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...V(),rt,st],I=()=>["auto","hidden","clip","visible","scroll"],J=()=>["auto","contain","none"],Y=()=>[rt,st,m],W=()=>[Ja,"full","auto",...Y()],ft=()=>[Ai,"none","subgrid",rt,st],ht=()=>["auto",{span:["full",Ai,rt,st]},Ai,rt,st],lt=()=>[Ai,"auto",rt,st],bt=()=>["auto","min","max","fr",rt,st],Et=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ut=()=>["start","end","center","stretch","center-safe","end-safe"],B=()=>["auto",...Y()],q=()=>[Ja,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],P=()=>[n,rt,st],ct=()=>[...V(),Bv,Lv,{position:[rt,st]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],X=()=>["auto","cover","contain",_C,OC,{size:[rt,st]}],tt=()=>[Nf,Tr,na],$=()=>["","none","full",p,rt,st],it=()=>["",At,Tr,na],vt=()=>["solid","dashed","dotted","double"],dt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],xt=()=>[At,Nf,Bv,Lv],nt=()=>["","none",C,rt,st],Tt=()=>["none",At,rt,st],qt=()=>["none",At,rt,st],Ot=()=>[At,rt,st],Ct=()=>[Ja,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qn],breakpoint:[Qn],color:[EC],container:[Qn],"drop-shadow":[Qn],ease:["in","out","in-out"],font:[DC],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qn],shadow:[Qn],spacing:["px",At],text:[Qn],"text-shadow":[Qn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ja,st,rt,E]}],container:["container"],columns:[{columns:[At,st,rt,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:J()}],"overscroll-x":[{"overscroll-x":J()}],"overscroll-y":[{"overscroll-y":J()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:W()}],"inset-x":[{"inset-x":W()}],"inset-y":[{"inset-y":W()}],start:[{start:W()}],end:[{end:W()}],top:[{top:W()}],right:[{right:W()}],bottom:[{bottom:W()}],left:[{left:W()}],visibility:["visible","invisible","collapse"],z:[{z:[Ai,"auto",rt,st]}],basis:[{basis:[Ja,"full","auto",h,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[At,Ja,"auto","initial","none",st]}],grow:[{grow:["",At,rt,st]}],shrink:[{shrink:["",At,rt,st]}],order:[{order:[Ai,"first","last","none",rt,st]}],"grid-cols":[{"grid-cols":ft()}],"col-start-end":[{col:ht()}],"col-start":[{"col-start":lt()}],"col-end":[{"col-end":lt()}],"grid-rows":[{"grid-rows":ft()}],"row-start-end":[{row:ht()}],"row-start":[{"row-start":lt()}],"row-end":[{"row-end":lt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":bt()}],"auto-rows":[{"auto-rows":bt()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...Et(),"normal"]}],"justify-items":[{"justify-items":[...ut(),"normal"]}],"justify-self":[{"justify-self":["auto",...ut()]}],"align-content":[{content:["normal",...Et()]}],"align-items":[{items:[...ut(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ut(),{baseline:["","last"]}]}],"place-content":[{"place-content":Et()}],"place-items":[{"place-items":[...ut(),"baseline"]}],"place-self":[{"place-self":["auto",...ut()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:B()}],mx:[{mx:B()}],my:[{my:B()}],ms:[{ms:B()}],me:[{me:B()}],mt:[{mt:B()}],mr:[{mr:B()}],mb:[{mb:B()}],ml:[{ml:B()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[h,"screen",...q()]}],"min-w":[{"min-w":[h,"screen","none",...q()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[f]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",s,Tr,na]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,rt,jf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Nf,st]}],"font-family":[{font:[jC,st,i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,rt,st]}],"line-clamp":[{"line-clamp":[At,"none",rt,jf]}],leading:[{leading:[d,...Y()]}],"list-image":[{"list-image":["none",rt,st]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",rt,st]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...vt(),"wavy"]}],"text-decoration-thickness":[{decoration:[At,"from-font","auto",rt,na]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[At,"auto",rt,st]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",rt,st]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",rt,st]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ct()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:X()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ai,rt,st],radial:["",rt,st],conic:[Ai,rt,st]},VC,NC]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:tt()}],"gradient-via-pos":[{via:tt()}],"gradient-to-pos":[{to:tt()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:it()}],"border-w-x":[{"border-x":it()}],"border-w-y":[{"border-y":it()}],"border-w-s":[{"border-s":it()}],"border-w-e":[{"border-e":it()}],"border-w-t":[{"border-t":it()}],"border-w-r":[{"border-r":it()}],"border-w-b":[{"border-b":it()}],"border-w-l":[{"border-l":it()}],"divide-x":[{"divide-x":it()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":it()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...vt(),"hidden","none"]}],"divide-style":[{divide:[...vt(),"hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:[...vt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[At,rt,st]}],"outline-w":[{outline:["",At,Tr,na]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",g,go,po]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",v,go,po]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:it()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[At,na]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":it()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",b,go,po]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[At,rt,st]}],"mix-blend":[{"mix-blend":[...dt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":dt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[At]}],"mask-image-linear-from-pos":[{"mask-linear-from":xt()}],"mask-image-linear-to-pos":[{"mask-linear-to":xt()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":xt()}],"mask-image-t-to-pos":[{"mask-t-to":xt()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":xt()}],"mask-image-r-to-pos":[{"mask-r-to":xt()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":xt()}],"mask-image-b-to-pos":[{"mask-b-to":xt()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":xt()}],"mask-image-l-to-pos":[{"mask-l-to":xt()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":xt()}],"mask-image-x-to-pos":[{"mask-x-to":xt()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":xt()}],"mask-image-y-to-pos":[{"mask-y-to":xt()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[rt,st]}],"mask-image-radial-from-pos":[{"mask-radial-from":xt()}],"mask-image-radial-to-pos":[{"mask-radial-to":xt()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":V()}],"mask-image-conic-pos":[{"mask-conic":[At]}],"mask-image-conic-from-pos":[{"mask-conic-from":xt()}],"mask-image-conic-to-pos":[{"mask-conic-to":xt()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ct()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:X()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",rt,st]}],filter:[{filter:["","none",rt,st]}],blur:[{blur:nt()}],brightness:[{brightness:[At,rt,st]}],contrast:[{contrast:[At,rt,st]}],"drop-shadow":[{"drop-shadow":["","none",T,go,po]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",At,rt,st]}],"hue-rotate":[{"hue-rotate":[At,rt,st]}],invert:[{invert:["",At,rt,st]}],saturate:[{saturate:[At,rt,st]}],sepia:[{sepia:["",At,rt,st]}],"backdrop-filter":[{"backdrop-filter":["","none",rt,st]}],"backdrop-blur":[{"backdrop-blur":nt()}],"backdrop-brightness":[{"backdrop-brightness":[At,rt,st]}],"backdrop-contrast":[{"backdrop-contrast":[At,rt,st]}],"backdrop-grayscale":[{"backdrop-grayscale":["",At,rt,st]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[At,rt,st]}],"backdrop-invert":[{"backdrop-invert":["",At,rt,st]}],"backdrop-opacity":[{"backdrop-opacity":[At,rt,st]}],"backdrop-saturate":[{"backdrop-saturate":[At,rt,st]}],"backdrop-sepia":[{"backdrop-sepia":["",At,rt,st]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",rt,st]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[At,"initial",rt,st]}],ease:[{ease:["linear","initial",N,rt,st]}],delay:[{delay:[At,rt,st]}],animate:[{animate:["none",z,rt,st]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[M,rt,st]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:Tt()}],"rotate-x":[{"rotate-x":Tt()}],"rotate-y":[{"rotate-y":Tt()}],"rotate-z":[{"rotate-z":Tt()}],scale:[{scale:qt()}],"scale-x":[{"scale-x":qt()}],"scale-y":[{"scale-y":qt()}],"scale-z":[{"scale-z":qt()}],"scale-3d":["scale-3d"],skew:[{skew:Ot()}],"skew-x":[{"skew-x":Ot()}],"skew-y":[{"skew-y":Ot()}],transform:[{transform:[rt,st,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ct()}],"translate-x":[{"translate-x":Ct()}],"translate-y":[{"translate-y":Ct()}],"translate-z":[{"translate-z":Ct()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",rt,st]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",rt,st]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[At,Tr,na,jf]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},UC=vC(BC);function Kt(...n){return UC(Ib(n))}const kC=Wb("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Sn(d){var f=d,{className:n,variant:i,size:s,asChild:l=!1}=f,u=et(f,["className","variant","size","asChild"]);const h=l?Qb:"button";return S.jsx(h,O({"data-slot":"button",className:Kt(kC({variant:i,size:s,className:n}))},u))}function Tn(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("div",O({"data-slot":"card",className:Kt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n)},i))}function wn(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("div",O({"data-slot":"card-header",className:Kt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n)},i))}function An(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("div",O({"data-slot":"card-title",className:Kt("leading-none font-semibold",n)},i))}function En(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("div",O({"data-slot":"card-content",className:Kt("px-6",n)},i))}function ts(l){var u=l,{className:n,type:i}=u,s=et(u,["className","type"]);return S.jsx("input",O({type:i,"data-slot":"input",className:Kt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n)},s))}var qr=g0();const HC=m0(qr);var PC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pt=PC.reduce((n,i)=>{const s=Br(`Primitive.${i}`),l=w.forwardRef((u,d)=>{const p=u,{asChild:f}=p,h=et(p,["asChild"]),m=f?s:i;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(m,K(O({},h),{ref:d}))});return l.displayName=`Primitive.${i}`,K(O({},n),{[i]:l})},{});function GC(n,i){n&&qr.flushSync(()=>n.dispatchEvent(i))}var YC="Label",ox=w.forwardRef((n,i)=>S.jsx(Pt.label,K(O({},n),{ref:i,onMouseDown:s=>{var u;s.target.closest("button, input, select, textarea")||((u=n.onMouseDown)==null||u.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}})));ox.displayName=YC;var qC=ox;function ia(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(qC,O({"data-slot":"label",className:Kt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",n)},i))}function Uv(n,[i,s]){return Math.min(s,Math.max(i,n))}function Bt(n,i,{checkForDefaultPrevented:s=!0}={}){return function(u){if(n==null||n(u),s===!1||!u.defaultPrevented)return i==null?void 0:i(u)}}function Xr(n,i=[]){let s=[];function l(d,f){const h=w.createContext(f),m=s.length;s=[...s,f];const p=v=>{var z;const N=v,{scope:b,children:T}=N,C=et(N,["scope","children"]),M=((z=b==null?void 0:b[n])==null?void 0:z[m])||h,E=w.useMemo(()=>C,Object.values(C));return S.jsx(M.Provider,{value:E,children:T})};p.displayName=d+"Provider";function g(v,b){var M;const T=((M=b==null?void 0:b[n])==null?void 0:M[m])||h,C=w.useContext(T);if(C)return C;if(f!==void 0)return f;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[p,g]}const u=()=>{const d=s.map(f=>w.createContext(f));return function(h){const m=(h==null?void 0:h[n])||d;return w.useMemo(()=>({[`__scope${n}`]:K(O({},h),{[n]:m})}),[h,m])}};return u.scopeName=n,[l,XC(u,...i)]}function XC(...n){const i=n[0];if(n.length===1)return i;const s=()=>{const l=n.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(d){const f=l.reduce((h,{useScope:m,scopeName:p})=>{const v=m(d)[`__scope${p}`];return O(O({},h),v)},{});return w.useMemo(()=>({[`__scope${i.scopeName}`]:f}),[f])}};return s.scopeName=i.scopeName,s}function ux(n){const i=n+"CollectionProvider",[s,l]=Xr(i),[u,d]=s(i,{collectionRef:{current:null},itemMap:new Map}),f=M=>{const{scope:E,children:N}=M,z=Ei.useRef(null),A=Ei.useRef(new Map).current;return S.jsx(u,{scope:E,itemMap:A,collectionRef:z,children:N})};f.displayName=i;const h=n+"CollectionSlot",m=Br(h),p=Ei.forwardRef((M,E)=>{const{scope:N,children:z}=M,A=d(h,N),V=ae(E,A.collectionRef);return S.jsx(m,{ref:V,children:z})});p.displayName=h;const g=n+"CollectionItemSlot",v="data-radix-collection-item",b=Br(g),T=Ei.forwardRef((M,E)=>{const J=M,{scope:N,children:z}=J,A=et(J,["scope","children"]),V=Ei.useRef(null),j=ae(E,V),I=d(g,N);return Ei.useEffect(()=>(I.itemMap.set(V,O({ref:V},A)),()=>void I.itemMap.delete(V))),S.jsx(b,{[v]:"",ref:j,children:z})});T.displayName=g;function C(M){const E=d(n+"CollectionConsumer",M);return Ei.useCallback(()=>{const z=E.collectionRef.current;if(!z)return[];const A=Array.from(z.querySelectorAll(`[${v}]`));return Array.from(E.itemMap.values()).sort((I,J)=>A.indexOf(I.ref.current)-A.indexOf(J.ref.current))},[E.collectionRef,E.itemMap])}return[{Provider:f,Slot:p,ItemSlot:T},C,l]}var KC=w.createContext(void 0);function ah(n){const i=w.useContext(KC);return n||i||"ltr"}function Oi(n){const i=w.useRef(n);return w.useEffect(()=>{i.current=n}),w.useMemo(()=>(...s)=>{var l;return(l=i.current)==null?void 0:l.call(i,...s)},[])}function ZC(n,i=globalThis==null?void 0:globalThis.document){const s=Oi(n);w.useEffect(()=>{const l=u=>{u.key==="Escape"&&s(u)};return i.addEventListener("keydown",l,{capture:!0}),()=>i.removeEventListener("keydown",l,{capture:!0})},[s,i])}var QC="DismissableLayer",hd="dismissableLayer.update",FC="dismissableLayer.pointerDownOutside",IC="dismissableLayer.focusOutside",kv,cx=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),fx=w.forwardRef((n,i)=>{var Y;const J=n,{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:h}=J,m=et(J,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),p=w.useContext(cx),[g,v]=w.useState(null),b=(Y=g==null?void 0:g.ownerDocument)!=null?Y:globalThis==null?void 0:globalThis.document,[,T]=w.useState({}),C=ae(i,W=>v(W)),M=Array.from(p.layers),[E]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),N=M.indexOf(E),z=g?M.indexOf(g):-1,A=p.layersWithOutsidePointerEventsDisabled.size>0,V=z>=N,j=JC(W=>{const ft=W.target,ht=[...p.branches].some(lt=>lt.contains(ft));!V||ht||(u==null||u(W),f==null||f(W),W.defaultPrevented||h==null||h())},b),I=tR(W=>{const ft=W.target;[...p.branches].some(lt=>lt.contains(ft))||(d==null||d(W),f==null||f(W),W.defaultPrevented||h==null||h())},b);return ZC(W=>{z===p.layers.size-1&&(l==null||l(W),!W.defaultPrevented&&h&&(W.preventDefault(),h()))},b),w.useEffect(()=>{if(g)return s&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(kv=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(g)),p.layers.add(g),Hv(),()=>{s&&p.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=kv)}},[g,b,s,p]),w.useEffect(()=>()=>{g&&(p.layers.delete(g),p.layersWithOutsidePointerEventsDisabled.delete(g),Hv())},[g,p]),w.useEffect(()=>{const W=()=>T({});return document.addEventListener(hd,W),()=>document.removeEventListener(hd,W)},[]),S.jsx(Pt.div,K(O({},m),{ref:C,style:O({pointerEvents:A?V?"auto":"none":void 0},n.style),onFocusCapture:Bt(n.onFocusCapture,I.onFocusCapture),onBlurCapture:Bt(n.onBlurCapture,I.onBlurCapture),onPointerDownCapture:Bt(n.onPointerDownCapture,j.onPointerDownCapture)}))});fx.displayName=QC;var WC="DismissableLayerBranch",$C=w.forwardRef((n,i)=>{const s=w.useContext(cx),l=w.useRef(null),u=ae(i,l);return w.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),S.jsx(Pt.div,K(O({},n),{ref:u}))});$C.displayName=WC;function JC(n,i=globalThis==null?void 0:globalThis.document){const s=Oi(n),l=w.useRef(!1),u=w.useRef(()=>{});return w.useEffect(()=>{const d=h=>{if(h.target&&!l.current){let m=function(){dx(FC,s,p,{discrete:!0})};const p={originalEvent:h};h.pointerType==="touch"?(i.removeEventListener("click",u.current),u.current=m,i.addEventListener("click",u.current,{once:!0})):m()}else i.removeEventListener("click",u.current);l.current=!1},f=window.setTimeout(()=>{i.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(f),i.removeEventListener("pointerdown",d),i.removeEventListener("click",u.current)}},[i,s]),{onPointerDownCapture:()=>l.current=!0}}function tR(n,i=globalThis==null?void 0:globalThis.document){const s=Oi(n),l=w.useRef(!1);return w.useEffect(()=>{const u=d=>{d.target&&!l.current&&dx(IC,s,{originalEvent:d},{discrete:!1})};return i.addEventListener("focusin",u),()=>i.removeEventListener("focusin",u)},[i,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Hv(){const n=new CustomEvent(hd);document.dispatchEvent(n)}function dx(n,i,s,{discrete:l}){const u=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});i&&u.addEventListener(n,i,{once:!0}),l?GC(u,d):u.dispatchEvent(d)}var _f=0;function eR(){w.useEffect(()=>{var i,s;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(i=n[0])!=null?i:Pv()),document.body.insertAdjacentElement("beforeend",(s=n[1])!=null?s:Pv()),_f++,()=>{_f===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),_f--}},[])}function Pv(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Vf="focusScope.autoFocusOnMount",zf="focusScope.autoFocusOnUnmount",Gv={bubbles:!1,cancelable:!0},nR="FocusScope",hx=w.forwardRef((n,i)=>{const M=n,{loop:s=!1,trapped:l=!1,onMountAutoFocus:u,onUnmountAutoFocus:d}=M,f=et(M,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[h,m]=w.useState(null),p=Oi(u),g=Oi(d),v=w.useRef(null),b=ae(i,E=>m(E)),T=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(l){let E=function(V){if(T.paused||!h)return;const j=V.target;h.contains(j)?v.current=j:Ci(v.current,{select:!0})},N=function(V){if(T.paused||!h)return;const j=V.relatedTarget;j!==null&&(h.contains(j)||Ci(v.current,{select:!0}))},z=function(V){if(document.activeElement===document.body)for(const I of V)I.removedNodes.length>0&&Ci(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",N);const A=new MutationObserver(z);return h&&A.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",N),A.disconnect()}}},[l,h,T.paused]),w.useEffect(()=>{if(h){qv.add(T);const E=document.activeElement;if(!h.contains(E)){const z=new CustomEvent(Vf,Gv);h.addEventListener(Vf,p),h.dispatchEvent(z),z.defaultPrevented||(iR(oR(mx(h)),{select:!0}),document.activeElement===E&&Ci(h))}return()=>{h.removeEventListener(Vf,p),setTimeout(()=>{const z=new CustomEvent(zf,Gv);h.addEventListener(zf,g),h.dispatchEvent(z),z.defaultPrevented||Ci(E!=null?E:document.body,{select:!0}),h.removeEventListener(zf,g),qv.remove(T)},0)}}},[h,p,g,T]);const C=w.useCallback(E=>{if(!s&&!l||T.paused)return;const N=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,z=document.activeElement;if(N&&z){const A=E.currentTarget,[V,j]=aR(A);V&&j?!E.shiftKey&&z===j?(E.preventDefault(),s&&Ci(V,{select:!0})):E.shiftKey&&z===V&&(E.preventDefault(),s&&Ci(j,{select:!0})):z===A&&E.preventDefault()}},[s,l,T.paused]);return S.jsx(Pt.div,K(O({tabIndex:-1},f),{ref:b,onKeyDown:C}))});hx.displayName=nR;function iR(n,{select:i=!1}={}){const s=document.activeElement;for(const l of n)if(Ci(l,{select:i}),document.activeElement!==s)return}function aR(n){const i=mx(n),s=Yv(i,n),l=Yv(i.reverse(),n);return[s,l]}function mx(n){const i=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const u=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||u?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)i.push(s.currentNode);return i}function Yv(n,i){for(const s of n)if(!sR(s,{upTo:i}))return s}function sR(n,{upTo:i}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(i!==void 0&&n===i)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function rR(n){return n instanceof HTMLInputElement&&"select"in n}function Ci(n,{select:i=!1}={}){if(n&&n.focus){const s=document.activeElement;n.focus({preventScroll:!0}),n!==s&&rR(n)&&i&&n.select()}}var qv=lR();function lR(){let n=[];return{add(i){const s=n[0];i!==s&&(s==null||s.pause()),n=Xv(n,i),n.unshift(i)},remove(i){var s;n=Xv(n,i),(s=n[0])==null||s.resume()}}}function Xv(n,i){const s=[...n],l=s.indexOf(i);return l!==-1&&s.splice(l,1),s}function oR(n){return n.filter(i=>i.tagName!=="A")}var we=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},uR=p0[" useId ".trim().toString()]||(()=>{}),cR=0;function Kr(n){const[i,s]=w.useState(uR());return we(()=>{s(l=>l!=null?l:String(cR++))},[n]),n||(i?`radix-${i}`:"")}const fR=["top","right","bottom","left"],Ni=Math.min,Ye=Math.max,Bo=Math.round,yo=Math.floor,On=n=>({x:n,y:n}),dR={left:"right",right:"left",bottom:"top",top:"bottom"},hR={start:"end",end:"start"};function md(n,i,s){return Ye(n,Ni(i,s))}function $n(n,i){return typeof n=="function"?n(i):n}function Jn(n){return n.split("-")[0]}function bs(n){return n.split("-")[1]}function sh(n){return n==="x"?"y":"x"}function rh(n){return n==="y"?"height":"width"}function Fn(n){return["top","bottom"].includes(Jn(n))?"y":"x"}function lh(n){return sh(Fn(n))}function mR(n,i,s){s===void 0&&(s=!1);const l=bs(n),u=lh(n),d=rh(u);let f=u==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return i.reference[d]>i.floating[d]&&(f=Uo(f)),[f,Uo(f)]}function pR(n){const i=Uo(n);return[pd(n),i,pd(i)]}function pd(n){return n.replace(/start|end/g,i=>hR[i])}function gR(n,i,s){const l=["left","right"],u=["right","left"],d=["top","bottom"],f=["bottom","top"];switch(n){case"top":case"bottom":return s?i?u:l:i?l:u;case"left":case"right":return i?d:f;default:return[]}}function yR(n,i,s,l){const u=bs(n);let d=gR(Jn(n),s==="start",l);return u&&(d=d.map(f=>f+"-"+u),i&&(d=d.concat(d.map(pd)))),d}function Uo(n){return n.replace(/left|right|bottom|top/g,i=>dR[i])}function vR(n){return O({top:0,right:0,bottom:0,left:0},n)}function px(n){return typeof n!="number"?vR(n):{top:n,right:n,bottom:n,left:n}}function ko(n){const{x:i,y:s,width:l,height:u}=n;return{width:l,height:u,top:s,left:i,right:i+l,bottom:s+u,x:i,y:s}}function Kv(n,i,s){let{reference:l,floating:u}=n;const d=Fn(i),f=lh(i),h=rh(f),m=Jn(i),p=d==="y",g=l.x+l.width/2-u.width/2,v=l.y+l.height/2-u.height/2,b=l[h]/2-u[h]/2;let T;switch(m){case"top":T={x:g,y:l.y-u.height};break;case"bottom":T={x:g,y:l.y+l.height};break;case"right":T={x:l.x+l.width,y:v};break;case"left":T={x:l.x-u.width,y:v};break;default:T={x:l.x,y:l.y}}switch(bs(i)){case"start":T[f]-=b*(s&&p?-1:1);break;case"end":T[f]+=b*(s&&p?-1:1);break}return T}const bR=(n,i,s)=>cn(null,null,function*(){const{placement:l="bottom",strategy:u="absolute",middleware:d=[],platform:f}=s,h=d.filter(Boolean),m=yield f.isRTL==null?void 0:f.isRTL(i);let p=yield f.getElementRects({reference:n,floating:i,strategy:u}),{x:g,y:v}=Kv(p,l,m),b=l,T={},C=0;for(let M=0;M<h.length;M++){const{name:E,fn:N}=h[M],{x:z,y:A,data:V,reset:j}=yield N({x:g,y:v,initialPlacement:l,placement:b,strategy:u,middlewareData:T,rects:p,platform:f,elements:{reference:n,floating:i}});g=z!=null?z:g,v=A!=null?A:v,T=K(O({},T),{[E]:O(O({},T[E]),V)}),j&&C<=50&&(C++,typeof j=="object"&&(j.placement&&(b=j.placement),j.rects&&(p=j.rects===!0?yield f.getElementRects({reference:n,floating:i,strategy:u}):j.rects),{x:g,y:v}=Kv(p,b,m)),M=-1)}return{x:g,y:v,placement:b,strategy:u,middlewareData:T}});function Ur(n,i){return cn(this,null,function*(){var s;i===void 0&&(i={});const{x:l,y:u,platform:d,rects:f,elements:h,strategy:m}=n,{boundary:p="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:b=!1,padding:T=0}=$n(i,n),C=px(T),E=h[b?v==="floating"?"reference":"floating":v],N=ko(yield d.getClippingRect({element:(s=yield d.isElement==null?void 0:d.isElement(E))==null||s?E:E.contextElement||(yield d.getDocumentElement==null?void 0:d.getDocumentElement(h.floating)),boundary:p,rootBoundary:g,strategy:m})),z=v==="floating"?{x:l,y:u,width:f.floating.width,height:f.floating.height}:f.reference,A=yield d.getOffsetParent==null?void 0:d.getOffsetParent(h.floating),V=(yield d.isElement==null?void 0:d.isElement(A))?(yield d.getScale==null?void 0:d.getScale(A))||{x:1,y:1}:{x:1,y:1},j=ko(d.convertOffsetParentRelativeRectToViewportRelativeRect?yield d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:z,offsetParent:A,strategy:m}):z);return{top:(N.top-j.top+C.top)/V.y,bottom:(j.bottom-N.bottom+C.bottom)/V.y,left:(N.left-j.left+C.left)/V.x,right:(j.right-N.right+C.right)/V.x}})}const xR=n=>({name:"arrow",options:n,fn(s){return cn(this,null,function*(){const{x:l,y:u,placement:d,rects:f,platform:h,elements:m,middlewareData:p}=s,{element:g,padding:v=0}=$n(n,s)||{};if(g==null)return{};const b=px(v),T={x:l,y:u},C=lh(d),M=rh(C),E=yield h.getDimensions(g),N=C==="y",z=N?"top":"left",A=N?"bottom":"right",V=N?"clientHeight":"clientWidth",j=f.reference[M]+f.reference[C]-T[C]-f.floating[M],I=T[C]-f.reference[C],J=yield h.getOffsetParent==null?void 0:h.getOffsetParent(g);let Y=J?J[V]:0;(!Y||!(yield h.isElement==null?void 0:h.isElement(J)))&&(Y=m.floating[V]||f.floating[M]);const W=j/2-I/2,ft=Y/2-E[M]/2-1,ht=Ni(b[z],ft),lt=Ni(b[A],ft),bt=ht,Et=Y-E[M]-lt,ut=Y/2-E[M]/2+W,B=md(bt,ut,Et),q=!p.arrow&&bs(d)!=null&&ut!==B&&f.reference[M]/2-(ut<bt?ht:lt)-E[M]/2<0,P=q?ut<bt?ut-bt:ut-Et:0;return{[C]:T[C]+P,data:O({[C]:B,centerOffset:ut-B-P},q&&{alignmentOffset:P}),reset:q}})}}),SR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,fn(s){return cn(this,null,function*(){var l,u;const{placement:d,middlewareData:f,rects:h,initialPlacement:m,platform:p,elements:g}=s,B=$n(n,s),{mainAxis:v=!0,crossAxis:b=!0,fallbackPlacements:T,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:E=!0}=B,N=et(B,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((l=f.arrow)!=null&&l.alignmentOffset)return{};const z=Jn(d),A=Fn(m),V=Jn(m)===m,j=yield p.isRTL==null?void 0:p.isRTL(g.floating),I=T||(V||!E?[Uo(m)]:pR(m)),J=M!=="none";!T&&J&&I.push(...yR(m,E,M,j));const Y=[m,...I],W=yield Ur(s,N),ft=[];let ht=((u=f.flip)==null?void 0:u.overflows)||[];if(v&&ft.push(W[z]),b){const q=mR(d,h,j);ft.push(W[q[0]],W[q[1]])}if(ht=[...ht,{placement:d,overflows:ft}],!ft.every(q=>q<=0)){var lt,bt;const q=(((lt=f.flip)==null?void 0:lt.index)||0)+1,P=Y[q];if(P){var Et;const D=b==="alignment"?A!==Fn(P):!1,X=((Et=ht[0])==null?void 0:Et.overflows[0])>0;if(!D||X)return{data:{index:q,overflows:ht},reset:{placement:P}}}let ct=(bt=ht.filter(D=>D.overflows[0]<=0).sort((D,X)=>D.overflows[1]-X.overflows[1])[0])==null?void 0:bt.placement;if(!ct)switch(C){case"bestFit":{var ut;const D=(ut=ht.filter(X=>{if(J){const tt=Fn(X.placement);return tt===A||tt==="y"}return!0}).map(X=>[X.placement,X.overflows.filter(tt=>tt>0).reduce((tt,$)=>tt+$,0)]).sort((X,tt)=>X[1]-tt[1])[0])==null?void 0:ut[0];D&&(ct=D);break}case"initialPlacement":ct=m;break}if(d!==ct)return{reset:{placement:ct}}}return{}})}}};function Zv(n,i){return{top:n.top-i.height,right:n.right-i.width,bottom:n.bottom-i.height,left:n.left-i.width}}function Qv(n){return fR.some(i=>n[i]>=0)}const TR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,fn(s){return cn(this,null,function*(){const{rects:l}=s,f=$n(n,s),{strategy:u="referenceHidden"}=f,d=et(f,["strategy"]);switch(u){case"referenceHidden":{const h=yield Ur(s,K(O({},d),{elementContext:"reference"})),m=Zv(h,l.reference);return{data:{referenceHiddenOffsets:m,referenceHidden:Qv(m)}}}case"escaped":{const h=yield Ur(s,K(O({},d),{altBoundary:!0})),m=Zv(h,l.floating);return{data:{escapedOffsets:m,escaped:Qv(m)}}}default:return{}}})}}};function wR(n,i){return cn(this,null,function*(){const{placement:s,platform:l,elements:u}=n,d=yield l.isRTL==null?void 0:l.isRTL(u.floating),f=Jn(s),h=bs(s),m=Fn(s)==="y",p=["left","top"].includes(f)?-1:1,g=d&&m?-1:1,v=$n(i,n);let{mainAxis:b,crossAxis:T,alignmentAxis:C}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return h&&typeof C=="number"&&(T=h==="end"?C*-1:C),m?{x:T*g,y:b*p}:{x:b*p,y:T*g}})}const AR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,fn(s){return cn(this,null,function*(){var l,u;const{x:d,y:f,placement:h,middlewareData:m}=s,p=yield wR(s,n);return h===((l=m.offset)==null?void 0:l.placement)&&(u=m.arrow)!=null&&u.alignmentOffset?{}:{x:d+p.x,y:f+p.y,data:K(O({},p),{placement:h})}})}}},ER=function(n){return n===void 0&&(n={}),{name:"shift",options:n,fn(s){return cn(this,null,function*(){const{x:l,y:u,placement:d}=s,N=$n(n,s),{mainAxis:f=!0,crossAxis:h=!1,limiter:m={fn:z=>{let{x:A,y:V}=z;return{x:A,y:V}}}}=N,p=et(N,["mainAxis","crossAxis","limiter"]),g={x:l,y:u},v=yield Ur(s,p),b=Fn(Jn(d)),T=sh(b);let C=g[T],M=g[b];if(f){const z=T==="y"?"top":"left",A=T==="y"?"bottom":"right",V=C+v[z],j=C-v[A];C=md(V,C,j)}if(h){const z=b==="y"?"top":"left",A=b==="y"?"bottom":"right",V=M+v[z],j=M-v[A];M=md(V,M,j)}const E=m.fn(K(O({},s),{[T]:C,[b]:M}));return K(O({},E),{data:{x:E.x-l,y:E.y-u,enabled:{[T]:f,[b]:h}}})})}}},MR=function(n){return n===void 0&&(n={}),{options:n,fn(i){const{x:s,y:l,placement:u,rects:d,middlewareData:f}=i,{offset:h=0,mainAxis:m=!0,crossAxis:p=!0}=$n(n,i),g={x:s,y:l},v=Fn(u),b=sh(v);let T=g[b],C=g[v];const M=$n(h,i),E=typeof M=="number"?{mainAxis:M,crossAxis:0}:O({mainAxis:0,crossAxis:0},M);if(m){const A=b==="y"?"height":"width",V=d.reference[b]-d.floating[A]+E.mainAxis,j=d.reference[b]+d.reference[A]-E.mainAxis;T<V?T=V:T>j&&(T=j)}if(p){var N,z;const A=b==="y"?"width":"height",V=["top","left"].includes(Jn(u)),j=d.reference[v]-d.floating[A]+(V&&((N=f.offset)==null?void 0:N[v])||0)+(V?0:E.crossAxis),I=d.reference[v]+d.reference[A]+(V?0:((z=f.offset)==null?void 0:z[v])||0)-(V?E.crossAxis:0);C<j?C=j:C>I&&(C=I)}return{[b]:T,[v]:C}}}},CR=function(n){return n===void 0&&(n={}),{name:"size",options:n,fn(s){return cn(this,null,function*(){var l,u;const{placement:d,rects:f,platform:h,elements:m}=s,ht=$n(n,s),{apply:p=()=>{}}=ht,g=et(ht,["apply"]),v=yield Ur(s,g),b=Jn(d),T=bs(d),C=Fn(d)==="y",{width:M,height:E}=f.floating;let N,z;b==="top"||b==="bottom"?(N=b,z=T===((yield h.isRTL==null?void 0:h.isRTL(m.floating))?"start":"end")?"left":"right"):(z=b,N=T==="end"?"top":"bottom");const A=E-v.top-v.bottom,V=M-v.left-v.right,j=Ni(E-v[N],A),I=Ni(M-v[z],V),J=!s.middlewareData.shift;let Y=j,W=I;if((l=s.middlewareData.shift)!=null&&l.enabled.x&&(W=V),(u=s.middlewareData.shift)!=null&&u.enabled.y&&(Y=A),J&&!T){const lt=Ye(v.left,0),bt=Ye(v.right,0),Et=Ye(v.top,0),ut=Ye(v.bottom,0);C?W=M-2*(lt!==0||bt!==0?lt+bt:Ye(v.left,v.right)):Y=E-2*(Et!==0||ut!==0?Et+ut:Ye(v.top,v.bottom))}yield p(K(O({},s),{availableWidth:W,availableHeight:Y}));const ft=yield h.getDimensions(m.floating);return M!==ft.width||E!==ft.height?{reset:{rects:!0}}:{}})}}};function Ko(){return typeof window!="undefined"}function xs(n){return gx(n)?(n.nodeName||"").toLowerCase():"#document"}function qe(n){var i;return(n==null||(i=n.ownerDocument)==null?void 0:i.defaultView)||window}function jn(n){var i;return(i=(gx(n)?n.ownerDocument:n.document)||window.document)==null?void 0:i.documentElement}function gx(n){return Ko()?n instanceof Node||n instanceof qe(n).Node:!1}function hn(n){return Ko()?n instanceof Element||n instanceof qe(n).Element:!1}function Nn(n){return Ko()?n instanceof HTMLElement||n instanceof qe(n).HTMLElement:!1}function Fv(n){return!Ko()||typeof ShadowRoot=="undefined"?!1:n instanceof ShadowRoot||n instanceof qe(n).ShadowRoot}function Zr(n){const{overflow:i,overflowX:s,overflowY:l,display:u}=mn(n);return/auto|scroll|overlay|hidden|clip/.test(i+l+s)&&!["inline","contents"].includes(u)}function RR(n){return["table","td","th"].includes(xs(n))}function Zo(n){return[":popover-open",":modal"].some(i=>{try{return n.matches(i)}catch(s){return!1}})}function oh(n){const i=uh(),s=hn(n)?mn(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!i&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!i&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function DR(n){let i=ji(n);for(;Nn(i)&&!hs(i);){if(oh(i))return i;if(Zo(i))return null;i=ji(i)}return null}function uh(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function hs(n){return["html","body","#document"].includes(xs(n))}function mn(n){return qe(n).getComputedStyle(n)}function Qo(n){return hn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function ji(n){if(xs(n)==="html")return n;const i=n.assignedSlot||n.parentNode||Fv(n)&&n.host||jn(n);return Fv(i)?i.host:i}function yx(n){const i=ji(n);return hs(i)?n.ownerDocument?n.ownerDocument.body:n.body:Nn(i)&&Zr(i)?i:yx(i)}function kr(n,i,s){var l;i===void 0&&(i=[]),s===void 0&&(s=!0);const u=yx(n),d=u===((l=n.ownerDocument)==null?void 0:l.body),f=qe(u);if(d){const h=gd(f);return i.concat(f,f.visualViewport||[],Zr(u)?u:[],h&&s?kr(h):[])}return i.concat(u,kr(u,[],s))}function gd(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function vx(n){const i=mn(n);let s=parseFloat(i.width)||0,l=parseFloat(i.height)||0;const u=Nn(n),d=u?n.offsetWidth:s,f=u?n.offsetHeight:l,h=Bo(s)!==d||Bo(l)!==f;return h&&(s=d,l=f),{width:s,height:l,$:h}}function ch(n){return hn(n)?n:n.contextElement}function us(n){const i=ch(n);if(!Nn(i))return On(1);const s=i.getBoundingClientRect(),{width:l,height:u,$:d}=vx(i);let f=(d?Bo(s.width):s.width)/l,h=(d?Bo(s.height):s.height)/u;return(!f||!Number.isFinite(f))&&(f=1),(!h||!Number.isFinite(h))&&(h=1),{x:f,y:h}}const OR=On(0);function bx(n){const i=qe(n);return!uh()||!i.visualViewport?OR:{x:i.visualViewport.offsetLeft,y:i.visualViewport.offsetTop}}function NR(n,i,s){return i===void 0&&(i=!1),!s||i&&s!==qe(n)?!1:i}function ua(n,i,s,l){i===void 0&&(i=!1),s===void 0&&(s=!1);const u=n.getBoundingClientRect(),d=ch(n);let f=On(1);i&&(l?hn(l)&&(f=us(l)):f=us(n));const h=NR(d,s,l)?bx(d):On(0);let m=(u.left+h.x)/f.x,p=(u.top+h.y)/f.y,g=u.width/f.x,v=u.height/f.y;if(d){const b=qe(d),T=l&&hn(l)?qe(l):l;let C=b,M=gd(C);for(;M&&l&&T!==C;){const E=us(M),N=M.getBoundingClientRect(),z=mn(M),A=N.left+(M.clientLeft+parseFloat(z.paddingLeft))*E.x,V=N.top+(M.clientTop+parseFloat(z.paddingTop))*E.y;m*=E.x,p*=E.y,g*=E.x,v*=E.y,m+=A,p+=V,C=qe(M),M=gd(C)}}return ko({width:g,height:v,x:m,y:p})}function fh(n,i){const s=Qo(n).scrollLeft;return i?i.left+s:ua(jn(n)).left+s}function xx(n,i,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),u=l.left+i.scrollLeft-(s?0:fh(n,l)),d=l.top+i.scrollTop;return{x:u,y:d}}function jR(n){let{elements:i,rect:s,offsetParent:l,strategy:u}=n;const d=u==="fixed",f=jn(l),h=i?Zo(i.floating):!1;if(l===f||h&&d)return s;let m={scrollLeft:0,scrollTop:0},p=On(1);const g=On(0),v=Nn(l);if((v||!v&&!d)&&((xs(l)!=="body"||Zr(f))&&(m=Qo(l)),Nn(l))){const T=ua(l);p=us(l),g.x=T.x+l.clientLeft,g.y=T.y+l.clientTop}const b=f&&!v&&!d?xx(f,m,!0):On(0);return{width:s.width*p.x,height:s.height*p.y,x:s.x*p.x-m.scrollLeft*p.x+g.x+b.x,y:s.y*p.y-m.scrollTop*p.y+g.y+b.y}}function _R(n){return Array.from(n.getClientRects())}function VR(n){const i=jn(n),s=Qo(n),l=n.ownerDocument.body,u=Ye(i.scrollWidth,i.clientWidth,l.scrollWidth,l.clientWidth),d=Ye(i.scrollHeight,i.clientHeight,l.scrollHeight,l.clientHeight);let f=-s.scrollLeft+fh(n);const h=-s.scrollTop;return mn(l).direction==="rtl"&&(f+=Ye(i.clientWidth,l.clientWidth)-u),{width:u,height:d,x:f,y:h}}function zR(n,i){const s=qe(n),l=jn(n),u=s.visualViewport;let d=l.clientWidth,f=l.clientHeight,h=0,m=0;if(u){d=u.width,f=u.height;const p=uh();(!p||p&&i==="fixed")&&(h=u.offsetLeft,m=u.offsetTop)}return{width:d,height:f,x:h,y:m}}function LR(n,i){const s=ua(n,!0,i==="fixed"),l=s.top+n.clientTop,u=s.left+n.clientLeft,d=Nn(n)?us(n):On(1),f=n.clientWidth*d.x,h=n.clientHeight*d.y,m=u*d.x,p=l*d.y;return{width:f,height:h,x:m,y:p}}function Iv(n,i,s){let l;if(i==="viewport")l=zR(n,s);else if(i==="document")l=VR(jn(n));else if(hn(i))l=LR(i,s);else{const u=bx(n);l={x:i.x-u.x,y:i.y-u.y,width:i.width,height:i.height}}return ko(l)}function Sx(n,i){const s=ji(n);return s===i||!hn(s)||hs(s)?!1:mn(s).position==="fixed"||Sx(s,i)}function BR(n,i){const s=i.get(n);if(s)return s;let l=kr(n,[],!1).filter(h=>hn(h)&&xs(h)!=="body"),u=null;const d=mn(n).position==="fixed";let f=d?ji(n):n;for(;hn(f)&&!hs(f);){const h=mn(f),m=oh(f);!m&&h.position==="fixed"&&(u=null),(d?!m&&!u:!m&&h.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||Zr(f)&&!m&&Sx(n,f))?l=l.filter(g=>g!==f):u=h,f=ji(f)}return i.set(n,l),l}function UR(n){let{element:i,boundary:s,rootBoundary:l,strategy:u}=n;const f=[...s==="clippingAncestors"?Zo(i)?[]:BR(i,this._c):[].concat(s),l],h=f[0],m=f.reduce((p,g)=>{const v=Iv(i,g,u);return p.top=Ye(v.top,p.top),p.right=Ni(v.right,p.right),p.bottom=Ni(v.bottom,p.bottom),p.left=Ye(v.left,p.left),p},Iv(i,h,u));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function kR(n){const{width:i,height:s}=vx(n);return{width:i,height:s}}function HR(n,i,s){const l=Nn(i),u=jn(i),d=s==="fixed",f=ua(n,!0,d,i);let h={scrollLeft:0,scrollTop:0};const m=On(0);function p(){m.x=fh(u)}if(l||!l&&!d)if((xs(i)!=="body"||Zr(u))&&(h=Qo(i)),l){const T=ua(i,!0,d,i);m.x=T.x+i.clientLeft,m.y=T.y+i.clientTop}else u&&p();d&&!l&&u&&p();const g=u&&!l&&!d?xx(u,h):On(0),v=f.left+h.scrollLeft-m.x-g.x,b=f.top+h.scrollTop-m.y-g.y;return{x:v,y:b,width:f.width,height:f.height}}function Lf(n){return mn(n).position==="static"}function Wv(n,i){if(!Nn(n)||mn(n).position==="fixed")return null;if(i)return i(n);let s=n.offsetParent;return jn(n)===s&&(s=s.ownerDocument.body),s}function Tx(n,i){const s=qe(n);if(Zo(n))return s;if(!Nn(n)){let u=ji(n);for(;u&&!hs(u);){if(hn(u)&&!Lf(u))return u;u=ji(u)}return s}let l=Wv(n,i);for(;l&&RR(l)&&Lf(l);)l=Wv(l,i);return l&&hs(l)&&Lf(l)&&!oh(l)?s:l||DR(n)||s}const PR=function(n){return cn(this,null,function*(){const i=this.getOffsetParent||Tx,s=this.getDimensions,l=yield s(n.floating);return{reference:HR(n.reference,yield i(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}})};function GR(n){return mn(n).direction==="rtl"}const YR={convertOffsetParentRelativeRectToViewportRelativeRect:jR,getDocumentElement:jn,getClippingRect:UR,getOffsetParent:Tx,getElementRects:PR,getClientRects:_R,getDimensions:kR,getScale:us,isElement:hn,isRTL:GR};function wx(n,i){return n.x===i.x&&n.y===i.y&&n.width===i.width&&n.height===i.height}function qR(n,i){let s=null,l;const u=jn(n);function d(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function f(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),d();const p=n.getBoundingClientRect(),{left:g,top:v,width:b,height:T}=p;if(h||i(),!b||!T)return;const C=yo(v),M=yo(u.clientWidth-(g+b)),E=yo(u.clientHeight-(v+T)),N=yo(g),A={rootMargin:-C+"px "+-M+"px "+-E+"px "+-N+"px",threshold:Ye(0,Ni(1,m))||1};let V=!0;function j(I){const J=I[0].intersectionRatio;if(J!==m){if(!V)return f();J?f(!1,J):l=setTimeout(()=>{f(!1,1e-7)},1e3)}J===1&&!wx(p,n.getBoundingClientRect())&&f(),V=!1}try{s=new IntersectionObserver(j,K(O({},A),{root:u.ownerDocument}))}catch(I){s=new IntersectionObserver(j,A)}s.observe(n)}return f(!0),d}function XR(n,i,s,l){l===void 0&&(l={});const{ancestorScroll:u=!0,ancestorResize:d=!0,elementResize:f=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=l,p=ch(n),g=u||d?[...p?kr(p):[],...kr(i)]:[];g.forEach(N=>{u&&N.addEventListener("scroll",s,{passive:!0}),d&&N.addEventListener("resize",s)});const v=p&&h?qR(p,s):null;let b=-1,T=null;f&&(T=new ResizeObserver(N=>{let[z]=N;z&&z.target===p&&T&&(T.unobserve(i),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var A;(A=T)==null||A.observe(i)})),s()}),p&&!m&&T.observe(p),T.observe(i));let C,M=m?ua(n):null;m&&E();function E(){const N=ua(n);M&&!wx(M,N)&&s(),M=N,C=requestAnimationFrame(E)}return s(),()=>{var N;g.forEach(z=>{u&&z.removeEventListener("scroll",s),d&&z.removeEventListener("resize",s)}),v==null||v(),(N=T)==null||N.disconnect(),T=null,m&&cancelAnimationFrame(C)}}const KR=AR,ZR=ER,QR=SR,FR=CR,IR=TR,$v=xR,WR=MR,$R=(n,i,s)=>{const l=new Map,u=O({platform:YR},s),d=K(O({},u.platform),{_c:l});return bR(n,i,K(O({},u),{platform:d}))};var Do=typeof document!="undefined"?w.useLayoutEffect:w.useEffect;function Ho(n,i){if(n===i)return!0;if(typeof n!=typeof i)return!1;if(typeof n=="function"&&n.toString()===i.toString())return!0;let s,l,u;if(n&&i&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==i.length)return!1;for(l=s;l--!==0;)if(!Ho(n[l],i[l]))return!1;return!0}if(u=Object.keys(n),s=u.length,s!==Object.keys(i).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(i,u[l]))return!1;for(l=s;l--!==0;){const d=u[l];if(!(d==="_owner"&&n.$$typeof)&&!Ho(n[d],i[d]))return!1}return!0}return n!==n&&i!==i}function Ax(n){return typeof window=="undefined"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Jv(n,i){const s=Ax(n);return Math.round(i*s)/s}function Bf(n){const i=w.useRef(n);return Do(()=>{i.current=n}),i}function JR(n){n===void 0&&(n={});const{placement:i="bottom",strategy:s="absolute",middleware:l=[],platform:u,elements:{reference:d,floating:f}={},transform:h=!0,whileElementsMounted:m,open:p}=n,[g,v]=w.useState({x:0,y:0,strategy:s,placement:i,middlewareData:{},isPositioned:!1}),[b,T]=w.useState(l);Ho(b,l)||T(l);const[C,M]=w.useState(null),[E,N]=w.useState(null),z=w.useCallback(P=>{P!==I.current&&(I.current=P,M(P))},[]),A=w.useCallback(P=>{P!==J.current&&(J.current=P,N(P))},[]),V=d||C,j=f||E,I=w.useRef(null),J=w.useRef(null),Y=w.useRef(g),W=m!=null,ft=Bf(m),ht=Bf(u),lt=Bf(p),bt=w.useCallback(()=>{if(!I.current||!J.current)return;const P={placement:i,strategy:s,middleware:b};ht.current&&(P.platform=ht.current),$R(I.current,J.current,P).then(ct=>{const D=K(O({},ct),{isPositioned:lt.current!==!1});Et.current&&!Ho(Y.current,D)&&(Y.current=D,qr.flushSync(()=>{v(D)}))})},[b,i,s,ht,lt]);Do(()=>{p===!1&&Y.current.isPositioned&&(Y.current.isPositioned=!1,v(P=>K(O({},P),{isPositioned:!1})))},[p]);const Et=w.useRef(!1);Do(()=>(Et.current=!0,()=>{Et.current=!1}),[]),Do(()=>{if(V&&(I.current=V),j&&(J.current=j),V&&j){if(ft.current)return ft.current(V,j,bt);bt()}},[V,j,bt,ft,W]);const ut=w.useMemo(()=>({reference:I,floating:J,setReference:z,setFloating:A}),[z,A]),B=w.useMemo(()=>({reference:V,floating:j}),[V,j]),q=w.useMemo(()=>{const P={position:s,left:0,top:0};if(!B.floating)return P;const ct=Jv(B.floating,g.x),D=Jv(B.floating,g.y);return h?O(K(O({},P),{transform:"translate("+ct+"px, "+D+"px)"}),Ax(B.floating)>=1.5&&{willChange:"transform"}):{position:s,left:ct,top:D}},[s,h,B.floating,g.x,g.y]);return w.useMemo(()=>K(O({},g),{update:bt,refs:ut,elements:B,floatingStyles:q}),[g,bt,ut,B,q])}const tD=n=>{function i(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:u}=typeof n=="function"?n(s):n;return l&&i(l)?l.current!=null?$v({element:l.current,padding:u}).fn(s):{}:l?$v({element:l,padding:u}).fn(s):{}}}},eD=(n,i)=>K(O({},KR(n)),{options:[n,i]}),nD=(n,i)=>K(O({},ZR(n)),{options:[n,i]}),iD=(n,i)=>K(O({},WR(n)),{options:[n,i]}),aD=(n,i)=>K(O({},QR(n)),{options:[n,i]}),sD=(n,i)=>K(O({},FR(n)),{options:[n,i]}),rD=(n,i)=>K(O({},IR(n)),{options:[n,i]}),lD=(n,i)=>K(O({},tD(n)),{options:[n,i]});var oD="Arrow",Ex=w.forwardRef((n,i)=>{const f=n,{children:s,width:l=10,height:u=5}=f,d=et(f,["children","width","height"]);return S.jsx(Pt.svg,K(O({},d),{ref:i,width:l,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:S.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Ex.displayName=oD;var uD=Ex;function cD(n){const[i,s]=w.useState(void 0);return we(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const d=u[0];let f,h;if("borderBoxSize"in d){const m=d.borderBoxSize,p=Array.isArray(m)?m[0]:m;f=p.inlineSize,h=p.blockSize}else f=n.offsetWidth,h=n.offsetHeight;s({width:f,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),i}var dh="Popper",[Mx,Cx]=Xr(dh),[fD,Rx]=Mx(dh),Dx=n=>{const{__scopePopper:i,children:s}=n,[l,u]=w.useState(null);return S.jsx(fD,{scope:i,anchor:l,onAnchorChange:u,children:s})};Dx.displayName=dh;var Ox="PopperAnchor",Nx=w.forwardRef((n,i)=>{const m=n,{__scopePopper:s,virtualRef:l}=m,u=et(m,["__scopePopper","virtualRef"]),d=Rx(Ox,s),f=w.useRef(null),h=ae(i,f);return w.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||f.current)}),l?null:S.jsx(Pt.div,K(O({},u),{ref:h}))});Nx.displayName=Ox;var hh="PopperContent",[dD,hD]=Mx(hh),jx=w.forwardRef((n,i)=>{var nt,Tt,qt,Ot,Ct,jt,de,Ae;const xt=n,{__scopePopper:s,side:l="bottom",sideOffset:u=0,align:d="center",alignOffset:f=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:b=!1,updatePositionStrategy:T="optimized",onPlaced:C}=xt,M=et(xt,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),E=Rx(hh,s),[N,z]=w.useState(null),A=ae(i,rn=>z(rn)),[V,j]=w.useState(null),I=cD(V),J=(nt=I==null?void 0:I.width)!=null?nt:0,Y=(Tt=I==null?void 0:I.height)!=null?Tt:0,W=l+(d!=="center"?"-"+d:""),ft=typeof g=="number"?g:O({top:0,right:0,bottom:0,left:0},g),ht=Array.isArray(p)?p:[p],lt=ht.length>0,bt={padding:ft,boundary:ht.filter(pD),altBoundary:lt},{refs:Et,floatingStyles:ut,placement:B,isPositioned:q,middlewareData:P}=JR({strategy:"fixed",placement:W,whileElementsMounted:(...rn)=>XR(...rn,{animationFrame:T==="always"}),elements:{reference:E.anchor},middleware:[eD({mainAxis:u+Y,alignmentAxis:f}),m&&nD(O({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?iD():void 0},bt)),m&&aD(O({},bt)),sD(K(O({},bt),{apply:({elements:rn,rects:he,availableWidth:Li,availableHeight:Fr})=>{const{width:Ir,height:da}=he.reference,ha=rn.floating.style;ha.setProperty("--radix-popper-available-width",`${Li}px`),ha.setProperty("--radix-popper-available-height",`${Fr}px`),ha.setProperty("--radix-popper-anchor-width",`${Ir}px`),ha.setProperty("--radix-popper-anchor-height",`${da}px`)}})),V&&lD({element:V,padding:h}),gD({arrowWidth:J,arrowHeight:Y}),b&&rD(O({strategy:"referenceHidden"},bt))]}),[ct,D]=zx(B),X=Oi(C);we(()=>{q&&(X==null||X())},[q,X]);const tt=(qt=P.arrow)==null?void 0:qt.x,$=(Ot=P.arrow)==null?void 0:Ot.y,it=((Ct=P.arrow)==null?void 0:Ct.centerOffset)!==0,[vt,dt]=w.useState();return we(()=>{N&&dt(window.getComputedStyle(N).zIndex)},[N]),S.jsx("div",{ref:Et.setFloating,"data-radix-popper-content-wrapper":"",style:O(K(O({},ut),{transform:q?ut.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:vt,"--radix-popper-transform-origin":[(jt=P.transformOrigin)==null?void 0:jt.x,(de=P.transformOrigin)==null?void 0:de.y].join(" ")}),((Ae=P.hide)==null?void 0:Ae.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:n.dir,children:S.jsx(dD,{scope:s,placedSide:ct,onArrowChange:j,arrowX:tt,arrowY:$,shouldHideArrow:it,children:S.jsx(Pt.div,K(O({"data-side":ct,"data-align":D},M),{ref:A,style:K(O({},M.style),{animation:q?void 0:"none"})}))})})});jx.displayName=hh;var _x="PopperArrow",mD={top:"bottom",right:"left",bottom:"top",left:"right"},Vx=w.forwardRef(function(i,s){const h=i,{__scopePopper:l}=h,u=et(h,["__scopePopper"]),d=hD(_x,l),f=mD[d.placedSide];return S.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:S.jsx(uD,K(O({},u),{ref:s,style:K(O({},u.style),{display:"block"})}))})});Vx.displayName=_x;function pD(n){return n!==null}var gD=n=>({name:"transformOrigin",options:n,fn(i){var E,N,z,A,V;const{placement:s,rects:l,middlewareData:u}=i,f=((E=u.arrow)==null?void 0:E.centerOffset)!==0,h=f?0:n.arrowWidth,m=f?0:n.arrowHeight,[p,g]=zx(s),v={start:"0%",center:"50%",end:"100%"}[g],b=((z=(N=u.arrow)==null?void 0:N.x)!=null?z:0)+h/2,T=((V=(A=u.arrow)==null?void 0:A.y)!=null?V:0)+m/2;let C="",M="";return p==="bottom"?(C=f?v:`${b}px`,M=`${-m}px`):p==="top"?(C=f?v:`${b}px`,M=`${l.floating.height+m}px`):p==="right"?(C=`${-m}px`,M=f?v:`${T}px`):p==="left"&&(C=`${l.floating.width+m}px`,M=f?v:`${T}px`),{data:{x:C,y:M}}}});function zx(n){const[i,s="center"]=n.split("-");return[i,s]}var yD=Dx,vD=Nx,bD=jx,xD=Vx,SD="Portal",Lx=w.forwardRef((n,i)=>{var m;const h=n,{container:s}=h,l=et(h,["container"]),[u,d]=w.useState(!1);we(()=>d(!0),[]);const f=s||u&&((m=globalThis==null?void 0:globalThis.document)==null?void 0:m.body);return f?HC.createPortal(S.jsx(Pt.div,K(O({},l),{ref:i})),f):null});Lx.displayName=SD;var TD=p0[" useInsertionEffect ".trim().toString()]||we;function Po({prop:n,defaultProp:i,onChange:s=()=>{},caller:l}){const[u,d,f]=wD({defaultProp:i,onChange:s}),h=n!==void 0,m=h?n:u;{const g=w.useRef(n!==void 0);w.useEffect(()=>{const v=g.current;v!==h&&console.warn(`${l} is changing from ${v?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=h},[h,l])}const p=w.useCallback(g=>{var v;if(h){const b=AD(g)?g(n):g;b!==n&&((v=f.current)==null||v.call(f,b))}else d(g)},[h,n,d,f]);return[m,p]}function wD({defaultProp:n,onChange:i}){const[s,l]=w.useState(n),u=w.useRef(s),d=w.useRef(i);return TD(()=>{d.current=i},[i]),w.useEffect(()=>{var f;u.current!==s&&((f=d.current)==null||f.call(d,s),u.current=s)},[s,u]),[s,l,d]}function AD(n){return typeof n=="function"}function ED(n){const i=w.useRef({value:n,previous:n});return w.useMemo(()=>(i.current.value!==n&&(i.current.previous=i.current.value,i.current.value=n),i.current.previous),[n])}var Bx=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),MD="VisuallyHidden",CD=w.forwardRef((n,i)=>S.jsx(Pt.span,K(O({},n),{ref:i,style:O(O({},Bx),n.style)})));CD.displayName=MD;var RD=function(n){if(typeof document=="undefined")return null;var i=Array.isArray(n)?n[0]:n;return i.ownerDocument.body},es=new WeakMap,vo=new WeakMap,bo={},Uf=0,Ux=function(n){return n&&(n.host||Ux(n.parentNode))},DD=function(n,i){return i.map(function(s){if(n.contains(s))return s;var l=Ux(s);return l&&n.contains(l)?l:(console.error("aria-hidden",s,"in not contained inside",n,". Doing nothing"),null)}).filter(function(s){return!!s})},OD=function(n,i,s,l){var u=DD(i,Array.isArray(n)?n:[n]);bo[s]||(bo[s]=new WeakMap);var d=bo[s],f=[],h=new Set,m=new Set(u),p=function(v){!v||h.has(v)||(h.add(v),p(v.parentNode))};u.forEach(p);var g=function(v){!v||m.has(v)||Array.prototype.forEach.call(v.children,function(b){if(h.has(b))g(b);else try{var T=b.getAttribute(l),C=T!==null&&T!=="false",M=(es.get(b)||0)+1,E=(d.get(b)||0)+1;es.set(b,M),d.set(b,E),f.push(b),M===1&&C&&vo.set(b,!0),E===1&&b.setAttribute(s,"true"),C||b.setAttribute(l,"true")}catch(N){console.error("aria-hidden: cannot operate on ",b,N)}})};return g(i),h.clear(),Uf++,function(){f.forEach(function(v){var b=es.get(v)-1,T=d.get(v)-1;es.set(v,b),d.set(v,T),b||(vo.has(v)||v.removeAttribute(l),vo.delete(v)),T||v.removeAttribute(s)}),Uf--,Uf||(es=new WeakMap,es=new WeakMap,vo=new WeakMap,bo={})}},ND=function(n,i,s){s===void 0&&(s="data-aria-hidden");var l=Array.from(Array.isArray(n)?n:[n]),u=RD(n);return u?(l.push.apply(l,Array.from(u.querySelectorAll("[aria-live]"))),OD(l,u,s,"aria-hidden")):function(){return null}},Mn=function(){return Mn=Object.assign||function(i){for(var s,l=1,u=arguments.length;l<u;l++){s=arguments[l];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(i[d]=s[d])}return i},Mn.apply(this,arguments)};function kx(n,i){var s={};for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&i.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,l=Object.getOwnPropertySymbols(n);u<l.length;u++)i.indexOf(l[u])<0&&Object.prototype.propertyIsEnumerable.call(n,l[u])&&(s[l[u]]=n[l[u]]);return s}function jD(n,i,s){if(s||arguments.length===2)for(var l=0,u=i.length,d;l<u;l++)(d||!(l in i))&&(d||(d=Array.prototype.slice.call(i,0,l)),d[l]=i[l]);return n.concat(d||Array.prototype.slice.call(i))}var Oo="right-scroll-bar-position",No="width-before-scroll-bar",_D="with-scroll-bars-hidden",VD="--removed-body-scroll-bar-size";function kf(n,i){return typeof n=="function"?n(i):n&&(n.current=i),n}function zD(n,i){var s=w.useState(function(){return{value:n,callback:i,facade:{get current(){return s.value},set current(l){var u=s.value;u!==l&&(s.value=l,s.callback(l,u))}}}})[0];return s.callback=i,s.facade}var LD=typeof window!="undefined"?w.useLayoutEffect:w.useEffect,t0=new WeakMap;function BD(n,i){var s=zD(null,function(l){return n.forEach(function(u){return kf(u,l)})});return LD(function(){var l=t0.get(s);if(l){var u=new Set(l),d=new Set(n),f=s.current;u.forEach(function(h){d.has(h)||kf(h,null)}),d.forEach(function(h){u.has(h)||kf(h,f)})}t0.set(s,n)},[n]),s}function UD(n){return n}function kD(n,i){i===void 0&&(i=UD);var s=[],l=!1,u={read:function(){if(l)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:n},useMedium:function(d){var f=i(d,l);return s.push(f),function(){s=s.filter(function(h){return h!==f})}},assignSyncMedium:function(d){for(l=!0;s.length;){var f=s;s=[],f.forEach(d)}s={push:function(h){return d(h)},filter:function(){return s}}},assignMedium:function(d){l=!0;var f=[];if(s.length){var h=s;s=[],h.forEach(d),f=s}var m=function(){var g=f;f=[],g.forEach(d)},p=function(){return Promise.resolve().then(m)};p(),s={push:function(g){f.push(g),p()},filter:function(g){return f=f.filter(g),s}}}};return u}function HD(n){n===void 0&&(n={});var i=kD(null);return i.options=Mn({async:!0,ssr:!1},n),i}var Hx=function(n){var i=n.sideCar,s=kx(n,["sideCar"]);if(!i)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var l=i.read();if(!l)throw new Error("Sidecar medium not found");return w.createElement(l,Mn({},s))};Hx.isSideCarExport=!0;function PD(n,i){return n.useMedium(i),Hx}var Px=HD(),Hf=function(){},Fo=w.forwardRef(function(n,i){var s=w.useRef(null),l=w.useState({onScrollCapture:Hf,onWheelCapture:Hf,onTouchMoveCapture:Hf}),u=l[0],d=l[1],f=n.forwardProps,h=n.children,m=n.className,p=n.removeScrollBar,g=n.enabled,v=n.shards,b=n.sideCar,T=n.noIsolation,C=n.inert,M=n.allowPinchZoom,E=n.as,N=E===void 0?"div":E,z=n.gapMode,A=kx(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),V=b,j=BD([s,i]),I=Mn(Mn({},A),u);return w.createElement(w.Fragment,null,g&&w.createElement(V,{sideCar:Px,removeScrollBar:p,shards:v,noIsolation:T,inert:C,setCallbacks:d,allowPinchZoom:!!M,lockRef:s,gapMode:z}),f?w.cloneElement(w.Children.only(h),Mn(Mn({},I),{ref:j})):w.createElement(N,Mn({},I,{className:m,ref:j}),h))});Fo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Fo.classNames={fullWidth:No,zeroRight:Oo};var GD=function(){if(typeof __webpack_nonce__!="undefined")return __webpack_nonce__};function YD(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var i=GD();return i&&n.setAttribute("nonce",i),n}function qD(n,i){n.styleSheet?n.styleSheet.cssText=i:n.appendChild(document.createTextNode(i))}function XD(n){var i=document.head||document.getElementsByTagName("head")[0];i.appendChild(n)}var KD=function(){var n=0,i=null;return{add:function(s){n==0&&(i=YD())&&(qD(i,s),XD(i)),n++},remove:function(){n--,!n&&i&&(i.parentNode&&i.parentNode.removeChild(i),i=null)}}},ZD=function(){var n=KD();return function(i,s){w.useEffect(function(){return n.add(i),function(){n.remove()}},[i&&s])}},Gx=function(){var n=ZD(),i=function(s){var l=s.styles,u=s.dynamic;return n(l,u),null};return i},QD={left:0,top:0,right:0,gap:0},Pf=function(n){return parseInt(n||"",10)||0},FD=function(n){var i=window.getComputedStyle(document.body),s=i[n==="padding"?"paddingLeft":"marginLeft"],l=i[n==="padding"?"paddingTop":"marginTop"],u=i[n==="padding"?"paddingRight":"marginRight"];return[Pf(s),Pf(l),Pf(u)]},ID=function(n){if(n===void 0&&(n="margin"),typeof window=="undefined")return QD;var i=FD(n),s=document.documentElement.clientWidth,l=window.innerWidth;return{left:i[0],top:i[1],right:i[2],gap:Math.max(0,l-s+i[2]-i[0])}},WD=Gx(),cs="data-scroll-locked",$D=function(n,i,s,l){var u=n.left,d=n.top,f=n.right,h=n.gap;return s===void 0&&(s="margin"),`
  .`.concat(_D,` {
   overflow: hidden `).concat(l,`;
   padding-right: `).concat(h,"px ").concat(l,`;
  }
  body[`).concat(cs,`] {
    overflow: hidden `).concat(l,`;
    overscroll-behavior: contain;
    `).concat([i&&"position: relative ".concat(l,";"),s==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(f,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(l,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(l,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Oo,` {
    right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(No,` {
    margin-right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(Oo," .").concat(Oo,` {
    right: 0 `).concat(l,`;
  }
  
  .`).concat(No," .").concat(No,` {
    margin-right: 0 `).concat(l,`;
  }
  
  body[`).concat(cs,`] {
    `).concat(VD,": ").concat(h,`px;
  }
`)},e0=function(){var n=parseInt(document.body.getAttribute(cs)||"0",10);return isFinite(n)?n:0},JD=function(){w.useEffect(function(){return document.body.setAttribute(cs,(e0()+1).toString()),function(){var n=e0()-1;n<=0?document.body.removeAttribute(cs):document.body.setAttribute(cs,n.toString())}},[])},tO=function(n){var i=n.noRelative,s=n.noImportant,l=n.gapMode,u=l===void 0?"margin":l;JD();var d=w.useMemo(function(){return ID(u)},[u]);return w.createElement(WD,{styles:$D(d,!i,u,s?"":"!important")})},yd=!1;if(typeof window!="undefined")try{var xo=Object.defineProperty({},"passive",{get:function(){return yd=!0,!0}});window.addEventListener("test",xo,xo),window.removeEventListener("test",xo,xo)}catch(n){yd=!1}var ns=yd?{passive:!1}:!1,eO=function(n){return n.tagName==="TEXTAREA"},Yx=function(n,i){if(!(n instanceof Element))return!1;var s=window.getComputedStyle(n);return s[i]!=="hidden"&&!(s.overflowY===s.overflowX&&!eO(n)&&s[i]==="visible")},nO=function(n){return Yx(n,"overflowY")},iO=function(n){return Yx(n,"overflowX")},n0=function(n,i){var s=i.ownerDocument,l=i;do{typeof ShadowRoot!="undefined"&&l instanceof ShadowRoot&&(l=l.host);var u=qx(n,l);if(u){var d=Xx(n,l),f=d[1],h=d[2];if(f>h)return!0}l=l.parentNode}while(l&&l!==s.body);return!1},aO=function(n){var i=n.scrollTop,s=n.scrollHeight,l=n.clientHeight;return[i,s,l]},sO=function(n){var i=n.scrollLeft,s=n.scrollWidth,l=n.clientWidth;return[i,s,l]},qx=function(n,i){return n==="v"?nO(i):iO(i)},Xx=function(n,i){return n==="v"?aO(i):sO(i)},rO=function(n,i){return n==="h"&&i==="rtl"?-1:1},lO=function(n,i,s,l,u){var d=rO(n,window.getComputedStyle(i).direction),f=d*l,h=s.target,m=i.contains(h),p=!1,g=f>0,v=0,b=0;do{var T=Xx(n,h),C=T[0],M=T[1],E=T[2],N=M-E-d*C;(C||N)&&qx(n,h)&&(v+=N,b+=C),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!m&&h!==document.body||m&&(i.contains(h)||i===h));return(g&&Math.abs(v)<1||!g&&Math.abs(b)<1)&&(p=!0),p},So=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},i0=function(n){return[n.deltaX,n.deltaY]},a0=function(n){return n&&"current"in n?n.current:n},oO=function(n,i){return n[0]===i[0]&&n[1]===i[1]},uO=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},cO=0,is=[];function fO(n){var i=w.useRef([]),s=w.useRef([0,0]),l=w.useRef(),u=w.useState(cO++)[0],d=w.useState(Gx)[0],f=w.useRef(n);w.useEffect(function(){f.current=n},[n]),w.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(u));var M=jD([n.lockRef.current],(n.shards||[]).map(a0),!0).filter(Boolean);return M.forEach(function(E){return E.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),M.forEach(function(E){return E.classList.remove("allow-interactivity-".concat(u))})}}},[n.inert,n.lockRef.current,n.shards]);var h=w.useCallback(function(M,E){if("touches"in M&&M.touches.length===2||M.type==="wheel"&&M.ctrlKey)return!f.current.allowPinchZoom;var N=So(M),z=s.current,A="deltaX"in M?M.deltaX:z[0]-N[0],V="deltaY"in M?M.deltaY:z[1]-N[1],j,I=M.target,J=Math.abs(A)>Math.abs(V)?"h":"v";if("touches"in M&&J==="h"&&I.type==="range")return!1;var Y=n0(J,I);if(!Y)return!0;if(Y?j=J:(j=J==="v"?"h":"v",Y=n0(J,I)),!Y)return!1;if(!l.current&&"changedTouches"in M&&(A||V)&&(l.current=j),!j)return!0;var W=l.current||j;return lO(W,E,M,W==="h"?A:V)},[]),m=w.useCallback(function(M){var E=M;if(!(!is.length||is[is.length-1]!==d)){var N="deltaY"in E?i0(E):So(E),z=i.current.filter(function(j){return j.name===E.type&&(j.target===E.target||E.target===j.shadowParent)&&oO(j.delta,N)})[0];if(z&&z.should){E.cancelable&&E.preventDefault();return}if(!z){var A=(f.current.shards||[]).map(a0).filter(Boolean).filter(function(j){return j.contains(E.target)}),V=A.length>0?h(E,A[0]):!f.current.noIsolation;V&&E.cancelable&&E.preventDefault()}}},[]),p=w.useCallback(function(M,E,N,z){var A={name:M,delta:E,target:N,should:z,shadowParent:dO(N)};i.current.push(A),setTimeout(function(){i.current=i.current.filter(function(V){return V!==A})},1)},[]),g=w.useCallback(function(M){s.current=So(M),l.current=void 0},[]),v=w.useCallback(function(M){p(M.type,i0(M),M.target,h(M,n.lockRef.current))},[]),b=w.useCallback(function(M){p(M.type,So(M),M.target,h(M,n.lockRef.current))},[]);w.useEffect(function(){return is.push(d),n.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:b}),document.addEventListener("wheel",m,ns),document.addEventListener("touchmove",m,ns),document.addEventListener("touchstart",g,ns),function(){is=is.filter(function(M){return M!==d}),document.removeEventListener("wheel",m,ns),document.removeEventListener("touchmove",m,ns),document.removeEventListener("touchstart",g,ns)}},[]);var T=n.removeScrollBar,C=n.inert;return w.createElement(w.Fragment,null,C?w.createElement(d,{styles:uO(u)}):null,T?w.createElement(tO,{gapMode:n.gapMode}):null)}function dO(n){for(var i=null;n!==null;)n instanceof ShadowRoot&&(i=n.host,n=n.host),n=n.parentNode;return i}const hO=PD(Px,fO);var Kx=w.forwardRef(function(n,i){return w.createElement(Fo,Mn({},n,{ref:i,sideCar:hO}))});Kx.classNames=Fo.classNames;var mO=[" ","Enter","ArrowUp","ArrowDown"],pO=[" ","Enter"],ca="Select",[Io,Wo,gO]=ux(ca),[Ss,l3]=Xr(ca,[gO,Cx]),$o=Cx(),[yO,Vi]=Ss(ca),[vO,bO]=Ss(ca),Zx=n=>{const{__scopeSelect:i,children:s,open:l,defaultOpen:u,onOpenChange:d,value:f,defaultValue:h,onValueChange:m,dir:p,name:g,autoComplete:v,disabled:b,required:T,form:C}=n,M=$o(i),[E,N]=w.useState(null),[z,A]=w.useState(null),[V,j]=w.useState(!1),I=ah(p),[J,Y]=Po({prop:l,defaultProp:u!=null?u:!1,onChange:d,caller:ca}),[W,ft]=Po({prop:f,defaultProp:h,onChange:m,caller:ca}),ht=w.useRef(null),lt=E?C||!!E.closest("form"):!0,[bt,Et]=w.useState(new Set),ut=Array.from(bt).map(B=>B.props.value).join(";");return S.jsx(yD,K(O({},M),{children:S.jsxs(yO,{required:T,scope:i,trigger:E,onTriggerChange:N,valueNode:z,onValueNodeChange:A,valueNodeHasChildren:V,onValueNodeHasChildrenChange:j,contentId:Kr(),value:W,onValueChange:ft,open:J,onOpenChange:Y,dir:I,triggerPointerDownPosRef:ht,disabled:b,children:[S.jsx(Io.Provider,{scope:i,children:S.jsx(vO,{scope:n.__scopeSelect,onNativeOptionAdd:w.useCallback(B=>{Et(q=>new Set(q).add(B))},[]),onNativeOptionRemove:w.useCallback(B=>{Et(q=>{const P=new Set(q);return P.delete(B),P})},[]),children:s})}),lt?S.jsxs(pS,{"aria-hidden":!0,required:T,tabIndex:-1,name:g,autoComplete:v,value:W,onChange:B=>ft(B.target.value),disabled:b,form:C,children:[W===void 0?S.jsx("option",{value:""}):null,Array.from(bt)]},ut):null]})}))};Zx.displayName=ca;var Qx="SelectTrigger",Fx=w.forwardRef((n,i)=>{const M=n,{__scopeSelect:s,disabled:l=!1}=M,u=et(M,["__scopeSelect","disabled"]),d=$o(s),f=Vi(Qx,s),h=f.disabled||l,m=ae(i,f.onTriggerChange),p=Wo(s),g=w.useRef("touch"),[v,b,T]=yS(E=>{const N=p().filter(V=>!V.disabled),z=N.find(V=>V.value===f.value),A=vS(N,E,z);A!==void 0&&f.onValueChange(A.value)}),C=E=>{h||(f.onOpenChange(!0),T()),E&&(f.triggerPointerDownPosRef.current={x:Math.round(E.pageX),y:Math.round(E.pageY)})};return S.jsx(vD,K(O({asChild:!0},d),{children:S.jsx(Pt.button,K(O({type:"button",role:"combobox","aria-controls":f.contentId,"aria-expanded":f.open,"aria-required":f.required,"aria-autocomplete":"none",dir:f.dir,"data-state":f.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":gS(f.value)?"":void 0},u),{ref:m,onClick:Bt(u.onClick,E=>{E.currentTarget.focus(),g.current!=="mouse"&&C(E)}),onPointerDown:Bt(u.onPointerDown,E=>{g.current=E.pointerType;const N=E.target;N.hasPointerCapture(E.pointerId)&&N.releasePointerCapture(E.pointerId),E.button===0&&E.ctrlKey===!1&&E.pointerType==="mouse"&&(C(E),E.preventDefault())}),onKeyDown:Bt(u.onKeyDown,E=>{const N=v.current!=="";!(E.ctrlKey||E.altKey||E.metaKey)&&E.key.length===1&&b(E.key),!(N&&E.key===" ")&&mO.includes(E.key)&&(C(),E.preventDefault())})}))}))});Fx.displayName=Qx;var Ix="SelectValue",Wx=w.forwardRef((n,i)=>{const b=n,{__scopeSelect:s,className:l,style:u,children:d,placeholder:f=""}=b,h=et(b,["__scopeSelect","className","style","children","placeholder"]),m=Vi(Ix,s),{onValueNodeHasChildrenChange:p}=m,g=d!==void 0,v=ae(i,m.onValueNodeChange);return we(()=>{p(g)},[p,g]),S.jsx(Pt.span,K(O({},h),{ref:v,style:{pointerEvents:"none"},children:gS(m.value)?S.jsx(S.Fragment,{children:f}):d}))});Wx.displayName=Ix;var xO="SelectIcon",$x=w.forwardRef((n,i)=>{const d=n,{__scopeSelect:s,children:l}=d,u=et(d,["__scopeSelect","children"]);return S.jsx(Pt.span,K(O({"aria-hidden":!0},u),{ref:i,children:l||"▼"}))});$x.displayName=xO;var SO="SelectPortal",Jx=n=>S.jsx(Lx,O({asChild:!0},n));Jx.displayName=SO;var fa="SelectContent",tS=w.forwardRef((n,i)=>{const s=Vi(fa,n.__scopeSelect),[l,u]=w.useState();if(we(()=>{u(new DocumentFragment)},[]),!s.open){const d=l;return d?qr.createPortal(S.jsx(eS,{scope:n.__scopeSelect,children:S.jsx(Io.Slot,{scope:n.__scopeSelect,children:S.jsx("div",{children:n.children})})}),d):null}return S.jsx(nS,K(O({},n),{ref:i}))});tS.displayName=fa;var dn=10,[eS,zi]=Ss(fa),TO="SelectContentImpl",wO=Br("SelectContent.RemoveScroll"),nS=w.forwardRef((n,i)=>{const xt=n,{__scopeSelect:s,position:l="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:d,onPointerDownOutside:f,side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:C,hideWhenDetached:M,avoidCollisions:E}=xt,N=et(xt,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),z=Vi(fa,s),[A,V]=w.useState(null),[j,I]=w.useState(null),J=ae(i,nt=>V(nt)),[Y,W]=w.useState(null),[ft,ht]=w.useState(null),lt=Wo(s),[bt,Et]=w.useState(!1),ut=w.useRef(!1);w.useEffect(()=>{if(A)return ND(A)},[A]),eR();const B=w.useCallback(nt=>{const[Tt,...qt]=lt().map(jt=>jt.ref.current),[Ot]=qt.slice(-1),Ct=document.activeElement;for(const jt of nt)if(jt===Ct||(jt==null||jt.scrollIntoView({block:"nearest"}),jt===Tt&&j&&(j.scrollTop=0),jt===Ot&&j&&(j.scrollTop=j.scrollHeight),jt==null||jt.focus(),document.activeElement!==Ct))return},[lt,j]),q=w.useCallback(()=>B([Y,A]),[B,Y,A]);w.useEffect(()=>{bt&&q()},[bt,q]);const{onOpenChange:P,triggerPointerDownPosRef:ct}=z;w.useEffect(()=>{if(A){let nt={x:0,y:0};const Tt=Ot=>{var Ct,jt,de,Ae;nt={x:Math.abs(Math.round(Ot.pageX)-((jt=(Ct=ct.current)==null?void 0:Ct.x)!=null?jt:0)),y:Math.abs(Math.round(Ot.pageY)-((Ae=(de=ct.current)==null?void 0:de.y)!=null?Ae:0))}},qt=Ot=>{nt.x<=10&&nt.y<=10?Ot.preventDefault():A.contains(Ot.target)||P(!1),document.removeEventListener("pointermove",Tt),ct.current=null};return ct.current!==null&&(document.addEventListener("pointermove",Tt),document.addEventListener("pointerup",qt,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",Tt),document.removeEventListener("pointerup",qt,{capture:!0})}}},[A,P,ct]),w.useEffect(()=>{const nt=()=>P(!1);return window.addEventListener("blur",nt),window.addEventListener("resize",nt),()=>{window.removeEventListener("blur",nt),window.removeEventListener("resize",nt)}},[P]);const[D,X]=yS(nt=>{const Tt=lt().filter(Ct=>!Ct.disabled),qt=Tt.find(Ct=>Ct.ref.current===document.activeElement),Ot=vS(Tt,nt,qt);Ot&&setTimeout(()=>Ot.ref.current.focus())}),tt=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(z.value!==void 0&&z.value===Tt||Ot)&&(W(nt),Ot&&(ut.current=!0))},[z.value]),$=w.useCallback(()=>A==null?void 0:A.focus(),[A]),it=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(z.value!==void 0&&z.value===Tt||Ot)&&ht(nt)},[z.value]),vt=l==="popper"?vd:iS,dt=vt===vd?{side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:C,hideWhenDetached:M,avoidCollisions:E}:{};return S.jsx(eS,{scope:s,content:A,viewport:j,onViewportChange:I,itemRefCallback:tt,selectedItem:Y,onItemLeave:$,itemTextRefCallback:it,focusSelectedItem:q,selectedItemText:ft,position:l,isPositioned:bt,searchRef:D,children:S.jsx(Kx,{as:wO,allowPinchZoom:!0,children:S.jsx(hx,{asChild:!0,trapped:z.open,onMountAutoFocus:nt=>{nt.preventDefault()},onUnmountAutoFocus:Bt(u,nt=>{var Tt;(Tt=z.trigger)==null||Tt.focus({preventScroll:!0}),nt.preventDefault()}),children:S.jsx(fx,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:nt=>nt.preventDefault(),onDismiss:()=>z.onOpenChange(!1),children:S.jsx(vt,K(O(O({role:"listbox",id:z.contentId,"data-state":z.open?"open":"closed",dir:z.dir,onContextMenu:nt=>nt.preventDefault()},N),dt),{onPlaced:()=>Et(!0),ref:J,style:O({display:"flex",flexDirection:"column",outline:"none"},N.style),onKeyDown:Bt(N.onKeyDown,nt=>{const Tt=nt.ctrlKey||nt.altKey||nt.metaKey;if(nt.key==="Tab"&&nt.preventDefault(),!Tt&&nt.key.length===1&&X(nt.key),["ArrowUp","ArrowDown","Home","End"].includes(nt.key)){let Ot=lt().filter(Ct=>!Ct.disabled).map(Ct=>Ct.ref.current);if(["ArrowUp","End"].includes(nt.key)&&(Ot=Ot.slice().reverse()),["ArrowUp","ArrowDown"].includes(nt.key)){const Ct=nt.target,jt=Ot.indexOf(Ct);Ot=Ot.slice(jt+1)}setTimeout(()=>B(Ot)),nt.preventDefault()}})}))})})})})});nS.displayName=TO;var AO="SelectItemAlignedPosition",iS=w.forwardRef((n,i)=>{const J=n,{__scopeSelect:s,onPlaced:l}=J,u=et(J,["__scopeSelect","onPlaced"]),d=Vi(fa,s),f=zi(fa,s),[h,m]=w.useState(null),[p,g]=w.useState(null),v=ae(i,Y=>g(Y)),b=Wo(s),T=w.useRef(!1),C=w.useRef(!0),{viewport:M,selectedItem:E,selectedItemText:N,focusSelectedItem:z}=f,A=w.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&p&&M&&E&&N){const Y=d.trigger.getBoundingClientRect(),W=p.getBoundingClientRect(),ft=d.valueNode.getBoundingClientRect(),ht=N.getBoundingClientRect();if(d.dir!=="rtl"){const Ct=ht.left-W.left,jt=ft.left-Ct,de=Y.left-jt,Ae=Y.width+de,rn=Math.max(Ae,W.width),he=window.innerWidth-dn,Li=Uv(jt,[dn,Math.max(dn,he-rn)]);h.style.minWidth=Ae+"px",h.style.left=Li+"px"}else{const Ct=W.right-ht.right,jt=window.innerWidth-ft.right-Ct,de=window.innerWidth-Y.right-jt,Ae=Y.width+de,rn=Math.max(Ae,W.width),he=window.innerWidth-dn,Li=Uv(jt,[dn,Math.max(dn,he-rn)]);h.style.minWidth=Ae+"px",h.style.right=Li+"px"}const lt=b(),bt=window.innerHeight-dn*2,Et=M.scrollHeight,ut=window.getComputedStyle(p),B=parseInt(ut.borderTopWidth,10),q=parseInt(ut.paddingTop,10),P=parseInt(ut.borderBottomWidth,10),ct=parseInt(ut.paddingBottom,10),D=B+q+Et+ct+P,X=Math.min(E.offsetHeight*5,D),tt=window.getComputedStyle(M),$=parseInt(tt.paddingTop,10),it=parseInt(tt.paddingBottom,10),vt=Y.top+Y.height/2-dn,dt=bt-vt,xt=E.offsetHeight/2,nt=E.offsetTop+xt,Tt=B+q+nt,qt=D-Tt;if(Tt<=vt){const Ct=lt.length>0&&E===lt[lt.length-1].ref.current;h.style.bottom="0px";const jt=p.clientHeight-M.offsetTop-M.offsetHeight,de=Math.max(dt,xt+(Ct?it:0)+jt+P),Ae=Tt+de;h.style.height=Ae+"px"}else{const Ct=lt.length>0&&E===lt[0].ref.current;h.style.top="0px";const de=Math.max(vt,B+M.offsetTop+(Ct?$:0)+xt)+qt;h.style.height=de+"px",M.scrollTop=Tt-vt+M.offsetTop}h.style.margin=`${dn}px 0`,h.style.minHeight=X+"px",h.style.maxHeight=bt+"px",l==null||l(),requestAnimationFrame(()=>T.current=!0)}},[b,d.trigger,d.valueNode,h,p,M,E,N,d.dir,l]);we(()=>A(),[A]);const[V,j]=w.useState();we(()=>{p&&j(window.getComputedStyle(p).zIndex)},[p]);const I=w.useCallback(Y=>{Y&&C.current===!0&&(A(),z==null||z(),C.current=!1)},[A,z]);return S.jsx(MO,{scope:s,contentWrapper:h,shouldExpandOnScrollRef:T,onScrollButtonChange:I,children:S.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:V},children:S.jsx(Pt.div,K(O({},u),{ref:v,style:O({boxSizing:"border-box",maxHeight:"100%"},u.style)}))})})});iS.displayName=AO;var EO="SelectPopperPosition",vd=w.forwardRef((n,i)=>{const h=n,{__scopeSelect:s,align:l="start",collisionPadding:u=dn}=h,d=et(h,["__scopeSelect","align","collisionPadding"]),f=$o(s);return S.jsx(bD,K(O(O({},f),d),{ref:i,align:l,collisionPadding:u,style:K(O({boxSizing:"border-box"},d.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});vd.displayName=EO;var[MO,mh]=Ss(fa,{}),bd="SelectViewport",aS=w.forwardRef((n,i)=>{const p=n,{__scopeSelect:s,nonce:l}=p,u=et(p,["__scopeSelect","nonce"]),d=zi(bd,s),f=mh(bd,s),h=ae(i,d.onViewportChange),m=w.useRef(0);return S.jsxs(S.Fragment,{children:[S.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),S.jsx(Io.Slot,{scope:s,children:S.jsx(Pt.div,K(O({"data-radix-select-viewport":"",role:"presentation"},u),{ref:h,style:O({position:"relative",flex:1,overflow:"hidden auto"},u.style),onScroll:Bt(u.onScroll,g=>{const v=g.currentTarget,{contentWrapper:b,shouldExpandOnScrollRef:T}=f;if(T!=null&&T.current&&b){const C=Math.abs(m.current-v.scrollTop);if(C>0){const M=window.innerHeight-dn*2,E=parseFloat(b.style.minHeight),N=parseFloat(b.style.height),z=Math.max(E,N);if(z<M){const A=z+C,V=Math.min(M,A),j=A-V;b.style.height=V+"px",b.style.bottom==="0px"&&(v.scrollTop=j>0?j:0,b.style.justifyContent="flex-end")}}}m.current=v.scrollTop})}))})]})});aS.displayName=bd;var sS="SelectGroup",[CO,RO]=Ss(sS),DO=w.forwardRef((n,i)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),u=Kr();return S.jsx(CO,{scope:s,id:u,children:S.jsx(Pt.div,K(O({role:"group","aria-labelledby":u},l),{ref:i}))})});DO.displayName=sS;var rS="SelectLabel",OO=w.forwardRef((n,i)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),u=RO(rS,s);return S.jsx(Pt.div,K(O({id:u.id},l),{ref:i}))});OO.displayName=rS;var Go="SelectItem",[NO,lS]=Ss(Go),oS=w.forwardRef((n,i)=>{const z=n,{__scopeSelect:s,value:l,disabled:u=!1,textValue:d}=z,f=et(z,["__scopeSelect","value","disabled","textValue"]),h=Vi(Go,s),m=zi(Go,s),p=h.value===l,[g,v]=w.useState(d!=null?d:""),[b,T]=w.useState(!1),C=ae(i,A=>{var V;return(V=m.itemRefCallback)==null?void 0:V.call(m,A,l,u)}),M=Kr(),E=w.useRef("touch"),N=()=>{u||(h.onValueChange(l),h.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return S.jsx(NO,{scope:s,value:l,disabled:u,textId:M,isSelected:p,onItemTextChange:w.useCallback(A=>{v(V=>{var j;return V||((j=A==null?void 0:A.textContent)!=null?j:"").trim()})},[]),children:S.jsx(Io.ItemSlot,{scope:s,value:l,disabled:u,textValue:g,children:S.jsx(Pt.div,K(O({role:"option","aria-labelledby":M,"data-highlighted":b?"":void 0,"aria-selected":p&&b,"data-state":p?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1},f),{ref:C,onFocus:Bt(f.onFocus,()=>T(!0)),onBlur:Bt(f.onBlur,()=>T(!1)),onClick:Bt(f.onClick,()=>{E.current!=="mouse"&&N()}),onPointerUp:Bt(f.onPointerUp,()=>{E.current==="mouse"&&N()}),onPointerDown:Bt(f.onPointerDown,A=>{E.current=A.pointerType}),onPointerMove:Bt(f.onPointerMove,A=>{var V;E.current=A.pointerType,u?(V=m.onItemLeave)==null||V.call(m):E.current==="mouse"&&A.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Bt(f.onPointerLeave,A=>{var V;A.currentTarget===document.activeElement&&((V=m.onItemLeave)==null||V.call(m))}),onKeyDown:Bt(f.onKeyDown,A=>{var j;((j=m.searchRef)==null?void 0:j.current)!==""&&A.key===" "||(pO.includes(A.key)&&N(),A.key===" "&&A.preventDefault())})}))})})});oS.displayName=Go;var Ar="SelectItemText",uS=w.forwardRef((n,i)=>{const N=n,{__scopeSelect:s,className:l,style:u}=N,d=et(N,["__scopeSelect","className","style"]),f=Vi(Ar,s),h=zi(Ar,s),m=lS(Ar,s),p=bO(Ar,s),[g,v]=w.useState(null),b=ae(i,z=>v(z),m.onItemTextChange,z=>{var A;return(A=h.itemTextRefCallback)==null?void 0:A.call(h,z,m.value,m.disabled)}),T=g==null?void 0:g.textContent,C=w.useMemo(()=>S.jsx("option",{value:m.value,disabled:m.disabled,children:T},m.value),[m.disabled,m.value,T]),{onNativeOptionAdd:M,onNativeOptionRemove:E}=p;return we(()=>(M(C),()=>E(C)),[M,E,C]),S.jsxs(S.Fragment,{children:[S.jsx(Pt.span,K(O({id:m.textId},d),{ref:b})),m.isSelected&&f.valueNode&&!f.valueNodeHasChildren?qr.createPortal(d.children,f.valueNode):null]})});uS.displayName=Ar;var cS="SelectItemIndicator",fS=w.forwardRef((n,i)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]);return lS(cS,s).isSelected?S.jsx(Pt.span,K(O({"aria-hidden":!0},l),{ref:i})):null});fS.displayName=cS;var xd="SelectScrollUpButton",dS=w.forwardRef((n,i)=>{const s=zi(xd,n.__scopeSelect),l=mh(xd,n.__scopeSelect),[u,d]=w.useState(!1),f=ae(i,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollTop>0;d(p)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(mS,K(O({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop-m.offsetHeight)}})):null});dS.displayName=xd;var Sd="SelectScrollDownButton",hS=w.forwardRef((n,i)=>{const s=zi(Sd,n.__scopeSelect),l=mh(Sd,n.__scopeSelect),[u,d]=w.useState(!1),f=ae(i,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollHeight-m.clientHeight,g=Math.ceil(m.scrollTop)<p;d(g)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(mS,K(O({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop+m.offsetHeight)}})):null});hS.displayName=Sd;var mS=w.forwardRef((n,i)=>{const p=n,{__scopeSelect:s,onAutoScroll:l}=p,u=et(p,["__scopeSelect","onAutoScroll"]),d=zi("SelectScrollButton",s),f=w.useRef(null),h=Wo(s),m=w.useCallback(()=>{f.current!==null&&(window.clearInterval(f.current),f.current=null)},[]);return w.useEffect(()=>()=>m(),[m]),we(()=>{var v;const g=h().find(b=>b.ref.current===document.activeElement);(v=g==null?void 0:g.ref.current)==null||v.scrollIntoView({block:"nearest"})},[h]),S.jsx(Pt.div,K(O({"aria-hidden":!0},u),{ref:i,style:O({flexShrink:0},u.style),onPointerDown:Bt(u.onPointerDown,()=>{f.current===null&&(f.current=window.setInterval(l,50))}),onPointerMove:Bt(u.onPointerMove,()=>{var g;(g=d.onItemLeave)==null||g.call(d),f.current===null&&(f.current=window.setInterval(l,50))}),onPointerLeave:Bt(u.onPointerLeave,()=>{m()})}))}),jO="SelectSeparator",_O=w.forwardRef((n,i)=>{const u=n,{__scopeSelect:s}=u,l=et(u,["__scopeSelect"]);return S.jsx(Pt.div,K(O({"aria-hidden":!0},l),{ref:i}))});_O.displayName=jO;var Td="SelectArrow",VO=w.forwardRef((n,i)=>{const h=n,{__scopeSelect:s}=h,l=et(h,["__scopeSelect"]),u=$o(s),d=Vi(Td,s),f=zi(Td,s);return d.open&&f.position==="popper"?S.jsx(xD,K(O(O({},u),l),{ref:i})):null});VO.displayName=Td;var zO="SelectBubbleInput",pS=w.forwardRef((u,l)=>{var d=u,{__scopeSelect:n,value:i}=d,s=et(d,["__scopeSelect","value"]);const f=w.useRef(null),h=ae(l,f),m=ED(i);return w.useEffect(()=>{const p=f.current;if(!p)return;const g=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(g,"value").set;if(m!==i&&b){const T=new Event("change",{bubbles:!0});b.call(p,i),p.dispatchEvent(T)}},[m,i]),S.jsx(Pt.select,K(O({},s),{style:O(O({},Bx),s.style),ref:h,defaultValue:i}))});pS.displayName=zO;function gS(n){return n===""||n===void 0}function yS(n){const i=Oi(n),s=w.useRef(""),l=w.useRef(0),u=w.useCallback(f=>{const h=s.current+f;i(h),function m(p){s.current=p,window.clearTimeout(l.current),p!==""&&(l.current=window.setTimeout(()=>m(""),1e3))}(h)},[i]),d=w.useCallback(()=>{s.current="",window.clearTimeout(l.current)},[]);return w.useEffect(()=>()=>window.clearTimeout(l.current),[]),[s,u,d]}function vS(n,i,s){const u=i.length>1&&Array.from(i).every(p=>p===i[0])?i[0]:i,d=s?n.indexOf(s):-1;let f=LO(n,Math.max(d,0));u.length===1&&(f=f.filter(p=>p!==s));const m=f.find(p=>p.textValue.toLowerCase().startsWith(u.toLowerCase()));return m!==s?m:void 0}function LO(n,i){return n.map((s,l)=>n[(i+l)%n.length])}var BO=Zx,UO=Fx,kO=Wx,HO=$x,PO=Jx,GO=tS,YO=aS,qO=oS,XO=uS,KO=fS,ZO=dS,QO=hS;/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FO=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),IO=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,s,l)=>l?l.toUpperCase():s.toLowerCase()),s0=n=>{const i=IO(n);return i.charAt(0).toUpperCase()+i.slice(1)},bS=(...n)=>n.filter((i,s,l)=>!!i&&i.trim()!==""&&l.indexOf(i)===s).join(" ").trim(),WO=n=>{for(const i in n)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $O={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JO=w.forwardRef((p,m)=>{var g=p,{color:n="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:f}=g,h=et(g,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return w.createElement("svg",O(O(K(O({ref:m},$O),{width:i,height:i,stroke:n,strokeWidth:l?Number(s)*24/Number(i):s,className:bS("lucide",u)}),!d&&!WO(h)&&{"aria-hidden":"true"}),h),[...f.map(([v,b])=>w.createElement(v,b)),...Array.isArray(d)?d:[d]])});/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=(n,i)=>{const s=w.forwardRef((f,d)=>{var h=f,{className:l}=h,u=et(h,["className"]);return w.createElement(JO,O({ref:d,iconNode:i,className:bS(`lucide-${FO(s0(n))}`,`lucide-${n}`,l)},u))});return s.displayName=s0(n),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],eN=Ce("building",tN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],iN=Ce("calendar",nN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],sN=Ce("check",aN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xS=Ce("chevron-down",rN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],oN=Ce("chevron-up",lN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],r0=Ce("circle-alert",uN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],l0=Ce("circle-check-big",cN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],o0=Ce("log-out",fN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dN=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],hN=Ce("refresh-cw",dN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],pN=Ce("search",mN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gN=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],yN=Ce("settings",gN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vN=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],bN=Ce("sparkles",vN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],SN=Ce("user-plus",xN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TN=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],wN=Ce("user",TN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],EN=Ce("users",AN);function MN(i){var n=et(i,[]);return S.jsx(BO,O({"data-slot":"select"},n))}function CN(i){var n=et(i,[]);return S.jsx(kO,O({"data-slot":"select-value"},n))}function RN(u){var d=u,{className:n,size:i="default",children:s}=d,l=et(d,["className","size","children"]);return S.jsxs(UO,K(O({"data-slot":"select-trigger","data-size":i,className:Kt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},l),{children:[s,S.jsx(HO,{asChild:!0,children:S.jsx(xS,{className:"size-4 opacity-50"})})]}))}function DN(u){var d=u,{className:n,children:i,position:s="popper"}=d,l=et(d,["className","children","position"]);return S.jsx(PO,{children:S.jsxs(GO,K(O({"data-slot":"select-content",className:Kt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:s},l),{children:[S.jsx(NN,{}),S.jsx(YO,{className:Kt("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:i}),S.jsx(jN,{})]}))})}function ON(l){var u=l,{className:n,children:i}=u,s=et(u,["className","children"]);return S.jsxs(qO,K(O({"data-slot":"select-item",className:Kt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n)},s),{children:[S.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:S.jsx(KO,{children:S.jsx(sN,{className:"size-4"})})}),S.jsx(XO,{children:i})]}))}function NN(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(ZO,K(O({"data-slot":"select-scroll-up-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},i),{children:S.jsx(oN,{className:"size-4"})}))}function jN(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(QO,K(O({"data-slot":"select-scroll-down-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},i),{children:S.jsx(xS,{className:"size-4"})}))}function u0(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:S.jsx("table",O({"data-slot":"table",className:Kt("w-full caption-bottom text-sm",n)},i))})}function c0(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("thead",O({"data-slot":"table-header",className:Kt("[&_tr]:border-b",n)},i))}function f0(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("tbody",O({"data-slot":"table-body",className:Kt("[&_tr:last-child]:border-0",n)},i))}function Gf(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("tr",O({"data-slot":"table-row",className:Kt("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",n)},i))}function en(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("th",O({"data-slot":"table-head",className:Kt("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},i))}function nn(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx("td",O({"data-slot":"table-cell",className:Kt("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},i))}const _N=Wb("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d0(u){var d=u,{className:n,variant:i,asChild:s=!1}=d,l=et(d,["className","variant","asChild"]);const f=s?Qb:"span";return S.jsx(f,O({"data-slot":"badge",className:Kt(_N({variant:i}),n)},l))}var Yf="rovingFocusGroup.onEntryFocus",VN={bubbles:!1,cancelable:!0},Qr="RovingFocusGroup",[wd,SS,zN]=ux(Qr),[LN,TS]=Xr(Qr,[zN]),[BN,UN]=LN(Qr),wS=w.forwardRef((n,i)=>S.jsx(wd.Provider,{scope:n.__scopeRovingFocusGroup,children:S.jsx(wd.Slot,{scope:n.__scopeRovingFocusGroup,children:S.jsx(kN,K(O({},n),{ref:i}))})}));wS.displayName=Qr;var kN=w.forwardRef((n,i)=>{const Y=n,{__scopeRovingFocusGroup:s,orientation:l,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:g=!1}=Y,v=et(Y,["__scopeRovingFocusGroup","orientation","loop","dir","currentTabStopId","defaultCurrentTabStopId","onCurrentTabStopIdChange","onEntryFocus","preventScrollOnEntryFocus"]),b=w.useRef(null),T=ae(i,b),C=ah(d),[M,E]=Po({prop:f,defaultProp:h!=null?h:null,onChange:m,caller:Qr}),[N,z]=w.useState(!1),A=Oi(p),V=SS(s),j=w.useRef(!1),[I,J]=w.useState(0);return w.useEffect(()=>{const W=b.current;if(W)return W.addEventListener(Yf,A),()=>W.removeEventListener(Yf,A)},[A]),S.jsx(BN,{scope:s,orientation:l,dir:C,loop:u,currentTabStopId:M,onItemFocus:w.useCallback(W=>E(W),[E]),onItemShiftTab:w.useCallback(()=>z(!0),[]),onFocusableItemAdd:w.useCallback(()=>J(W=>W+1),[]),onFocusableItemRemove:w.useCallback(()=>J(W=>W-1),[]),children:S.jsx(Pt.div,K(O({tabIndex:N||I===0?-1:0,"data-orientation":l},v),{ref:T,style:O({outline:"none"},n.style),onMouseDown:Bt(n.onMouseDown,()=>{j.current=!0}),onFocus:Bt(n.onFocus,W=>{const ft=!j.current;if(W.target===W.currentTarget&&ft&&!N){const ht=new CustomEvent(Yf,VN);if(W.currentTarget.dispatchEvent(ht),!ht.defaultPrevented){const lt=V().filter(q=>q.focusable),bt=lt.find(q=>q.active),Et=lt.find(q=>q.id===M),B=[bt,Et,...lt].filter(Boolean).map(q=>q.ref.current);MS(B,g)}}j.current=!1}),onBlur:Bt(n.onBlur,()=>z(!1))}))})}),AS="RovingFocusGroupItem",ES=w.forwardRef((n,i)=>{const E=n,{__scopeRovingFocusGroup:s,focusable:l=!0,active:u=!1,tabStopId:d,children:f}=E,h=et(E,["__scopeRovingFocusGroup","focusable","active","tabStopId","children"]),m=Kr(),p=d||m,g=UN(AS,s),v=g.currentTabStopId===p,b=SS(s),{onFocusableItemAdd:T,onFocusableItemRemove:C,currentTabStopId:M}=g;return w.useEffect(()=>{if(l)return T(),()=>C()},[l,T,C]),S.jsx(wd.ItemSlot,{scope:s,id:p,focusable:l,active:u,children:S.jsx(Pt.span,K(O({tabIndex:v?0:-1,"data-orientation":g.orientation},h),{ref:i,onMouseDown:Bt(n.onMouseDown,N=>{l?g.onItemFocus(p):N.preventDefault()}),onFocus:Bt(n.onFocus,()=>g.onItemFocus(p)),onKeyDown:Bt(n.onKeyDown,N=>{if(N.key==="Tab"&&N.shiftKey){g.onItemShiftTab();return}if(N.target!==N.currentTarget)return;const z=GN(N,g.orientation,g.dir);if(z!==void 0){if(N.metaKey||N.ctrlKey||N.altKey||N.shiftKey)return;N.preventDefault();let V=b().filter(j=>j.focusable).map(j=>j.ref.current);if(z==="last")V.reverse();else if(z==="prev"||z==="next"){z==="prev"&&V.reverse();const j=V.indexOf(N.currentTarget);V=g.loop?YN(V,j+1):V.slice(j+1)}setTimeout(()=>MS(V))}}),children:typeof f=="function"?f({isCurrentTabStop:v,hasTabStop:M!=null}):f}))})});ES.displayName=AS;var HN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function PN(n,i){return i!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function GN(n,i,s){const l=PN(n.key,s);if(!(i==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(i==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return HN[l]}function MS(n,i=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:i}),document.activeElement!==s))return}function YN(n,i){return n.map((s,l)=>n[(i+l)%n.length])}var qN=wS,XN=ES;function KN(n,i){return w.useReducer((s,l)=>{const u=i[s][l];return u!=null?u:s},n)}var CS=n=>{const{present:i,children:s}=n,l=ZN(i),u=typeof s=="function"?s({present:l.isPresent}):w.Children.only(s),d=ae(l.ref,QN(u));return typeof s=="function"||l.isPresent?w.cloneElement(u,{ref:d}):null};CS.displayName="Presence";function ZN(n){const[i,s]=w.useState(),l=w.useRef(null),u=w.useRef(n),d=w.useRef("none"),f=n?"mounted":"unmounted",[h,m]=KN(f,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const p=To(l.current);d.current=h==="mounted"?p:"none"},[h]),we(()=>{const p=l.current,g=u.current;if(g!==n){const b=d.current,T=To(p);n?m("MOUNT"):T==="none"||(p==null?void 0:p.display)==="none"?m("UNMOUNT"):m(g&&b!==T?"ANIMATION_OUT":"UNMOUNT"),u.current=n}},[n,m]),we(()=>{var p;if(i){let g;const v=(p=i.ownerDocument.defaultView)!=null?p:window,b=C=>{const E=To(l.current).includes(C.animationName);if(C.target===i&&E&&(m("ANIMATION_END"),!u.current)){const N=i.style.animationFillMode;i.style.animationFillMode="forwards",g=v.setTimeout(()=>{i.style.animationFillMode==="forwards"&&(i.style.animationFillMode=N)})}},T=C=>{C.target===i&&(d.current=To(l.current))};return i.addEventListener("animationstart",T),i.addEventListener("animationcancel",b),i.addEventListener("animationend",b),()=>{v.clearTimeout(g),i.removeEventListener("animationstart",T),i.removeEventListener("animationcancel",b),i.removeEventListener("animationend",b)}}else m("ANIMATION_END")},[i,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(p=>{l.current=p?getComputedStyle(p):null,s(p)},[])}}function To(n){return(n==null?void 0:n.animationName)||"none"}function QN(n){var l,u;let i=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=i&&"isReactWarning"in i&&i.isReactWarning;return s?n.ref:(i=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=i&&"isReactWarning"in i&&i.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var Jo="Tabs",[FN,o3]=Xr(Jo,[TS]),RS=TS(),[IN,ph]=FN(Jo),DS=w.forwardRef((n,i)=>{const T=n,{__scopeTabs:s,value:l,onValueChange:u,defaultValue:d,orientation:f="horizontal",dir:h,activationMode:m="automatic"}=T,p=et(T,["__scopeTabs","value","onValueChange","defaultValue","orientation","dir","activationMode"]),g=ah(h),[v,b]=Po({prop:l,onChange:u,defaultProp:d!=null?d:"",caller:Jo});return S.jsx(IN,{scope:s,baseId:Kr(),value:v,onValueChange:b,orientation:f,dir:g,activationMode:m,children:S.jsx(Pt.div,K(O({dir:g,"data-orientation":f},p),{ref:i}))})});DS.displayName=Jo;var OS="TabsList",NS=w.forwardRef((n,i)=>{const h=n,{__scopeTabs:s,loop:l=!0}=h,u=et(h,["__scopeTabs","loop"]),d=ph(OS,s),f=RS(s);return S.jsx(qN,K(O({asChild:!0},f),{orientation:d.orientation,dir:d.dir,loop:l,children:S.jsx(Pt.div,K(O({role:"tablist","aria-orientation":d.orientation},u),{ref:i}))}))});NS.displayName=OS;var jS="TabsTrigger",_S=w.forwardRef((n,i)=>{const v=n,{__scopeTabs:s,value:l,disabled:u=!1}=v,d=et(v,["__scopeTabs","value","disabled"]),f=ph(jS,s),h=RS(s),m=LS(f.baseId,l),p=BS(f.baseId,l),g=l===f.value;return S.jsx(XN,K(O({asChild:!0},h),{focusable:!u,active:g,children:S.jsx(Pt.button,K(O({type:"button",role:"tab","aria-selected":g,"aria-controls":p,"data-state":g?"active":"inactive","data-disabled":u?"":void 0,disabled:u,id:m},d),{ref:i,onMouseDown:Bt(n.onMouseDown,b=>{!u&&b.button===0&&b.ctrlKey===!1?f.onValueChange(l):b.preventDefault()}),onKeyDown:Bt(n.onKeyDown,b=>{[" ","Enter"].includes(b.key)&&f.onValueChange(l)}),onFocus:Bt(n.onFocus,()=>{const b=f.activationMode!=="manual";!g&&!u&&b&&f.onValueChange(l)})}))}))});_S.displayName=jS;var VS="TabsContent",zS=w.forwardRef((n,i)=>{const b=n,{__scopeTabs:s,value:l,forceMount:u,children:d}=b,f=et(b,["__scopeTabs","value","forceMount","children"]),h=ph(VS,s),m=LS(h.baseId,l),p=BS(h.baseId,l),g=l===h.value,v=w.useRef(g);return w.useEffect(()=>{const T=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(T)},[]),S.jsx(CS,{present:u||g,children:({present:T})=>S.jsx(Pt.div,K(O({"data-state":g?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":m,hidden:!T,id:p,tabIndex:0},f),{ref:i,style:K(O({},n.style),{animationDuration:v.current?"0s":void 0}),children:T&&d}))})});zS.displayName=VS;function LS(n,i){return`${n}-trigger-${i}`}function BS(n,i){return`${n}-content-${i}`}var WN=DS,$N=NS,JN=_S,t3=zS;function e3(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(WN,O({"data-slot":"tabs",className:Kt("flex flex-col gap-2",n)},i))}function n3(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx($N,O({"data-slot":"tabs-list",className:Kt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n)},i))}function wo(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(JN,O({"data-slot":"tabs-trigger",className:Kt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},i))}function Ao(s){var l=s,{className:n}=l,i=et(l,["className"]);return S.jsx(t3,O({"data-slot":"tabs-content",className:Kt("flex-1 outline-none",n)},i))}function h0(n,i){const[s,l]=w.useState(()=>{try{const d=window.localStorage.getItem(n);return d?JSON.parse(d):i}catch(d){return console.error(`خطأ في قراءة ${n} من localStorage:`,d),i}});return[s,d=>{try{const f=d instanceof Function?d(s):d;l(f),window.localStorage.setItem(n,JSON.stringify(f))}catch(f){console.error(`خطأ في حفظ ${n} في localStorage:`,f)}}]}function i3(){const[n,i]=w.useState(!0),[s,l]=h0("work-attendance-employees",[]),[u,d]=w.useState({name:"",department:"",startMonth:""}),[f,h]=h0("work-attendance-search-filters",{name:"",department:"",startMonth:"",status:""}),m=["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"];w.useEffect(()=>{const A=setTimeout(()=>{i(!1)},4e3);return()=>clearTimeout(A)},[]),w.useEffect(()=>{const A=V=>{(V.ctrlKey&&V.key==="q"||V.altKey&&V.key==="F4")&&(V.preventDefault(),N())};return document.addEventListener("keydown",A),()=>document.removeEventListener("keydown",A)},[]);const p=A=>{const V=new Date().getMonth(),j=m.indexOf(A);return(V-j+12)%12>=3},g=A=>{if(A.preventDefault(),u.name&&u.department&&u.startMonth){const V=K(O({id:Date.now()},u),{status:p(u.startMonth)});l([...s,V]),d({name:"",department:"",startMonth:""}),C("✅ تم حفظ البيانات بنجاح!")}},v=()=>{l(s.map(A=>K(O({},A),{status:p(A.startMonth)})))},b=()=>{const A={employees:s,exportDate:new Date().toISOString(),version:"1.0"},V=JSON.stringify(A,null,2),j=new Blob([V],{type:"application/json"}),I=document.createElement("a");I.href=URL.createObjectURL(j),I.download=`work-attendance-backup-${new Date().toISOString().split("T")[0]}.json`,I.click(),C("📁 تم تصدير البيانات بنجاح!","success")},T=A=>{const V=A.target.files[0];if(!V)return;const j=new FileReader;j.onload=I=>{try{const J=JSON.parse(I.target.result);J.employees&&Array.isArray(J.employees)?(l(J.employees),C("📥 تم استيراد البيانات بنجاح!","success")):C("❌ ملف غير صالح!","error")}catch(J){console.error("خطأ في استيراد البيانات:",J),C("❌ خطأ في قراءة الملف!","error")}},j.readAsText(V),A.target.value=""},C=(A,V="success")=>{const j=document.createElement("div"),I=V==="success"?"bg-green-500":"bg-red-500";j.className=`fixed top-4 right-4 ${I} text-white px-6 py-3 rounded-lg shadow-lg z-50`,j.textContent=A,document.body.appendChild(j),setTimeout(()=>{document.body.contains(j)&&document.body.removeChild(j)},3e3)},M=()=>{window.confirm("هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!")&&(l([]),h({name:"",department:"",startMonth:"",status:""}),C("🗑️ تم مسح جميع البيانات!","success"))},E=A=>{const V=s.find(j=>j.id===A);V&&window.confirm(`هل أنت متأكد من حذف الموظف "${V.name}"؟`)&&(l(s.filter(j=>j.id!==A)),C(`🗑️ تم حذف الموظف "${V.name}" بنجاح!`,"success"))},N=()=>{window.confirm(`هل تريد إغلاق البرنامج؟

سيتم حفظ جميع البيانات تلقائياً.`)&&(C("👋 شكراً لاستخدام البرنامج!","success"),setTimeout(()=>{try{window.close()}catch(A){try{self.close()}catch(V){window.location.href="about:blank"}}},1e3))},z=s.filter(A=>(!f.name||A.name.includes(f.name))&&(!f.department||A.department.includes(f.department))&&(!f.startMonth||A.startMonth.includes(f.startMonth))&&(!f.status||f.status.includes("مطلوب")&&A.status||f.status.includes("تم")&&!A.status));return n?S.jsx("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:S.jsxs(xn.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1},className:"text-center",children:[S.jsx(xn.h1,{className:"text-6xl font-bold text-white mb-8 typewriter",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"🌟 برنامج استحقاق كشف عمل 🌟"}),S.jsx(xn.p,{className:"text-2xl text-white mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:"👨‍💻 المبرمج: علي عاجل خشان المحنّة"}),S.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:S.jsx(Sn,{onClick:()=>i(!1),className:"pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4",children:"ابدأ الآن"})})]})}):S.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900",children:[S.jsxs("div",{className:"flex",children:[S.jsxs(xn.div,{initial:{x:-300},animate:{x:0},className:"w-80 min-h-screen glassmorphism p-6",children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-2xl font-bold gradient-text mb-2",children:"برنامج استحقاق كشف عمل"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة"})]}),S.jsxs(Sn,{variant:"destructive",className:"w-full mb-6 hover-lift",onClick:N,children:[S.jsx(o0,{className:"mr-2 h-4 w-4"}),"خروج"]})]}),S.jsx("div",{className:"flex-1 p-8",children:S.jsxs(e3,{defaultValue:"input",className:"w-full",children:[S.jsxs(n3,{className:"grid w-full grid-cols-4 mb-8",children:[S.jsxs(wo,{value:"input",className:"flex items-center gap-2",children:[S.jsx(SN,{className:"h-4 w-4"}),"إدخال البيانات"]}),S.jsxs(wo,{value:"employees",className:"flex items-center gap-2",children:[S.jsx(EN,{className:"h-4 w-4"}),"المستحقين للكشوفات"]}),S.jsxs(wo,{value:"search",className:"flex items-center gap-2",children:[S.jsx(pN,{className:"h-4 w-4"}),"البحث المتقدم"]}),S.jsxs(wo,{value:"settings",className:"flex items-center gap-2",children:[S.jsx(yN,{className:"h-4 w-4"}),"الإعدادات"]})]}),S.jsx(Ao,{value:"input",children:S.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(Tn,{className:"max-w-2xl mx-auto neomorphism hover-lift",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-center gradient-text text-2xl",children:"📥 إدخال بيانات موظف جديد"})}),S.jsx(En,{children:S.jsxs("form",{onSubmit:g,className:"space-y-6",children:[S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ia,{htmlFor:"name",className:"flex items-center gap-2",children:[S.jsx(wN,{className:"h-4 w-4"}),"اسم الموظف"]}),S.jsx(ts,{id:"name",value:u.name,onChange:A=>d(K(O({},u),{name:A.target.value})),placeholder:"أدخل اسم الموظف",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ia,{htmlFor:"department",className:"flex items-center gap-2",children:[S.jsx(eN,{className:"h-4 w-4"}),"القسم"]}),S.jsx(ts,{id:"department",value:u.department,onChange:A=>d(K(O({},u),{department:A.target.value})),placeholder:"أدخل اسم القسم",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ia,{htmlFor:"startMonth",className:"flex items-center gap-2",children:[S.jsx(iN,{className:"h-4 w-4"}),"أول شهر للكشف 🌙"]}),S.jsxs(MN,{value:u.startMonth,onValueChange:A=>d(K(O({},u),{startMonth:A})),required:!0,children:[S.jsx(RN,{children:S.jsx(CN,{placeholder:"اختر الشهر"})}),S.jsx(DN,{children:m.map(A=>S.jsx(ON,{value:A,children:A},A))})]})]}),S.jsx(Sn,{type:"submit",className:"w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6",children:"➕ حفظ البيانات"})]})})]})})}),S.jsx(Ao,{value:"employees",children:S.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(Tn,{className:"neomorphism",children:[S.jsxs(wn,{className:"flex flex-row items-center justify-between",children:[S.jsx(An,{className:"gradient-text text-2xl",children:"📄 المستحقين للكشوفات"}),S.jsxs(Sn,{onClick:v,className:"bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500",children:[S.jsx(hN,{className:"mr-2 h-4 w-4"}),"🔁 تحديث الكشفات"]})]}),S.jsx(En,{children:s.length===0?S.jsxs("div",{className:"text-center py-12",children:[S.jsx(bN,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),S.jsx("p",{className:"text-gray-500 text-lg",children:"لا توجد بيانات موظفين حتى الآن"})]}):S.jsxs(u0,{children:[S.jsx(c0,{children:S.jsxs(Gf,{children:[S.jsx(en,{className:"text-right",children:"#"}),S.jsx(en,{className:"text-right",children:"اسم الموظف"}),S.jsx(en,{className:"text-right",children:"القسم"}),S.jsx(en,{className:"text-right",children:"أول شهر"}),S.jsx(en,{className:"text-right",children:"الحالة"}),S.jsx(en,{className:"text-right",children:"إجراءات"})]})}),S.jsx(f0,{children:s.map((A,V)=>S.jsxs(xn.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:V*.1},className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[S.jsx(nn,{children:V+1}),S.jsx(nn,{className:"font-medium",children:A.name}),S.jsx(nn,{children:A.department}),S.jsx(nn,{children:A.startMonth}),S.jsx(nn,{children:S.jsx(d0,{className:A.status?"status-required":"status-completed",children:A.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),S.jsx(nn,{children:S.jsx(Sn,{variant:"destructive",size:"sm",onClick:()=>E(A.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},A.id))})]})})]})})}),S.jsx(Ao,{value:"search",children:S.jsxs(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"gradient-text text-2xl",children:"🔍 البحث المتقدم"})}),S.jsx(En,{children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[S.jsxs("div",{children:[S.jsx(ia,{children:"البحث بالاسم"}),S.jsx(ts,{placeholder:"🔎 اسم الموظف",value:f.name,onChange:A=>h(K(O({},f),{name:A.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ia,{children:"القسم"}),S.jsx(ts,{placeholder:"🏢 اسم القسم",value:f.department,onChange:A=>h(K(O({},f),{department:A.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ia,{children:"أول شهر"}),S.jsx(ts,{placeholder:"🗓️ اسم الشهر",value:f.startMonth,onChange:A=>h(K(O({},f),{startMonth:A.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ia,{children:"الحالة"}),S.jsx(ts,{placeholder:"🔍 الحالة (مطلوب/تم)",value:f.status,onChange:A=>h(K(O({},f),{status:A.target.value})),className:"text-right"})]})]})})]}),S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{children:"نتائج البحث"})}),S.jsx(En,{children:z.length===0?S.jsx("div",{className:"text-center py-8",children:S.jsx("p",{className:"text-gray-500",children:"🚫 لا توجد بيانات مطابقة!"})}):S.jsxs(u0,{children:[S.jsx(c0,{children:S.jsxs(Gf,{children:[S.jsx(en,{className:"text-right",children:"#"}),S.jsx(en,{className:"text-right",children:"اسم الموظف"}),S.jsx(en,{className:"text-right",children:"القسم"}),S.jsx(en,{className:"text-right",children:"أول شهر"}),S.jsx(en,{className:"text-right",children:"الحالة"}),S.jsx(en,{className:"text-right",children:"إجراءات"})]})}),S.jsx(f0,{children:z.map((A,V)=>S.jsxs(Gf,{children:[S.jsx(nn,{children:V+1}),S.jsx(nn,{className:"font-medium",children:A.name}),S.jsx(nn,{children:A.department}),S.jsx(nn,{children:A.startMonth}),S.jsx(nn,{children:S.jsx(d0,{className:A.status?"status-required":"status-completed",children:A.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),S.jsx(nn,{children:S.jsx(Sn,{variant:"destructive",size:"sm",onClick:()=>E(A.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},A.id))})]})})]})]})}),S.jsx(Ao,{value:"settings",children:S.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto space-y-6",children:S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"gradient-text text-2xl",children:"⚙️ الإعدادات"})}),S.jsx(En,{className:"space-y-6",children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[S.jsxs(Tn,{className:"glassmorphism md:col-span-2",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"💾 إدارة البيانات"})}),S.jsxs(En,{children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[S.jsx(Sn,{onClick:b,className:"bg-blue-500 hover:bg-blue-600 text-white",children:"📁 تصدير البيانات"}),S.jsxs("div",{children:[S.jsx("input",{type:"file",accept:".json",onChange:T,style:{display:"none"},id:"import-file"}),S.jsx(Sn,{onClick:()=>document.getElementById("import-file").click(),className:"bg-green-500 hover:bg-green-600 text-white w-full",children:"📥 استيراد البيانات"})]}),S.jsx(Sn,{onClick:M,className:"bg-red-500 hover:bg-red-600 text-white",children:"🗑️ مسح جميع البيانات"})]}),S.jsxs("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[S.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 ",S.jsx("strong",{children:"نصائح:"})]}),S.jsxs("ul",{className:"text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1",children:[S.jsx("li",{children:"• يتم حفظ البيانات تلقائياً في متصفحك"}),S.jsx("li",{children:"• استخدم التصدير لإنشاء نسخة احتياطية"}),S.jsx("li",{children:"• يمكنك استيراد البيانات من ملف JSON"}),S.jsx("li",{children:"• البيانات محفوظة حتى لو أغلقت المتصفح"})]})]})]})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"📝 نبذة عن البرنامج"})}),S.jsx(En,{children:S.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات."})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"👨‍💻 عن المبرمج"})}),S.jsx(En,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"علي عاجل خشان المحنّة"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"مبرمج محترف بخبرة واسعة في:"}),S.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[S.jsx("li",{children:"• VB.NET / C# / Java"}),S.jsx("li",{children:"• JavaScript / React / Node.js"}),S.jsx("li",{children:"• Microsoft Access و Excel VBA"}),S.jsx("li",{children:"• أنظمة الأرشفة الذكية والتحكم الآلي"})]})]})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"🆔 إصدار البرنامج"})}),S.jsx(En,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"v2.0.0"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات."})]})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"🎖️ حقوق التصميم"})}),S.jsx(En,{children:S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"جميع الحقوق محفوظة © 2025"})})]})]})})]})})})]})})]}),S.jsx(Sn,{variant:"destructive",className:"fixed bottom-5 left-5 z-50 rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all duration-300",onClick:N,title:"إغلاق البرنامج (Ctrl+Q)",children:S.jsx(o0,{className:"h-6 w-6"})})]})}gw.createRoot(document.getElementById("root")).render(S.jsx(w.StrictMode,{children:S.jsx(i3,{})}))});export default a3();
