import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Label } from '@/components/ui/label.jsx'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx'
import { 
  UserPlus, 
  Users, 
  Search, 
  Settings, 
  LogOut, 
  RefreshCw, 
  Calendar,
  Building,
  User,
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react'
import './App.css'

// Hook مخصص للحفظ الدائم باستخدام localStorage مع تحسينات إضافية
function useLocalStorage(key, initialValue) {
  // الحصول على القيمة من localStorage أو استخدام القيمة الافتراضية
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key)
      if (item) {
        const parsed = JSON.parse(item)
        // التحقق من صحة البيانات
        if (Array.isArray(parsed) && key.includes('employees')) {
          // التأكد من أن كل موظف له الحقول المطلوبة
          return parsed.filter(emp => emp.id && emp.name && emp.department && emp.startMonth)
        }
        return parsed
      }
      return initialValue
    } catch (error) {
      console.error(`خطأ في قراءة ${key} من localStorage:`, error)
      // محاولة استرداد نسخة احتياطية
      try {
        const backupKey = `${key}_backup`
        const backupItem = window.localStorage.getItem(backupKey)
        if (backupItem) {
          console.log(`استرداد البيانات من النسخة الاحتياطية: ${backupKey}`)
          return JSON.parse(backupItem)
        }
      } catch (backupError) {
        console.error(`خطأ في استرداد النسخة الاحتياطية:`, backupError)
      }
      return initialValue
    }
  })

  // دالة لتحديث القيمة في localStorage والـ state مع حفظ نسخة احتياطية
  const setValue = (value) => {
    try {
      // السماح بتمرير دالة كما في useState العادي
      const valueToStore = value instanceof Function ? value(storedValue) : value

      // حفظ نسخة احتياطية من البيانات الحالية قبل التحديث
      if (storedValue && JSON.stringify(storedValue) !== JSON.stringify(initialValue)) {
        const backupKey = `${key}_backup`
        window.localStorage.setItem(backupKey, JSON.stringify(storedValue))
      }

      // حفظ البيانات الجديدة
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))

      // حفظ طابع زمني للتحديث الأخير
      window.localStorage.setItem(`${key}_lastUpdate`, new Date().toISOString())

    } catch (error) {
      console.error(`خطأ في حفظ ${key} في localStorage:`, error)
      // محاولة تنظيف localStorage إذا كان ممتلئاً
      if (error.name === 'QuotaExceededError') {
        console.log('محاولة تنظيف localStorage...')
        try {
          // حذف البيانات القديمة غير المهمة
          Object.keys(localStorage).forEach(storageKey => {
            if (storageKey.includes('_backup') && !storageKey.includes(key)) {
              localStorage.removeItem(storageKey)
            }
          })
          // محاولة الحفظ مرة أخرى
          window.localStorage.setItem(key, JSON.stringify(valueToStore))
          setStoredValue(valueToStore)
        } catch (retryError) {
          console.error('فشل في الحفظ حتى بعد التنظيف:', retryError)
        }
      }
    }
  }

  return [storedValue, setValue]
}

function App() {
  const [showSplash, setShowSplash] = useState(true)
  const [showUpcomingAlert, setShowUpcomingAlert] = useState(false)
  // استخدام hook الحفظ الدائم للموظفين
  const [employees, setEmployees] = useLocalStorage('work-attendance-employees', [])
  const [formData, setFormData] = useState({
    name: '',
    department: '',
    startMonth: ''
  })
  // حفظ إعدادات البحث أيضاً
  const [searchFilters, setSearchFilters] = useLocalStorage('work-attendance-search-filters', {
    name: '',
    department: '',
    startMonth: '',
    status: ''
  })
  const [activeTab, setActiveTab] = useState('input')

  const months = [
    'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران',
    'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'
  ]

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSplash(false)

      // فحص الموظفين قريبي الاستحقاق بعد إخفاء شاشة البداية
      setTimeout(() => {
        const upcomingCount = getUpcomingCount()
        if (upcomingCount > 0) {
          setShowUpcomingAlert(true)
        }
      }, 500)
    }, 4000)
    return () => clearTimeout(timer)
  }, [employees])

  // حفظ تلقائي دوري للبيانات (كل 30 ثانية)
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (employees.length > 0) {
        try {
          // التحقق من آخر تحديث
          const lastUpdate = localStorage.getItem('work-attendance-employees_lastUpdate')
          const now = new Date().toISOString()

          // حفظ إضافي في حالة عدم وجود تحديث حديث
          if (!lastUpdate || new Date(now) - new Date(lastUpdate) > 30000) {
            localStorage.setItem('work-attendance-employees', JSON.stringify(employees))
            localStorage.setItem('work-attendance-employees_lastUpdate', now)
            console.log('تم الحفظ التلقائي للبيانات')
          }
        } catch (error) {
          console.error('خطأ في الحفظ التلقائي:', error)
        }
      }
    }, 30000) // كل 30 ثانية

    return () => clearInterval(autoSaveInterval)
  }, [employees])

  // حفظ البيانات عند إغلاق النافذة
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      try {
        localStorage.setItem('work-attendance-employees', JSON.stringify(employees))
        localStorage.setItem('work-attendance-search-filters', JSON.stringify(searchFilters))
        console.log('تم حفظ البيانات قبل إغلاق النافذة')
      } catch (error) {
        console.error('خطأ في حفظ البيانات قبل الإغلاق:', error)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [employees, searchFilters])

  // إضافة اختصار لوحة المفاتيح للخروج
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey && e.key === 'q') || (e.altKey && e.key === 'F4')) {
        e.preventDefault()
        exitProgram()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const calculateStatus = (startMonth) => {
    const currentMonth = new Date().getMonth()
    const startMonthIndex = months.indexOf(startMonth)
    const monthsDiff = (currentMonth - startMonthIndex + 12) % 12
    return monthsDiff >= 3
  }

  // دالة حساب الموظفين قريبي الاستحقاق (قبل شهر من الاستحقاق)
  const getUpcomingEmployees = () => {
    const currentMonth = new Date().getMonth()

    return employees.filter(emp => {
      const startMonthIndex = months.indexOf(emp.startMonth)
      const monthsDiff = (currentMonth - startMonthIndex + 12) % 12

      // الموظفون الذين أكملوا شهرين (قبل شهر من الاستحقاق)
      return monthsDiff === 2 && !emp.status
    })
  }

  // دالة حساب عدد الموظفين قريبي الاستحقاق
  const getUpcomingCount = () => {
    return getUpcomingEmployees().length
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.name && formData.department && formData.startMonth) {
      const newEmployee = {
        id: Date.now(),
        ...formData,
        status: calculateStatus(formData.startMonth),
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }
      setEmployees([...employees, newEmployee])
      setFormData({ name: '', department: '', startMonth: '' })

      // إشعار نجاح الحفظ مع تفاصيل إضافية
      showNotification(`✅ تم حفظ بيانات الموظف "${newEmployee.name}" بنجاح!`, 'success')

      // تأكيد الحفظ في localStorage
      setTimeout(() => {
        const saved = localStorage.getItem('work-attendance-employees')
        if (saved && JSON.parse(saved).find(emp => emp.id === newEmployee.id)) {
          console.log('تأكيد: تم حفظ البيانات في التخزين المحلي')
        }
      }, 100)
    }
  }

  const updateStatuses = () => {
    setEmployees(employees.map(emp => ({
      ...emp,
      status: calculateStatus(emp.startMonth)
    })))
  }

  // وظيفة تصدير البيانات إلى ملف JSON
  const exportData = () => {
    const dataToExport = {
      employees: employees,
      exportDate: new Date().toISOString(),
      version: '1.0'
    }

    const dataStr = JSON.stringify(dataToExport, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `work-attendance-backup-${new Date().toISOString().split('T')[0]}.json`
    link.click()

    // إشعار نجاح التصدير
    showNotification('📁 تم تصدير البيانات بنجاح!', 'success')
  }

  // وظيفة تصدير البيانات إلى ملف Excel حقيقي بامتداد .xls
  const exportToExcel = (dataToExport = null) => {
    // استخدام البيانات المرشحة إذا تم تمريرها، وإلا استخدام جميع الموظفين
    const exportEmployees = dataToExport || employees

    if (exportEmployees.length === 0) {
      showNotification('❌ لا توجد بيانات للتصدير!', 'error')
      return
    }

    // إنشاء محتوى Excel بتنسيق HTML مع دعم العربية
    const excelContent = `
      <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <!--[if gte mso 9]>
        <xml>
          <x:ExcelWorkbook>
            <x:ExcelWorksheets>
              <x:ExcelWorksheet>
                <x:Name>كشف الحضور</x:Name>
                <x:WorksheetOptions>
                  <x:DisplayGridlines/>
                </x:WorksheetOptions>
              </x:ExcelWorksheet>
            </x:ExcelWorksheets>
          </x:ExcelWorkbook>
        </xml>
        <![endif]-->
        <style>
          table { border-collapse: collapse; width: 100%; direction: rtl; }
          th, td { border: 1px solid #000; padding: 8px; text-align: center; }
          th { background-color: #4CAF50; color: white; font-weight: bold; }
          .status-required { background-color: #ffebee; color: #c62828; }
          .status-completed { background-color: #e8f5e8; color: #2e7d32; }
        </style>
      </head>
      <body>
        <table>
          <thead>
            <tr>
              <th>الرقم</th>
              <th>اسم الموظف</th>
              <th>القسم</th>
              <th>أول شهر عمل</th>
              <th>حالة الاستحقاق</th>
              <th>تاريخ التصدير</th>
            </tr>
          </thead>
          <tbody>
            ${exportEmployees.map((emp, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${emp.name}</td>
                <td>${emp.department}</td>
                <td>${emp.startMonth}</td>
                <td class="${emp.status ? 'status-required' : 'status-completed'}">
                  ${emp.status ? 'مطلوب كشف' : 'تم استلام الكشف'}
                </td>
                <td>${new Date().toLocaleDateString('en-GB')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `

    // إنشاء ملف Excel بتنسيق .xls
    const blob = new Blob(['\ufeff', excelContent], {
      type: 'application/vnd.ms-excel;charset=utf-8'
    })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `كشف-الحضور-${new Date().toISOString().split('T')[0]}.xls`
    link.click()

    // تنظيف الذاكرة
    URL.revokeObjectURL(link.href)

    // إشعار نجاح التصدير
    showNotification(`📊 تم تصدير ${exportEmployees.length} موظف إلى Excel بنجاح!`, 'success')
  }

  // وظيفة استيراد البيانات من ملف JSON
  const importData = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target.result)
        
        if (importedData.employees && Array.isArray(importedData.employees)) {
          setEmployees(importedData.employees)
          showNotification('📥 تم استيراد البيانات بنجاح!', 'success')
        } else {
          showNotification('❌ ملف غير صالح!', 'error')
        }
      } catch (error) {
        console.error('خطأ في استيراد البيانات:', error)
        showNotification('❌ خطأ في قراءة الملف!', 'error')
      }
    }
    reader.readAsText(file)
    // إعادة تعيين قيمة input لتمكين استيراد نفس الملف مرة أخرى
    event.target.value = ''
  }

  // وظيفة مساعدة لعرض الإشعارات
  const showNotification = (message, type = 'success') => {
    const notification = document.createElement('div')
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500'
    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50`
    notification.textContent = message
    document.body.appendChild(notification)
    setTimeout(() => {
      if (document.body.contains(notification)) {
        document.body.removeChild(notification)
      }
    }, 3000)
  }

  // وظيفة مسح جميع البيانات
  const clearAllData = () => {
    if (window.confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
      setEmployees([])
      setSearchFilters({
        name: '',
        department: '',
        startMonth: '',
        status: ''
      })
      showNotification('🗑️ تم مسح جميع البيانات!', 'success')
    }
  }

  // دالة حذف موظف واحد
  const deleteEmployee = (employeeId) => {
    const employee = employees.find(emp => emp.id === employeeId)
    if (!employee) return

    if (window.confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟`)) {
      setEmployees(employees.filter(emp => emp.id !== employeeId))
      showNotification(`🗑️ تم حذف الموظف "${employee.name}" بنجاح!`, 'success')
    }
  }

  // دالة الخروج من البرنامج
  const exitProgram = () => {
    if (window.confirm('هل تريد إغلاق البرنامج؟\n\nسيتم حفظ جميع البيانات تلقائياً.')) {
      showNotification('👋 شكراً لاستخدام البرنامج!', 'success')

      setTimeout(() => {
        try {
          window.close()
        } catch (e) {
          try {
            self.close()
          } catch (e2) {
            window.location.href = 'about:blank'
          }
        }
      }, 1000)
    }
  }

  const filteredEmployees = employees.filter(emp => {
    return (
      (!searchFilters.name || emp.name.includes(searchFilters.name)) &&
      (!searchFilters.department || emp.department.includes(searchFilters.department)) &&
      (!searchFilters.startMonth || emp.startMonth.includes(searchFilters.startMonth)) &&
      (!searchFilters.status || 
        (searchFilters.status.includes('مطلوب') && emp.status) ||
        (searchFilters.status.includes('تم') && !emp.status))
    )
  })

  if (showSplash) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <motion.div 
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1 }}
          className="text-center"
        >
          <motion.h1 
            className="text-6xl font-bold text-white mb-8 typewriter"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            🌟 برنامج استحقاق كشف عمل 🌟
          </motion.h1>
          
          <motion.p 
            className="text-2xl text-white mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2 }}
          >
            👨‍💻 المبرمج: علي عاجل خشان المحنّة
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 3 }}
          >
            <Button 
              onClick={() => setShowSplash(false)}
              className="pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4"
            >
              ابدأ الآن
            </Button>
          </motion.div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900">
      <div className="flex">
        {/* Sidebar */}
        <motion.div 
          initial={{ x: -300 }}
          animate={{ x: 0 }}
          className="w-80 min-h-screen glassmorphism p-6"
        >
          <div className="mb-8">
            <h2 className="text-2xl font-bold gradient-text mb-2">برنامج استحقاق كشف عمل</h2>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة
            </p>
          </div>
          
          <Button
            variant="destructive"
            className="w-full mb-6 hover-lift"
            onClick={exitProgram}
          >
            <LogOut className="mr-2 h-4 w-4" />
            خروج
          </Button>
        </motion.div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-8">
              <TabsTrigger value="input" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                إدخال البيانات
              </TabsTrigger>
              <TabsTrigger value="employees" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                المستحقين للكشوفات
              </TabsTrigger>
              <TabsTrigger value="search" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                البحث المتقدم
              </TabsTrigger>
              <TabsTrigger value="upcoming" className="flex items-center gap-2 relative">
                <AlertCircle className="h-4 w-4" />
                إشعارات الاستحقاق
                {getUpcomingCount() > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {getUpcomingCount()}
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                الإعدادات
              </TabsTrigger>
            </TabsList>

            {/* Input Tab */}
            <TabsContent value="input">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="max-w-2xl mx-auto neomorphism hover-lift">
                  <CardHeader>
                    <CardTitle className="text-center gradient-text text-2xl">
                      📥 إدخال بيانات موظف جديد
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          اسم الموظف
                        </Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="أدخل اسم الموظف"
                          className="text-right"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="department" className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          القسم
                        </Label>
                        <Input
                          id="department"
                          value={formData.department}
                          onChange={(e) => setFormData({...formData, department: e.target.value})}
                          placeholder="أدخل اسم القسم"
                          className="text-right"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="startMonth" className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          أول شهر للكشف 🌙
                        </Label>
                        <Select 
                          value={formData.startMonth} 
                          onValueChange={(value) => setFormData({...formData, startMonth: value})}
                          required
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر الشهر" />
                          </SelectTrigger>
                          <SelectContent>
                            {months.map(month => (
                              <SelectItem key={month} value={month}>{month}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <Button 
                        type="submit" 
                        className="w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6"
                      >
                        ➕ حفظ البيانات
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Employees Tab */}
            <TabsContent value="employees">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="neomorphism">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="gradient-text text-2xl">
                      📄 المستحقين للكشوفات
                    </CardTitle>
                    <Button 
                      onClick={updateStatuses}
                      className="bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500"
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      🔁 تحديث الكشفات
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {employees.length === 0 ? (
                      <div className="text-center py-12">
                        <Sparkles className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                        <p className="text-gray-500 text-lg">لا توجد بيانات موظفين حتى الآن</p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">#</TableHead>
                            <TableHead className="text-right">اسم الموظف</TableHead>
                            <TableHead className="text-right">القسم</TableHead>
                            <TableHead className="text-right">أول شهر</TableHead>
                            <TableHead className="text-right">الحالة</TableHead>
                            <TableHead className="text-right">إجراءات</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {employees.map((employee, index) => (
                            <motion.tr
                              key={employee.id}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="hover:bg-gray-50 dark:hover:bg-gray-800"
                            >
                              <TableCell>{index + 1}</TableCell>
                              <TableCell className="font-medium">{employee.name}</TableCell>
                              <TableCell>{employee.department}</TableCell>
                              <TableCell>{employee.startMonth}</TableCell>
                              <TableCell>
                                <Badge
                                  className={employee.status ? 'status-required' : 'status-completed'}
                                >
                                  {employee.status ? (
                                    <>
                                      <AlertCircle className="mr-1 h-3 w-3" />
                                      🔴 مطلوب كشف
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle className="mr-1 h-3 w-3" />
                                      🟢 تم التسليم
                                    </>
                                  )}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => deleteEmployee(employee.id)}
                                  className="h-8 px-3"
                                >
                                  🗑️ حذف
                                </Button>
                              </TableCell>
                            </motion.tr>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Search Tab */}
            <TabsContent value="search">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <Card className="neomorphism">
                  <CardHeader>
                    <CardTitle className="gradient-text text-2xl">
                      🔍 البحث المتقدم
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <Label>البحث بالاسم</Label>
                        <Input
                          placeholder="🔎 اسم الموظف"
                          value={searchFilters.name}
                          onChange={(e) => setSearchFilters({...searchFilters, name: e.target.value})}
                          className="text-right"
                        />
                      </div>
                      
                      <div>
                        <Label>القسم</Label>
                        <Input
                          placeholder="🏢 اسم القسم"
                          value={searchFilters.department}
                          onChange={(e) => setSearchFilters({...searchFilters, department: e.target.value})}
                          className="text-right"
                        />
                      </div>
                      
                      <div>
                        <Label>أول شهر</Label>
                        <Input
                          placeholder="🗓️ اسم الشهر"
                          value={searchFilters.startMonth}
                          onChange={(e) => setSearchFilters({...searchFilters, startMonth: e.target.value})}
                          className="text-right"
                        />
                      </div>
                      
                      <div>
                        <Label>الحالة</Label>
                        <Input
                          placeholder="🔍 الحالة (مطلوب/تم)"
                          value={searchFilters.status}
                          onChange={(e) => setSearchFilters({...searchFilters, status: e.target.value})}
                          className="text-right"
                        />
                      </div>
                    </div>

                    {/* أزرار العمليات */}
                    <div className="flex flex-wrap gap-4 mt-6 pt-4 border-t">
                      <Button
                        onClick={() => exportToExcel(filteredEmployees)}
                        className="bg-green-500 hover:bg-green-600 text-white flex items-center gap-2"
                        disabled={filteredEmployees.length === 0}
                      >
                        📊 تصدير إلى Excel
                      </Button>

                      <Button
                        onClick={() => exportToExcel()}
                        className="bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
                      >
                        📋 تصدير جميع البيانات
                      </Button>

                      <div className="text-sm text-gray-600 flex items-center">
                        📈 عدد النتائج: {filteredEmployees.length} من أصل {employees.length}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="neomorphism">
                  <CardHeader>
                    <CardTitle>نتائج البحث</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {filteredEmployees.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">🚫 لا توجد بيانات مطابقة!</p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">#</TableHead>
                            <TableHead className="text-right">اسم الموظف</TableHead>
                            <TableHead className="text-right">القسم</TableHead>
                            <TableHead className="text-right">أول شهر</TableHead>
                            <TableHead className="text-right">الحالة</TableHead>
                            <TableHead className="text-right">إجراءات</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredEmployees.map((employee, index) => (
                            <TableRow key={employee.id}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell className="font-medium">{employee.name}</TableCell>
                              <TableCell>{employee.department}</TableCell>
                              <TableCell>{employee.startMonth}</TableCell>
                              <TableCell>
                                <Badge
                                  className={employee.status ? 'status-required' : 'status-completed'}
                                >
                                  {employee.status ? (
                                    <>
                                      <AlertCircle className="mr-1 h-3 w-3" />
                                      🔴 مطلوب كشف
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle className="mr-1 h-3 w-3" />
                                      🟢 تم التسليم
                                    </>
                                  )}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => deleteEmployee(employee.id)}
                                  className="h-8 px-3"
                                >
                                  🗑️ حذف
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Upcoming Tab */}
            <TabsContent value="upcoming">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <Card className="neomorphism">
                  <CardHeader>
                    <CardTitle className="gradient-text text-2xl flex items-center gap-2">
                      🔔 إشعارات قرب الاستحقاق
                      {getUpcomingCount() > 0 && (
                        <Badge className="bg-orange-500 text-white">
                          {getUpcomingCount()} موظف
                        </Badge>
                      )}
                    </CardTitle>
                    <p className="text-gray-600 dark:text-gray-400">
                      الموظفون الذين سيستحقون كشف العمل خلال شهر واحد
                    </p>
                  </CardHeader>
                  <CardContent>
                    {getUpcomingEmployees().length === 0 ? (
                      <div className="text-center py-12">
                        <div className="text-6xl mb-4">🎉</div>
                        <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                          لا توجد إشعارات جديدة!
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400">
                          جميع الموظفين إما لم يحن موعد استحقاقهم بعد أو تم تسليم كشوفاتهم
                        </p>
                      </div>
                    ) : (
                      <>
                        <div className="mb-6 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                          <div className="flex items-center gap-2 text-orange-700 dark:text-orange-300">
                            <AlertCircle className="h-5 w-5" />
                            <span className="font-semibold">تنبيه مهم:</span>
                          </div>
                          <p className="text-orange-600 dark:text-orange-400 mt-1">
                            الموظفون التالون سيستحقون كشف العمل خلال شهر واحد. يُنصح بالتحضير مسبقاً.
                          </p>
                        </div>

                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="text-right">#</TableHead>
                              <TableHead className="text-right">اسم الموظف</TableHead>
                              <TableHead className="text-right">القسم</TableHead>
                              <TableHead className="text-right">أول شهر عمل</TableHead>
                              <TableHead className="text-right">الأشهر المكتملة</TableHead>
                              <TableHead className="text-right">موعد الاستحقاق المتوقع</TableHead>
                              <TableHead className="text-right">إجراءات</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {getUpcomingEmployees().map((employee, index) => {
                              const startMonthIndex = months.indexOf(employee.startMonth)
                              const expectedMonth = months[(startMonthIndex + 3) % 12]

                              return (
                                <motion.tr
                                  key={employee.id}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: index * 0.1 }}
                                  className="hover:bg-orange-50 dark:hover:bg-orange-900/10"
                                >
                                  <TableCell>{index + 1}</TableCell>
                                  <TableCell className="font-medium">{employee.name}</TableCell>
                                  <TableCell>{employee.department}</TableCell>
                                  <TableCell>{employee.startMonth}</TableCell>
                                  <TableCell>
                                    <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                      شهرين مكتملين
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                      {expectedMonth}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          setActiveTab('employees')
                                          // التمرير للموظف في قائمة الموظفين
                                        }}
                                        className="h-8 px-3"
                                      >
                                        👁️ عرض
                                      </Button>
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => deleteEmployee(employee.id)}
                                        className="h-8 px-3"
                                      >
                                        🗑️ حذف
                                      </Button>
                                    </div>
                                  </TableCell>
                                </motion.tr>
                              )
                            })}
                          </TableBody>
                        </Table>

                        <div className="mt-6 flex flex-wrap gap-4">
                          <Button
                            onClick={() => exportToExcel(getUpcomingEmployees())}
                            className="bg-orange-500 hover:bg-orange-600 text-white flex items-center gap-2"
                          >
                            📊 تصدير قائمة الإشعارات
                          </Button>

                          <Button
                            onClick={() => setActiveTab('input')}
                            className="bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
                          >
                            ➕ إضافة موظف جديد
                          </Button>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="max-w-4xl mx-auto space-y-6"
              >
                <Card className="neomorphism">
                  <CardHeader>
                    <CardTitle className="gradient-text text-2xl">
                      ⚙️ الإعدادات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* قسم إدارة البيانات */}
                      <Card className="glassmorphism md:col-span-2">
                        <CardHeader>
                          <CardTitle className="text-lg">💾 إدارة البيانات</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Button 
                              onClick={exportData}
                              className="bg-blue-500 hover:bg-blue-600 text-white"
                            >
                              📁 تصدير البيانات
                            </Button>
                            
                            <div>
                              <input
                                type="file"
                                accept=".json"
                                onChange={importData}
                                style={{ display: 'none' }}
                                id="import-file"
                              />
                              <Button 
                                onClick={() => document.getElementById('import-file').click()}
                                className="bg-green-500 hover:bg-green-600 text-white w-full"
                              >
                                📥 استيراد البيانات
                              </Button>
                            </div>
                            
                            <Button 
                              onClick={clearAllData}
                              className="bg-red-500 hover:bg-red-600 text-white"
                            >
                              🗑️ مسح جميع البيانات
                            </Button>
                          </div>
                          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <p className="text-sm text-blue-700 dark:text-blue-300">
                              💡 <strong>نصائح:</strong>
                            </p>
                            <ul className="text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1">
                              <li>• يتم حفظ البيانات تلقائياً في متصفحك</li>
                              <li>• استخدم التصدير لإنشاء نسخة احتياطية</li>
                              <li>• يمكنك استيراد البيانات من ملف JSON</li>
                              <li>• البيانات محفوظة حتى لو أغلقت المتصفح</li>
                              <li>• يتم إنشاء نسخة احتياطية تلقائياً عند كل تحديث</li>
                            </ul>
                          </div>

                          {/* معلومات حالة التخزين */}
                          <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <p className="text-sm text-green-700 dark:text-green-300 font-semibold mb-2">
                              📊 معلومات التخزين:
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-green-600 dark:text-green-400">
                                  👥 عدد الموظفين المحفوظين: <strong>{employees.length}</strong>
                                </span>
                              </div>
                              <div>
                                <span className="text-green-600 dark:text-green-400">
                                  💾 حجم البيانات: <strong>
                                    {Math.round(JSON.stringify(employees).length / 1024)} KB
                                  </strong>
                                </span>
                              </div>
                              <div>
                                <span className="text-green-600 dark:text-green-400">
                                  🕒 آخر تحديث: <strong>
                                    {localStorage.getItem('work-attendance-employees_lastUpdate')
                                      ? new Date(localStorage.getItem('work-attendance-employees_lastUpdate')).toLocaleString('ar-SA')
                                      : 'غير محدد'
                                    }
                                  </strong>
                                </span>
                              </div>
                              <div>
                                <span className="text-green-600 dark:text-green-400">
                                  🔄 الحفظ التلقائي: <strong>مفعل</strong>
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="glassmorphism">
                        <CardHeader>
                          <CardTitle className="text-lg">📝 نبذة عن البرنامج</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-600 dark:text-gray-300">
                            برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات.
                          </p>
                        </CardContent>
                      </Card>

                      <Card className="glassmorphism">
                        <CardHeader>
                          <CardTitle className="text-lg">👨‍💻 عن المبرمج</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <p className="font-semibold">علي عاجل خشان المحنّة</p>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              مبرمج محترف بخبرة واسعة في:
                            </p>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              <li>• VB.NET / C# / Java</li>
                              <li>• JavaScript / React / Node.js</li>
                              <li>• Microsoft Access و Excel VBA</li>
                              <li>• أنظمة الأرشفة الذكية والتحكم الآلي</li>
                            </ul>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="glassmorphism">
                        <CardHeader>
                          <CardTitle className="text-lg">🆔 إصدار البرنامج</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <p className="font-semibold">v2.0.0</p>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات.
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="glassmorphism">
                        <CardHeader>
                          <CardTitle className="text-lg">🎖️ حقوق التصميم</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            جميع الحقوق محفوظة © 2025
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* زر الخروج الثابت */}
      <Button
        variant="destructive"
        className="fixed bottom-5 left-5 z-50 rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all duration-300"
        onClick={exitProgram}
        title="إغلاق البرنامج (Ctrl+Q)"
      >
        <LogOut className="h-6 w-6" />
      </Button>

      {/* نافذة إشعار قرب الاستحقاق */}
      {showUpcomingAlert && getUpcomingCount() > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-md w-full mx-4 p-6"
          >
            <div className="text-center">
              <div className="text-6xl mb-4">🔔</div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                تنبيه: موظفون قريبون من الاستحقاق!
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                يوجد <strong className="text-orange-600">{getUpcomingCount()}</strong> موظف سيستحق كشف العمل خلال شهر واحد
              </p>

              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 mb-6">
                <div className="text-sm text-orange-700 dark:text-orange-300">
                  <strong>الموظفون:</strong>
                  <ul className="mt-2 space-y-1">
                    {getUpcomingEmployees().slice(0, 3).map((emp, index) => (
                      <li key={emp.id} className="flex justify-between">
                        <span>{emp.name}</span>
                        <span className="text-xs">{emp.department}</span>
                      </li>
                    ))}
                    {getUpcomingCount() > 3 && (
                      <li className="text-xs text-orange-500">
                        و {getUpcomingCount() - 3} موظف آخر...
                      </li>
                    )}
                  </ul>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    setShowUpcomingAlert(false)
                    setActiveTab('upcoming')
                  }}
                  className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                >
                  📋 عرض التفاصيل
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowUpcomingAlert(false)}
                  className="flex-1"
                >
                  ❌ إغلاق
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default App

