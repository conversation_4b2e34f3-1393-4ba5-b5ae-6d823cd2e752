{"name": "embla-carousel", "version": "8.6.0", "author": "<PERSON>", "description": "A lightweight carousel library with fluid motion and great swipe precision", "repository": {"type": "git", "url": "git+https://github.com/davidjerleke/embla-carousel"}, "bugs": {"url": "https://github.com/david<PERSON>le<PERSON>/embla-carousel/issues"}, "homepage": "https://www.embla-carousel.com", "license": "MIT", "keywords": ["slider", "carousel", "slideshow", "gallery", "lightweight", "touch", "javascript", "typescript", "react", "vue", "svelte", "solid"], "main": "embla-carousel.umd.js", "unpkg": "embla-carousel.umd.js", "module": "./esm/embla-carousel.esm.js", "types": "index.d.ts", "sideEffects": false, "files": ["embla-carousel*", "components/**/*", "index.d.ts", "esm/**/*", "cjs/**/*"], "scripts": {"test": "jest --config jest.config.js", "build": "rollup --bundleConfigAsCjs -c", "start": "rollup --bundleConfigAsCjs -c --watch --environment BUILD:development", "eslint:report": "eslint \"src/**/*.{js,tsx,ts}\""}, "devDependencies": {"@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "2.8.8", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./esm/index.d.ts", "default": "./esm/embla-carousel.esm.js"}, "require": {"types": "./cjs/index.d.ts", "default": "./cjs/embla-carousel.cjs.js"}}}}