@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\bin\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\bin\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules\@electron\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\@electron+windows-sign@1.2.2\node_modules;C:\Users\<USER>\Desktop\كوثر\work-attendance-system-v2.0-final\work-attendance-system\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\bin\electron-windows-sign.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\@electron+windows-sign@1.2.2\node_modules\@electron\windows-sign\bin\electron-windows-sign.js" %*
)
