<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج استحقاق كشف عمل - علي عاجل خشان المحنّة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
            width: auto;
            margin: 5px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-required {
            background: #ffebee;
            color: #c62828;
        }
        
        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .btn-delete {
            background: #f44336;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-delete:hover {
            background: #d32f2f;
        }

        .exit-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
            transition: all 0.3s;
            z-index: 1000;
        }

        .exit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4);
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            padding: 5px;
        }
        
        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.3s;
        }
        
        .tab.active {
            background: rgba(255,255,255,0.3);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: #4caf50;
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 20px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .search-filters {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 برنامج استحقاق كشف عمل 🌟</h1>
            <p>👨‍💻 المبرمج: علي عاجل خشان المحنّة</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('input')">📥 إدخال البيانات</button>
            <button class="tab" onclick="showTab('employees')">👥 المستحقين للكشوفات</button>
            <button class="tab" onclick="showTab('search')">🔍 البحث المتقدم</button>
            <button class="tab" onclick="showTab('upcoming')" id="upcomingTab">
                🔔 إشعارات الاستحقاق
                <span id="upcomingBadge" style="display: none; background: #ff4444; color: white; border-radius: 50%; padding: 2px 6px; font-size: 12px; margin-left: 5px;"></span>
            </button>
            <button class="tab" onclick="showTab('settings')">⚙️ الإعدادات</button>
        </div>
        
        <!-- تبويب إدخال البيانات -->
        <div id="input" class="tab-content active">
            <div class="card">
                <h2 style="text-align: center; margin-bottom: 30px; color: #667eea;">📥 إدخال بيانات موظف جديد</h2>
                <form id="employeeForm">
                    <div class="form-group">
                        <label for="name">👤 اسم الموظف</label>
                        <input type="text" id="name" required placeholder="أدخل اسم الموظف">
                    </div>
                    
                    <div class="form-group">
                        <label for="department">🏢 القسم</label>
                        <input type="text" id="department" required placeholder="أدخل اسم القسم">
                    </div>
                    
                    <div class="form-group">
                        <label for="startMonth">📅 أول شهر للكشف</label>
                        <select id="startMonth" required>
                            <option value="">اختر الشهر</option>
                            <option value="كانون الثاني">كانون الثاني</option>
                            <option value="شباط">شباط</option>
                            <option value="آذار">آذار</option>
                            <option value="نيسان">نيسان</option>
                            <option value="أيار">أيار</option>
                            <option value="حزيران">حزيران</option>
                            <option value="تموز">تموز</option>
                            <option value="آب">آب</option>
                            <option value="أيلول">أيلول</option>
                            <option value="تشرين الأول">تشرين الأول</option>
                            <option value="تشرين الثاني">تشرين الثاني</option>
                            <option value="كانون الأول">كانون الأول</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">➕ حفظ البيانات</button>
                </form>
            </div>
        </div>
        
        <!-- تبويب المستحقين للكشوفات -->
        <div id="employees" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 style="color: #667eea;">📄 المستحقين للكشوفات</h2>
                    <button class="btn btn-small" onclick="updateStatuses()">🔄 تحديث الكشفات</button>
                </div>
                <div id="employeesList">
                    <p style="text-align: center; color: #666; padding: 40px;">لا توجد بيانات موظفين حتى الآن</p>
                </div>
            </div>
        </div>
        
        <!-- تبويب البحث المتقدم -->
        <div id="search" class="tab-content">
            <div class="card">
                <h2 style="color: #667eea; margin-bottom: 20px;">🔍 البحث المتقدم</h2>
                <div class="search-filters">
                    <div class="form-group">
                        <label>البحث بالاسم</label>
                        <input type="text" id="searchName" placeholder="🔎 اسم الموظف" oninput="filterEmployees()">
                    </div>
                    <div class="form-group">
                        <label>القسم</label>
                        <input type="text" id="searchDepartment" placeholder="🏢 اسم القسم" oninput="filterEmployees()">
                    </div>
                    <div class="form-group">
                        <label>أول شهر</label>
                        <input type="text" id="searchMonth" placeholder="📅 اسم الشهر" oninput="filterEmployees()">
                    </div>
                    <div class="form-group">
                        <label>الحالة</label>
                        <input type="text" id="searchStatus" placeholder="🔍 الحالة (مطلوب/تم)" oninput="filterEmployees()">
                    </div>
                </div>

                <!-- أزرار تصدير Excel -->
                <div style="margin: 20px 0; padding: 15px; border-top: 2px solid #e0e0e0; display: flex; gap: 10px; flex-wrap: wrap; align-items: center;">
                    <button onclick="exportFilteredToExcel()"
                            style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-size: 14px; display: flex; align-items: center; gap: 5px;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                        📊 تصدير النتائج إلى Excel
                    </button>

                    <button onclick="exportToExcel()"
                            style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-size: 14px; display: flex; align-items: center; gap: 5px;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                        📋 تصدير جميع البيانات
                    </button>

                    <span id="searchCount" style="color: #666; font-size: 14px; margin-left: 10px;">
                        📈 عدد النتائج: 0
                    </span>
                </div>

                <div id="searchResults">
                    <p style="text-align: center; color: #666; padding: 40px;">أدخل معايير البحث أعلاه</p>
                </div>
            </div>
        </div>

        <!-- تبويب إشعارات الاستحقاق -->
        <div id="upcoming" class="tab-content">
            <div class="card">
                <h2 style="color: #667eea; margin-bottom: 20px;">
                    🔔 إشعارات قرب الاستحقاق
                    <span id="upcomingCountDisplay" style="background: #ff9800; color: white; padding: 4px 8px; border-radius: 12px; font-size: 14px; margin-right: 10px;"></span>
                </h2>
                <p style="color: #666; margin-bottom: 20px;">الموظفون الذين سيستحقون كشف العمل خلال شهر واحد</p>

                <div id="upcomingContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>

        <!-- تبويب الإعدادات -->
        <div id="settings" class="tab-content">
            <div class="card">
                <h2 style="color: #667eea; margin-bottom: 30px;">⚙️ الإعدادات</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                    <button class="btn btn-small" onclick="exportData()" style="background: #2196f3;">📁 تصدير البيانات</button>
                    <button class="btn btn-small" onclick="document.getElementById('importFile').click()" style="background: #4caf50;">📥 استيراد البيانات</button>
                    <button class="btn btn-small" onclick="clearAllData()" style="background: #f44336;">🗑️ مسح جميع البيانات</button>
                </div>
                
                <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                
                <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="color: #1976d2; margin-bottom: 15px;">💡 نصائح مهمة:</h3>
                    <ul style="color: #1565c0; line-height: 1.8;">
                        <li>• يتم حفظ البيانات تلقائياً في متصفحك</li>
                        <li>• استخدم التصدير لإنشاء نسخة احتياطية</li>
                        <li>• يمكنك استيراد البيانات من ملف JSON</li>
                        <li>• البيانات محفوظة حتى لو أغلقت المتصفح</li>
                    </ul>
                </div>
                
                <div style="background: #f5f5f5; padding: 20px; border-radius: 10px;">
                    <h3 style="color: #333; margin-bottom: 15px;">📝 معلومات البرنامج:</h3>
                    <p><strong>الاسم:</strong> برنامج استحقاق كشف عمل</p>
                    <p><strong>الإصدار:</strong> v2.0.0 (نسخة مستقلة)</p>
                    <p><strong>المبرمج:</strong> علي عاجل خشان المحنّة</p>
                    <p><strong>التاريخ:</strong> 2025</p>
                    <p><strong>الوصف:</strong> برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها</p>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الخروج -->
    <button class="exit-button" onclick="exitProgram()" title="إغلاق البرنامج (Ctrl+Q)">
        🚪 خروج
    </button>

    <div id="notification" class="notification"></div>

    <script>
        // متغيرات عامة
        let employees = JSON.parse(localStorage.getItem('work-attendance-employees') || '[]');
        let searchFilters = JSON.parse(localStorage.getItem('work-attendance-search-filters') || '{}');
        let filteredEmployees = []; // لحفظ النتائج المرشحة

        const months = [
            'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران',
            'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'
        ];

        // دالة عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // تحديث المحتوى حسب التبويب
            if (tabName === 'employees') {
                displayEmployees();
            } else if (tabName === 'search') {
                filterEmployees();
            } else if (tabName === 'upcoming') {
                displayUpcomingEmployees();
            }
        }

        // دالة حساب حالة الاستحقاق
        function calculateStatus(startMonth) {
            const currentMonth = new Date().getMonth();
            const startMonthIndex = months.indexOf(startMonth);
            const monthsDiff = (currentMonth - startMonthIndex + 12) % 12;
            return monthsDiff >= 3;
        }

        // دالة حساب الموظفين قريبي الاستحقاق (قبل شهر من الاستحقاق)
        function getUpcomingEmployees() {
            const currentMonth = new Date().getMonth();

            return employees.filter(emp => {
                const startMonthIndex = months.indexOf(emp.startMonth);
                const monthsDiff = (currentMonth - startMonthIndex + 12) % 12;

                // الموظفون الذين أكملوا شهرين (قبل شهر من الاستحقاق)
                return monthsDiff === 2 && !emp.status;
            });
        }

        // دالة حساب عدد الموظفين قريبي الاستحقاق
        function getUpcomingCount() {
            return getUpcomingEmployees().length;
        }

        // دالة حفظ البيانات المحسنة مع نسخ احتياطية
        function saveData() {
            try {
                // حفظ نسخة احتياطية من البيانات الحالية
                const currentData = localStorage.getItem('work-attendance-employees');
                if (currentData && currentData !== '[]') {
                    localStorage.setItem('work-attendance-employees_backup', currentData);
                }

                // حفظ البيانات الجديدة
                localStorage.setItem('work-attendance-employees', JSON.stringify(employees));
                localStorage.setItem('work-attendance-search-filters', JSON.stringify(searchFilters));

                // حفظ طابع زمني للتحديث الأخير
                localStorage.setItem('work-attendance-employees_lastUpdate', new Date().toISOString());

                console.log('تم حفظ البيانات بنجاح');
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);

                // محاولة تنظيف localStorage إذا كان ممتلئاً
                if (error.name === 'QuotaExceededError') {
                    console.log('محاولة تنظيف localStorage...');
                    try {
                        // حذف البيانات القديمة غير المهمة
                        Object.keys(localStorage).forEach(key => {
                            if (key.includes('_backup') && !key.includes('work-attendance')) {
                                localStorage.removeItem(key);
                            }
                        });
                        // محاولة الحفظ مرة أخرى
                        localStorage.setItem('work-attendance-employees', JSON.stringify(employees));
                        localStorage.setItem('work-attendance-search-filters', JSON.stringify(searchFilters));
                    } catch (retryError) {
                        console.error('فشل في الحفظ حتى بعد التنظيف:', retryError);
                        showNotification('❌ خطأ في حفظ البيانات! مساحة التخزين ممتلئة.', 'error');
                    }
                }
            }
        }

        // دالة عرض الإشعارات
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.background = type === 'success' ? '#4caf50' : '#f44336';
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // دالة إضافة موظف جديد
        function addEmployee(event) {
            event.preventDefault();

            const name = document.getElementById('name').value;
            const department = document.getElementById('department').value;
            const startMonth = document.getElementById('startMonth').value;

            if (name && department && startMonth) {
                const newEmployee = {
                    id: Date.now(),
                    name: name,
                    department: department,
                    startMonth: startMonth,
                    status: calculateStatus(startMonth)
                };

                employees.push(newEmployee);
                saveData();

                // إعادة تعيين النموذج
                document.getElementById('employeeForm').reset();

                showNotification('✅ تم حفظ البيانات بنجاح!');

                // تحديث العرض إذا كان في تبويب الموظفين
                if (document.getElementById('employees').classList.contains('active')) {
                    displayEmployees();
                }
            }
        }

        // دالة عرض الموظفين
        function displayEmployees() {
            const container = document.getElementById('employeesList');

            if (employees.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">لا توجد بيانات موظفين حتى الآن</p>';
                return;
            }

            let html = '<table class="table"><thead><tr>';
            html += '<th>#</th><th>اسم الموظف</th><th>القسم</th><th>أول شهر</th><th>الحالة</th><th>إجراءات</th>';
            html += '</tr></thead><tbody>';

            employees.forEach((employee, index) => {
                const statusClass = employee.status ? 'status-required' : 'status-completed';
                const statusText = employee.status ? '🔴 مطلوب كشف' : '🟢 تم التسليم';

                html += `<tr>
                    <td>${index + 1}</td>
                    <td>${employee.name}</td>
                    <td>${employee.department}</td>
                    <td>${employee.startMonth}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="btn-delete" onclick="deleteEmployee(${employee.id})" title="حذف الموظف">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // دالة تحديث حالات الاستحقاق
        function updateStatuses() {
            employees.forEach(emp => {
                emp.status = calculateStatus(emp.startMonth);
            });
            saveData();
            displayEmployees();
            showNotification('🔄 تم تحديث الكشفات بنجاح!');
        }

        // دالة البحث والتصفية
        function filterEmployees() {
            const nameFilter = document.getElementById('searchName').value.toLowerCase();
            const departmentFilter = document.getElementById('searchDepartment').value.toLowerCase();
            const monthFilter = document.getElementById('searchMonth').value.toLowerCase();
            const statusFilter = document.getElementById('searchStatus').value.toLowerCase();

            const filtered = employees.filter(emp => {
                const nameMatch = !nameFilter || emp.name.toLowerCase().includes(nameFilter);
                const deptMatch = !departmentFilter || emp.department.toLowerCase().includes(departmentFilter);
                const monthMatch = !monthFilter || emp.startMonth.toLowerCase().includes(monthFilter);
                const statusMatch = !statusFilter ||
                    (statusFilter.includes('مطلوب') && emp.status) ||
                    (statusFilter.includes('تم') && !emp.status);

                return nameMatch && deptMatch && monthMatch && statusMatch;
            });

            // حفظ النتائج المرشحة للتصدير
            filteredEmployees = filtered;

            // تحديث عداد النتائج
            const searchCountElement = document.getElementById('searchCount');
            if (searchCountElement) {
                searchCountElement.textContent = `📈 عدد النتائج: ${filtered.length} من أصل ${employees.length}`;
            }

            const container = document.getElementById('searchResults');

            if (filtered.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">🚫 لا توجد بيانات مطابقة!</p>';
                return;
            }

            let html = '<table class="table"><thead><tr>';
            html += '<th>#</th><th>اسم الموظف</th><th>القسم</th><th>أول شهر</th><th>الحالة</th><th>إجراءات</th>';
            html += '</tr></thead><tbody>';

            filtered.forEach((employee, index) => {
                const statusClass = employee.status ? 'status-required' : 'status-completed';
                const statusText = employee.status ? '🔴 مطلوب كشف' : '🟢 تم التسليم';

                html += `<tr>
                    <td>${index + 1}</td>
                    <td>${employee.name}</td>
                    <td>${employee.department}</td>
                    <td>${employee.startMonth}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="btn-delete" onclick="deleteEmployee(${employee.id})" title="حذف الموظف">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // دالة تصدير البيانات
        function exportData() {
            const dataToExport = {
                employees: employees,
                exportDate: new Date().toISOString(),
                version: '2.0.0'
            };

            const dataStr = JSON.stringify(dataToExport, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `work-attendance-backup-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('📁 تم تصدير البيانات بنجاح!');
        }

        // دالة تصدير البيانات إلى ملف Excel حقيقي بامتداد .xls
        function exportToExcel(dataToExport = null) {
            // استخدام البيانات المرشحة إذا تم تمريرها، وإلا استخدام جميع الموظفين
            const exportEmployees = dataToExport || employees;

            if (exportEmployees.length === 0) {
                showNotification('❌ لا توجد بيانات للتصدير!', 'error');
                return;
            }

            // إنشاء محتوى Excel بتنسيق HTML مع دعم العربية
            const excelContent = `
                <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
                <head>
                    <meta charset="UTF-8">
                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                    <!--[if gte mso 9]>
                    <xml>
                        <x:ExcelWorkbook>
                            <x:ExcelWorksheets>
                                <x:ExcelWorksheet>
                                    <x:Name>كشف الحضور</x:Name>
                                    <x:WorksheetOptions>
                                        <x:DisplayGridlines/>
                                    </x:WorksheetOptions>
                                </x:ExcelWorksheet>
                            </x:ExcelWorksheets>
                        </x:ExcelWorkbook>
                    </xml>
                    <![endif]-->
                    <style>
                        table {
                            border-collapse: collapse;
                            width: 100%;
                            direction: rtl;
                            font-size: 14px;
                            table-layout: auto;
                        }
                        th, td {
                            border: 1px solid #000;
                            padding: 6px 8px;
                            text-align: center;
                            white-space: nowrap;
                            overflow: hidden;
                        }
                        th {
                            background-color: #4CAF50;
                            color: white;
                            font-weight: bold;
                            font-size: 15px;
                        }
                        .status-required {
                            background-color: #ffebee;
                            color: #c62828;
                            font-weight: bold;
                        }
                        .status-completed {
                            background-color: #e8f5e8;
                            color: #2e7d32;
                            font-weight: bold;
                        }
                        /* تحسين عرض الأعمدة - حجم تلقائي مناسب للمحتوى */
                        th, td {
                            width: auto;
                            min-width: fit-content;
                        }
                        /* تحسين خاص لكل عمود */
                        th:nth-child(1), td:nth-child(1) {
                            width: 1%;
                            white-space: nowrap;
                        } /* الرقم - أصغر حجم ممكن */
                        th:nth-child(2), td:nth-child(2) {
                            width: auto;
                            min-width: max-content;
                        } /* اسم الموظف - حسب طول الاسم */
                        th:nth-child(3), td:nth-child(3) {
                            width: auto;
                            min-width: max-content;
                        } /* القسم - حسب طول اسم القسم */
                        th:nth-child(4), td:nth-child(4) {
                            width: auto;
                            min-width: max-content;
                        } /* أول شهر - حسب طول اسم الشهر */
                        th:nth-child(5), td:nth-child(5) {
                            width: auto;
                            min-width: max-content;
                        } /* الحالة - حسب طول نص الحالة */
                        th:nth-child(6), td:nth-child(6) {
                            width: auto;
                            min-width: max-content;
                        } /* التاريخ - حسب طول التاريخ */
                    </style>
                </head>
                <body>
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الموظف</th>
                                <th>القسم</th>
                                <th>أول شهر عمل</th>
                                <th>حالة الاستحقاق</th>
                                <th>تاريخ التصدير</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${exportEmployees.map((emp, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${emp.name}</td>
                                    <td>${emp.department}</td>
                                    <td>${emp.startMonth}</td>
                                    <td class="${emp.status ? 'status-required' : 'status-completed'}">
                                        ${emp.status ? 'مطلوب كشف' : 'تم استلام الكشف'}
                                    </td>
                                    <td>${new Date().toLocaleDateString('en-GB')}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </body>
                </html>
            `;

            // إنشاء ملف Excel بتنسيق .xls
            const blob = new Blob(['\ufeff', excelContent], {
                type: 'application/vnd.ms-excel;charset=utf-8'
            });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `كشف-الحضور-${new Date().toISOString().split('T')[0]}.xls`;
            link.click();

            // تنظيف الذاكرة
            URL.revokeObjectURL(link.href);

            // إشعار نجاح التصدير
            showNotification(`📊 تم تصدير ${exportEmployees.length} موظف إلى Excel بنجاح!`);
        }

        // دالة تصدير النتائج المرشحة إلى Excel
        function exportFilteredToExcel() {
            if (filteredEmployees.length === 0) {
                showNotification('❌ لا توجد نتائج بحث للتصدير! قم بالبحث أولاً.', 'error');
                return;
            }
            exportToExcel(filteredEmployees);
        }

        // دالة عرض محتوى تبويب الإشعارات
        function displayUpcomingEmployees() {
            const upcomingEmployees = getUpcomingEmployees();
            const container = document.getElementById('upcomingContent');
            const countDisplay = document.getElementById('upcomingCountDisplay');
            const badge = document.getElementById('upcomingBadge');

            // تحديث العداد والشارة
            if (upcomingEmployees.length > 0) {
                countDisplay.textContent = `${upcomingEmployees.length} موظف`;
                countDisplay.style.display = 'inline';
                badge.textContent = upcomingEmployees.length;
                badge.style.display = 'inline';
            } else {
                countDisplay.style.display = 'none';
                badge.style.display = 'none';
            }

            if (upcomingEmployees.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">🎉</div>
                        <h3 style="color: #4caf50; margin-bottom: 10px;">لا توجد إشعارات جديدة!</h3>
                        <p style="color: #666;">جميع الموظفين إما لم يحن موعد استحقاقهم بعد أو تم تسليم كشوفاتهم</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; gap: 8px; color: #856404; font-weight: bold; margin-bottom: 8px;">
                        <span>⚠️</span>
                        <span>تنبيه مهم:</span>
                    </div>
                    <p style="color: #856404; margin: 0;">
                        الموظفون التالون سيستحقون كشف العمل خلال شهر واحد. يُنصح بالتحضير مسبقاً.
                    </p>
                </div>

                <div style="margin-bottom: 20px;">
                    <button onclick="exportToExcel(getUpcomingEmployees())"
                            style="background: #ff9800; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin-left: 10px;">
                        📊 تصدير قائمة الإشعارات
                    </button>
                    <button onclick="showTab('input')"
                            style="background: #2196f3; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                        ➕ إضافة موظف جديد
                    </button>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم الموظف</th>
                            <th>القسم</th>
                            <th>أول شهر عمل</th>
                            <th>الأشهر المكتملة</th>
                            <th>موعد الاستحقاق المتوقع</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            upcomingEmployees.forEach((employee, index) => {
                const startMonthIndex = months.indexOf(employee.startMonth);
                const expectedMonth = months[(startMonthIndex + 3) % 12];

                html += `
                    <tr style="background-color: #fff8e1;">
                        <td>${index + 1}</td>
                        <td style="font-weight: bold;">${employee.name}</td>
                        <td>${employee.department}</td>
                        <td>${employee.startMonth}</td>
                        <td>
                            <span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                شهرين مكتملين
                            </span>
                        </td>
                        <td>
                            <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                ${expectedMonth}
                            </span>
                        </td>
                        <td>
                            <button onclick="showTab('employees')"
                                    style="background: #4caf50; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 5px; font-size: 12px;">
                                👁️ عرض
                            </button>
                            <button onclick="deleteEmployee(${employee.id})"
                                    style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                🗑️ حذف
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // دالة عرض نافذة الإشعار
        function showUpcomingAlert() {
            const upcomingEmployees = getUpcomingEmployees();
            const upcomingCount = upcomingEmployees.length;

            if (upcomingCount === 0) return;

            const alertHtml = `
                <div id="upcomingAlert" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">🔔</div>
                        <h3 style="color: #333; margin-bottom: 15px; font-size: 1.5rem;">تنبيه: موظفون قريبون من الاستحقاق!</h3>
                        <p style="color: #666; margin-bottom: 20px;">
                            يوجد <strong style="color: #ff9800;">${upcomingCount}</strong> موظف سيستحق كشف العمل خلال شهر واحد
                        </p>

                        <div style="background: #fff3cd; border-radius: 8px; padding: 15px; margin-bottom: 20px; text-align: right;">
                            <div style="font-size: 14px; color: #856404;">
                                <strong>الموظفون:</strong>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    ${upcomingEmployees.slice(0, 3).map(emp => `
                                        <li style="margin: 5px 0; display: flex; justify-content: space-between;">
                                            <span>${emp.name}</span>
                                            <span style="font-size: 12px;">${emp.department}</span>
                                        </li>
                                    `).join('')}
                                    ${upcomingCount > 3 ? `<li style="font-size: 12px; color: #ff9800;">و ${upcomingCount - 3} موظف آخر...</li>` : ''}
                                </ul>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button onclick="closeUpcomingAlert(); showTab('upcoming');"
                                    style="background: #ff9800; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 14px;">
                                📋 عرض التفاصيل
                            </button>
                            <button onclick="closeUpcomingAlert();"
                                    style="background: #666; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-size: 14px;">
                                ❌ إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);
        }

        // دالة إغلاق نافذة الإشعار
        function closeUpcomingAlert() {
            const alert = document.getElementById('upcomingAlert');
            if (alert) {
                alert.remove();
            }
        }

        // دالة استيراد البيانات
        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (importedData.employees && Array.isArray(importedData.employees)) {
                        employees = importedData.employees;
                        saveData();
                        displayEmployees();
                        showNotification('📥 تم استيراد البيانات بنجاح!');
                    } else {
                        showNotification('❌ ملف غير صالح!', 'error');
                    }
                } catch (error) {
                    showNotification('❌ خطأ في قراءة الملف!', 'error');
                }
            };
            reader.readAsText(file);
            event.target.value = '';
        }

        // دالة مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                employees = [];
                searchFilters = {};
                saveData();
                displayEmployees();

                // مسح حقول البحث
                document.getElementById('searchName').value = '';
                document.getElementById('searchDepartment').value = '';
                document.getElementById('searchMonth').value = '';
                document.getElementById('searchStatus').value = '';

                showNotification('🗑️ تم مسح جميع البيانات!');
            }
        }

        // دالة حذف موظف واحد
        function deleteEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟`)) {
                employees = employees.filter(emp => emp.id !== employeeId);
                saveData();
                displayEmployees();

                // تحديث نتائج البحث إذا كانت مفتوحة
                if (document.getElementById('search').classList.contains('active')) {
                    filterEmployees();
                }

                showNotification(`🗑️ تم حذف الموظف "${employee.name}" بنجاح!`);
            }
        }

        // دالة الخروج من البرنامج
        function exitProgram() {
            if (confirm('هل تريد إغلاق البرنامج؟\n\nسيتم حفظ جميع البيانات تلقائياً.')) {
                // حفظ البيانات قبل الإغلاق
                saveData();

                // عرض رسالة وداع
                showNotification('👋 شكراً لاستخدام البرنامج!');

                // محاولات متعددة لإغلاق النافذة
                setTimeout(() => {
                    try {
                        // الطريقة الأولى
                        window.close();
                    } catch (e) {
                        try {
                            // الطريقة الثانية
                            self.close();
                        } catch (e2) {
                            try {
                                // الطريقة الثالثة
                                window.open('', '_self', '');
                                window.close();
                            } catch (e3) {
                                // إذا فشلت جميع الطرق
                                if (confirm('لا يمكن إغلاق النافذة تلقائياً.\n\nهل تريد الانتقال لصفحة فارغة؟')) {
                                    document.body.innerHTML = `
                                        <div style="
                                            display: flex;
                                            justify-content: center;
                                            align-items: center;
                                            height: 100vh;
                                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                            color: white;
                                            font-family: Arial, sans-serif;
                                            text-align: center;
                                            direction: rtl;
                                        ">
                                            <div>
                                                <h1 style="font-size: 3rem; margin-bottom: 20px;">👋</h1>
                                                <h2>شكراً لاستخدام برنامج استحقاق كشف عمل</h2>
                                                <p style="margin: 20px 0;">المبرمج: علي عاجل خشان المحنّة</p>
                                                <p style="font-size: 14px; opacity: 0.8;">يمكنك إغلاق هذه النافذة الآن</p>
                                                <button onclick="location.reload()" style="
                                                    background: rgba(255,255,255,0.2);
                                                    color: white;
                                                    border: 1px solid white;
                                                    padding: 10px 20px;
                                                    border-radius: 5px;
                                                    cursor: pointer;
                                                    margin-top: 20px;
                                                ">العودة للبرنامج</button>
                                            </div>
                                        </div>
                                    `;
                                }
                            }
                        }
                    }
                }, 1000);
            }
        }

        // تهيئة البرنامج عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // ربط النموذج
            document.getElementById('employeeForm').addEventListener('submit', addEmployee);

            // عرض البيانات الأولية
            displayEmployees();

            // إضافة اختصار لوحة المفاتيح للخروج (Ctrl+Q أو Alt+F4)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey && e.key === 'q') || (e.altKey && e.key === 'F4')) {
                    e.preventDefault();
                    exitProgram();
                }
            });

            // إعداد الحفظ التلقائي الدوري (كل 30 ثانية)
            setInterval(() => {
                if (employees.length > 0) {
                    try {
                        const lastUpdate = localStorage.getItem('work-attendance-employees_lastUpdate');
                        const now = new Date().toISOString();

                        // حفظ إضافي في حالة عدم وجود تحديث حديث
                        if (!lastUpdate || new Date(now) - new Date(lastUpdate) > 30000) {
                            localStorage.setItem('work-attendance-employees', JSON.stringify(employees));
                            localStorage.setItem('work-attendance-employees_lastUpdate', now);
                            console.log('تم الحفظ التلقائي للبيانات');
                        }
                    } catch (error) {
                        console.error('خطأ في الحفظ التلقائي:', error);
                    }
                }
            }, 30000); // كل 30 ثانية

            // حفظ البيانات عند إغلاق النافذة
            window.addEventListener('beforeunload', (e) => {
                try {
                    localStorage.setItem('work-attendance-employees', JSON.stringify(employees));
                    localStorage.setItem('work-attendance-search-filters', JSON.stringify(searchFilters));
                    console.log('تم حفظ البيانات قبل إغلاق النافذة');
                } catch (error) {
                    console.error('خطأ في حفظ البيانات قبل الإغلاق:', error);
                }
            });

            // فحص الموظفين قريبي الاستحقاق وعرض الإشعار
            setTimeout(() => {
                const upcomingCount = getUpcomingCount();
                if (upcomingCount > 0) {
                    showUpcomingAlert();
                }
                // تحديث شارة التبويب
                displayUpcomingEmployees();
            }, 1000);

            console.log('✅ تم تحميل برنامج استحقاق كشف عمل بنجاح');
            console.log('👨‍💻 المبرمج: علي عاجل خشان المحنّة');
            console.log('💡 اختصارات لوحة المفاتيح: Ctrl+Q للخروج');
            console.log('💾 تم تفعيل الحفظ التلقائي والنسخ الاحتياطية');
        });
    </script>
</body>
</html>
