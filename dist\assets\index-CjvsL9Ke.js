var nw=Object.defineProperty,aw=Object.defineProperties;var iw=Object.getOwnPropertyDescriptors;var co=Object.getOwnPropertySymbols;var ly=Object.prototype.hasOwnProperty,oy=Object.prototype.propertyIsEnumerable;var df=Math.pow,ry=(n,a,s)=>a in n?nw(n,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[a]=s,N=(n,a)=>{for(var s in a||(a={}))ly.call(a,s)&&ry(n,s,a[s]);if(co)for(var s of co(a))oy.call(a,s)&&ry(n,s,a[s]);return n},K=(n,a)=>aw(n,iw(a));var et=(n,a)=>{var s={};for(var l in n)ly.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&co)for(var l of co(n))a.indexOf(l)<0&&oy.call(n,l)&&(s[l]=n[l]);return s};var sw=(n,a)=>()=>(a||n((a={exports:{}}).exports,a),a.exports);var on=(n,a,s)=>new Promise((l,u)=>{var d=m=>{try{h(s.next(m))}catch(p){u(p)}},f=m=>{try{h(s.throw(m))}catch(p){u(p)}},h=m=>m.done?l(m.value):Promise.resolve(m.value).then(d,f);h((s=s.apply(n,a)).next())});var i3=sw(BS=>{function rw(n,a){for(var s=0;s<a.length;s++){const l=a[s];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const d=Object.getOwnPropertyDescriptor(l,u);d&&Object.defineProperty(n,u,d.get?d:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function s(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(u){if(u.ep)return;u.ep=!0;const d=s(u);fetch(u.href,d)}})();function h0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var hf={exports:{}},br={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uy;function lw(){if(uy)return br;uy=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(l,u,d){var f=null;if(d!==void 0&&(f=""+d),u.key!==void 0&&(f=""+u.key),"key"in u){d={};for(var h in u)h!=="key"&&(d[h]=u[h])}else d=u;return u=d.ref,{$$typeof:n,type:l,key:f,ref:u!==void 0?u:null,props:d}}return br.Fragment=a,br.jsx=s,br.jsxs=s,br}var cy;function ow(){return cy||(cy=1,hf.exports=lw()),hf.exports}var S=ow(),mf={exports:{}},St={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy;function uw(){if(fy)return St;fy=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function b(D){return D===null||typeof D!="object"?null:(D=v&&D[v]||D["@@iterator"],typeof D=="function"?D:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,M={};function A(D,X,J){this.props=D,this.context=X,this.refs=M,this.updater=J||T}A.prototype.isReactComponent={},A.prototype.setState=function(D,X){if(typeof D!="object"&&typeof D!="function"&&D!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,D,X,"setState")},A.prototype.forceUpdate=function(D){this.updater.enqueueForceUpdate(this,D,"forceUpdate")};function E(){}E.prototype=A.prototype;function j(D,X,J){this.props=D,this.context=X,this.refs=M,this.updater=J||T}var O=j.prototype=new E;O.constructor=j,C(O,A.prototype),O.isPureReactComponent=!0;var z=Array.isArray,_={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function tt(D,X,J,$,at,vt){return J=vt.ref,{$$typeof:n,type:D,key:X,ref:J!==void 0?J:null,props:vt}}function Y(D,X){return tt(D.type,X,void 0,void 0,void 0,D.props)}function W(D){return typeof D=="object"&&D!==null&&D.$$typeof===n}function ft(D){var X={"=":"=0",":":"=2"};return"$"+D.replace(/[=:]/g,function(J){return X[J]})}var ht=/\/+/g;function lt(D,X){return typeof D=="object"&&D!==null&&D.key!=null?ft(""+D.key):X.toString(36)}function bt(){}function Et(D){switch(D.status){case"fulfilled":return D.value;case"rejected":throw D.reason;default:switch(typeof D.status=="string"?D.then(bt,bt):(D.status="pending",D.then(function(X){D.status==="pending"&&(D.status="fulfilled",D.value=X)},function(X){D.status==="pending"&&(D.status="rejected",D.reason=X)})),D.status){case"fulfilled":return D.value;case"rejected":throw D.reason}}throw D}function ut(D,X,J,$,at){var vt=typeof D;(vt==="undefined"||vt==="boolean")&&(D=null);var dt=!1;if(D===null)dt=!0;else switch(vt){case"bigint":case"string":case"number":dt=!0;break;case"object":switch(D.$$typeof){case n:case a:dt=!0;break;case g:return dt=D._init,ut(dt(D._payload),X,J,$,at)}}if(dt)return at=at(D),dt=$===""?"."+lt(D,0):$,z(at)?(J="",dt!=null&&(J=dt.replace(ht,"$&/")+"/"),ut(at,X,J,"",function(Tt){return Tt})):at!=null&&(W(at)&&(at=Y(at,J+(at.key==null||D&&D.key===at.key?"":(""+at.key).replace(ht,"$&/")+"/")+dt)),X.push(at)),1;dt=0;var xt=$===""?".":$+":";if(z(D))for(var nt=0;nt<D.length;nt++)$=D[nt],vt=xt+lt($,nt),dt+=ut($,X,J,vt,at);else if(nt=b(D),typeof nt=="function")for(D=nt.call(D),nt=0;!($=D.next()).done;)$=$.value,vt=xt+lt($,nt++),dt+=ut($,X,J,vt,at);else if(vt==="object"){if(typeof D.then=="function")return ut(Et(D),X,J,$,at);throw X=String(D),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(D).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return dt}function B(D,X,J){if(D==null)return D;var $=[],at=0;return ut(D,$,"","",function(vt){return X.call(J,vt,at++)}),$}function q(D){if(D._status===-1){var X=D._result;X=X(),X.then(function(J){(D._status===0||D._status===-1)&&(D._status=1,D._result=J)},function(J){(D._status===0||D._status===-1)&&(D._status=2,D._result=J)}),D._status===-1&&(D._status=0,D._result=X)}if(D._status===1)return D._result.default;throw D._result}var P=typeof reportError=="function"?reportError:function(D){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof D=="object"&&D!==null&&typeof D.message=="string"?String(D.message):String(D),error:D});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",D);return}console.error(D)};function ct(){}return St.Children={map:B,forEach:function(D,X,J){B(D,function(){X.apply(this,arguments)},J)},count:function(D){var X=0;return B(D,function(){X++}),X},toArray:function(D){return B(D,function(X){return X})||[]},only:function(D){if(!W(D))throw Error("React.Children.only expected to receive a single React element child.");return D}},St.Component=A,St.Fragment=s,St.Profiler=u,St.PureComponent=j,St.StrictMode=l,St.Suspense=m,St.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,St.__COMPILER_RUNTIME={__proto__:null,c:function(D){return _.H.useMemoCache(D)}},St.cache=function(D){return function(){return D.apply(null,arguments)}},St.cloneElement=function(D,X,J){if(D==null)throw Error("The argument must be a React element, but you passed "+D+".");var $=C({},D.props),at=D.key,vt=void 0;if(X!=null)for(dt in X.ref!==void 0&&(vt=void 0),X.key!==void 0&&(at=""+X.key),X)!I.call(X,dt)||dt==="key"||dt==="__self"||dt==="__source"||dt==="ref"&&X.ref===void 0||($[dt]=X[dt]);var dt=arguments.length-2;if(dt===1)$.children=J;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];$.children=xt}return tt(D.type,at,void 0,void 0,vt,$)},St.createContext=function(D){return D={$$typeof:f,_currentValue:D,_currentValue2:D,_threadCount:0,Provider:null,Consumer:null},D.Provider=D,D.Consumer={$$typeof:d,_context:D},D},St.createElement=function(D,X,J){var $,at={},vt=null;if(X!=null)for($ in X.key!==void 0&&(vt=""+X.key),X)I.call(X,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(at[$]=X[$]);var dt=arguments.length-2;if(dt===1)at.children=J;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];at.children=xt}if(D&&D.defaultProps)for($ in dt=D.defaultProps,dt)at[$]===void 0&&(at[$]=dt[$]);return tt(D,vt,void 0,void 0,null,at)},St.createRef=function(){return{current:null}},St.forwardRef=function(D){return{$$typeof:h,render:D}},St.isValidElement=W,St.lazy=function(D){return{$$typeof:g,_payload:{_status:-1,_result:D},_init:q}},St.memo=function(D,X){return{$$typeof:p,type:D,compare:X===void 0?null:X}},St.startTransition=function(D){var X=_.T,J={};_.T=J;try{var $=D(),at=_.S;at!==null&&at(J,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(ct,P)}catch(vt){P(vt)}finally{_.T=X}},St.unstable_useCacheRefresh=function(){return _.H.useCacheRefresh()},St.use=function(D){return _.H.use(D)},St.useActionState=function(D,X,J){return _.H.useActionState(D,X,J)},St.useCallback=function(D,X){return _.H.useCallback(D,X)},St.useContext=function(D){return _.H.useContext(D)},St.useDebugValue=function(){},St.useDeferredValue=function(D,X){return _.H.useDeferredValue(D,X)},St.useEffect=function(D,X,J){var $=_.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(D,X)},St.useId=function(){return _.H.useId()},St.useImperativeHandle=function(D,X,J){return _.H.useImperativeHandle(D,X,J)},St.useInsertionEffect=function(D,X){return _.H.useInsertionEffect(D,X)},St.useLayoutEffect=function(D,X){return _.H.useLayoutEffect(D,X)},St.useMemo=function(D,X){return _.H.useMemo(D,X)},St.useOptimistic=function(D,X){return _.H.useOptimistic(D,X)},St.useReducer=function(D,X,J){return _.H.useReducer(D,X,J)},St.useRef=function(D){return _.H.useRef(D)},St.useState=function(D){return _.H.useState(D)},St.useSyncExternalStore=function(D,X,J){return _.H.useSyncExternalStore(D,X,J)},St.useTransition=function(){return _.H.useTransition()},St.version="19.1.0",St}var dy;function Ad(){return dy||(dy=1,mf.exports=uw()),mf.exports}var w=Ad();const Aa=h0(w),m0=rw({__proto__:null,default:Aa},[w]);var pf={exports:{}},xr={},gf={exports:{}},yf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hy;function cw(){return hy||(hy=1,function(n){function a(B,q){var P=B.length;B.push(q);t:for(;0<P;){var ct=P-1>>>1,D=B[ct];if(0<u(D,q))B[ct]=q,B[P]=D,P=ct;else break t}}function s(B){return B.length===0?null:B[0]}function l(B){if(B.length===0)return null;var q=B[0],P=B.pop();if(P!==q){B[0]=P;t:for(var ct=0,D=B.length,X=D>>>1;ct<X;){var J=2*(ct+1)-1,$=B[J],at=J+1,vt=B[at];if(0>u($,P))at<D&&0>u(vt,$)?(B[ct]=vt,B[at]=P,ct=at):(B[ct]=$,B[J]=P,ct=J);else if(at<D&&0>u(vt,P))B[ct]=vt,B[at]=P,ct=at;else break t}}return q}function u(B,q){var P=B.sortIndex-q.sortIndex;return P!==0?P:B.id-q.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();n.unstable_now=function(){return f.now()-h}}var m=[],p=[],g=1,v=null,b=3,T=!1,C=!1,M=!1,A=!1,E=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,O=typeof setImmediate!="undefined"?setImmediate:null;function z(B){for(var q=s(p);q!==null;){if(q.callback===null)l(p);else if(q.startTime<=B)l(p),q.sortIndex=q.expirationTime,a(m,q);else break;q=s(p)}}function _(B){if(M=!1,z(B),!C)if(s(m)!==null)C=!0,I||(I=!0,lt());else{var q=s(p);q!==null&&ut(_,q.startTime-B)}}var I=!1,tt=-1,Y=5,W=-1;function ft(){return A?!0:!(n.unstable_now()-W<Y)}function ht(){if(A=!1,I){var B=n.unstable_now();W=B;var q=!0;try{t:{C=!1,M&&(M=!1,j(tt),tt=-1),T=!0;var P=b;try{e:{for(z(B),v=s(m);v!==null&&!(v.expirationTime>B&&ft());){var ct=v.callback;if(typeof ct=="function"){v.callback=null,b=v.priorityLevel;var D=ct(v.expirationTime<=B);if(B=n.unstable_now(),typeof D=="function"){v.callback=D,z(B),q=!0;break e}v===s(m)&&l(m),z(B)}else l(m);v=s(m)}if(v!==null)q=!0;else{var X=s(p);X!==null&&ut(_,X.startTime-B),q=!1}}break t}finally{v=null,b=P,T=!1}q=void 0}}finally{q?lt():I=!1}}}var lt;if(typeof O=="function")lt=function(){O(ht)};else if(typeof MessageChannel!="undefined"){var bt=new MessageChannel,Et=bt.port2;bt.port1.onmessage=ht,lt=function(){Et.postMessage(null)}}else lt=function(){E(ht,0)};function ut(B,q){tt=E(function(){B(n.unstable_now())},q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(B){switch(b){case 1:case 2:case 3:var q=3;break;default:q=b}var P=b;b=q;try{return B()}finally{b=P}},n.unstable_requestPaint=function(){A=!0},n.unstable_runWithPriority=function(B,q){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var P=b;b=B;try{return q()}finally{b=P}},n.unstable_scheduleCallback=function(B,q,P){var ct=n.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?ct+P:ct):P=ct,B){case 1:var D=-1;break;case 2:D=250;break;case 5:D=1073741823;break;case 4:D=1e4;break;default:D=5e3}return D=P+D,B={id:g++,callback:q,priorityLevel:B,startTime:P,expirationTime:D,sortIndex:-1},P>ct?(B.sortIndex=P,a(p,B),s(m)===null&&B===s(p)&&(M?(j(tt),tt=-1):M=!0,ut(_,P-ct))):(B.sortIndex=D,a(m,B),C||T||(C=!0,I||(I=!0,lt()))),B},n.unstable_shouldYield=ft,n.unstable_wrapCallback=function(B){var q=b;return function(){var P=b;b=q;try{return B.apply(this,arguments)}finally{b=P}}}}(yf)),yf}var my;function fw(){return my||(my=1,gf.exports=cw()),gf.exports}var vf={exports:{}},xe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function dw(){if(py)return xe;py=1;var n=Ad();function a(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)p+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function d(m,p,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:v==null?null:""+v,children:m,containerInfo:p,implementation:g}}var f=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return xe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,xe.createPortal=function(m,p){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(a(299));return d(m,p,null,g)},xe.flushSync=function(m){var p=f.T,g=l.p;try{if(f.T=null,l.p=2,m)return m()}finally{f.T=p,l.p=g,l.d.f()}},xe.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,l.d.C(m,p))},xe.prefetchDNS=function(m){typeof m=="string"&&l.d.D(m)},xe.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,T=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;g==="style"?l.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:b,fetchPriority:T}):g==="script"&&l.d.X(m,{crossOrigin:v,integrity:b,fetchPriority:T,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},xe.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var g=h(p.as,p.crossOrigin);l.d.M(m,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&l.d.M(m)},xe.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin);l.d.L(m,g,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},xe.preloadModule=function(m,p){if(typeof m=="string")if(p){var g=h(p.as,p.crossOrigin);l.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else l.d.m(m)},xe.requestFormReset=function(m){l.d.r(m)},xe.unstable_batchedUpdates=function(m,p){return m(p)},xe.useFormState=function(m,p,g){return f.H.useFormState(m,p,g)},xe.useFormStatus=function(){return f.H.useHostTransitionStatus()},xe.version="19.1.0",xe}var gy;function p0(){if(gy)return vf.exports;gy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),vf.exports=dw(),vf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yy;function hw(){if(yy)return xr;yy=1;var n=fw(),a=Ad(),s=p0();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(d(t)!==t)throw Error(l(188))}function m(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(l(188));return e!==t?null:t}for(var i=t,r=e;;){var o=i.return;if(o===null)break;var c=o.alternate;if(c===null){if(r=o.return,r!==null){i=r;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===i)return h(o),t;if(c===r)return h(o),e;c=c.sibling}throw Error(l(188))}if(i.return!==r.return)i=o,r=c;else{for(var y=!1,x=o.child;x;){if(x===i){y=!0,i=o,r=c;break}if(x===r){y=!0,r=o,i=c;break}x=x.sibling}if(!y){for(x=c.child;x;){if(x===i){y=!0,i=c,r=o;break}if(x===r){y=!0,r=c,i=o;break}x=x.sibling}if(!y)throw Error(l(189))}}if(i.alternate!==r)throw Error(l(190))}if(i.tag!==3)throw Error(l(188));return i.stateNode.current===i?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),A=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),j=Symbol.for("react.consumer"),O=Symbol.for("react.context"),z=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),tt=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),ft=Symbol.for("react.memo_cache_sentinel"),ht=Symbol.iterator;function lt(t){return t===null||typeof t!="object"?null:(t=ht&&t[ht]||t["@@iterator"],typeof t=="function"?t:null)}var bt=Symbol.for("react.client.reference");function Et(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===bt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case C:return"Fragment";case A:return"Profiler";case M:return"StrictMode";case _:return"Suspense";case I:return"SuspenseList";case W:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case T:return"Portal";case O:return(t.displayName||"Context")+".Provider";case j:return(t._context.displayName||"Context")+".Consumer";case z:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case tt:return e=t.displayName||null,e!==null?e:Et(t.type)||"Memo";case Y:e=t._payload,t=t._init;try{return Et(t(e))}catch(i){}}return null}var ut=Array.isArray,B=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},ct=[],D=-1;function X(t){return{current:t}}function J(t){0>D||(t.current=ct[D],ct[D]=null,D--)}function $(t,e){D++,ct[D]=t.current,t.current=e}var at=X(null),vt=X(null),dt=X(null),xt=X(null);function nt(t,e){switch($(dt,e),$(vt,t),$(at,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Vg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Vg(e),t=zg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}J(at),$(at,t)}function Tt(){J(at),J(vt),J(dt)}function qt(t){t.memoizedState!==null&&$(xt,t);var e=at.current,i=zg(e,t.type);e!==i&&($(vt,t),$(at,i))}function Ot(t){vt.current===t&&(J(at),J(vt)),xt.current===t&&(J(xt),mr._currentValue=P)}var Ct=Object.prototype.hasOwnProperty,_t=n.unstable_scheduleCallback,de=n.unstable_cancelCallback,Ae=n.unstable_shouldYield,an=n.unstable_requestPaint,he=n.unstable_now,za=n.unstable_getCurrentPriorityLevel,Fr=n.unstable_ImmediatePriority,Ir=n.unstable_UserBlockingPriority,di=n.unstable_NormalPriority,hi=n.unstable_LowPriority,gh=n.unstable_IdlePriority,US=n.log,kS=n.unstable_setDisableYieldValue,Ts=null,Ve=null;function Jn(t){if(typeof US=="function"&&kS(t),Ve&&typeof Ve.setStrictMode=="function")try{Ve.setStrictMode(Ts,t)}catch(e){}}var ze=Math.clz32?Math.clz32:GS,HS=Math.log,PS=Math.LN2;function GS(t){return t>>>=0,t===0?32:31-(HS(t)/PS|0)|0}var Wr=256,$r=4194304;function La(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Jr(t,e,i){var r=t.pendingLanes;if(r===0)return 0;var o=0,c=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var x=r&134217727;return x!==0?(r=x&~c,r!==0?o=La(r):(y&=x,y!==0?o=La(y):i||(i=x&~t,i!==0&&(o=La(i))))):(x=r&~c,x!==0?o=La(x):y!==0?o=La(y):i||(i=r&~t,i!==0&&(o=La(i)))),o===0?0:e!==0&&e!==o&&(e&c)===0&&(c=o&-o,i=e&-e,c>=i||c===32&&(i&4194048)!==0)?e:o}function ws(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function YS(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function yh(){var t=Wr;return Wr<<=1,(Wr&4194048)===0&&(Wr=256),t}function vh(){var t=$r;return $r<<=1,($r&62914560)===0&&($r=4194304),t}function tu(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function As(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function qS(t,e,i,r,o,c){var y=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var x=t.entanglements,R=t.expirationTimes,k=t.hiddenUpdates;for(i=y&~i;0<i;){var Z=31-ze(i),F=1<<Z;x[Z]=0,R[Z]=-1;var H=k[Z];if(H!==null)for(k[Z]=null,Z=0;Z<H.length;Z++){var G=H[Z];G!==null&&(G.lane&=-536870913)}i&=~F}r!==0&&bh(t,r,0),c!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=c&~(y&~e))}function bh(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-ze(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|i&4194090}function xh(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var r=31-ze(i),o=1<<r;o&e|t[r]&e&&(t[r]|=e),i&=~o}}function eu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function nu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Sh(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:ty(t.type))}function XS(t,e){var i=q.p;try{return q.p=t,e()}finally{q.p=i}}var ta=Math.random().toString(36).slice(2),ve="__reactFiber$"+ta,Re="__reactProps$"+ta,mi="__reactContainer$"+ta,au="__reactEvents$"+ta,KS="__reactListeners$"+ta,ZS="__reactHandles$"+ta,Th="__reactResources$"+ta,Es="__reactMarker$"+ta;function iu(t){delete t[ve],delete t[Re],delete t[au],delete t[KS],delete t[ZS]}function pi(t){var e=t[ve];if(e)return e;for(var i=t.parentNode;i;){if(e=i[mi]||i[ve]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=kg(t);t!==null;){if(i=t[ve])return i;t=kg(t)}return e}t=i,i=t.parentNode}return null}function gi(t){if(t=t[ve]||t[mi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ms(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function yi(t){var e=t[Th];return e||(e=t[Th]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function oe(t){t[Es]=!0}var wh=new Set,Ah={};function Ba(t,e){vi(t,e),vi(t+"Capture",e)}function vi(t,e){for(Ah[t]=e,t=0;t<e.length;t++)wh.add(e[t])}var QS=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Eh={},Mh={};function FS(t){return Ct.call(Mh,t)?!0:Ct.call(Eh,t)?!1:QS.test(t)?Mh[t]=!0:(Eh[t]=!0,!1)}function tl(t,e,i){if(FS(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function el(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function _n(t,e,i,r){if(r===null)t.removeAttribute(i);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+r)}}var su,Ch;function bi(t){if(su===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);su=e&&e[1]||"",Ch=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+su+t+Ch}var ru=!1;function lu(t,e){if(!t||ru)return"";ru=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var F=function(){throw Error()};if(Object.defineProperty(F.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(F,[])}catch(G){var H=G}Reflect.construct(t,[],F)}else{try{F.call()}catch(G){H=G}t.call(F.prototype)}}else{try{throw Error()}catch(G){H=G}(F=t())&&typeof F.catch=="function"&&F.catch(function(){})}}catch(G){if(G&&H&&typeof G.stack=="string")return[G.stack,H.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=r.DetermineComponentFrameRoot(),y=c[0],x=c[1];if(y&&x){var R=y.split(`
`),k=x.split(`
`);for(o=r=0;r<R.length&&!R[r].includes("DetermineComponentFrameRoot");)r++;for(;o<k.length&&!k[o].includes("DetermineComponentFrameRoot");)o++;if(r===R.length||o===k.length)for(r=R.length-1,o=k.length-1;1<=r&&0<=o&&R[r]!==k[o];)o--;for(;1<=r&&0<=o;r--,o--)if(R[r]!==k[o]){if(r!==1||o!==1)do if(r--,o--,0>o||R[r]!==k[o]){var Z=`
`+R[r].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=r&&0<=o);break}}}finally{ru=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?bi(i):""}function IS(t){switch(t.tag){case 26:case 27:case 5:return bi(t.type);case 16:return bi("Lazy");case 13:return bi("Suspense");case 19:return bi("SuspenseList");case 0:case 15:return lu(t.type,!1);case 11:return lu(t.type.render,!1);case 1:return lu(t.type,!0);case 31:return bi("Activity");default:return""}}function Rh(t){try{var e="";do e+=IS(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Xe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Dh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function WS(t){var e=Dh(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof i!="undefined"&&typeof i.get=="function"&&typeof i.set=="function"){var o=i.get,c=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){r=""+y,c.call(this,y)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function nl(t){t._valueTracker||(t._valueTracker=WS(t))}function Oh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),r="";return t&&(r=Dh(t)?t.checked?"true":"false":t.value),t=r,t!==i?(e.setValue(t),!0):!1}function al(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var $S=/[\n"\\]/g;function Ke(t){return t.replace($S,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function ou(t,e,i,r,o,c,y,x){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Xe(e)):t.value!==""+Xe(e)&&(t.value=""+Xe(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?uu(t,y,Xe(e)):i!=null?uu(t,y,Xe(i)):r!=null&&t.removeAttribute("value"),o==null&&c!=null&&(t.defaultChecked=!!c),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?t.name=""+Xe(x):t.removeAttribute("name")}function Nh(t,e,i,r,o,c,y,x){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||i!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;i=i!=null?""+Xe(i):"",e=e!=null?""+Xe(e):i,x||e===t.value||(t.value=e),t.defaultValue=e}r=r!=null?r:o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=x?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function uu(t,e,i){e==="number"&&al(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function xi(t,e,i,r){if(t=t.options,e){e={};for(var o=0;o<i.length;o++)e["$"+i[o]]=!0;for(i=0;i<t.length;i++)o=e.hasOwnProperty("$"+t[i].value),t[i].selected!==o&&(t[i].selected=o),o&&r&&(t[i].defaultSelected=!0)}else{for(i=""+Xe(i),e=null,o=0;o<t.length;o++){if(t[o].value===i){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function _h(t,e,i){if(e!=null&&(e=""+Xe(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Xe(i):""}function jh(t,e,i,r){if(e==null){if(r!=null){if(i!=null)throw Error(l(92));if(ut(r)){if(1<r.length)throw Error(l(93));r=r[0]}i=r}i==null&&(i=""),e=i}i=Xe(e),t.defaultValue=i,r=t.textContent,r===i&&r!==""&&r!==null&&(t.value=r)}function Si(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var JS=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Vh(t,e,i){var r=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,i):typeof i!="number"||i===0||JS.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function zh(t,e,i){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,i!=null){for(var r in i)!i.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var o in e)r=e[o],e.hasOwnProperty(o)&&i[o]!==r&&Vh(t,o,r)}else for(var c in e)e.hasOwnProperty(c)&&Vh(t,c,e[c])}function cu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var t1=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),e1=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function il(t){return e1.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var fu=null;function du(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ti=null,wi=null;function Lh(t){var e=gi(t);if(e&&(t=e.stateNode)){var i=t[Re]||null;t:switch(t=e.stateNode,e.type){case"input":if(ou(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Ke(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var r=i[e];if(r!==t&&r.form===t.form){var o=r[Re]||null;if(!o)throw Error(l(90));ou(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<i.length;e++)r=i[e],r.form===t.form&&Oh(r)}break t;case"textarea":_h(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&xi(t,!!i.multiple,e,!1)}}}var hu=!1;function Bh(t,e,i){if(hu)return t(e,i);hu=!0;try{var r=t(e);return r}finally{if(hu=!1,(Ti!==null||wi!==null)&&(Yl(),Ti&&(e=Ti,t=wi,wi=Ti=null,Lh(e),t)))for(e=0;e<t.length;e++)Lh(t[e])}}function Cs(t,e){var i=t.stateNode;if(i===null)return null;var r=i[Re]||null;if(r===null)return null;i=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(l(231,e,typeof i));return i}var jn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),mu=!1;if(jn)try{var Rs={};Object.defineProperty(Rs,"passive",{get:function(){mu=!0}}),window.addEventListener("test",Rs,Rs),window.removeEventListener("test",Rs,Rs)}catch(t){mu=!1}var ea=null,pu=null,sl=null;function Uh(){if(sl)return sl;var t,e=pu,i=e.length,r,o="value"in ea?ea.value:ea.textContent,c=o.length;for(t=0;t<i&&e[t]===o[t];t++);var y=i-t;for(r=1;r<=y&&e[i-r]===o[c-r];r++);return sl=o.slice(t,1<r?1-r:void 0)}function rl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ll(){return!0}function kh(){return!1}function De(t){function e(i,r,o,c,y){this._reactName=i,this._targetInst=o,this.type=r,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var x in t)t.hasOwnProperty(x)&&(i=t[x],this[x]=i?i(c):c[x]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ll:kh,this.isPropagationStopped=kh,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ll)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ll)},persist:function(){},isPersistent:ll}),e}var Ua={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ol=De(Ua),Ds=g({},Ua,{view:0,detail:0}),n1=De(Ds),gu,yu,Os,ul=g({},Ds,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Os&&(Os&&t.type==="mousemove"?(gu=t.screenX-Os.screenX,yu=t.screenY-Os.screenY):yu=gu=0,Os=t),gu)},movementY:function(t){return"movementY"in t?t.movementY:yu}}),Hh=De(ul),a1=g({},ul,{dataTransfer:0}),i1=De(a1),s1=g({},Ds,{relatedTarget:0}),vu=De(s1),r1=g({},Ua,{animationName:0,elapsedTime:0,pseudoElement:0}),l1=De(r1),o1=g({},Ua,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),u1=De(o1),c1=g({},Ua,{data:0}),Ph=De(c1),f1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},d1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},h1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function m1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=h1[t])?!!e[t]:!1}function bu(){return m1}var p1=g({},Ds,{key:function(t){if(t.key){var e=f1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=rl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?d1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bu,charCode:function(t){return t.type==="keypress"?rl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?rl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),g1=De(p1),y1=g({},ul,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gh=De(y1),v1=g({},Ds,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bu}),b1=De(v1),x1=g({},Ua,{propertyName:0,elapsedTime:0,pseudoElement:0}),S1=De(x1),T1=g({},ul,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),w1=De(T1),A1=g({},Ua,{newState:0,oldState:0}),E1=De(A1),M1=[9,13,27,32],xu=jn&&"CompositionEvent"in window,Ns=null;jn&&"documentMode"in document&&(Ns=document.documentMode);var C1=jn&&"TextEvent"in window&&!Ns,Yh=jn&&(!xu||Ns&&8<Ns&&11>=Ns),qh=" ",Xh=!1;function Kh(t,e){switch(t){case"keyup":return M1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ai=!1;function R1(t,e){switch(t){case"compositionend":return Zh(e);case"keypress":return e.which!==32?null:(Xh=!0,qh);case"textInput":return t=e.data,t===qh&&Xh?null:t;default:return null}}function D1(t,e){if(Ai)return t==="compositionend"||!xu&&Kh(t,e)?(t=Uh(),sl=pu=ea=null,Ai=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Yh&&e.locale!=="ko"?null:e.data;default:return null}}var O1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!O1[t.type]:e==="textarea"}function Fh(t,e,i,r){Ti?wi?wi.push(r):wi=[r]:Ti=r,e=Fl(e,"onChange"),0<e.length&&(i=new ol("onChange","change",null,i,r),t.push({event:i,listeners:e}))}var _s=null,js=null;function N1(t){Dg(t,0)}function cl(t){var e=Ms(t);if(Oh(e))return t}function Ih(t,e){if(t==="change")return e}var Wh=!1;if(jn){var Su;if(jn){var Tu="oninput"in document;if(!Tu){var $h=document.createElement("div");$h.setAttribute("oninput","return;"),Tu=typeof $h.oninput=="function"}Su=Tu}else Su=!1;Wh=Su&&(!document.documentMode||9<document.documentMode)}function Jh(){_s&&(_s.detachEvent("onpropertychange",tm),js=_s=null)}function tm(t){if(t.propertyName==="value"&&cl(js)){var e=[];Fh(e,js,t,du(t)),Bh(N1,e)}}function _1(t,e,i){t==="focusin"?(Jh(),_s=e,js=i,_s.attachEvent("onpropertychange",tm)):t==="focusout"&&Jh()}function j1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return cl(js)}function V1(t,e){if(t==="click")return cl(e)}function z1(t,e){if(t==="input"||t==="change")return cl(e)}function L1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Le=typeof Object.is=="function"?Object.is:L1;function Vs(t,e){if(Le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),r=Object.keys(e);if(i.length!==r.length)return!1;for(r=0;r<i.length;r++){var o=i[r];if(!Ct.call(e,o)||!Le(t[o],e[o]))return!1}return!0}function em(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function nm(t,e){var i=em(t);t=0;for(var r;i;){if(i.nodeType===3){if(r=t+i.textContent.length,t<=e&&r>=e)return{node:i,offset:e-t};t=r}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=em(i)}}function am(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?am(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function im(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=al(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch(r){i=!1}if(i)t=e.contentWindow;else break;e=al(t.document)}return e}function wu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var B1=jn&&"documentMode"in document&&11>=document.documentMode,Ei=null,Au=null,zs=null,Eu=!1;function sm(t,e,i){var r=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Eu||Ei==null||Ei!==al(r)||(r=Ei,"selectionStart"in r&&wu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zs&&Vs(zs,r)||(zs=r,r=Fl(Au,"onSelect"),0<r.length&&(e=new ol("onSelect","select",null,e,i),t.push({event:e,listeners:r}),e.target=Ei)))}function ka(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var Mi={animationend:ka("Animation","AnimationEnd"),animationiteration:ka("Animation","AnimationIteration"),animationstart:ka("Animation","AnimationStart"),transitionrun:ka("Transition","TransitionRun"),transitionstart:ka("Transition","TransitionStart"),transitioncancel:ka("Transition","TransitionCancel"),transitionend:ka("Transition","TransitionEnd")},Mu={},rm={};jn&&(rm=document.createElement("div").style,"AnimationEvent"in window||(delete Mi.animationend.animation,delete Mi.animationiteration.animation,delete Mi.animationstart.animation),"TransitionEvent"in window||delete Mi.transitionend.transition);function Ha(t){if(Mu[t])return Mu[t];if(!Mi[t])return t;var e=Mi[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in rm)return Mu[t]=e[i];return t}var lm=Ha("animationend"),om=Ha("animationiteration"),um=Ha("animationstart"),U1=Ha("transitionrun"),k1=Ha("transitionstart"),H1=Ha("transitioncancel"),cm=Ha("transitionend"),fm=new Map,Cu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cu.push("scrollEnd");function sn(t,e){fm.set(t,e),Ba(e,[t])}var dm=new WeakMap;function Ze(t,e){if(typeof t=="object"&&t!==null){var i=dm.get(t);return i!==void 0?i:(e={value:t,source:e,stack:Rh(e)},dm.set(t,e),e)}return{value:t,source:e,stack:Rh(e)}}var Qe=[],Ci=0,Ru=0;function fl(){for(var t=Ci,e=Ru=Ci=0;e<t;){var i=Qe[e];Qe[e++]=null;var r=Qe[e];Qe[e++]=null;var o=Qe[e];Qe[e++]=null;var c=Qe[e];if(Qe[e++]=null,r!==null&&o!==null){var y=r.pending;y===null?o.next=o:(o.next=y.next,y.next=o),r.pending=o}c!==0&&hm(i,o,c)}}function dl(t,e,i,r){Qe[Ci++]=t,Qe[Ci++]=e,Qe[Ci++]=i,Qe[Ci++]=r,Ru|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function Du(t,e,i,r){return dl(t,e,i,r),hl(t)}function Ri(t,e){return dl(t,null,null,e),hl(t)}function hm(t,e,i){t.lanes|=i;var r=t.alternate;r!==null&&(r.lanes|=i);for(var o=!1,c=t.return;c!==null;)c.childLanes|=i,r=c.alternate,r!==null&&(r.childLanes|=i),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(o=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,o&&e!==null&&(o=31-ze(i),t=c.hiddenUpdates,r=t[o],r===null?t[o]=[e]:r.push(e),e.lane=i|536870912),c):null}function hl(t){if(50<rr)throw rr=0,zc=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Di={};function P1(t,e,i,r){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Be(t,e,i,r){return new P1(t,e,i,r)}function Ou(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Vn(t,e){var i=t.alternate;return i===null?(i=Be(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function mm(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ml(t,e,i,r,o,c){var y=0;if(r=t,typeof t=="function")Ou(t)&&(y=1);else if(typeof t=="string")y=YT(t,i,at.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case W:return t=Be(31,i,e,o),t.elementType=W,t.lanes=c,t;case C:return Pa(i.children,o,c,e);case M:y=8,o|=24;break;case A:return t=Be(12,i,e,o|2),t.elementType=A,t.lanes=c,t;case _:return t=Be(13,i,e,o),t.elementType=_,t.lanes=c,t;case I:return t=Be(19,i,e,o),t.elementType=I,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case E:case O:y=10;break t;case j:y=9;break t;case z:y=11;break t;case tt:y=14;break t;case Y:y=16,r=null;break t}y=29,i=Error(l(130,t===null?"null":typeof t,"")),r=null}return e=Be(y,i,e,o),e.elementType=t,e.type=r,e.lanes=c,e}function Pa(t,e,i,r){return t=Be(7,t,r,e),t.lanes=i,t}function Nu(t,e,i){return t=Be(6,t,null,e),t.lanes=i,t}function _u(t,e,i){return e=Be(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oi=[],Ni=0,pl=null,gl=0,Fe=[],Ie=0,Ga=null,zn=1,Ln="";function Ya(t,e){Oi[Ni++]=gl,Oi[Ni++]=pl,pl=t,gl=e}function pm(t,e,i){Fe[Ie++]=zn,Fe[Ie++]=Ln,Fe[Ie++]=Ga,Ga=t;var r=zn;t=Ln;var o=32-ze(r)-1;r&=~(1<<o),i+=1;var c=32-ze(e)+o;if(30<c){var y=o-o%5;c=(r&(1<<y)-1).toString(32),r>>=y,o-=y,zn=1<<32-ze(e)+o|i<<o|r,Ln=c+t}else zn=1<<c|i<<o|r,Ln=t}function ju(t){t.return!==null&&(Ya(t,1),pm(t,1,0))}function Vu(t){for(;t===pl;)pl=Oi[--Ni],Oi[Ni]=null,gl=Oi[--Ni],Oi[Ni]=null;for(;t===Ga;)Ga=Fe[--Ie],Fe[Ie]=null,Ln=Fe[--Ie],Fe[Ie]=null,zn=Fe[--Ie],Fe[Ie]=null}var Ee=null,It=null,Vt=!1,qa=null,hn=!1,zu=Error(l(519));function Xa(t){var e=Error(l(418,""));throw Us(Ze(e,t)),zu}function gm(t){var e=t.stateNode,i=t.type,r=t.memoizedProps;switch(e[ve]=t,e[Re]=r,i){case"dialog":Dt("cancel",e),Dt("close",e);break;case"iframe":case"object":case"embed":Dt("load",e);break;case"video":case"audio":for(i=0;i<or.length;i++)Dt(or[i],e);break;case"source":Dt("error",e);break;case"img":case"image":case"link":Dt("error",e),Dt("load",e);break;case"details":Dt("toggle",e);break;case"input":Dt("invalid",e),Nh(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),nl(e);break;case"select":Dt("invalid",e);break;case"textarea":Dt("invalid",e),jh(e,r.value,r.defaultValue,r.children),nl(e)}i=r.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||r.suppressHydrationWarning===!0||jg(e.textContent,i)?(r.popover!=null&&(Dt("beforetoggle",e),Dt("toggle",e)),r.onScroll!=null&&Dt("scroll",e),r.onScrollEnd!=null&&Dt("scrollend",e),r.onClick!=null&&(e.onclick=Il),e=!0):e=!1,e||Xa(t)}function ym(t){for(Ee=t.return;Ee;)switch(Ee.tag){case 5:case 13:hn=!1;return;case 27:case 3:hn=!0;return;default:Ee=Ee.return}}function Ls(t){if(t!==Ee)return!1;if(!Vt)return ym(t),Vt=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||Wc(t.type,t.memoizedProps)),i=!i),i&&It&&Xa(t),ym(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){It=ln(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}It=null}}else e===27?(e=It,ya(t.type)?(t=ef,ef=null,It=t):It=e):It=Ee?ln(t.stateNode.nextSibling):null;return!0}function Bs(){It=Ee=null,Vt=!1}function vm(){var t=qa;return t!==null&&(_e===null?_e=t:_e.push.apply(_e,t),qa=null),t}function Us(t){qa===null?qa=[t]:qa.push(t)}var Lu=X(null),Ka=null,Bn=null;function na(t,e,i){$(Lu,e._currentValue),e._currentValue=i}function Un(t){t._currentValue=Lu.current,J(Lu)}function Bu(t,e,i){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===i)break;t=t.return}}function Uu(t,e,i,r){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var c=o.dependencies;if(c!==null){var y=o.child;c=c.firstContext;t:for(;c!==null;){var x=c;c=o;for(var R=0;R<e.length;R++)if(x.context===e[R]){c.lanes|=i,x=c.alternate,x!==null&&(x.lanes|=i),Bu(c.return,i,t),r||(y=null);break t}c=x.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(l(341));y.lanes|=i,c=y.alternate,c!==null&&(c.lanes|=i),Bu(y,i,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function ks(t,e,i,r){t=null;for(var o=e,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var x=o.type;Le(o.pendingProps.value,y.value)||(t!==null?t.push(x):t=[x])}}else if(o===xt.current){if(y=o.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(mr):t=[mr])}o=o.return}t!==null&&Uu(e,t,i,r),e.flags|=262144}function yl(t){for(t=t.firstContext;t!==null;){if(!Le(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Za(t){Ka=t,Bn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function be(t){return bm(Ka,t)}function vl(t,e){return Ka===null&&Za(t),bm(t,e)}function bm(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},Bn===null){if(t===null)throw Error(l(308));Bn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Bn=Bn.next=e;return i}var G1=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},Y1=n.unstable_scheduleCallback,q1=n.unstable_NormalPriority,se={$$typeof:O,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ku(){return{controller:new G1,data:new Map,refCount:0}}function Hs(t){t.refCount--,t.refCount===0&&Y1(q1,function(){t.controller.abort()})}var Ps=null,Hu=0,_i=0,ji=null;function X1(t,e){if(Ps===null){var i=Ps=[];Hu=0,_i=Gc(),ji={status:"pending",value:void 0,then:function(r){i.push(r)}}}return Hu++,e.then(xm,xm),e}function xm(){if(--Hu===0&&Ps!==null){ji!==null&&(ji.status="fulfilled");var t=Ps;Ps=null,_i=0,ji=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function K1(t,e){var i=[],r={status:"pending",value:null,reason:null,then:function(o){i.push(o)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var o=0;o<i.length;o++)(0,i[o])(e)},function(o){for(r.status="rejected",r.reason=o,o=0;o<i.length;o++)(0,i[o])(void 0)}),r}var Sm=B.S;B.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&X1(t,e),Sm!==null&&Sm(t,e)};var Qa=X(null);function Pu(){var t=Qa.current;return t!==null?t:Yt.pooledCache}function bl(t,e){e===null?$(Qa,Qa.current):$(Qa,e.pool)}function Tm(){var t=Pu();return t===null?null:{parent:se._currentValue,pool:t}}var Gs=Error(l(460)),wm=Error(l(474)),xl=Error(l(542)),Gu={then:function(){}};function Am(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Sl(){}function Em(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(Sl,Sl),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t;default:if(typeof e.status=="string")e.then(Sl,Sl);else{if(t=Yt,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=r}},function(r){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t}throw Ys=e,Gs}}var Ys=null;function Mm(){if(Ys===null)throw Error(l(459));var t=Ys;return Ys=null,t}function Cm(t){if(t===Gs||t===xl)throw Error(l(483))}var aa=!1;function Yu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function ia(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function sa(t,e,i){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(zt&2)!==0){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,e=hl(t),hm(t,null,i),e}return dl(t,r,e,i),hl(t)}function qs(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,xh(t,i)}}function Xu(t,e){var i=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,i===r)){var o=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var y={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};c===null?o=c=y:c=c.next=y,i=i.next}while(i!==null);c===null?o=c=e:c=c.next=e}else o=c=e;i={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:r.shared,callbacks:r.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var Ku=!1;function Xs(){if(Ku){var t=ji;if(t!==null)throw t}}function Ks(t,e,i,r){Ku=!1;var o=t.updateQueue;aa=!1;var c=o.firstBaseUpdate,y=o.lastBaseUpdate,x=o.shared.pending;if(x!==null){o.shared.pending=null;var R=x,k=R.next;R.next=null,y===null?c=k:y.next=k,y=R;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,x=Z.lastBaseUpdate,x!==y&&(x===null?Z.firstBaseUpdate=k:x.next=k,Z.lastBaseUpdate=R))}if(c!==null){var F=o.baseState;y=0,Z=k=R=null,x=c;do{var H=x.lane&-536870913,G=H!==x.lane;if(G?(Nt&H)===H:(r&H)===H){H!==0&&H===_i&&(Ku=!0),Z!==null&&(Z=Z.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});t:{var gt=t,mt=x;H=e;var Ht=i;switch(mt.tag){case 1:if(gt=mt.payload,typeof gt=="function"){F=gt.call(Ht,F,H);break t}F=gt;break t;case 3:gt.flags=gt.flags&-65537|128;case 0:if(gt=mt.payload,H=typeof gt=="function"?gt.call(Ht,F,H):gt,H==null)break t;F=g({},F,H);break t;case 2:aa=!0}}H=x.callback,H!==null&&(t.flags|=64,G&&(t.flags|=8192),G=o.callbacks,G===null?o.callbacks=[H]:G.push(H))}else G={lane:H,tag:x.tag,payload:x.payload,callback:x.callback,next:null},Z===null?(k=Z=G,R=F):Z=Z.next=G,y|=H;if(x=x.next,x===null){if(x=o.shared.pending,x===null)break;G=x,x=G.next,G.next=null,o.lastBaseUpdate=G,o.shared.pending=null}}while(!0);Z===null&&(R=F),o.baseState=R,o.firstBaseUpdate=k,o.lastBaseUpdate=Z,c===null&&(o.shared.lanes=0),ha|=y,t.lanes=y,t.memoizedState=F}}function Rm(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Dm(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)Rm(i[t],e)}var Vi=X(null),Tl=X(0);function Om(t,e){t=Xn,$(Tl,t),$(Vi,e),Xn=t|e.baseLanes}function Zu(){$(Tl,Xn),$(Vi,Vi.current)}function Qu(){Xn=Tl.current,J(Vi),J(Tl)}var ra=0,wt=null,Ut=null,ne=null,wl=!1,zi=!1,Fa=!1,Al=0,Zs=0,Li=null,Z1=0;function Jt(){throw Error(l(321))}function Fu(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!Le(t[i],e[i]))return!1;return!0}function Iu(t,e,i,r,o,c){return ra=c,wt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,B.H=t===null||t.memoizedState===null?hp:mp,Fa=!1,c=i(r,o),Fa=!1,zi&&(c=_m(e,i,r,o)),Nm(t),c}function Nm(t){B.H=Ol;var e=Ut!==null&&Ut.next!==null;if(ra=0,ne=Ut=wt=null,wl=!1,Zs=0,Li=null,e)throw Error(l(300));t===null||ue||(t=t.dependencies,t!==null&&yl(t)&&(ue=!0))}function _m(t,e,i,r){wt=t;var o=0;do{if(zi&&(Li=null),Zs=0,zi=!1,25<=o)throw Error(l(301));if(o+=1,ne=Ut=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}B.H=tT,c=e(i,r)}while(zi);return c}function Q1(){var t=B.H,e=t.useState()[0];return e=typeof e.then=="function"?Qs(e):e,t=t.useState()[0],(Ut!==null?Ut.memoizedState:null)!==t&&(wt.flags|=1024),e}function Wu(){var t=Al!==0;return Al=0,t}function $u(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function Ju(t){if(wl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}wl=!1}ra=0,ne=Ut=wt=null,zi=!1,Zs=Al=0,Li=null}function Oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?wt.memoizedState=ne=t:ne=ne.next=t,ne}function ae(){if(Ut===null){var t=wt.alternate;t=t!==null?t.memoizedState:null}else t=Ut.next;var e=ne===null?wt.memoizedState:ne.next;if(e!==null)ne=e,Ut=t;else{if(t===null)throw wt.alternate===null?Error(l(467)):Error(l(310));Ut=t,t={memoizedState:Ut.memoizedState,baseState:Ut.baseState,baseQueue:Ut.baseQueue,queue:Ut.queue,next:null},ne===null?wt.memoizedState=ne=t:ne=ne.next=t}return ne}function tc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qs(t){var e=Zs;return Zs+=1,Li===null&&(Li=[]),t=Em(Li,t,e),e=wt,(ne===null?e.memoizedState:ne.next)===null&&(e=e.alternate,B.H=e===null||e.memoizedState===null?hp:mp),t}function El(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qs(t);if(t.$$typeof===O)return be(t)}throw Error(l(438,String(t)))}function ec(t){var e=null,i=wt.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var r=wt.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=tc(),wt.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),r=0;r<t;r++)i[r]=ft;return e.index++,i}function kn(t,e){return typeof e=="function"?e(t):e}function Ml(t){var e=ae();return nc(e,Ut,t)}function nc(t,e,i){var r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=i;var o=t.baseQueue,c=r.pending;if(c!==null){if(o!==null){var y=o.next;o.next=c.next,c.next=y}e.baseQueue=o=c,r.pending=null}if(c=t.baseState,o===null)t.memoizedState=c;else{e=o.next;var x=y=null,R=null,k=e,Z=!1;do{var F=k.lane&-536870913;if(F!==k.lane?(Nt&F)===F:(ra&F)===F){var H=k.revertLane;if(H===0)R!==null&&(R=R.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),F===_i&&(Z=!0);else if((ra&H)===H){k=k.next,H===_i&&(Z=!0);continue}else F={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=F,y=c):R=R.next=F,wt.lanes|=H,ha|=H;F=k.action,Fa&&i(c,F),c=k.hasEagerState?k.eagerState:i(c,F)}else H={lane:F,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=H,y=c):R=R.next=H,wt.lanes|=F,ha|=F;k=k.next}while(k!==null&&k!==e);if(R===null?y=c:R.next=x,!Le(c,t.memoizedState)&&(ue=!0,Z&&(i=ji,i!==null)))throw i;t.memoizedState=c,t.baseState=y,t.baseQueue=R,r.lastRenderedState=c}return o===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function ac(t){var e=ae(),i=e.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=t;var r=i.dispatch,o=i.pending,c=e.memoizedState;if(o!==null){i.pending=null;var y=o=o.next;do c=t(c,y.action),y=y.next;while(y!==o);Le(c,e.memoizedState)||(ue=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),i.lastRenderedState=c}return[c,r]}function jm(t,e,i){var r=wt,o=ae(),c=Vt;if(c){if(i===void 0)throw Error(l(407));i=i()}else i=e();var y=!Le((Ut||o).memoizedState,i);y&&(o.memoizedState=i,ue=!0),o=o.queue;var x=Lm.bind(null,r,o,t);if(Fs(2048,8,x,[t]),o.getSnapshot!==e||y||ne!==null&&ne.memoizedState.tag&1){if(r.flags|=2048,Bi(9,Cl(),zm.bind(null,r,o,i,e),null),Yt===null)throw Error(l(349));c||(ra&124)!==0||Vm(r,e,i)}return i}function Vm(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=wt.updateQueue,e===null?(e=tc(),wt.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function zm(t,e,i,r){e.value=i,e.getSnapshot=r,Bm(e)&&Um(t)}function Lm(t,e,i){return i(function(){Bm(e)&&Um(t)})}function Bm(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!Le(t,i)}catch(r){return!0}}function Um(t){var e=Ri(t,2);e!==null&&Ge(e,t,2)}function ic(t){var e=Oe();if(typeof t=="function"){var i=t;if(t=i(),Fa){Jn(!0);try{i()}finally{Jn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:t},e}function km(t,e,i,r){return t.baseState=i,nc(t,Ut,typeof r=="function"?r:kn)}function F1(t,e,i,r,o){if(Dl(t))throw Error(l(485));if(t=e.action,t!==null){var c={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};B.T!==null?i(!0):c.isTransition=!1,r(c),i=e.pending,i===null?(c.next=e.pending=c,Hm(e,c)):(c.next=i.next,e.pending=i.next=c)}}function Hm(t,e){var i=e.action,r=e.payload,o=t.state;if(e.isTransition){var c=B.T,y={};B.T=y;try{var x=i(o,r),R=B.S;R!==null&&R(y,x),Pm(t,e,x)}catch(k){sc(t,e,k)}finally{B.T=c}}else try{c=i(o,r),Pm(t,e,c)}catch(k){sc(t,e,k)}}function Pm(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(r){Gm(t,e,r)},function(r){return sc(t,e,r)}):Gm(t,e,i)}function Gm(t,e,i){e.status="fulfilled",e.value=i,Ym(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,Hm(t,i)))}function sc(t,e,i){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=i,Ym(e),e=e.next;while(e!==r)}t.action=null}function Ym(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function qm(t,e){return e}function Xm(t,e){if(Vt){var i=Yt.formState;if(i!==null){t:{var r=wt;if(Vt){if(It){e:{for(var o=It,c=hn;o.nodeType!==8;){if(!c){o=null;break e}if(o=ln(o.nextSibling),o===null){o=null;break e}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){It=ln(o.nextSibling),r=o.data==="F!";break t}}Xa(r)}r=!1}r&&(e=i[0])}}return i=Oe(),i.memoizedState=i.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qm,lastRenderedState:e},i.queue=r,i=cp.bind(null,wt,r),r.dispatch=i,r=ic(!1),c=cc.bind(null,wt,!1,r.queue),r=Oe(),o={state:e,dispatch:null,action:t,pending:null},r.queue=o,i=F1.bind(null,wt,o,c,i),o.dispatch=i,r.memoizedState=t,[e,i,!1]}function Km(t){var e=ae();return Zm(e,Ut,t)}function Zm(t,e,i){if(e=nc(t,e,qm)[0],t=Ml(kn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Qs(e)}catch(y){throw y===Gs?xl:y}else r=e;e=ae();var o=e.queue,c=o.dispatch;return i!==e.memoizedState&&(wt.flags|=2048,Bi(9,Cl(),I1.bind(null,o,i),null)),[r,c,t]}function I1(t,e){t.action=e}function Qm(t){var e=ae(),i=Ut;if(i!==null)return Zm(e,i,t);ae(),e=e.memoizedState,i=ae();var r=i.queue.dispatch;return i.memoizedState=t,[e,r,!1]}function Bi(t,e,i,r){return t={tag:t,create:i,deps:r,inst:e,next:null},e=wt.updateQueue,e===null&&(e=tc(),wt.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(r=i.next,i.next=t,t.next=r,e.lastEffect=t),t}function Cl(){return{destroy:void 0,resource:void 0}}function Fm(){return ae().memoizedState}function Rl(t,e,i,r){var o=Oe();r=r===void 0?null:r,wt.flags|=t,o.memoizedState=Bi(1|e,Cl(),i,r)}function Fs(t,e,i,r){var o=ae();r=r===void 0?null:r;var c=o.memoizedState.inst;Ut!==null&&r!==null&&Fu(r,Ut.memoizedState.deps)?o.memoizedState=Bi(e,c,i,r):(wt.flags|=t,o.memoizedState=Bi(1|e,c,i,r))}function Im(t,e){Rl(8390656,8,t,e)}function Wm(t,e){Fs(2048,8,t,e)}function $m(t,e){return Fs(4,2,t,e)}function Jm(t,e){return Fs(4,4,t,e)}function tp(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ep(t,e,i){i=i!=null?i.concat([t]):null,Fs(4,4,tp.bind(null,e,t),i)}function rc(){}function np(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;return e!==null&&Fu(e,r[1])?r[0]:(i.memoizedState=[t,e],t)}function ap(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;if(e!==null&&Fu(e,r[1]))return r[0];if(r=t(),Fa){Jn(!0);try{t()}finally{Jn(!1)}}return i.memoizedState=[r,e],r}function lc(t,e,i){return i===void 0||(ra&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=rg(),wt.lanes|=t,ha|=t,i)}function ip(t,e,i,r){return Le(i,e)?i:Vi.current!==null?(t=lc(t,i,r),Le(t,e)||(ue=!0),t):(ra&42)===0?(ue=!0,t.memoizedState=i):(t=rg(),wt.lanes|=t,ha|=t,e)}function sp(t,e,i,r,o){var c=q.p;q.p=c!==0&&8>c?c:8;var y=B.T,x={};B.T=x,cc(t,!1,e,i);try{var R=o(),k=B.S;if(k!==null&&k(x,R),R!==null&&typeof R=="object"&&typeof R.then=="function"){var Z=K1(R,r);Is(t,e,Z,Pe(t))}else Is(t,e,r,Pe(t))}catch(F){Is(t,e,{then:function(){},status:"rejected",reason:F},Pe())}finally{q.p=c,B.T=y}}function W1(){}function oc(t,e,i,r){if(t.tag!==5)throw Error(l(476));var o=rp(t).queue;sp(t,o,e,P,i===null?W1:function(){return lp(t),i(r)})}function rp(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:P},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function lp(t){var e=rp(t).next.queue;Is(t,e,{},Pe())}function uc(){return be(mr)}function op(){return ae().memoizedState}function up(){return ae().memoizedState}function $1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=Pe();t=ia(i);var r=sa(e,t,i);r!==null&&(Ge(r,e,i),qs(r,e,i)),e={cache:ku()},t.payload=e;return}e=e.return}}function J1(t,e,i){var r=Pe();i={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Dl(t)?fp(e,i):(i=Du(t,e,i,r),i!==null&&(Ge(i,t,r),dp(i,e,r)))}function cp(t,e,i){var r=Pe();Is(t,e,i,r)}function Is(t,e,i,r){var o={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Dl(t))fp(e,o);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var y=e.lastRenderedState,x=c(y,i);if(o.hasEagerState=!0,o.eagerState=x,Le(x,y))return dl(t,e,o,0),Yt===null&&fl(),!1}catch(R){}finally{}if(i=Du(t,e,o,r),i!==null)return Ge(i,t,r),dp(i,e,r),!0}return!1}function cc(t,e,i,r){if(r={lane:2,revertLane:Gc(),action:r,hasEagerState:!1,eagerState:null,next:null},Dl(t)){if(e)throw Error(l(479))}else e=Du(t,i,r,2),e!==null&&Ge(e,t,2)}function Dl(t){var e=t.alternate;return t===wt||e!==null&&e===wt}function fp(t,e){zi=wl=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function dp(t,e,i){if((i&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,xh(t,i)}}var Ol={readContext:be,use:El,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},hp={readContext:be,use:El,useCallback:function(t,e){return Oe().memoizedState=[t,e===void 0?null:e],t},useContext:be,useEffect:Im,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Rl(4194308,4,tp.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Rl(4194308,4,t,e)},useInsertionEffect:function(t,e){Rl(4,2,t,e)},useMemo:function(t,e){var i=Oe();e=e===void 0?null:e;var r=t();if(Fa){Jn(!0);try{t()}finally{Jn(!1)}}return i.memoizedState=[r,e],r},useReducer:function(t,e,i){var r=Oe();if(i!==void 0){var o=i(e);if(Fa){Jn(!0);try{i(e)}finally{Jn(!1)}}}else o=e;return r.memoizedState=r.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},r.queue=t,t=t.dispatch=J1.bind(null,wt,t),[r.memoizedState,t]},useRef:function(t){var e=Oe();return t={current:t},e.memoizedState=t},useState:function(t){t=ic(t);var e=t.queue,i=cp.bind(null,wt,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:rc,useDeferredValue:function(t,e){var i=Oe();return lc(i,t,e)},useTransition:function(){var t=ic(!1);return t=sp.bind(null,wt,t.queue,!0,!1),Oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var r=wt,o=Oe();if(Vt){if(i===void 0)throw Error(l(407));i=i()}else{if(i=e(),Yt===null)throw Error(l(349));(Nt&124)!==0||Vm(r,e,i)}o.memoizedState=i;var c={value:i,getSnapshot:e};return o.queue=c,Im(Lm.bind(null,r,c,t),[t]),r.flags|=2048,Bi(9,Cl(),zm.bind(null,r,c,i,e),null),i},useId:function(){var t=Oe(),e=Yt.identifierPrefix;if(Vt){var i=Ln,r=zn;i=(r&~(1<<32-ze(r)-1)).toString(32)+i,e="«"+e+"R"+i,i=Al++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=Z1++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:uc,useFormState:Xm,useActionState:Xm,useOptimistic:function(t){var e=Oe();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=cc.bind(null,wt,!0,i),i.dispatch=e,[t,e]},useMemoCache:ec,useCacheRefresh:function(){return Oe().memoizedState=$1.bind(null,wt)}},mp={readContext:be,use:El,useCallback:np,useContext:be,useEffect:Wm,useImperativeHandle:ep,useInsertionEffect:$m,useLayoutEffect:Jm,useMemo:ap,useReducer:Ml,useRef:Fm,useState:function(){return Ml(kn)},useDebugValue:rc,useDeferredValue:function(t,e){var i=ae();return ip(i,Ut.memoizedState,t,e)},useTransition:function(){var t=Ml(kn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:jm,useId:op,useHostTransitionStatus:uc,useFormState:Km,useActionState:Km,useOptimistic:function(t,e){var i=ae();return km(i,Ut,t,e)},useMemoCache:ec,useCacheRefresh:up},tT={readContext:be,use:El,useCallback:np,useContext:be,useEffect:Wm,useImperativeHandle:ep,useInsertionEffect:$m,useLayoutEffect:Jm,useMemo:ap,useReducer:ac,useRef:Fm,useState:function(){return ac(kn)},useDebugValue:rc,useDeferredValue:function(t,e){var i=ae();return Ut===null?lc(i,t,e):ip(i,Ut.memoizedState,t,e)},useTransition:function(){var t=ac(kn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:jm,useId:op,useHostTransitionStatus:uc,useFormState:Qm,useActionState:Qm,useOptimistic:function(t,e){var i=ae();return Ut!==null?km(i,Ut,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:ec,useCacheRefresh:up},Ui=null,Ws=0;function Nl(t){var e=Ws;return Ws+=1,Ui===null&&(Ui=[]),Em(Ui,t,e)}function $s(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function _l(t,e){throw e.$$typeof===v?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function pp(t){var e=t._init;return e(t._payload)}function gp(t){function e(L,V){if(t){var U=L.deletions;U===null?(L.deletions=[V],L.flags|=16):U.push(V)}}function i(L,V){if(!t)return null;for(;V!==null;)e(L,V),V=V.sibling;return null}function r(L){for(var V=new Map;L!==null;)L.key!==null?V.set(L.key,L):V.set(L.index,L),L=L.sibling;return V}function o(L,V){return L=Vn(L,V),L.index=0,L.sibling=null,L}function c(L,V,U){return L.index=U,t?(U=L.alternate,U!==null?(U=U.index,U<V?(L.flags|=67108866,V):U):(L.flags|=67108866,V)):(L.flags|=1048576,V)}function y(L){return t&&L.alternate===null&&(L.flags|=67108866),L}function x(L,V,U,Q){return V===null||V.tag!==6?(V=Nu(U,L.mode,Q),V.return=L,V):(V=o(V,U),V.return=L,V)}function R(L,V,U,Q){var it=U.type;return it===C?Z(L,V,U.props.children,Q,U.key):V!==null&&(V.elementType===it||typeof it=="object"&&it!==null&&it.$$typeof===Y&&pp(it)===V.type)?(V=o(V,U.props),$s(V,U),V.return=L,V):(V=ml(U.type,U.key,U.props,null,L.mode,Q),$s(V,U),V.return=L,V)}function k(L,V,U,Q){return V===null||V.tag!==4||V.stateNode.containerInfo!==U.containerInfo||V.stateNode.implementation!==U.implementation?(V=_u(U,L.mode,Q),V.return=L,V):(V=o(V,U.children||[]),V.return=L,V)}function Z(L,V,U,Q,it){return V===null||V.tag!==7?(V=Pa(U,L.mode,Q,it),V.return=L,V):(V=o(V,U),V.return=L,V)}function F(L,V,U){if(typeof V=="string"&&V!==""||typeof V=="number"||typeof V=="bigint")return V=Nu(""+V,L.mode,U),V.return=L,V;if(typeof V=="object"&&V!==null){switch(V.$$typeof){case b:return U=ml(V.type,V.key,V.props,null,L.mode,U),$s(U,V),U.return=L,U;case T:return V=_u(V,L.mode,U),V.return=L,V;case Y:var Q=V._init;return V=Q(V._payload),F(L,V,U)}if(ut(V)||lt(V))return V=Pa(V,L.mode,U,null),V.return=L,V;if(typeof V.then=="function")return F(L,Nl(V),U);if(V.$$typeof===O)return F(L,vl(L,V),U);_l(L,V)}return null}function H(L,V,U,Q){var it=V!==null?V.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return it!==null?null:x(L,V,""+U,Q);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case b:return U.key===it?R(L,V,U,Q):null;case T:return U.key===it?k(L,V,U,Q):null;case Y:return it=U._init,U=it(U._payload),H(L,V,U,Q)}if(ut(U)||lt(U))return it!==null?null:Z(L,V,U,Q,null);if(typeof U.then=="function")return H(L,V,Nl(U),Q);if(U.$$typeof===O)return H(L,V,vl(L,U),Q);_l(L,U)}return null}function G(L,V,U,Q,it){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return L=L.get(U)||null,x(V,L,""+Q,it);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case b:return L=L.get(Q.key===null?U:Q.key)||null,R(V,L,Q,it);case T:return L=L.get(Q.key===null?U:Q.key)||null,k(V,L,Q,it);case Y:var Mt=Q._init;return Q=Mt(Q._payload),G(L,V,U,Q,it)}if(ut(Q)||lt(Q))return L=L.get(U)||null,Z(V,L,Q,it,null);if(typeof Q.then=="function")return G(L,V,U,Nl(Q),it);if(Q.$$typeof===O)return G(L,V,U,vl(V,Q),it);_l(V,Q)}return null}function gt(L,V,U,Q){for(var it=null,Mt=null,ot=V,pt=V=0,fe=null;ot!==null&&pt<U.length;pt++){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var jt=H(L,ot,U[pt],Q);if(jt===null){ot===null&&(ot=fe);break}t&&ot&&jt.alternate===null&&e(L,ot),V=c(jt,V,pt),Mt===null?it=jt:Mt.sibling=jt,Mt=jt,ot=fe}if(pt===U.length)return i(L,ot),Vt&&Ya(L,pt),it;if(ot===null){for(;pt<U.length;pt++)ot=F(L,U[pt],Q),ot!==null&&(V=c(ot,V,pt),Mt===null?it=ot:Mt.sibling=ot,Mt=ot);return Vt&&Ya(L,pt),it}for(ot=r(ot);pt<U.length;pt++)fe=G(ot,L,pt,U[pt],Q),fe!==null&&(t&&fe.alternate!==null&&ot.delete(fe.key===null?pt:fe.key),V=c(fe,V,pt),Mt===null?it=fe:Mt.sibling=fe,Mt=fe);return t&&ot.forEach(function(Ta){return e(L,Ta)}),Vt&&Ya(L,pt),it}function mt(L,V,U,Q){if(U==null)throw Error(l(151));for(var it=null,Mt=null,ot=V,pt=V=0,fe=null,jt=U.next();ot!==null&&!jt.done;pt++,jt=U.next()){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var Ta=H(L,ot,jt.value,Q);if(Ta===null){ot===null&&(ot=fe);break}t&&ot&&Ta.alternate===null&&e(L,ot),V=c(Ta,V,pt),Mt===null?it=Ta:Mt.sibling=Ta,Mt=Ta,ot=fe}if(jt.done)return i(L,ot),Vt&&Ya(L,pt),it;if(ot===null){for(;!jt.done;pt++,jt=U.next())jt=F(L,jt.value,Q),jt!==null&&(V=c(jt,V,pt),Mt===null?it=jt:Mt.sibling=jt,Mt=jt);return Vt&&Ya(L,pt),it}for(ot=r(ot);!jt.done;pt++,jt=U.next())jt=G(ot,L,pt,jt.value,Q),jt!==null&&(t&&jt.alternate!==null&&ot.delete(jt.key===null?pt:jt.key),V=c(jt,V,pt),Mt===null?it=jt:Mt.sibling=jt,Mt=jt);return t&&ot.forEach(function(ew){return e(L,ew)}),Vt&&Ya(L,pt),it}function Ht(L,V,U,Q){if(typeof U=="object"&&U!==null&&U.type===C&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case b:t:{for(var it=U.key;V!==null;){if(V.key===it){if(it=U.type,it===C){if(V.tag===7){i(L,V.sibling),Q=o(V,U.props.children),Q.return=L,L=Q;break t}}else if(V.elementType===it||typeof it=="object"&&it!==null&&it.$$typeof===Y&&pp(it)===V.type){i(L,V.sibling),Q=o(V,U.props),$s(Q,U),Q.return=L,L=Q;break t}i(L,V);break}else e(L,V);V=V.sibling}U.type===C?(Q=Pa(U.props.children,L.mode,Q,U.key),Q.return=L,L=Q):(Q=ml(U.type,U.key,U.props,null,L.mode,Q),$s(Q,U),Q.return=L,L=Q)}return y(L);case T:t:{for(it=U.key;V!==null;){if(V.key===it)if(V.tag===4&&V.stateNode.containerInfo===U.containerInfo&&V.stateNode.implementation===U.implementation){i(L,V.sibling),Q=o(V,U.children||[]),Q.return=L,L=Q;break t}else{i(L,V);break}else e(L,V);V=V.sibling}Q=_u(U,L.mode,Q),Q.return=L,L=Q}return y(L);case Y:return it=U._init,U=it(U._payload),Ht(L,V,U,Q)}if(ut(U))return gt(L,V,U,Q);if(lt(U)){if(it=lt(U),typeof it!="function")throw Error(l(150));return U=it.call(U),mt(L,V,U,Q)}if(typeof U.then=="function")return Ht(L,V,Nl(U),Q);if(U.$$typeof===O)return Ht(L,V,vl(L,U),Q);_l(L,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,V!==null&&V.tag===6?(i(L,V.sibling),Q=o(V,U),Q.return=L,L=Q):(i(L,V),Q=Nu(U,L.mode,Q),Q.return=L,L=Q),y(L)):i(L,V)}return function(L,V,U,Q){try{Ws=0;var it=Ht(L,V,U,Q);return Ui=null,it}catch(ot){if(ot===Gs||ot===xl)throw ot;var Mt=Be(29,ot,null,L.mode);return Mt.lanes=Q,Mt.return=L,Mt}finally{}}}var ki=gp(!0),yp=gp(!1),We=X(null),mn=null;function la(t){var e=t.alternate;$(re,re.current&1),$(We,t),mn===null&&(e===null||Vi.current!==null||e.memoizedState!==null)&&(mn=t)}function vp(t){if(t.tag===22){if($(re,re.current),$(We,t),mn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(mn=t)}}else oa()}function oa(){$(re,re.current),$(We,We.current)}function Hn(t){J(We),mn===t&&(mn=null),J(re)}var re=X(0);function jl(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||tf(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function fc(t,e,i,r){e=t.memoizedState,i=i(r,e),i=i==null?e:g({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var dc={enqueueSetState:function(t,e,i){t=t._reactInternals;var r=Pe(),o=ia(r);o.payload=e,i!=null&&(o.callback=i),e=sa(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var r=Pe(),o=ia(r);o.tag=1,o.payload=e,i!=null&&(o.callback=i),e=sa(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=Pe(),r=ia(i);r.tag=2,e!=null&&(r.callback=e),e=sa(t,r,i),e!==null&&(Ge(e,t,i),qs(e,t,i))}};function bp(t,e,i,r,o,c,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,c,y):e.prototype&&e.prototype.isPureReactComponent?!Vs(i,r)||!Vs(o,c):!0}function xp(t,e,i,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,r),e.state!==t&&dc.enqueueReplaceState(e,e.state,null)}function Ia(t,e){var i=e;if("ref"in e){i={};for(var r in e)r!=="ref"&&(i[r]=e[r])}if(t=t.defaultProps){i===e&&(i=g({},i));for(var o in t)i[o]===void 0&&(i[o]=t[o])}return i}var Vl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Sp(t){Vl(t)}function Tp(t){console.error(t)}function wp(t){Vl(t)}function zl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function Ap(t,e,i){try{var r=t.onCaughtError;r(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function hc(t,e,i){return i=ia(i),i.tag=3,i.payload={element:null},i.callback=function(){zl(t,e)},i}function Ep(t){return t=ia(t),t.tag=3,t}function Mp(t,e,i,r){var o=i.type.getDerivedStateFromError;if(typeof o=="function"){var c=r.value;t.payload=function(){return o(c)},t.callback=function(){Ap(e,i,r)}}var y=i.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Ap(e,i,r),typeof o!="function"&&(ma===null?ma=new Set([this]):ma.add(this));var x=r.stack;this.componentDidCatch(r.value,{componentStack:x!==null?x:""})})}function eT(t,e,i,r,o){if(i.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=i.alternate,e!==null&&ks(e,i,o,!0),i=We.current,i!==null){switch(i.tag){case 13:return mn===null?Bc():i.alternate===null&&Wt===0&&(Wt=3),i.flags&=-257,i.flags|=65536,i.lanes=o,r===Gu?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([r]):e.add(r),kc(t,r,o)),!1;case 22:return i.flags|=65536,r===Gu?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([r]):i.add(r)),kc(t,r,o)),!1}throw Error(l(435,i.tag))}return kc(t,r,o),Bc(),!1}if(Vt)return e=We.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,r!==zu&&(t=Error(l(422),{cause:r}),Us(Ze(t,i)))):(r!==zu&&(e=Error(l(423),{cause:r}),Us(Ze(e,i))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,r=Ze(r,i),o=hc(t.stateNode,r,o),Xu(t,o),Wt!==4&&(Wt=2)),!1;var c=Error(l(520),{cause:r});if(c=Ze(c,i),sr===null?sr=[c]:sr.push(c),Wt!==4&&(Wt=2),e===null)return!0;r=Ze(r,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=o&-o,i.lanes|=t,t=hc(i.stateNode,r,t),Xu(i,t),!1;case 1:if(e=i.type,c=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(ma===null||!ma.has(c))))return i.flags|=65536,o&=-o,i.lanes|=o,o=Ep(o),Mp(o,t,i,r),Xu(i,o),!1}i=i.return}while(i!==null);return!1}var Cp=Error(l(461)),ue=!1;function me(t,e,i,r){e.child=t===null?yp(e,null,i,r):ki(e,t.child,i,r)}function Rp(t,e,i,r,o){i=i.render;var c=e.ref;if("ref"in r){var y={};for(var x in r)x!=="ref"&&(y[x]=r[x])}else y=r;return Za(e),r=Iu(t,e,i,y,c,o),x=Wu(),t!==null&&!ue?($u(t,e,o),Pn(t,e,o)):(Vt&&x&&ju(e),e.flags|=1,me(t,e,r,o),e.child)}function Dp(t,e,i,r,o){if(t===null){var c=i.type;return typeof c=="function"&&!Ou(c)&&c.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=c,Op(t,e,c,r,o)):(t=ml(i.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!Sc(t,o)){var y=c.memoizedProps;if(i=i.compare,i=i!==null?i:Vs,i(y,r)&&t.ref===e.ref)return Pn(t,e,o)}return e.flags|=1,t=Vn(c,r),t.ref=e.ref,t.return=e,e.child=t}function Op(t,e,i,r,o){if(t!==null){var c=t.memoizedProps;if(Vs(c,r)&&t.ref===e.ref)if(ue=!1,e.pendingProps=r=c,Sc(t,o))(t.flags&131072)!==0&&(ue=!0);else return e.lanes=t.lanes,Pn(t,e,o)}return mc(t,e,i,r,o)}function Np(t,e,i){var r=e.pendingProps,o=r.children,c=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=c!==null?c.baseLanes|i:i,t!==null){for(o=e.child=t.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;e.childLanes=c&~r}else e.childLanes=0,e.child=null;return _p(t,e,r,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&bl(e,c!==null?c.cachePool:null),c!==null?Om(e,c):Zu(),vp(e);else return e.lanes=e.childLanes=536870912,_p(t,e,c!==null?c.baseLanes|i:i,i)}else c!==null?(bl(e,c.cachePool),Om(e,c),oa(),e.memoizedState=null):(t!==null&&bl(e,null),Zu(),oa());return me(t,e,o,i),e.child}function _p(t,e,i,r){var o=Pu();return o=o===null?null:{parent:se._currentValue,pool:o},e.memoizedState={baseLanes:i,cachePool:o},t!==null&&bl(e,null),Zu(),vp(e),t!==null&&ks(t,e,r,!0),null}function Ll(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(l(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function mc(t,e,i,r,o){return Za(e),i=Iu(t,e,i,r,void 0,o),r=Wu(),t!==null&&!ue?($u(t,e,o),Pn(t,e,o)):(Vt&&r&&ju(e),e.flags|=1,me(t,e,i,o),e.child)}function jp(t,e,i,r,o,c){return Za(e),e.updateQueue=null,i=_m(e,r,i,o),Nm(t),r=Wu(),t!==null&&!ue?($u(t,e,c),Pn(t,e,c)):(Vt&&r&&ju(e),e.flags|=1,me(t,e,i,c),e.child)}function Vp(t,e,i,r,o){if(Za(e),e.stateNode===null){var c=Di,y=i.contextType;typeof y=="object"&&y!==null&&(c=be(y)),c=new i(r,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=dc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=r,c.state=e.memoizedState,c.refs={},Yu(e),y=i.contextType,c.context=typeof y=="object"&&y!==null?be(y):Di,c.state=e.memoizedState,y=i.getDerivedStateFromProps,typeof y=="function"&&(fc(e,i,y,r),c.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&dc.enqueueReplaceState(c,c.state,null),Ks(e,r,c,o),Xs(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){c=e.stateNode;var x=e.memoizedProps,R=Ia(i,x);c.props=R;var k=c.context,Z=i.contextType;y=Di,typeof Z=="object"&&Z!==null&&(y=be(Z));var F=i.getDerivedStateFromProps;Z=typeof F=="function"||typeof c.getSnapshotBeforeUpdate=="function",x=e.pendingProps!==x,Z||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(x||k!==y)&&xp(e,c,r,y),aa=!1;var H=e.memoizedState;c.state=H,Ks(e,r,c,o),Xs(),k=e.memoizedState,x||H!==k||aa?(typeof F=="function"&&(fc(e,i,F,r),k=e.memoizedState),(R=aa||bp(e,i,R,r,H,k,y))?(Z||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=k),c.props=r,c.state=k,c.context=y,r=R):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{c=e.stateNode,qu(t,e),y=e.memoizedProps,Z=Ia(i,y),c.props=Z,F=e.pendingProps,H=c.context,k=i.contextType,R=Di,typeof k=="object"&&k!==null&&(R=be(k)),x=i.getDerivedStateFromProps,(k=typeof x=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==F||H!==R)&&xp(e,c,r,R),aa=!1,H=e.memoizedState,c.state=H,Ks(e,r,c,o),Xs();var G=e.memoizedState;y!==F||H!==G||aa||t!==null&&t.dependencies!==null&&yl(t.dependencies)?(typeof x=="function"&&(fc(e,i,x,r),G=e.memoizedState),(Z=aa||bp(e,i,Z,r,H,G,R)||t!==null&&t.dependencies!==null&&yl(t.dependencies))?(k||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(r,G,R),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(r,G,R)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=G),c.props=r,c.state=G,c.context=R,r=Z):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),r=!1)}return c=r,Ll(t,e),r=(e.flags&128)!==0,c||r?(c=e.stateNode,i=r&&typeof i.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&r?(e.child=ki(e,t.child,null,o),e.child=ki(e,null,i,o)):me(t,e,i,o),e.memoizedState=c.state,t=e.child):t=Pn(t,e,o),t}function zp(t,e,i,r){return Bs(),e.flags|=256,me(t,e,i,r),e.child}var pc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function gc(t){return{baseLanes:t,cachePool:Tm()}}function yc(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=$e),t}function Lp(t,e,i){var r=e.pendingProps,o=!1,c=(e.flags&128)!==0,y;if((y=c)||(y=t!==null&&t.memoizedState===null?!1:(re.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(Vt){if(o?la(e):oa(),Vt){var x=It,R;if(R=x){t:{for(R=x,x=hn;R.nodeType!==8;){if(!x){x=null;break t}if(R=ln(R.nextSibling),R===null){x=null;break t}}x=R}x!==null?(e.memoizedState={dehydrated:x,treeContext:Ga!==null?{id:zn,overflow:Ln}:null,retryLane:536870912,hydrationErrors:null},R=Be(18,null,null,0),R.stateNode=x,R.return=e,e.child=R,Ee=e,It=null,R=!0):R=!1}R||Xa(e)}if(x=e.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return tf(x)?e.lanes=32:e.lanes=536870912,null;Hn(e)}return x=r.children,r=r.fallback,o?(oa(),o=e.mode,x=Bl({mode:"hidden",children:x},o),r=Pa(r,o,i,null),x.return=e,r.return=e,x.sibling=r,e.child=x,o=e.child,o.memoizedState=gc(i),o.childLanes=yc(t,y,i),e.memoizedState=pc,r):(la(e),vc(e,x))}if(R=t.memoizedState,R!==null&&(x=R.dehydrated,x!==null)){if(c)e.flags&256?(la(e),e.flags&=-257,e=bc(t,e,i)):e.memoizedState!==null?(oa(),e.child=t.child,e.flags|=128,e=null):(oa(),o=r.fallback,x=e.mode,r=Bl({mode:"visible",children:r.children},x),o=Pa(o,x,i,null),o.flags|=2,r.return=e,o.return=e,r.sibling=o,e.child=r,ki(e,t.child,null,i),r=e.child,r.memoizedState=gc(i),r.childLanes=yc(t,y,i),e.memoizedState=pc,e=o);else if(la(e),tf(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var k=y.dgst;y=k,r=Error(l(419)),r.stack="",r.digest=y,Us({value:r,source:null,stack:null}),e=bc(t,e,i)}else if(ue||ks(t,e,i,!1),y=(i&t.childLanes)!==0,ue||y){if(y=Yt,y!==null&&(r=i&-i,r=(r&42)!==0?1:eu(r),r=(r&(y.suspendedLanes|i))!==0?0:r,r!==0&&r!==R.retryLane))throw R.retryLane=r,Ri(t,r),Ge(y,t,r),Cp;x.data==="$?"||Bc(),e=bc(t,e,i)}else x.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=R.treeContext,It=ln(x.nextSibling),Ee=e,Vt=!0,qa=null,hn=!1,t!==null&&(Fe[Ie++]=zn,Fe[Ie++]=Ln,Fe[Ie++]=Ga,zn=t.id,Ln=t.overflow,Ga=e),e=vc(e,r.children),e.flags|=4096);return e}return o?(oa(),o=r.fallback,x=e.mode,R=t.child,k=R.sibling,r=Vn(R,{mode:"hidden",children:r.children}),r.subtreeFlags=R.subtreeFlags&65011712,k!==null?o=Vn(k,o):(o=Pa(o,x,i,null),o.flags|=2),o.return=e,r.return=e,r.sibling=o,e.child=r,r=o,o=e.child,x=t.child.memoizedState,x===null?x=gc(i):(R=x.cachePool,R!==null?(k=se._currentValue,R=R.parent!==k?{parent:k,pool:k}:R):R=Tm(),x={baseLanes:x.baseLanes|i,cachePool:R}),o.memoizedState=x,o.childLanes=yc(t,y,i),e.memoizedState=pc,r):(la(e),i=t.child,t=i.sibling,i=Vn(i,{mode:"visible",children:r.children}),i.return=e,i.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=i,e.memoizedState=null,i)}function vc(t,e){return e=Bl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Bl(t,e){return t=Be(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function bc(t,e,i){return ki(e,t.child,null,i),t=vc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Bp(t,e,i){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Bu(t.return,e,i)}function xc(t,e,i,r,o){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:i,tailMode:o}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=i,c.tailMode=o)}function Up(t,e,i){var r=e.pendingProps,o=r.revealOrder,c=r.tail;if(me(t,e,r.children,i),r=re.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Bp(t,i,e);else if(t.tag===19)Bp(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch($(re,r),o){case"forwards":for(i=e.child,o=null;i!==null;)t=i.alternate,t!==null&&jl(t)===null&&(o=i),i=i.sibling;i=o,i===null?(o=e.child,e.child=null):(o=i.sibling,i.sibling=null),xc(e,!1,o,i,c);break;case"backwards":for(i=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&jl(t)===null){e.child=o;break}t=o.sibling,o.sibling=i,i=o,o=t}xc(e,!0,i,null,c);break;case"together":xc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Pn(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),ha|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(ks(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,i=Vn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=Vn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Sc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&yl(t)))}function nT(t,e,i){switch(e.tag){case 3:nt(e,e.stateNode.containerInfo),na(e,se,t.memoizedState.cache),Bs();break;case 27:case 5:qt(e);break;case 4:nt(e,e.stateNode.containerInfo);break;case 10:na(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(la(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Lp(t,e,i):(la(e),t=Pn(t,e,i),t!==null?t.sibling:null);la(e);break;case 19:var o=(t.flags&128)!==0;if(r=(i&e.childLanes)!==0,r||(ks(t,e,i,!1),r=(i&e.childLanes)!==0),o){if(r)return Up(t,e,i);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),$(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Np(t,e,i);case 24:na(e,se,t.memoizedState.cache)}return Pn(t,e,i)}function kp(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)ue=!0;else{if(!Sc(t,i)&&(e.flags&128)===0)return ue=!1,nT(t,e,i);ue=(t.flags&131072)!==0}else ue=!1,Vt&&(e.flags&1048576)!==0&&pm(e,gl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,o=r._init;if(r=o(r._payload),e.type=r,typeof r=="function")Ou(r)?(t=Ia(r,t),e.tag=1,e=Vp(null,e,r,t,i)):(e.tag=0,e=mc(null,e,r,t,i));else{if(r!=null){if(o=r.$$typeof,o===z){e.tag=11,e=Rp(null,e,r,t,i);break t}else if(o===tt){e.tag=14,e=Dp(null,e,r,t,i);break t}}throw e=Et(r)||r,Error(l(306,e,""))}}return e;case 0:return mc(t,e,e.type,e.pendingProps,i);case 1:return r=e.type,o=Ia(r,e.pendingProps),Vp(t,e,r,o,i);case 3:t:{if(nt(e,e.stateNode.containerInfo),t===null)throw Error(l(387));r=e.pendingProps;var c=e.memoizedState;o=c.element,qu(t,e),Ks(e,r,null,i);var y=e.memoizedState;if(r=y.cache,na(e,se,r),r!==c.cache&&Uu(e,[se],i,!0),Xs(),r=y.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=zp(t,e,r,i);break t}else if(r!==o){o=Ze(Error(l(424)),e),Us(o),e=zp(t,e,r,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(It=ln(t.firstChild),Ee=e,Vt=!0,qa=null,hn=!0,i=yp(e,null,r,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(Bs(),r===o){e=Pn(t,e,i);break t}me(t,e,r,i)}e=e.child}return e;case 26:return Ll(t,e),t===null?(i=Yg(e.type,null,e.pendingProps,null))?e.memoizedState=i:Vt||(i=e.type,t=e.pendingProps,r=Wl(dt.current).createElement(i),r[ve]=e,r[Re]=t,ge(r,i,t),oe(r),e.stateNode=r):e.memoizedState=Yg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return qt(e),t===null&&Vt&&(r=e.stateNode=Hg(e.type,e.pendingProps,dt.current),Ee=e,hn=!0,o=It,ya(e.type)?(ef=o,It=ln(r.firstChild)):It=o),me(t,e,e.pendingProps.children,i),Ll(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Vt&&((o=r=It)&&(r=OT(r,e.type,e.pendingProps,hn),r!==null?(e.stateNode=r,Ee=e,It=ln(r.firstChild),hn=!1,o=!0):o=!1),o||Xa(e)),qt(e),o=e.type,c=e.pendingProps,y=t!==null?t.memoizedProps:null,r=c.children,Wc(o,c)?r=null:y!==null&&Wc(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Iu(t,e,Q1,null,null,i),mr._currentValue=o),Ll(t,e),me(t,e,r,i),e.child;case 6:return t===null&&Vt&&((t=i=It)&&(i=NT(i,e.pendingProps,hn),i!==null?(e.stateNode=i,Ee=e,It=null,t=!0):t=!1),t||Xa(e)),null;case 13:return Lp(t,e,i);case 4:return nt(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=ki(e,null,r,i):me(t,e,r,i),e.child;case 11:return Rp(t,e,e.type,e.pendingProps,i);case 7:return me(t,e,e.pendingProps,i),e.child;case 8:return me(t,e,e.pendingProps.children,i),e.child;case 12:return me(t,e,e.pendingProps.children,i),e.child;case 10:return r=e.pendingProps,na(e,e.type,r.value),me(t,e,r.children,i),e.child;case 9:return o=e.type._context,r=e.pendingProps.children,Za(e),o=be(o),r=r(o),e.flags|=1,me(t,e,r,i),e.child;case 14:return Dp(t,e,e.type,e.pendingProps,i);case 15:return Op(t,e,e.type,e.pendingProps,i);case 19:return Up(t,e,i);case 31:return r=e.pendingProps,i=e.mode,r={mode:r.mode,children:r.children},t===null?(i=Bl(r,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=Vn(t.child,r),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Np(t,e,i);case 24:return Za(e),r=be(se),t===null?(o=Pu(),o===null&&(o=Yt,c=ku(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=i),o=c),e.memoizedState={parent:r,cache:o},Yu(e),na(e,se,o)):((t.lanes&i)!==0&&(qu(t,e),Ks(e,null,null,i),Xs()),o=t.memoizedState,c=e.memoizedState,o.parent!==r?(o={parent:r,cache:r},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),na(e,se,r)):(r=c.cache,na(e,se,r),r!==o.cache&&Uu(e,[se],i,!0))),me(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function Gn(t){t.flags|=4}function Hp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Qg(e)){if(e=We.current,e!==null&&((Nt&4194048)===Nt?mn!==null:(Nt&62914560)!==Nt&&(Nt&536870912)===0||e!==mn))throw Ys=Gu,wm;t.flags|=8192}}function Ul(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?vh():536870912,t.lanes|=e,Yi|=e)}function Js(t,e){if(!Vt)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var r=null;i!==null;)i.alternate!==null&&(r=i),i=i.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Zt(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,r=0;if(e)for(var o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=i,e}function aT(t,e,i){var r=e.pendingProps;switch(Vu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zt(e),null;case 1:return Zt(e),null;case 3:return i=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),Un(se),Tt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(Ls(e)?Gn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,vm())),Zt(e),null;case 26:return i=e.memoizedState,t===null?(Gn(e),i!==null?(Zt(e),Hp(e,i)):(Zt(e),e.flags&=-16777217)):i?i!==t.memoizedState?(Gn(e),Zt(e),Hp(e,i)):(Zt(e),e.flags&=-16777217):(t.memoizedProps!==r&&Gn(e),Zt(e),e.flags&=-16777217),null;case 27:Ot(e),i=dt.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Gn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}t=at.current,Ls(e)?gm(e):(t=Hg(o,r,i),e.stateNode=t,Gn(e))}return Zt(e),null;case 5:if(Ot(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Gn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}if(t=at.current,Ls(e))gm(e);else{switch(o=Wl(dt.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?o.createElement(i,{is:r.is}):o.createElement(i)}}t[ve]=e,t[Re]=r;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(ge(t,i,r),i){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Gn(e)}}return Zt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&Gn(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(l(166));if(t=dt.current,Ls(e)){if(t=e.stateNode,i=e.memoizedProps,r=null,o=Ee,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}t[ve]=e,t=!!(t.nodeValue===i||r!==null&&r.suppressHydrationWarning===!0||jg(t.nodeValue,i)),t||Xa(e)}else t=Wl(t).createTextNode(r),t[ve]=e,e.stateNode=t}return Zt(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ls(e),r!==null&&r.dehydrated!==null){if(t===null){if(!o)throw Error(l(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(l(317));o[ve]=e}else Bs(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Zt(e),o=!1}else o=vm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Hn(e),e):(Hn(e),null)}if(Hn(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=r!==null,t=t!==null&&t.memoizedState!==null,i){r=e.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var c=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(c=r.memoizedState.cachePool.pool),c!==o&&(r.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Ul(e,e.updateQueue),Zt(e),null;case 4:return Tt(),t===null&&Kc(e.stateNode.containerInfo),Zt(e),null;case 10:return Un(e.type),Zt(e),null;case 19:if(J(re),o=e.memoizedState,o===null)return Zt(e),null;if(r=(e.flags&128)!==0,c=o.rendering,c===null)if(r)Js(o,!1);else{if(Wt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=jl(t),c!==null){for(e.flags|=128,Js(o,!1),t=c.updateQueue,e.updateQueue=t,Ul(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)mm(i,t),i=i.sibling;return $(re,re.current&1|2),e.child}t=t.sibling}o.tail!==null&&he()>Pl&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304)}else{if(!r)if(t=jl(c),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Ul(e,t),Js(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!Vt)return Zt(e),null}else 2*he()-o.renderingStartTime>Pl&&i!==536870912&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304);o.isBackwards?(c.sibling=e.child,e.child=c):(t=o.last,t!==null?t.sibling=c:e.child=c,o.last=c)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=he(),e.sibling=null,t=re.current,$(re,r?t&1|2:t&1),e):(Zt(e),null);case 22:case 23:return Hn(e),Qu(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(i&536870912)!==0&&(e.flags&128)===0&&(Zt(e),e.subtreeFlags&6&&(e.flags|=8192)):Zt(e),i=e.updateQueue,i!==null&&Ul(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==i&&(e.flags|=2048),t!==null&&J(Qa),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),Un(se),Zt(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function iT(t,e){switch(Vu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Un(se),Tt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ot(e),null;case 13:if(Hn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));Bs()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return J(re),null;case 4:return Tt(),null;case 10:return Un(e.type),null;case 22:case 23:return Hn(e),Qu(),t!==null&&J(Qa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Un(se),null;case 25:return null;default:return null}}function Pp(t,e){switch(Vu(e),e.tag){case 3:Un(se),Tt();break;case 26:case 27:case 5:Ot(e);break;case 4:Tt();break;case 13:Hn(e);break;case 19:J(re);break;case 10:Un(e.type);break;case 22:case 23:Hn(e),Qu(),t!==null&&J(Qa);break;case 24:Un(se)}}function tr(t,e){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var o=r.next;i=o;do{if((i.tag&t)===t){r=void 0;var c=i.create,y=i.inst;r=c(),y.destroy=r}i=i.next}while(i!==o)}}catch(x){Gt(e,e.return,x)}}function ua(t,e,i){try{var r=e.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var c=o.next;r=c;do{if((r.tag&t)===t){var y=r.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,o=e;var R=i,k=x;try{k()}catch(Z){Gt(o,R,Z)}}}r=r.next}while(r!==c)}}catch(Z){Gt(e,e.return,Z)}}function Gp(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{Dm(e,i)}catch(r){Gt(t,t.return,r)}}}function Yp(t,e,i){i.props=Ia(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(r){Gt(t,e,r)}}function er(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof i=="function"?t.refCleanup=i(r):i.current=r}}catch(o){Gt(t,e,o)}}function pn(t,e){var i=t.ref,r=t.refCleanup;if(i!==null)if(typeof r=="function")try{r()}catch(o){Gt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(o){Gt(t,e,o)}else i.current=null}function qp(t){var e=t.type,i=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break t;case"img":i.src?r.src=i.src:i.srcSet&&(r.srcset=i.srcSet)}}catch(o){Gt(t,t.return,o)}}function Tc(t,e,i){try{var r=t.stateNode;ET(r,t.type,i,e),r[Re]=e}catch(o){Gt(t,t.return,o)}}function Xp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ya(t.type)||t.tag===4}function wc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Xp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ya(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Ac(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=Il));else if(r!==4&&(r===27&&ya(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(Ac(t,e,i),t=t.sibling;t!==null;)Ac(t,e,i),t=t.sibling}function kl(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(r!==4&&(r===27&&ya(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(kl(t,e,i),t=t.sibling;t!==null;)kl(t,e,i),t=t.sibling}function Kp(t){var e=t.stateNode,i=t.memoizedProps;try{for(var r=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);ge(e,r,i),e[ve]=t,e[Re]=i}catch(c){Gt(t,t.return,c)}}var Yn=!1,te=!1,Ec=!1,Zp=typeof WeakSet=="function"?WeakSet:Set,ce=null;function sT(t,e){if(t=t.containerInfo,Fc=ao,t=im(t),wu(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var r=i.getSelection&&i.getSelection();if(r&&r.rangeCount!==0){i=r.anchorNode;var o=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{i.nodeType,c.nodeType}catch(mt){i=null;break t}var y=0,x=-1,R=-1,k=0,Z=0,F=t,H=null;e:for(;;){for(var G;F!==i||o!==0&&F.nodeType!==3||(x=y+o),F!==c||r!==0&&F.nodeType!==3||(R=y+r),F.nodeType===3&&(y+=F.nodeValue.length),(G=F.firstChild)!==null;)H=F,F=G;for(;;){if(F===t)break e;if(H===i&&++k===o&&(x=y),H===c&&++Z===r&&(R=y),(G=F.nextSibling)!==null)break;F=H,H=F.parentNode}F=G}i=x===-1||R===-1?null:{start:x,end:R}}else i=null}i=i||{start:0,end:0}}else i=null;for(Ic={focusedElem:t,selectionRange:i},ao=!1,ce=e;ce!==null;)if(e=ce,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ce=t;else for(;ce!==null;){switch(e=ce,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,i=e,o=c.memoizedProps,c=c.memoizedState,r=i.stateNode;try{var gt=Ia(i.type,o,i.elementType===i.type);t=r.getSnapshotBeforeUpdate(gt,c),r.__reactInternalSnapshotBeforeUpdate=t}catch(mt){Gt(i,i.return,mt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)Jc(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Jc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,ce=t;break}ce=e.return}}function Qp(t,e,i){var r=i.flags;switch(i.tag){case 0:case 11:case 15:ca(t,i),r&4&&tr(5,i);break;case 1:if(ca(t,i),r&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(y){Gt(i,i.return,y)}else{var o=Ia(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Gt(i,i.return,y)}}r&64&&Gp(i),r&512&&er(i,i.return);break;case 3:if(ca(t,i),r&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Dm(t,e)}catch(y){Gt(i,i.return,y)}}break;case 27:e===null&&r&4&&Kp(i);case 26:case 5:ca(t,i),e===null&&r&4&&qp(i),r&512&&er(i,i.return);break;case 12:ca(t,i);break;case 13:ca(t,i),r&4&&Wp(t,i),r&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=mT.bind(null,i),_T(t,i))));break;case 22:if(r=i.memoizedState!==null||Yn,!r){e=e!==null&&e.memoizedState!==null||te,o=Yn;var c=te;Yn=r,(te=e)&&!c?fa(t,i,(i.subtreeFlags&8772)!==0):ca(t,i),Yn=o,te=c}break;case 30:break;default:ca(t,i)}}function Fp(t){var e=t.alternate;e!==null&&(t.alternate=null,Fp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&iu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ne=!1;function qn(t,e,i){for(i=i.child;i!==null;)Ip(t,e,i),i=i.sibling}function Ip(t,e,i){if(Ve&&typeof Ve.onCommitFiberUnmount=="function")try{Ve.onCommitFiberUnmount(Ts,i)}catch(c){}switch(i.tag){case 26:te||pn(i,e),qn(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:te||pn(i,e);var r=Xt,o=Ne;ya(i.type)&&(Xt=i.stateNode,Ne=!1),qn(t,e,i),cr(i.stateNode),Xt=r,Ne=o;break;case 5:te||pn(i,e);case 6:if(r=Xt,o=Ne,Xt=null,qn(t,e,i),Xt=r,Ne=o,Xt!==null)if(Ne)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(i.stateNode)}catch(c){Gt(i,e,c)}else try{Xt.removeChild(i.stateNode)}catch(c){Gt(i,e,c)}break;case 18:Xt!==null&&(Ne?(t=Xt,Ug(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),vr(t)):Ug(Xt,i.stateNode));break;case 4:r=Xt,o=Ne,Xt=i.stateNode.containerInfo,Ne=!0,qn(t,e,i),Xt=r,Ne=o;break;case 0:case 11:case 14:case 15:te||ua(2,i,e),te||ua(4,i,e),qn(t,e,i);break;case 1:te||(pn(i,e),r=i.stateNode,typeof r.componentWillUnmount=="function"&&Yp(i,e,r)),qn(t,e,i);break;case 21:qn(t,e,i);break;case 22:te=(r=te)||i.memoizedState!==null,qn(t,e,i),te=r;break;default:qn(t,e,i)}}function Wp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{vr(t)}catch(i){Gt(e,e.return,i)}}function rT(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Zp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Zp),e;default:throw Error(l(435,t.tag))}}function Mc(t,e){var i=rT(t);e.forEach(function(r){var o=pT.bind(null,t,r);i.has(r)||(i.add(r),r.then(o,o))})}function Ue(t,e){var i=e.deletions;if(i!==null)for(var r=0;r<i.length;r++){var o=i[r],c=t,y=e,x=y;t:for(;x!==null;){switch(x.tag){case 27:if(ya(x.type)){Xt=x.stateNode,Ne=!1;break t}break;case 5:Xt=x.stateNode,Ne=!1;break t;case 3:case 4:Xt=x.stateNode.containerInfo,Ne=!0;break t}x=x.return}if(Xt===null)throw Error(l(160));Ip(c,y,o),Xt=null,Ne=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)$p(e,t),e=e.sibling}var rn=null;function $p(t,e){var i=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ue(e,t),ke(t),r&4&&(ua(3,t,t.return),tr(3,t),ua(5,t,t.return));break;case 1:Ue(e,t),ke(t),r&512&&(te||i===null||pn(i,i.return)),r&64&&Yn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?r:i.concat(r))));break;case 26:var o=rn;if(Ue(e,t),ke(t),r&512&&(te||i===null||pn(i,i.return)),r&4){var c=i!==null?i.memoizedState:null;if(r=t.memoizedState,i===null)if(r===null)if(t.stateNode===null){t:{r=t.type,i=t.memoizedProps,o=o.ownerDocument||o;e:switch(r){case"title":c=o.getElementsByTagName("title")[0],(!c||c[Es]||c[ve]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(r),o.head.insertBefore(c,o.querySelector("head > title"))),ge(c,r,i),c[ve]=t,oe(c),r=c;break t;case"link":var y=Kg("link","href",o).get(r+(i.href||""));if(y){for(var x=0;x<y.length;x++)if(c=y[x],c.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&c.getAttribute("rel")===(i.rel==null?null:i.rel)&&c.getAttribute("title")===(i.title==null?null:i.title)&&c.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){y.splice(x,1);break e}}c=o.createElement(r),ge(c,r,i),o.head.appendChild(c);break;case"meta":if(y=Kg("meta","content",o).get(r+(i.content||""))){for(x=0;x<y.length;x++)if(c=y[x],c.getAttribute("content")===(i.content==null?null:""+i.content)&&c.getAttribute("name")===(i.name==null?null:i.name)&&c.getAttribute("property")===(i.property==null?null:i.property)&&c.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&c.getAttribute("charset")===(i.charSet==null?null:i.charSet)){y.splice(x,1);break e}}c=o.createElement(r),ge(c,r,i),o.head.appendChild(c);break;default:throw Error(l(468,r))}c[ve]=t,oe(c),r=c}t.stateNode=r}else Zg(o,t.type,t.stateNode);else t.stateNode=Xg(o,r,t.memoizedProps);else c!==r?(c===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):c.count--,r===null?Zg(o,t.type,t.stateNode):Xg(o,r,t.memoizedProps)):r===null&&t.stateNode!==null&&Tc(t,t.memoizedProps,i.memoizedProps)}break;case 27:Ue(e,t),ke(t),r&512&&(te||i===null||pn(i,i.return)),i!==null&&r&4&&Tc(t,t.memoizedProps,i.memoizedProps);break;case 5:if(Ue(e,t),ke(t),r&512&&(te||i===null||pn(i,i.return)),t.flags&32){o=t.stateNode;try{Si(o,"")}catch(G){Gt(t,t.return,G)}}r&4&&t.stateNode!=null&&(o=t.memoizedProps,Tc(t,o,i!==null?i.memoizedProps:o)),r&1024&&(Ec=!0);break;case 6:if(Ue(e,t),ke(t),r&4){if(t.stateNode===null)throw Error(l(162));r=t.memoizedProps,i=t.stateNode;try{i.nodeValue=r}catch(G){Gt(t,t.return,G)}}break;case 3:if(to=null,o=rn,rn=$l(e.containerInfo),Ue(e,t),rn=o,ke(t),r&4&&i!==null&&i.memoizedState.isDehydrated)try{vr(e.containerInfo)}catch(G){Gt(t,t.return,G)}Ec&&(Ec=!1,Jp(t));break;case 4:r=rn,rn=$l(t.stateNode.containerInfo),Ue(e,t),ke(t),rn=r;break;case 12:Ue(e,t),ke(t);break;case 13:Ue(e,t),ke(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(_c=he()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mc(t,r)));break;case 22:o=t.memoizedState!==null;var R=i!==null&&i.memoizedState!==null,k=Yn,Z=te;if(Yn=k||o,te=Z||R,Ue(e,t),te=Z,Yn=k,ke(t),r&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(i===null||R||Yn||te||Wa(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){R=i=e;try{if(c=R.stateNode,o)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=R.stateNode;var F=R.memoizedProps.style,H=F!=null&&F.hasOwnProperty("display")?F.display:null;x.style.display=H==null||typeof H=="boolean"?"":(""+H).trim()}}catch(G){Gt(R,R.return,G)}}}else if(e.tag===6){if(i===null){R=e;try{R.stateNode.nodeValue=o?"":R.memoizedProps}catch(G){Gt(R,R.return,G)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(i=r.retryQueue,i!==null&&(r.retryQueue=null,Mc(t,i))));break;case 19:Ue(e,t),ke(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mc(t,r)));break;case 30:break;case 21:break;default:Ue(e,t),ke(t)}}function ke(t){var e=t.flags;if(e&2){try{for(var i,r=t.return;r!==null;){if(Xp(r)){i=r;break}r=r.return}if(i==null)throw Error(l(160));switch(i.tag){case 27:var o=i.stateNode,c=wc(t);kl(t,c,o);break;case 5:var y=i.stateNode;i.flags&32&&(Si(y,""),i.flags&=-33);var x=wc(t);kl(t,x,y);break;case 3:case 4:var R=i.stateNode.containerInfo,k=wc(t);Ac(t,k,R);break;default:throw Error(l(161))}}catch(Z){Gt(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ca(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Qp(t,e.alternate,e),e=e.sibling}function Wa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ua(4,e,e.return),Wa(e);break;case 1:pn(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Yp(e,e.return,i),Wa(e);break;case 27:cr(e.stateNode);case 26:case 5:pn(e,e.return),Wa(e);break;case 22:e.memoizedState===null&&Wa(e);break;case 30:Wa(e);break;default:Wa(e)}t=t.sibling}}function fa(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,o=t,c=e,y=c.flags;switch(c.tag){case 0:case 11:case 15:fa(o,c,i),tr(4,c);break;case 1:if(fa(o,c,i),r=c,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(k){Gt(r,r.return,k)}if(r=c,o=r.updateQueue,o!==null){var x=r.stateNode;try{var R=o.shared.hiddenCallbacks;if(R!==null)for(o.shared.hiddenCallbacks=null,o=0;o<R.length;o++)Rm(R[o],x)}catch(k){Gt(r,r.return,k)}}i&&y&64&&Gp(c),er(c,c.return);break;case 27:Kp(c);case 26:case 5:fa(o,c,i),i&&r===null&&y&4&&qp(c),er(c,c.return);break;case 12:fa(o,c,i);break;case 13:fa(o,c,i),i&&y&4&&Wp(o,c);break;case 22:c.memoizedState===null&&fa(o,c,i),er(c,c.return);break;case 30:break;default:fa(o,c,i)}e=e.sibling}}function Cc(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&Hs(i))}function Rc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t))}function gn(t,e,i,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)tg(t,e,i,r),e=e.sibling}function tg(t,e,i,r){var o=e.flags;switch(e.tag){case 0:case 11:case 15:gn(t,e,i,r),o&2048&&tr(9,e);break;case 1:gn(t,e,i,r);break;case 3:gn(t,e,i,r),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t)));break;case 12:if(o&2048){gn(t,e,i,r),t=e.stateNode;try{var c=e.memoizedProps,y=c.id,x=c.onPostCommit;typeof x=="function"&&x(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(R){Gt(e,e.return,R)}}else gn(t,e,i,r);break;case 13:gn(t,e,i,r);break;case 23:break;case 22:c=e.stateNode,y=e.alternate,e.memoizedState!==null?c._visibility&2?gn(t,e,i,r):nr(t,e):c._visibility&2?gn(t,e,i,r):(c._visibility|=2,Hi(t,e,i,r,(e.subtreeFlags&10256)!==0)),o&2048&&Cc(y,e);break;case 24:gn(t,e,i,r),o&2048&&Rc(e.alternate,e);break;default:gn(t,e,i,r)}}function Hi(t,e,i,r,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,y=e,x=i,R=r,k=y.flags;switch(y.tag){case 0:case 11:case 15:Hi(c,y,x,R,o),tr(8,y);break;case 23:break;case 22:var Z=y.stateNode;y.memoizedState!==null?Z._visibility&2?Hi(c,y,x,R,o):nr(c,y):(Z._visibility|=2,Hi(c,y,x,R,o)),o&&k&2048&&Cc(y.alternate,y);break;case 24:Hi(c,y,x,R,o),o&&k&2048&&Rc(y.alternate,y);break;default:Hi(c,y,x,R,o)}e=e.sibling}}function nr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,r=e,o=r.flags;switch(r.tag){case 22:nr(i,r),o&2048&&Cc(r.alternate,r);break;case 24:nr(i,r),o&2048&&Rc(r.alternate,r);break;default:nr(i,r)}e=e.sibling}}var ar=8192;function Pi(t){if(t.subtreeFlags&ar)for(t=t.child;t!==null;)eg(t),t=t.sibling}function eg(t){switch(t.tag){case 26:Pi(t),t.flags&ar&&t.memoizedState!==null&&XT(rn,t.memoizedState,t.memoizedProps);break;case 5:Pi(t);break;case 3:case 4:var e=rn;rn=$l(t.stateNode.containerInfo),Pi(t),rn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ar,ar=16777216,Pi(t),ar=e):Pi(t));break;default:Pi(t)}}function ng(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ir(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];ce=r,ig(r,t)}ng(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ag(t),t=t.sibling}function ag(t){switch(t.tag){case 0:case 11:case 15:ir(t),t.flags&2048&&ua(9,t,t.return);break;case 3:ir(t);break;case 12:ir(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Hl(t)):ir(t);break;default:ir(t)}}function Hl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];ce=r,ig(r,t)}ng(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ua(8,e,e.return),Hl(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,Hl(e));break;default:Hl(e)}t=t.sibling}}function ig(t,e){for(;ce!==null;){var i=ce;switch(i.tag){case 0:case 11:case 15:ua(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var r=i.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Hs(i.memoizedState.cache)}if(r=i.child,r!==null)r.return=i,ce=r;else t:for(i=t;ce!==null;){r=ce;var o=r.sibling,c=r.return;if(Fp(r),r===i){ce=null;break t}if(o!==null){o.return=c,ce=o;break t}ce=c}}}var lT={getCacheForType:function(t){var e=be(se),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},oT=typeof WeakMap=="function"?WeakMap:Map,zt=0,Yt=null,Rt=null,Nt=0,Lt=0,He=null,da=!1,Gi=!1,Dc=!1,Xn=0,Wt=0,ha=0,$a=0,Oc=0,$e=0,Yi=0,sr=null,_e=null,Nc=!1,_c=0,Pl=1/0,Gl=null,ma=null,pe=0,pa=null,qi=null,Xi=0,jc=0,Vc=null,sg=null,rr=0,zc=null;function Pe(){if((zt&2)!==0&&Nt!==0)return Nt&-Nt;if(B.T!==null){var t=_i;return t!==0?t:Gc()}return Sh()}function rg(){$e===0&&($e=(Nt&536870912)===0||Vt?yh():536870912);var t=We.current;return t!==null&&(t.flags|=32),$e}function Ge(t,e,i){(t===Yt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ki(t,0),ga(t,Nt,$e,!1)),As(t,i),((zt&2)===0||t!==Yt)&&(t===Yt&&((zt&2)===0&&($a|=i),Wt===4&&ga(t,Nt,$e,!1)),yn(t))}function lg(t,e,i){if((zt&6)!==0)throw Error(l(327));var r=!i&&(e&124)===0&&(e&t.expiredLanes)===0||ws(t,e),o=r?fT(t,e):Uc(t,e,!0),c=r;do{if(o===0){Gi&&!r&&ga(t,e,0,!1);break}else{if(i=t.current.alternate,c&&!uT(i)){o=Uc(t,e,!1),c=!1;continue}if(o===2){if(c=e,t.errorRecoveryDisabledLanes&c)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var x=t;o=sr;var R=x.current.memoizedState.isDehydrated;if(R&&(Ki(x,y).flags|=256),y=Uc(x,y,!1),y!==2){if(Dc&&!R){x.errorRecoveryDisabledLanes|=c,$a|=c,o=4;break t}c=_e,_e=o,c!==null&&(_e===null?_e=c:_e.push.apply(_e,c))}o=y}if(c=!1,o!==2)continue}}if(o===1){Ki(t,0),ga(t,e,0,!0);break}t:{switch(r=t,c=o,c){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:ga(r,e,$e,!da);break t;case 2:_e=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(o=_c+300-he(),10<o)){if(ga(r,e,$e,!da),Jr(r,0,!0)!==0)break t;r.timeoutHandle=Lg(og.bind(null,r,i,_e,Gl,Nc,e,$e,$a,Yi,da,c,2,-0,0),o);break t}og(r,i,_e,Gl,Nc,e,$e,$a,Yi,da,c,0,-0,0)}}break}while(!0);yn(t)}function og(t,e,i,r,o,c,y,x,R,k,Z,F,H,G){if(t.timeoutHandle=-1,F=e.subtreeFlags,(F&8192||(F&16785408)===16785408)&&(hr={stylesheets:null,count:0,unsuspend:qT},eg(e),F=KT(),F!==null)){t.cancelPendingCommit=F(pg.bind(null,t,e,c,i,r,o,y,x,R,Z,1,H,G)),ga(t,c,y,!k);return}pg(t,e,c,i,r,o,y,x,R)}function uT(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var r=0;r<i.length;r++){var o=i[r],c=o.getSnapshot;o=o.value;try{if(!Le(c(),o))return!1}catch(y){return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ga(t,e,i,r){e&=~Oc,e&=~$a,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var o=e;0<o;){var c=31-ze(o),y=1<<c;r[c]=-1,o&=~y}i!==0&&bh(t,i,e)}function Yl(){return(zt&6)===0?(lr(0),!1):!0}function Lc(){if(Rt!==null){if(Lt===0)var t=Rt.return;else t=Rt,Bn=Ka=null,Ju(t),Ui=null,Ws=0,t=Rt;for(;t!==null;)Pp(t.alternate,t),t=t.return;Rt=null}}function Ki(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,CT(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Lc(),Yt=t,Rt=i=Vn(t.current,null),Nt=e,Lt=0,He=null,da=!1,Gi=ws(t,e),Dc=!1,Yi=$e=Oc=$a=ha=Wt=0,_e=sr=null,Nc=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var o=31-ze(r),c=1<<o;e|=t[o],r&=~c}return Xn=e,fl(),i}function ug(t,e){wt=null,B.H=Ol,e===Gs||e===xl?(e=Mm(),Lt=3):e===wm?(e=Mm(),Lt=4):Lt=e===Cp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,He=e,Rt===null&&(Wt=1,zl(t,Ze(e,t.current)))}function cg(){var t=B.H;return B.H=Ol,t===null?Ol:t}function fg(){var t=B.A;return B.A=lT,t}function Bc(){Wt=4,da||(Nt&4194048)!==Nt&&We.current!==null||(Gi=!0),(ha&134217727)===0&&($a&134217727)===0||Yt===null||ga(Yt,Nt,$e,!1)}function Uc(t,e,i){var r=zt;zt|=2;var o=cg(),c=fg();(Yt!==t||Nt!==e)&&(Gl=null,Ki(t,e)),e=!1;var y=Wt;t:do try{if(Lt!==0&&Rt!==null){var x=Rt,R=He;switch(Lt){case 8:Lc(),y=6;break t;case 3:case 2:case 9:case 6:We.current===null&&(e=!0);var k=Lt;if(Lt=0,He=null,Zi(t,x,R,k),i&&Gi){y=0;break t}break;default:k=Lt,Lt=0,He=null,Zi(t,x,R,k)}}cT(),y=Wt;break}catch(Z){ug(t,Z)}while(!0);return e&&t.shellSuspendCounter++,Bn=Ka=null,zt=r,B.H=o,B.A=c,Rt===null&&(Yt=null,Nt=0,fl()),y}function cT(){for(;Rt!==null;)dg(Rt)}function fT(t,e){var i=zt;zt|=2;var r=cg(),o=fg();Yt!==t||Nt!==e?(Gl=null,Pl=he()+500,Ki(t,e)):Gi=ws(t,e);t:do try{if(Lt!==0&&Rt!==null){e=Rt;var c=He;e:switch(Lt){case 1:Lt=0,He=null,Zi(t,e,c,1);break;case 2:case 9:if(Am(c)){Lt=0,He=null,hg(e);break}e=function(){Lt!==2&&Lt!==9||Yt!==t||(Lt=7),yn(t)},c.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:Am(c)?(Lt=0,He=null,hg(e)):(Lt=0,He=null,Zi(t,e,c,7));break;case 5:var y=null;switch(Rt.tag){case 26:y=Rt.memoizedState;case 5:case 27:var x=Rt;if(!y||Qg(y)){Lt=0,He=null;var R=x.sibling;if(R!==null)Rt=R;else{var k=x.return;k!==null?(Rt=k,ql(k)):Rt=null}break e}}Lt=0,He=null,Zi(t,e,c,5);break;case 6:Lt=0,He=null,Zi(t,e,c,6);break;case 8:Lc(),Wt=6;break t;default:throw Error(l(462))}}dT();break}catch(Z){ug(t,Z)}while(!0);return Bn=Ka=null,B.H=r,B.A=o,zt=i,Rt!==null?0:(Yt=null,Nt=0,fl(),Wt)}function dT(){for(;Rt!==null&&!Ae();)dg(Rt)}function dg(t){var e=kp(t.alternate,t,Xn);t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function hg(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=jp(i,e,e.pendingProps,e.type,void 0,Nt);break;case 11:e=jp(i,e,e.pendingProps,e.type.render,e.ref,Nt);break;case 5:Ju(e);default:Pp(i,e),e=Rt=mm(e,Xn),e=kp(i,e,Xn)}t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function Zi(t,e,i,r){Bn=Ka=null,Ju(e),Ui=null,Ws=0;var o=e.return;try{if(eT(t,o,e,i,Nt)){Wt=1,zl(t,Ze(i,t.current)),Rt=null;return}}catch(c){if(o!==null)throw Rt=o,c;Wt=1,zl(t,Ze(i,t.current)),Rt=null;return}e.flags&32768?(Vt||r===1?t=!0:Gi||(Nt&536870912)!==0?t=!1:(da=t=!0,(r===2||r===9||r===3||r===6)&&(r=We.current,r!==null&&r.tag===13&&(r.flags|=16384))),mg(e,t)):ql(e)}function ql(t){var e=t;do{if((e.flags&32768)!==0){mg(e,da);return}t=e.return;var i=aT(e.alternate,e,Xn);if(i!==null){Rt=i;return}if(e=e.sibling,e!==null){Rt=e;return}Rt=e=t}while(e!==null);Wt===0&&(Wt=5)}function mg(t,e){do{var i=iT(t.alternate,t);if(i!==null){i.flags&=32767,Rt=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){Rt=t;return}Rt=t=i}while(t!==null);Wt=6,Rt=null}function pg(t,e,i,r,o,c,y,x,R){t.cancelPendingCommit=null;do Xl();while(pe!==0);if((zt&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(c=e.lanes|e.childLanes,c|=Ru,qS(t,i,c,y,x,R),t===Yt&&(Rt=Yt=null,Nt=0),qi=e,pa=t,Xi=i,jc=c,Vc=o,sg=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,gT(di,function(){return xg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=B.T,B.T=null,o=q.p,q.p=2,y=zt,zt|=4;try{sT(t,e,i)}finally{zt=y,q.p=o,B.T=r}}pe=1,gg(),yg(),vg()}}function gg(){if(pe===1){pe=0;var t=pa,e=qi,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{$p(e,t);var c=Ic,y=im(t.containerInfo),x=c.focusedElem,R=c.selectionRange;if(y!==x&&x&&x.ownerDocument&&am(x.ownerDocument.documentElement,x)){if(R!==null&&wu(x)){var k=R.start,Z=R.end;if(Z===void 0&&(Z=k),"selectionStart"in x)x.selectionStart=k,x.selectionEnd=Math.min(Z,x.value.length);else{var F=x.ownerDocument||document,H=F&&F.defaultView||window;if(H.getSelection){var G=H.getSelection(),gt=x.textContent.length,mt=Math.min(R.start,gt),Ht=R.end===void 0?mt:Math.min(R.end,gt);!G.extend&&mt>Ht&&(y=Ht,Ht=mt,mt=y);var L=nm(x,mt),V=nm(x,Ht);if(L&&V&&(G.rangeCount!==1||G.anchorNode!==L.node||G.anchorOffset!==L.offset||G.focusNode!==V.node||G.focusOffset!==V.offset)){var U=F.createRange();U.setStart(L.node,L.offset),G.removeAllRanges(),mt>Ht?(G.addRange(U),G.extend(V.node,V.offset)):(U.setEnd(V.node,V.offset),G.addRange(U))}}}}for(F=[],G=x;G=G.parentNode;)G.nodeType===1&&F.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<F.length;x++){var Q=F[x];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}ao=!!Fc,Ic=Fc=null}finally{zt=o,q.p=r,B.T=i}}t.current=e,pe=2}}function yg(){if(pe===2){pe=0;var t=pa,e=qi,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{Qp(t,e.alternate,e)}finally{zt=o,q.p=r,B.T=i}}pe=3}}function vg(){if(pe===4||pe===3){pe=0,an();var t=pa,e=qi,i=Xi,r=sg;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?pe=5:(pe=0,qi=pa=null,bg(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(ma=null),nu(i),e=e.stateNode,Ve&&typeof Ve.onCommitFiberRoot=="function")try{Ve.onCommitFiberRoot(Ts,e,void 0,(e.current.flags&128)===128)}catch(R){}if(r!==null){e=B.T,o=q.p,q.p=2,B.T=null;try{for(var c=t.onRecoverableError,y=0;y<r.length;y++){var x=r[y];c(x.value,{componentStack:x.stack})}}finally{B.T=e,q.p=o}}(Xi&3)!==0&&Xl(),yn(t),o=t.pendingLanes,(i&4194090)!==0&&(o&42)!==0?t===zc?rr++:(rr=0,zc=t):rr=0,lr(0)}}function bg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Hs(e)))}function Xl(t){return gg(),yg(),vg(),xg()}function xg(){if(pe!==5)return!1;var t=pa,e=jc;jc=0;var i=nu(Xi),r=B.T,o=q.p;try{q.p=32>i?32:i,B.T=null,i=Vc,Vc=null;var c=pa,y=Xi;if(pe=0,qi=pa=null,Xi=0,(zt&6)!==0)throw Error(l(331));var x=zt;if(zt|=4,ag(c.current),tg(c,c.current,y,i),zt=x,lr(0,!1),Ve&&typeof Ve.onPostCommitFiberRoot=="function")try{Ve.onPostCommitFiberRoot(Ts,c)}catch(R){}return!0}finally{q.p=o,B.T=r,bg(t,e)}}function Sg(t,e,i){e=Ze(i,e),e=hc(t.stateNode,e,2),t=sa(t,e,2),t!==null&&(As(t,2),yn(t))}function Gt(t,e,i){if(t.tag===3)Sg(t,t,i);else for(;e!==null;){if(e.tag===3){Sg(e,t,i);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ma===null||!ma.has(r))){t=Ze(i,t),i=Ep(2),r=sa(e,i,2),r!==null&&(Mp(i,r,e,t),As(r,2),yn(r));break}}e=e.return}}function kc(t,e,i){var r=t.pingCache;if(r===null){r=t.pingCache=new oT;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(i)||(Dc=!0,o.add(i),t=hT.bind(null,t,e,i),e.then(t,t))}function hT(t,e,i){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,Yt===t&&(Nt&i)===i&&(Wt===4||Wt===3&&(Nt&62914560)===Nt&&300>he()-_c?(zt&2)===0&&Ki(t,0):Oc|=i,Yi===Nt&&(Yi=0)),yn(t)}function Tg(t,e){e===0&&(e=vh()),t=Ri(t,e),t!==null&&(As(t,e),yn(t))}function mT(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),Tg(t,i)}function pT(t,e){var i=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(i=o.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(l(314))}r!==null&&r.delete(e),Tg(t,i)}function gT(t,e){return _t(t,e)}var Kl=null,Qi=null,Hc=!1,Zl=!1,Pc=!1,Ja=0;function yn(t){t!==Qi&&t.next===null&&(Qi===null?Kl=Qi=t:Qi=Qi.next=t),Zl=!0,Hc||(Hc=!0,vT())}function lr(t,e){if(!Pc&&Zl){Pc=!0;do for(var i=!1,r=Kl;r!==null;){if(t!==0){var o=r.pendingLanes;if(o===0)var c=0;else{var y=r.suspendedLanes,x=r.pingedLanes;c=(1<<31-ze(42|t)+1)-1,c&=o&~(y&~x),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(i=!0,Mg(r,c))}else c=Nt,c=Jr(r,r===Yt?c:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(c&3)===0||ws(r,c)||(i=!0,Mg(r,c));r=r.next}while(i);Pc=!1}}function yT(){wg()}function wg(){Zl=Hc=!1;var t=0;Ja!==0&&(MT()&&(t=Ja),Ja=0);for(var e=he(),i=null,r=Kl;r!==null;){var o=r.next,c=Ag(r,e);c===0?(r.next=null,i===null?Kl=o:i.next=o,o===null&&(Qi=i)):(i=r,(t!==0||(c&3)!==0)&&(Zl=!0)),r=o}lr(t)}function Ag(t,e){for(var i=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var y=31-ze(c),x=1<<y,R=o[y];R===-1?((x&i)===0||(x&r)!==0)&&(o[y]=YS(x,e)):R<=e&&(t.expiredLanes|=x),c&=~x}if(e=Yt,i=Nt,i=Jr(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,i===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&de(r),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||ws(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(r!==null&&de(r),nu(i)){case 2:case 8:i=Ir;break;case 32:i=di;break;case 268435456:i=gh;break;default:i=di}return r=Eg.bind(null,t),i=_t(i,r),t.callbackPriority=e,t.callbackNode=i,e}return r!==null&&r!==null&&de(r),t.callbackPriority=2,t.callbackNode=null,2}function Eg(t,e){if(pe!==0&&pe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Xl()&&t.callbackNode!==i)return null;var r=Nt;return r=Jr(t,t===Yt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(lg(t,r,e),Ag(t,he()),t.callbackNode!=null&&t.callbackNode===i?Eg.bind(null,t):null)}function Mg(t,e){if(Xl())return null;lg(t,e,!0)}function vT(){RT(function(){(zt&6)!==0?_t(Fr,yT):wg()})}function Gc(){return Ja===0&&(Ja=yh()),Ja}function Cg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:il(""+t)}function Rg(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function bT(t,e,i,r,o){if(e==="submit"&&i&&i.stateNode===o){var c=Cg((o[Re]||null).action),y=r.submitter;y&&(e=(e=y[Re]||null)?Cg(e.formAction):y.getAttribute("formAction"),e!==null&&(c=e,y=null));var x=new ol("action","action",null,r,o);t.push({event:x,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(Ja!==0){var R=y?Rg(o,y):new FormData(o);oc(i,{pending:!0,data:R,method:o.method,action:c},null,R)}}else typeof c=="function"&&(x.preventDefault(),R=y?Rg(o,y):new FormData(o),oc(i,{pending:!0,data:R,method:o.method,action:c},c,R))},currentTarget:o}]})}}for(var Yc=0;Yc<Cu.length;Yc++){var qc=Cu[Yc],xT=qc.toLowerCase(),ST=qc[0].toUpperCase()+qc.slice(1);sn(xT,"on"+ST)}sn(lm,"onAnimationEnd"),sn(om,"onAnimationIteration"),sn(um,"onAnimationStart"),sn("dblclick","onDoubleClick"),sn("focusin","onFocus"),sn("focusout","onBlur"),sn(U1,"onTransitionRun"),sn(k1,"onTransitionStart"),sn(H1,"onTransitionCancel"),sn(cm,"onTransitionEnd"),vi("onMouseEnter",["mouseout","mouseover"]),vi("onMouseLeave",["mouseout","mouseover"]),vi("onPointerEnter",["pointerout","pointerover"]),vi("onPointerLeave",["pointerout","pointerover"]),Ba("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ba("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ba("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ba("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ba("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ba("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),TT=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(or));function Dg(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var r=t[i],o=r.event;r=r.listeners;t:{var c=void 0;if(e)for(var y=r.length-1;0<=y;y--){var x=r[y],R=x.instance,k=x.currentTarget;if(x=x.listener,R!==c&&o.isPropagationStopped())break t;c=x,o.currentTarget=k;try{c(o)}catch(Z){Vl(Z)}o.currentTarget=null,c=R}else for(y=0;y<r.length;y++){if(x=r[y],R=x.instance,k=x.currentTarget,x=x.listener,R!==c&&o.isPropagationStopped())break t;c=x,o.currentTarget=k;try{c(o)}catch(Z){Vl(Z)}o.currentTarget=null,c=R}}}}function Dt(t,e){var i=e[au];i===void 0&&(i=e[au]=new Set);var r=t+"__bubble";i.has(r)||(Og(e,t,2,!1),i.add(r))}function Xc(t,e,i){var r=0;e&&(r|=4),Og(i,t,r,e)}var Ql="_reactListening"+Math.random().toString(36).slice(2);function Kc(t){if(!t[Ql]){t[Ql]=!0,wh.forEach(function(i){i!=="selectionchange"&&(TT.has(i)||Xc(i,!1,t),Xc(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ql]||(e[Ql]=!0,Xc("selectionchange",!1,e))}}function Og(t,e,i,r){switch(ty(e)){case 2:var o=FT;break;case 8:o=IT;break;default:o=lf}i=o.bind(null,e,i,t),o=void 0,!mu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,i,{capture:!0,passive:o}):t.addEventListener(e,i,!0):o!==void 0?t.addEventListener(e,i,{passive:o}):t.addEventListener(e,i,!1)}function Zc(t,e,i,r,o){var c=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var x=r.stateNode.containerInfo;if(x===o)break;if(y===4)for(y=r.return;y!==null;){var R=y.tag;if((R===3||R===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;x!==null;){if(y=pi(x),y===null)return;if(R=y.tag,R===5||R===6||R===26||R===27){r=c=y;continue t}x=x.parentNode}}r=r.return}Bh(function(){var k=c,Z=du(i),F=[];t:{var H=fm.get(t);if(H!==void 0){var G=ol,gt=t;switch(t){case"keypress":if(rl(i)===0)break t;case"keydown":case"keyup":G=g1;break;case"focusin":gt="focus",G=vu;break;case"focusout":gt="blur",G=vu;break;case"beforeblur":case"afterblur":G=vu;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Hh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=i1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=b1;break;case lm:case om:case um:G=l1;break;case cm:G=S1;break;case"scroll":case"scrollend":G=n1;break;case"wheel":G=w1;break;case"copy":case"cut":case"paste":G=u1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=Gh;break;case"toggle":case"beforetoggle":G=E1}var mt=(e&4)!==0,Ht=!mt&&(t==="scroll"||t==="scrollend"),L=mt?H!==null?H+"Capture":null:H;mt=[];for(var V=k,U;V!==null;){var Q=V;if(U=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||U===null||L===null||(Q=Cs(V,L),Q!=null&&mt.push(ur(V,Q,U))),Ht)break;V=V.return}0<mt.length&&(H=new G(H,gt,null,i,Z),F.push({event:H,listeners:mt}))}}if((e&7)===0){t:{if(H=t==="mouseover"||t==="pointerover",G=t==="mouseout"||t==="pointerout",H&&i!==fu&&(gt=i.relatedTarget||i.fromElement)&&(pi(gt)||gt[mi]))break t;if((G||H)&&(H=Z.window===Z?Z:(H=Z.ownerDocument)?H.defaultView||H.parentWindow:window,G?(gt=i.relatedTarget||i.toElement,G=k,gt=gt?pi(gt):null,gt!==null&&(Ht=d(gt),mt=gt.tag,gt!==Ht||mt!==5&&mt!==27&&mt!==6)&&(gt=null)):(G=null,gt=k),G!==gt)){if(mt=Hh,Q="onMouseLeave",L="onMouseEnter",V="mouse",(t==="pointerout"||t==="pointerover")&&(mt=Gh,Q="onPointerLeave",L="onPointerEnter",V="pointer"),Ht=G==null?H:Ms(G),U=gt==null?H:Ms(gt),H=new mt(Q,V+"leave",G,i,Z),H.target=Ht,H.relatedTarget=U,Q=null,pi(Z)===k&&(mt=new mt(L,V+"enter",gt,i,Z),mt.target=U,mt.relatedTarget=Ht,Q=mt),Ht=Q,G&&gt)e:{for(mt=G,L=gt,V=0,U=mt;U;U=Fi(U))V++;for(U=0,Q=L;Q;Q=Fi(Q))U++;for(;0<V-U;)mt=Fi(mt),V--;for(;0<U-V;)L=Fi(L),U--;for(;V--;){if(mt===L||L!==null&&mt===L.alternate)break e;mt=Fi(mt),L=Fi(L)}mt=null}else mt=null;G!==null&&Ng(F,H,G,mt,!1),gt!==null&&Ht!==null&&Ng(F,Ht,gt,mt,!0)}}t:{if(H=k?Ms(k):window,G=H.nodeName&&H.nodeName.toLowerCase(),G==="select"||G==="input"&&H.type==="file")var it=Ih;else if(Qh(H))if(Wh)it=z1;else{it=j1;var Mt=_1}else G=H.nodeName,!G||G.toLowerCase()!=="input"||H.type!=="checkbox"&&H.type!=="radio"?k&&cu(k.elementType)&&(it=Ih):it=V1;if(it&&(it=it(t,k))){Fh(F,it,i,Z);break t}Mt&&Mt(t,H,k),t==="focusout"&&k&&H.type==="number"&&k.memoizedProps.value!=null&&uu(H,"number",H.value)}switch(Mt=k?Ms(k):window,t){case"focusin":(Qh(Mt)||Mt.contentEditable==="true")&&(Ei=Mt,Au=k,zs=null);break;case"focusout":zs=Au=Ei=null;break;case"mousedown":Eu=!0;break;case"contextmenu":case"mouseup":case"dragend":Eu=!1,sm(F,i,Z);break;case"selectionchange":if(B1)break;case"keydown":case"keyup":sm(F,i,Z)}var ot;if(xu)t:{switch(t){case"compositionstart":var pt="onCompositionStart";break t;case"compositionend":pt="onCompositionEnd";break t;case"compositionupdate":pt="onCompositionUpdate";break t}pt=void 0}else Ai?Kh(t,i)&&(pt="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(pt="onCompositionStart");pt&&(Yh&&i.locale!=="ko"&&(Ai||pt!=="onCompositionStart"?pt==="onCompositionEnd"&&Ai&&(ot=Uh()):(ea=Z,pu="value"in ea?ea.value:ea.textContent,Ai=!0)),Mt=Fl(k,pt),0<Mt.length&&(pt=new Ph(pt,t,null,i,Z),F.push({event:pt,listeners:Mt}),ot?pt.data=ot:(ot=Zh(i),ot!==null&&(pt.data=ot)))),(ot=C1?R1(t,i):D1(t,i))&&(pt=Fl(k,"onBeforeInput"),0<pt.length&&(Mt=new Ph("onBeforeInput","beforeinput",null,i,Z),F.push({event:Mt,listeners:pt}),Mt.data=ot)),bT(F,t,k,i,Z)}Dg(F,e)})}function ur(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Fl(t,e){for(var i=e+"Capture",r=[];t!==null;){var o=t,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Cs(t,i),o!=null&&r.unshift(ur(t,o,c)),o=Cs(t,e),o!=null&&r.push(ur(t,o,c))),t.tag===3)return r;t=t.return}return[]}function Fi(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Ng(t,e,i,r,o){for(var c=e._reactName,y=[];i!==null&&i!==r;){var x=i,R=x.alternate,k=x.stateNode;if(x=x.tag,R!==null&&R===r)break;x!==5&&x!==26&&x!==27||k===null||(R=k,o?(k=Cs(i,c),k!=null&&y.unshift(ur(i,k,R))):o||(k=Cs(i,c),k!=null&&y.push(ur(i,k,R)))),i=i.return}y.length!==0&&t.push({event:e,listeners:y})}var wT=/\r\n?/g,AT=/\u0000|\uFFFD/g;function _g(t){return(typeof t=="string"?t:""+t).replace(wT,`
`).replace(AT,"")}function jg(t,e){return e=_g(e),_g(t)===e}function Il(){}function kt(t,e,i,r,o,c){switch(i){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||Si(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&Si(t,""+r);break;case"className":el(t,"class",r);break;case"tabIndex":el(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":el(t,i,r);break;case"style":zh(t,r,c);break;case"data":if(e!=="object"){el(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=il(""+r),t.setAttribute(i,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(i==="formAction"?(e!=="input"&&kt(t,e,"name",o.name,o,null),kt(t,e,"formEncType",o.formEncType,o,null),kt(t,e,"formMethod",o.formMethod,o,null),kt(t,e,"formTarget",o.formTarget,o,null)):(kt(t,e,"encType",o.encType,o,null),kt(t,e,"method",o.method,o,null),kt(t,e,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=il(""+r),t.setAttribute(i,r);break;case"onClick":r!=null&&(t.onclick=Il);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}i=il(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""+r):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":r===!0?t.setAttribute(i,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,r):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(i,r):t.removeAttribute(i);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(i):t.setAttribute(i,r);break;case"popover":Dt("beforetoggle",t),Dt("toggle",t),tl(t,"popover",r);break;case"xlinkActuate":_n(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":_n(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":_n(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":_n(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":_n(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":_n(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":_n(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":_n(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":_n(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tl(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=t1.get(i)||i,tl(t,i,r))}}function Qc(t,e,i,r,o,c){switch(i){case"style":zh(t,r,c);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"children":typeof r=="string"?Si(t,r):(typeof r=="number"||typeof r=="bigint")&&Si(t,""+r);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Il);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ah.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(o=i.endsWith("Capture"),e=i.slice(2,o?i.length-7:void 0),c=t[Re]||null,c=c!=null?c[i]:null,typeof c=="function"&&t.removeEventListener(e,c,o),typeof r=="function")){typeof c!="function"&&c!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,r,o);break t}i in t?t[i]=r:r===!0?t.setAttribute(i,""):tl(t,i,r)}}}function ge(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dt("error",t),Dt("load",t);var r=!1,o=!1,c;for(c in i)if(i.hasOwnProperty(c)){var y=i[c];if(y!=null)switch(c){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,c,y,i,null)}}o&&kt(t,e,"srcSet",i.srcSet,i,null),r&&kt(t,e,"src",i.src,i,null);return;case"input":Dt("invalid",t);var x=c=y=o=null,R=null,k=null;for(r in i)if(i.hasOwnProperty(r)){var Z=i[r];if(Z!=null)switch(r){case"name":o=Z;break;case"type":y=Z;break;case"checked":R=Z;break;case"defaultChecked":k=Z;break;case"value":c=Z;break;case"defaultValue":x=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(l(137,e));break;default:kt(t,e,r,Z,i,null)}}Nh(t,c,x,R,k,y,o,!1),nl(t);return;case"select":Dt("invalid",t),r=y=c=null;for(o in i)if(i.hasOwnProperty(o)&&(x=i[o],x!=null))switch(o){case"value":c=x;break;case"defaultValue":y=x;break;case"multiple":r=x;default:kt(t,e,o,x,i,null)}e=c,i=y,t.multiple=!!r,e!=null?xi(t,!!r,e,!1):i!=null&&xi(t,!!r,i,!0);return;case"textarea":Dt("invalid",t),c=o=r=null;for(y in i)if(i.hasOwnProperty(y)&&(x=i[y],x!=null))switch(y){case"value":r=x;break;case"defaultValue":o=x;break;case"children":c=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(l(91));break;default:kt(t,e,y,x,i,null)}jh(t,r,o,c),nl(t);return;case"option":for(R in i)if(i.hasOwnProperty(R)&&(r=i[R],r!=null))switch(R){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:kt(t,e,R,r,i,null)}return;case"dialog":Dt("beforetoggle",t),Dt("toggle",t),Dt("cancel",t),Dt("close",t);break;case"iframe":case"object":Dt("load",t);break;case"video":case"audio":for(r=0;r<or.length;r++)Dt(or[r],t);break;case"image":Dt("error",t),Dt("load",t);break;case"details":Dt("toggle",t);break;case"embed":case"source":case"link":Dt("error",t),Dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in i)if(i.hasOwnProperty(k)&&(r=i[k],r!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,k,r,i,null)}return;default:if(cu(e)){for(Z in i)i.hasOwnProperty(Z)&&(r=i[Z],r!==void 0&&Qc(t,e,Z,r,i,void 0));return}}for(x in i)i.hasOwnProperty(x)&&(r=i[x],r!=null&&kt(t,e,x,r,i,null))}function ET(t,e,i,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,y=null,x=null,R=null,k=null,Z=null;for(G in i){var F=i[G];if(i.hasOwnProperty(G)&&F!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":R=F;default:r.hasOwnProperty(G)||kt(t,e,G,null,r,F)}}for(var H in r){var G=r[H];if(F=i[H],r.hasOwnProperty(H)&&(G!=null||F!=null))switch(H){case"type":c=G;break;case"name":o=G;break;case"checked":k=G;break;case"defaultChecked":Z=G;break;case"value":y=G;break;case"defaultValue":x=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(l(137,e));break;default:G!==F&&kt(t,e,H,G,r,F)}}ou(t,y,x,R,k,Z,c,o);return;case"select":G=y=x=H=null;for(c in i)if(R=i[c],i.hasOwnProperty(c)&&R!=null)switch(c){case"value":break;case"multiple":G=R;default:r.hasOwnProperty(c)||kt(t,e,c,null,r,R)}for(o in r)if(c=r[o],R=i[o],r.hasOwnProperty(o)&&(c!=null||R!=null))switch(o){case"value":H=c;break;case"defaultValue":x=c;break;case"multiple":y=c;default:c!==R&&kt(t,e,o,c,r,R)}e=x,i=y,r=G,H!=null?xi(t,!!i,H,!1):!!r!=!!i&&(e!=null?xi(t,!!i,e,!0):xi(t,!!i,i?[]:"",!1));return;case"textarea":G=H=null;for(x in i)if(o=i[x],i.hasOwnProperty(x)&&o!=null&&!r.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:kt(t,e,x,null,r,o)}for(y in r)if(o=r[y],c=i[y],r.hasOwnProperty(y)&&(o!=null||c!=null))switch(y){case"value":H=o;break;case"defaultValue":G=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(l(91));break;default:o!==c&&kt(t,e,y,o,r,c)}_h(t,H,G);return;case"option":for(var gt in i)if(H=i[gt],i.hasOwnProperty(gt)&&H!=null&&!r.hasOwnProperty(gt))switch(gt){case"selected":t.selected=!1;break;default:kt(t,e,gt,null,r,H)}for(R in r)if(H=r[R],G=i[R],r.hasOwnProperty(R)&&H!==G&&(H!=null||G!=null))switch(R){case"selected":t.selected=H&&typeof H!="function"&&typeof H!="symbol";break;default:kt(t,e,R,H,r,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var mt in i)H=i[mt],i.hasOwnProperty(mt)&&H!=null&&!r.hasOwnProperty(mt)&&kt(t,e,mt,null,r,H);for(k in r)if(H=r[k],G=i[k],r.hasOwnProperty(k)&&H!==G&&(H!=null||G!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(l(137,e));break;default:kt(t,e,k,H,r,G)}return;default:if(cu(e)){for(var Ht in i)H=i[Ht],i.hasOwnProperty(Ht)&&H!==void 0&&!r.hasOwnProperty(Ht)&&Qc(t,e,Ht,void 0,r,H);for(Z in r)H=r[Z],G=i[Z],!r.hasOwnProperty(Z)||H===G||H===void 0&&G===void 0||Qc(t,e,Z,H,r,G);return}}for(var L in i)H=i[L],i.hasOwnProperty(L)&&H!=null&&!r.hasOwnProperty(L)&&kt(t,e,L,null,r,H);for(F in r)H=r[F],G=i[F],!r.hasOwnProperty(F)||H===G||H==null&&G==null||kt(t,e,F,H,r,G)}var Fc=null,Ic=null;function Wl(t){return t.nodeType===9?t:t.ownerDocument}function Vg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function zg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Wc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var $c=null;function MT(){var t=window.event;return t&&t.type==="popstate"?t===$c?!1:($c=t,!0):($c=null,!1)}var Lg=typeof setTimeout=="function"?setTimeout:void 0,CT=typeof clearTimeout=="function"?clearTimeout:void 0,Bg=typeof Promise=="function"?Promise:void 0,RT=typeof queueMicrotask=="function"?queueMicrotask:typeof Bg!="undefined"?function(t){return Bg.resolve(null).then(t).catch(DT)}:Lg;function DT(t){setTimeout(function(){throw t})}function ya(t){return t==="head"}function Ug(t,e){var i=e,r=0,o=0;do{var c=i.nextSibling;if(t.removeChild(i),c&&c.nodeType===8)if(i=c.data,i==="/$"){if(0<r&&8>r){i=r;var y=t.ownerDocument;if(i&1&&cr(y.documentElement),i&2&&cr(y.body),i&4)for(i=y.head,cr(i),y=i.firstChild;y;){var x=y.nextSibling,R=y.nodeName;y[Es]||R==="SCRIPT"||R==="STYLE"||R==="LINK"&&y.rel.toLowerCase()==="stylesheet"||i.removeChild(y),y=x}}if(o===0){t.removeChild(c),vr(e);return}o--}else i==="$"||i==="$?"||i==="$!"?o++:r=i.charCodeAt(0)-48;else r=0;i=c}while(i);vr(e)}function Jc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Jc(i),iu(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function OT(t,e,i,r){for(;t.nodeType===1;){var o=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[Es])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=ln(t.nextSibling),t===null)break}return null}function NT(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=ln(t.nextSibling),t===null))return null;return t}function tf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function _T(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var r=function(){e(),i.removeEventListener("DOMContentLoaded",r)};i.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function ln(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ef=null;function kg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function Hg(t,e,i){switch(e=Wl(i),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function cr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);iu(t)}var Je=new Map,Pg=new Set;function $l(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Kn=q.d;q.d={f:jT,r:VT,D:zT,C:LT,L:BT,m:UT,X:HT,S:kT,M:PT};function jT(){var t=Kn.f(),e=Yl();return t||e}function VT(t){var e=gi(t);e!==null&&e.tag===5&&e.type==="form"?lp(e):Kn.r(t)}var Ii=typeof document=="undefined"?null:document;function Gg(t,e,i){var r=Ii;if(r&&typeof e=="string"&&e){var o=Ke(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof i=="string"&&(o+='[crossorigin="'+i+'"]'),Pg.has(o)||(Pg.add(o),t={rel:t,crossOrigin:i,href:e},r.querySelector(o)===null&&(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function zT(t){Kn.D(t),Gg("dns-prefetch",t,null)}function LT(t,e){Kn.C(t,e),Gg("preconnect",t,e)}function BT(t,e,i){Kn.L(t,e,i);var r=Ii;if(r&&t&&e){var o='link[rel="preload"][as="'+Ke(e)+'"]';e==="image"&&i&&i.imageSrcSet?(o+='[imagesrcset="'+Ke(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(o+='[imagesizes="'+Ke(i.imageSizes)+'"]')):o+='[href="'+Ke(t)+'"]';var c=o;switch(e){case"style":c=Wi(t);break;case"script":c=$i(t)}Je.has(c)||(t=g({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),Je.set(c,t),r.querySelector(o)!==null||e==="style"&&r.querySelector(fr(c))||e==="script"&&r.querySelector(dr(c))||(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function UT(t,e){Kn.m(t,e);var i=Ii;if(i&&t){var r=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ke(r)+'"][href="'+Ke(t)+'"]',c=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=$i(t)}if(!Je.has(c)&&(t=g({rel:"modulepreload",href:t},e),Je.set(c,t),i.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(dr(c)))return}r=i.createElement("link"),ge(r,"link",t),oe(r),i.head.appendChild(r)}}}function kT(t,e,i){Kn.S(t,e,i);var r=Ii;if(r&&t){var o=yi(r).hoistableStyles,c=Wi(t);e=e||"default";var y=o.get(c);if(!y){var x={loading:0,preload:null};if(y=r.querySelector(fr(c)))x.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},i),(i=Je.get(c))&&nf(t,i);var R=y=r.createElement("link");oe(R),ge(R,"link",t),R._p=new Promise(function(k,Z){R.onload=k,R.onerror=Z}),R.addEventListener("load",function(){x.loading|=1}),R.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Jl(y,e,r)}y={type:"stylesheet",instance:y,count:1,state:x},o.set(c,y)}}}function HT(t,e){Kn.X(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=$i(t),c=r.get(o);c||(c=i.querySelector(dr(o)),c||(t=g({src:t,async:!0},e),(e=Je.get(o))&&af(t,e),c=i.createElement("script"),oe(c),ge(c,"link",t),i.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function PT(t,e){Kn.M(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=$i(t),c=r.get(o);c||(c=i.querySelector(dr(o)),c||(t=g({src:t,async:!0,type:"module"},e),(e=Je.get(o))&&af(t,e),c=i.createElement("script"),oe(c),ge(c,"link",t),i.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Yg(t,e,i,r){var o=(o=dt.current)?$l(o):null;if(!o)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=Wi(i.href),i=yi(o).hoistableStyles,r=i.get(e),r||(r={type:"style",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=Wi(i.href);var c=yi(o).hoistableStyles,y=c.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,y),(c=o.querySelector(fr(t)))&&!c._p&&(y.instance=c,y.state.loading=5),Je.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Je.set(t,i),c||GT(o,t,i,y.state))),e&&r===null)throw Error(l(528,""));return y}if(e&&r!==null)throw Error(l(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$i(i),i=yi(o).hoistableScripts,r=i.get(e),r||(r={type:"script",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function Wi(t){return'href="'+Ke(t)+'"'}function fr(t){return'link[rel="stylesheet"]['+t+"]"}function qg(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function GT(t,e,i,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),ge(e,"link",i),oe(e),t.head.appendChild(e))}function $i(t){return'[src="'+Ke(t)+'"]'}function dr(t){return"script[async]"+t}function Xg(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Ke(i.href)+'"]');if(r)return e.instance=r,oe(r),r;var o=g({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),oe(r),ge(r,"style",o),Jl(r,i.precedence,t),e.instance=r;case"stylesheet":o=Wi(i.href);var c=t.querySelector(fr(o));if(c)return e.state.loading|=4,e.instance=c,oe(c),c;r=qg(i),(o=Je.get(o))&&nf(r,o),c=(t.ownerDocument||t).createElement("link"),oe(c);var y=c;return y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(c,"link",r),e.state.loading|=4,Jl(c,i.precedence,t),e.instance=c;case"script":return c=$i(i.src),(o=t.querySelector(dr(c)))?(e.instance=o,oe(o),o):(r=i,(o=Je.get(c))&&(r=g({},i),af(r,o)),t=t.ownerDocument||t,o=t.createElement("script"),oe(o),ge(o,"link",r),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,Jl(r,i.precedence,t));return e.instance}function Jl(t,e,i){for(var r=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,c=o,y=0;y<r.length;y++){var x=r[y];if(x.dataset.precedence===e)c=x;else if(c!==o)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function nf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function af(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var to=null;function Kg(t,e,i){if(to===null){var r=new Map,o=to=new Map;o.set(i,r)}else o=to,r=o.get(i),r||(r=new Map,o.set(i,r));if(r.has(t))return r;for(r.set(t,null),i=i.getElementsByTagName(t),o=0;o<i.length;o++){var c=i[o];if(!(c[Es]||c[ve]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(e)||"";y=t+y;var x=r.get(y);x?x.push(c):r.set(y,[c])}}return r}function Zg(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function YT(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Qg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var hr=null;function qT(){}function XT(t,e,i){if(hr===null)throw Error(l(475));var r=hr;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Wi(i.href),c=t.querySelector(fr(o));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=eo.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=c,oe(c);return}c=t.ownerDocument||t,i=qg(i),(o=Je.get(o))&&nf(i,o),c=c.createElement("link"),oe(c);var y=c;y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(c,"link",i),e.instance=c}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=eo.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function KT(){if(hr===null)throw Error(l(475));var t=hr;return t.stylesheets&&t.count===0&&sf(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&sf(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function eo(){if(this.count--,this.count===0){if(this.stylesheets)sf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var no=null;function sf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,no=new Map,e.forEach(ZT,t),no=null,eo.call(t))}function ZT(t,e){if(!(e.state.loading&4)){var i=no.get(t);if(i)var r=i.get(null);else{i=new Map,no.set(t,i);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var y=o[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(i.set(y.dataset.precedence,y),r=y)}r&&i.set(null,r)}o=e.instance,y=o.getAttribute("data-precedence"),c=i.get(y)||r,c===r&&i.set(null,o),i.set(y,o),this.count++,r=eo.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),c?c.parentNode.insertBefore(o,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var mr={$$typeof:O,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function QT(t,e,i,r,o,c,y,x){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=tu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tu(0),this.hiddenUpdates=tu(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function Fg(t,e,i,r,o,c,y,x,R,k,Z,F){return t=new QT(t,e,i,y,x,R,k,F),e=1,c===!0&&(e|=24),c=Be(3,null,null,e),t.current=c,c.stateNode=t,e=ku(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:r,isDehydrated:i,cache:e},Yu(c),t}function Ig(t){return t?(t=Di,t):Di}function Wg(t,e,i,r,o,c){o=Ig(o),r.context===null?r.context=o:r.pendingContext=o,r=ia(e),r.payload={element:i},c=c===void 0?null:c,c!==null&&(r.callback=c),i=sa(t,r,e),i!==null&&(Ge(i,t,e),qs(i,t,e))}function $g(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function rf(t,e){$g(t,e),(t=t.alternate)&&$g(t,e)}function Jg(t){if(t.tag===13){var e=Ri(t,67108864);e!==null&&Ge(e,t,67108864),rf(t,67108864)}}var ao=!0;function FT(t,e,i,r){var o=B.T;B.T=null;var c=q.p;try{q.p=2,lf(t,e,i,r)}finally{q.p=c,B.T=o}}function IT(t,e,i,r){var o=B.T;B.T=null;var c=q.p;try{q.p=8,lf(t,e,i,r)}finally{q.p=c,B.T=o}}function lf(t,e,i,r){if(ao){var o=of(r);if(o===null)Zc(t,e,r,io,i),ey(t,r);else if($T(o,t,e,i,r))r.stopPropagation();else if(ey(t,r),e&4&&-1<WT.indexOf(t)){for(;o!==null;){var c=gi(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=La(c.pendingLanes);if(y!==0){var x=c;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var R=1<<31-ze(y);x.entanglements[1]|=R,y&=~R}yn(c),(zt&6)===0&&(Pl=he()+500,lr(0))}}break;case 13:x=Ri(c,2),x!==null&&Ge(x,c,2),Yl(),rf(c,2)}if(c=of(r),c===null&&Zc(t,e,r,io,i),c===o)break;o=c}o!==null&&r.stopPropagation()}else Zc(t,e,r,null,i)}}function of(t){return t=du(t),uf(t)}var io=null;function uf(t){if(io=null,t=pi(t),t!==null){var e=d(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=f(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return io=t,null}function ty(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(za()){case Fr:return 2;case Ir:return 8;case di:case hi:return 32;case gh:return 268435456;default:return 32}default:return 32}}var cf=!1,va=null,ba=null,xa=null,pr=new Map,gr=new Map,Sa=[],WT="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ey(t,e){switch(t){case"focusin":case"focusout":va=null;break;case"dragenter":case"dragleave":ba=null;break;case"mouseover":case"mouseout":xa=null;break;case"pointerover":case"pointerout":pr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(e.pointerId)}}function yr(t,e,i,r,o,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:i,eventSystemFlags:r,nativeEvent:c,targetContainers:[o]},e!==null&&(e=gi(e),e!==null&&Jg(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function $T(t,e,i,r,o){switch(e){case"focusin":return va=yr(va,t,e,i,r,o),!0;case"dragenter":return ba=yr(ba,t,e,i,r,o),!0;case"mouseover":return xa=yr(xa,t,e,i,r,o),!0;case"pointerover":var c=o.pointerId;return pr.set(c,yr(pr.get(c)||null,t,e,i,r,o)),!0;case"gotpointercapture":return c=o.pointerId,gr.set(c,yr(gr.get(c)||null,t,e,i,r,o)),!0}return!1}function ny(t){var e=pi(t.target);if(e!==null){var i=d(e);if(i!==null){if(e=i.tag,e===13){if(e=f(i),e!==null){t.blockedOn=e,XS(t.priority,function(){if(i.tag===13){var r=Pe();r=eu(r);var o=Ri(i,r);o!==null&&Ge(o,i,r),rf(i,r)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function so(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=of(t.nativeEvent);if(i===null){i=t.nativeEvent;var r=new i.constructor(i.type,i);fu=r,i.target.dispatchEvent(r),fu=null}else return e=gi(i),e!==null&&Jg(e),t.blockedOn=i,!1;e.shift()}return!0}function ay(t,e,i){so(t)&&i.delete(e)}function JT(){cf=!1,va!==null&&so(va)&&(va=null),ba!==null&&so(ba)&&(ba=null),xa!==null&&so(xa)&&(xa=null),pr.forEach(ay),gr.forEach(ay)}function ro(t,e){t.blockedOn===e&&(t.blockedOn=null,cf||(cf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,JT)))}var lo=null;function iy(t){lo!==t&&(lo=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){lo===t&&(lo=null);for(var e=0;e<t.length;e+=3){var i=t[e],r=t[e+1],o=t[e+2];if(typeof r!="function"){if(uf(r||i)===null)continue;break}var c=gi(i);c!==null&&(t.splice(e,3),e-=3,oc(c,{pending:!0,data:o,method:i.method,action:r},r,o))}}))}function vr(t){function e(R){return ro(R,t)}va!==null&&ro(va,t),ba!==null&&ro(ba,t),xa!==null&&ro(xa,t),pr.forEach(e),gr.forEach(e);for(var i=0;i<Sa.length;i++){var r=Sa[i];r.blockedOn===t&&(r.blockedOn=null)}for(;0<Sa.length&&(i=Sa[0],i.blockedOn===null);)ny(i),i.blockedOn===null&&Sa.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(r=0;r<i.length;r+=3){var o=i[r],c=i[r+1],y=o[Re]||null;if(typeof c=="function")y||iy(i);else if(y){var x=null;if(c&&c.hasAttribute("formAction")){if(o=c,y=c[Re]||null)x=y.formAction;else if(uf(o)!==null)continue}else x=y.action;typeof x=="function"?i[r+1]=x:(i.splice(r,3),r-=3),iy(i)}}}function ff(t){this._internalRoot=t}oo.prototype.render=ff.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var i=e.current,r=Pe();Wg(i,r,t,e,null,null)},oo.prototype.unmount=ff.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Wg(t.current,2,null,t,null,null),Yl(),e[mi]=null}};function oo(t){this._internalRoot=t}oo.prototype.unstable_scheduleHydration=function(t){if(t){var e=Sh();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Sa.length&&e!==0&&e<Sa[i].priority;i++);Sa.splice(i,0,t),i===0&&ny(t)}};var sy=a.version;if(sy!=="19.1.0")throw Error(l(527,sy,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=m(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var tw={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var uo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!uo.isDisabled&&uo.supportsFiber)try{Ts=uo.inject(tw),Ve=uo}catch(t){}}return xr.createRoot=function(t,e){if(!u(t))throw Error(l(299));var i=!1,r="",o=Sp,c=Tp,y=wp,x=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(x=e.unstable_transitionCallbacks)),e=Fg(t,1,!1,null,null,i,r,o,c,y,x,null),t[mi]=e.current,Kc(t),new ff(e)},xr.hydrateRoot=function(t,e,i){if(!u(t))throw Error(l(299));var r=!1,o="",c=Sp,y=Tp,x=wp,R=null,k=null;return i!=null&&(i.unstable_strictMode===!0&&(r=!0),i.identifierPrefix!==void 0&&(o=i.identifierPrefix),i.onUncaughtError!==void 0&&(c=i.onUncaughtError),i.onCaughtError!==void 0&&(y=i.onCaughtError),i.onRecoverableError!==void 0&&(x=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(R=i.unstable_transitionCallbacks),i.formState!==void 0&&(k=i.formState)),e=Fg(t,1,!0,e,i!=null?i:null,r,o,c,y,x,R,k),e.context=Ig(null),i=e.current,r=Pe(),r=eu(r),o=ia(r),o.callback=null,sa(i,o,r),i=r,e.current.lanes=i,As(e,i),yn(e),t[mi]=e.current,Kc(t),new oo(e)},xr.version="19.1.0",xr}var vy;function mw(){if(vy)return pf.exports;vy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),pf.exports=hw(),pf.exports}var pw=mw();const g0=w.createContext({});function gw(n){const a=w.useRef(null);return a.current===null&&(a.current=n()),a.current}const Ed=typeof window!="undefined",yw=Ed?w.useLayoutEffect:w.useEffect,Md=w.createContext(null);function Cd(n,a){n.indexOf(a)===-1&&n.push(a)}function Rd(n,a){const s=n.indexOf(a);s>-1&&n.splice(s,1)}const Fn=(n,a,s)=>s>a?a:s<n?n:s;let Dd=()=>{};const In={},y0=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function v0(n){return typeof n=="object"&&n!==null}const b0=n=>/^0[^.\s]+$/u.test(n);function Od(n){let a;return()=>(a===void 0&&(a=n()),a)}const nn=n=>n,vw=(n,a)=>s=>a(n(s)),Hr=(...n)=>n.reduce(vw),Or=(n,a,s)=>{const l=a-n;return l===0?1:(s-n)/l};class Nd{constructor(){this.subscriptions=[]}add(a){return Cd(this.subscriptions,a),()=>Rd(this.subscriptions,a)}notify(a,s,l){const u=this.subscriptions.length;if(u)if(u===1)this.subscriptions[0](a,s,l);else for(let d=0;d<u;d++){const f=this.subscriptions[d];f&&f(a,s,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Mn=n=>n*1e3,Cn=n=>n/1e3;function x0(n,a){return a?n*(1e3/a):0}const S0=(n,a,s)=>(((1-3*s+3*a)*n+(3*s-6*a))*n+3*a)*n,bw=1e-7,xw=12;function Sw(n,a,s,l,u){let d,f,h=0;do f=a+(s-a)/2,d=S0(f,l,u)-n,d>0?s=f:a=f;while(Math.abs(d)>bw&&++h<xw);return f}function Pr(n,a,s,l){if(n===a&&s===l)return nn;const u=d=>Sw(d,0,1,n,s);return d=>d===0||d===1?d:S0(u(d),a,l)}const T0=n=>a=>a<=.5?n(2*a)/2:(2-n(2*(1-a)))/2,w0=n=>a=>1-n(1-a),A0=Pr(.33,1.53,.69,.99),_d=w0(A0),E0=T0(_d),M0=n=>(n*=2)<1?.5*_d(n):.5*(2-Math.pow(2,-10*(n-1))),jd=n=>1-Math.sin(Math.acos(n)),C0=w0(jd),R0=T0(jd),Tw=Pr(.42,0,1,1),ww=Pr(0,0,.58,1),D0=Pr(.42,0,.58,1),Aw=n=>Array.isArray(n)&&typeof n[0]!="number",O0=n=>Array.isArray(n)&&typeof n[0]=="number",Ew={linear:nn,easeIn:Tw,easeInOut:D0,easeOut:ww,circIn:jd,circInOut:R0,circOut:C0,backIn:_d,backInOut:E0,backOut:A0,anticipate:M0},Mw=n=>typeof n=="string",by=n=>{if(O0(n)){Dd(n.length===4);const[a,s,l,u]=n;return Pr(a,s,l,u)}else if(Mw(n))return Ew[n];return n},fo=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],xy={value:null};function Cw(n,a){let s=new Set,l=new Set,u=!1,d=!1;const f=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},m=0;function p(v){f.has(v)&&(g.schedule(v),n()),m++,v(h)}const g={schedule:(v,b=!1,T=!1)=>{const M=T&&u?s:l;return b&&f.add(v),M.has(v)||M.add(v),v},cancel:v=>{l.delete(v),f.delete(v)},process:v=>{if(h=v,u){d=!0;return}u=!0,[s,l]=[l,s],s.forEach(p),a&&xy.value&&xy.value.frameloop[a].push(m),m=0,s.clear(),u=!1,d&&(d=!1,g.process(v))}};return g}const Rw=40;function N0(n,a){let s=!1,l=!0;const u={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=fo.reduce((O,z)=>(O[z]=Cw(d,a?z:void 0),O),{}),{setup:h,read:m,resolveKeyframes:p,preUpdate:g,update:v,preRender:b,render:T,postRender:C}=f,M=()=>{const O=In.useManualTiming?u.timestamp:performance.now();s=!1,In.useManualTiming||(u.delta=l?1e3/60:Math.max(Math.min(O-u.timestamp,Rw),1)),u.timestamp=O,u.isProcessing=!0,h.process(u),m.process(u),p.process(u),g.process(u),v.process(u),b.process(u),T.process(u),C.process(u),u.isProcessing=!1,s&&a&&(l=!1,n(M))},A=()=>{s=!0,l=!0,u.isProcessing||n(M)};return{schedule:fo.reduce((O,z)=>{const _=f[z];return O[z]=(I,tt=!1,Y=!1)=>(s||A(),_.schedule(I,tt,Y)),O},{}),cancel:O=>{for(let z=0;z<fo.length;z++)f[fo[z]].cancel(O)},state:u,steps:f}}const{schedule:Ft,cancel:Ca,state:ye,steps:bf}=N0(typeof requestAnimationFrame!="undefined"?requestAnimationFrame:nn,!0);let Eo;function Dw(){Eo=void 0}const je={now:()=>(Eo===void 0&&je.set(ye.isProcessing||In.useManualTiming?ye.timestamp:performance.now()),Eo),set:n=>{Eo=n,queueMicrotask(Dw)}},_0=n=>a=>typeof a=="string"&&a.startsWith(n),Vd=_0("--"),Ow=_0("var(--"),zd=n=>Ow(n)?Nw.test(n.split("/*")[0].trim()):!1,Nw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ms={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Nr=K(N({},ms),{transform:n=>Fn(0,1,n)}),ho=K(N({},ms),{default:1}),Er=n=>Math.round(n*1e5)/1e5,Ld=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function _w(n){return n==null}const jw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Bd=(n,a)=>s=>!!(typeof s=="string"&&jw.test(s)&&s.startsWith(n)||a&&!_w(s)&&Object.prototype.hasOwnProperty.call(s,a)),j0=(n,a,s)=>l=>{if(typeof l!="string")return l;const[u,d,f,h]=l.match(Ld);return{[n]:parseFloat(u),[a]:parseFloat(d),[s]:parseFloat(f),alpha:h!==void 0?parseFloat(h):1}},Vw=n=>Fn(0,255,n),xf=K(N({},ms),{transform:n=>Math.round(Vw(n))}),si={test:Bd("rgb","red"),parse:j0("red","green","blue"),transform:({red:n,green:a,blue:s,alpha:l=1})=>"rgba("+xf.transform(n)+", "+xf.transform(a)+", "+xf.transform(s)+", "+Er(Nr.transform(l))+")"};function zw(n){let a="",s="",l="",u="";return n.length>5?(a=n.substring(1,3),s=n.substring(3,5),l=n.substring(5,7),u=n.substring(7,9)):(a=n.substring(1,2),s=n.substring(2,3),l=n.substring(3,4),u=n.substring(4,5),a+=a,s+=s,l+=l,u+=u),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(l,16),alpha:u?parseInt(u,16)/255:1}}const qf={test:Bd("#"),parse:zw,transform:si.transform},Gr=n=>({test:a=>typeof a=="string"&&a.endsWith(n)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${n}`}),Ea=Gr("deg"),Rn=Gr("%"),yt=Gr("px"),Lw=Gr("vh"),Bw=Gr("vw"),Sy=K(N({},Rn),{parse:n=>Rn.parse(n)/100,transform:n=>Rn.transform(n*100)}),is={test:Bd("hsl","hue"),parse:j0("hue","saturation","lightness"),transform:({hue:n,saturation:a,lightness:s,alpha:l=1})=>"hsla("+Math.round(n)+", "+Rn.transform(Er(a))+", "+Rn.transform(Er(s))+", "+Er(Nr.transform(l))+")"},Se={test:n=>si.test(n)||qf.test(n)||is.test(n),parse:n=>si.test(n)?si.parse(n):is.test(n)?is.parse(n):qf.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?si.transform(n):is.transform(n)},Uw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function kw(n){var a,s;return isNaN(n)&&typeof n=="string"&&(((a=n.match(Ld))==null?void 0:a.length)||0)+(((s=n.match(Uw))==null?void 0:s.length)||0)>0}const V0="number",z0="color",Hw="var",Pw="var(",Ty="${}",Gw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function _r(n){const a=n.toString(),s=[],l={color:[],number:[],var:[]},u=[];let d=0;const h=a.replace(Gw,m=>(Se.test(m)?(l.color.push(d),u.push(z0),s.push(Se.parse(m))):m.startsWith(Pw)?(l.var.push(d),u.push(Hw),s.push(m)):(l.number.push(d),u.push(V0),s.push(parseFloat(m))),++d,Ty)).split(Ty);return{values:s,split:h,indexes:l,types:u}}function L0(n){return _r(n).values}function B0(n){const{split:a,types:s}=_r(n),l=a.length;return u=>{let d="";for(let f=0;f<l;f++)if(d+=a[f],u[f]!==void 0){const h=s[f];h===V0?d+=Er(u[f]):h===z0?d+=Se.transform(u[f]):d+=u[f]}return d}}const Yw=n=>typeof n=="number"?0:n;function qw(n){const a=L0(n);return B0(n)(a.map(Yw))}const Ra={test:kw,parse:L0,createTransformer:B0,getAnimatableNone:qw};function Sf(n,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(a-n)*6*s:s<1/2?a:s<2/3?n+(a-n)*(2/3-s)*6:n}function Xw({hue:n,saturation:a,lightness:s,alpha:l}){n/=360,a/=100,s/=100;let u=0,d=0,f=0;if(!a)u=d=f=s;else{const h=s<.5?s*(1+a):s+a-s*a,m=2*s-h;u=Sf(m,h,n+1/3),d=Sf(m,h,n),f=Sf(m,h,n-1/3)}return{red:Math.round(u*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:l}}function _o(n,a){return s=>s>0?a:n}const Qt=(n,a,s)=>n+(a-n)*s,Tf=(n,a,s)=>{const l=n*n,u=s*(a*a-l)+l;return u<0?0:Math.sqrt(u)},Kw=[qf,si,is],Zw=n=>Kw.find(a=>a.test(n));function wy(n){const a=Zw(n);if(!a)return!1;let s=a.parse(n);return a===is&&(s=Xw(s)),s}const Ay=(n,a)=>{const s=wy(n),l=wy(a);if(!s||!l)return _o(n,a);const u=N({},s);return d=>(u.red=Tf(s.red,l.red,d),u.green=Tf(s.green,l.green,d),u.blue=Tf(s.blue,l.blue,d),u.alpha=Qt(s.alpha,l.alpha,d),si.transform(u))},Xf=new Set(["none","hidden"]);function Qw(n,a){return Xf.has(n)?s=>s<=0?n:a:s=>s>=1?a:n}function Fw(n,a){return s=>Qt(n,a,s)}function Ud(n){return typeof n=="number"?Fw:typeof n=="string"?zd(n)?_o:Se.test(n)?Ay:$w:Array.isArray(n)?U0:typeof n=="object"?Se.test(n)?Ay:Iw:_o}function U0(n,a){const s=[...n],l=s.length,u=n.map((d,f)=>Ud(d)(d,a[f]));return d=>{for(let f=0;f<l;f++)s[f]=u[f](d);return s}}function Iw(n,a){const s=N(N({},n),a),l={};for(const u in s)n[u]!==void 0&&a[u]!==void 0&&(l[u]=Ud(n[u])(n[u],a[u]));return u=>{for(const d in l)s[d]=l[d](u);return s}}function Ww(n,a){var u;const s=[],l={color:0,var:0,number:0};for(let d=0;d<a.values.length;d++){const f=a.types[d],h=n.indexes[f][l[f]],m=(u=n.values[h])!=null?u:0;s[d]=m,l[f]++}return s}const $w=(n,a)=>{const s=Ra.createTransformer(a),l=_r(n),u=_r(a);return l.indexes.var.length===u.indexes.var.length&&l.indexes.color.length===u.indexes.color.length&&l.indexes.number.length>=u.indexes.number.length?Xf.has(n)&&!u.values.length||Xf.has(a)&&!l.values.length?Qw(n,a):Hr(U0(Ww(l,u),u.values),s):_o(n,a)};function k0(n,a,s){return typeof n=="number"&&typeof a=="number"&&typeof s=="number"?Qt(n,a,s):Ud(n)(n,a)}const Jw=n=>{const a=({timestamp:s})=>n(s);return{start:(s=!0)=>Ft.update(a,s),stop:()=>Ca(a),now:()=>ye.isProcessing?ye.timestamp:je.now()}},H0=(n,a,s=10)=>{let l="";const u=Math.max(Math.round(a/s),2);for(let d=0;d<u;d++)l+=n(d/(u-1))+", ";return`linear(${l.substring(0,l.length-2)})`},jo=2e4;function kd(n){let a=0;const s=50;let l=n.next(a);for(;!l.done&&a<jo;)a+=s,l=n.next(a);return a>=jo?1/0:a}function tA(n,a=100,s){const l=s(K(N({},n),{keyframes:[0,a]})),u=Math.min(kd(l),jo);return{type:"keyframes",ease:d=>l.next(u*d).value/a,duration:Cn(u)}}const eA=5;function P0(n,a,s){const l=Math.max(a-eA,0);return x0(s-n(l),a-l)}const $t={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Ey=.001;function nA({duration:n=$t.duration,bounce:a=$t.bounce,velocity:s=$t.velocity,mass:l=$t.mass}){let u,d,f=1-a;f=Fn($t.minDamping,$t.maxDamping,f),n=Fn($t.minDuration,$t.maxDuration,Cn(n)),f<1?(u=p=>{const g=p*f,v=g*n,b=g-s,T=Kf(p,f),C=Math.exp(-v);return Ey-b/T*C},d=p=>{const v=p*f*n,b=v*s+s,T=Math.pow(f,2)*Math.pow(p,2)*n,C=Math.exp(-v),M=Kf(Math.pow(p,2),f);return(-u(p)+Ey>0?-1:1)*((b-T)*C)/M}):(u=p=>{const g=Math.exp(-p*n),v=(p-s)*n+1;return-.001+g*v},d=p=>{const g=Math.exp(-p*n),v=(s-p)*(n*n);return g*v});const h=5/n,m=iA(u,d,h);if(n=Mn(n),isNaN(m))return{stiffness:$t.stiffness,damping:$t.damping,duration:n};{const p=Math.pow(m,2)*l;return{stiffness:p,damping:f*2*Math.sqrt(l*p),duration:n}}}const aA=12;function iA(n,a,s){let l=s;for(let u=1;u<aA;u++)l=l-n(l)/a(l);return l}function Kf(n,a){return n*Math.sqrt(1-a*a)}const sA=["duration","bounce"],rA=["stiffness","damping","mass"];function My(n,a){return a.some(s=>n[s]!==void 0)}function lA(n){let a=N({velocity:$t.velocity,stiffness:$t.stiffness,damping:$t.damping,mass:$t.mass,isResolvedFromDuration:!1},n);if(!My(n,rA)&&My(n,sA))if(n.visualDuration){const s=n.visualDuration,l=2*Math.PI/(s*1.2),u=l*l,d=2*Fn(.05,1,1-(n.bounce||0))*Math.sqrt(u);a=K(N({},a),{mass:$t.mass,stiffness:u,damping:d})}else{const s=nA(n);a=K(N(N({},a),s),{mass:$t.mass}),a.isResolvedFromDuration=!0}return a}function Vo(n=$t.visualDuration,a=$t.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:a}:n;let{restSpeed:l,restDelta:u}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:m,damping:p,mass:g,duration:v,velocity:b,isResolvedFromDuration:T}=lA(K(N({},s),{velocity:-Cn(s.velocity||0)})),C=b||0,M=p/(2*Math.sqrt(m*g)),A=f-d,E=Cn(Math.sqrt(m/g)),j=Math.abs(A)<5;l||(l=j?$t.restSpeed.granular:$t.restSpeed.default),u||(u=j?$t.restDelta.granular:$t.restDelta.default);let O;if(M<1){const _=Kf(E,M);O=I=>{const tt=Math.exp(-M*E*I);return f-tt*((C+M*E*A)/_*Math.sin(_*I)+A*Math.cos(_*I))}}else if(M===1)O=_=>f-Math.exp(-E*_)*(A+(C+E*A)*_);else{const _=E*Math.sqrt(M*M-1);O=I=>{const tt=Math.exp(-M*E*I),Y=Math.min(_*I,300);return f-tt*((C+M*E*A)*Math.sinh(Y)+_*A*Math.cosh(Y))/_}}const z={calculatedDuration:T&&v||null,next:_=>{const I=O(_);if(T)h.done=_>=v;else{let tt=_===0?C:0;M<1&&(tt=_===0?Mn(C):P0(O,_,I));const Y=Math.abs(tt)<=l,W=Math.abs(f-I)<=u;h.done=Y&&W}return h.value=h.done?f:I,h},toString:()=>{const _=Math.min(kd(z),jo),I=H0(tt=>z.next(_*tt).value,_,30);return _+"ms "+I},toTransition:()=>{}};return z}Vo.applyToOptions=n=>{const a=tA(n,100,Vo);return n.ease=a.ease,n.duration=Mn(a.duration),n.type="keyframes",n};function Zf({keyframes:n,velocity:a=0,power:s=.8,timeConstant:l=325,bounceDamping:u=10,bounceStiffness:d=500,modifyTarget:f,min:h,max:m,restDelta:p=.5,restSpeed:g}){const v=n[0],b={done:!1,value:v},T=Y=>h!==void 0&&Y<h||m!==void 0&&Y>m,C=Y=>h===void 0?m:m===void 0||Math.abs(h-Y)<Math.abs(m-Y)?h:m;let M=s*a;const A=v+M,E=f===void 0?A:f(A);E!==A&&(M=E-v);const j=Y=>-M*Math.exp(-Y/l),O=Y=>E+j(Y),z=Y=>{const W=j(Y),ft=O(Y);b.done=Math.abs(W)<=p,b.value=b.done?E:ft};let _,I;const tt=Y=>{T(b.value)&&(_=Y,I=Vo({keyframes:[b.value,C(b.value)],velocity:P0(O,Y,b.value),damping:u,stiffness:d,restDelta:p,restSpeed:g}))};return tt(0),{calculatedDuration:null,next:Y=>{let W=!1;return!I&&_===void 0&&(W=!0,z(Y),tt(Y)),_!==void 0&&Y>=_?I.next(Y-_):(!W&&z(Y),b)}}}function oA(n,a,s){const l=[],u=s||In.mix||k0,d=n.length-1;for(let f=0;f<d;f++){let h=u(n[f],n[f+1]);if(a){const m=Array.isArray(a)?a[f]||nn:a;h=Hr(m,h)}l.push(h)}return l}function uA(n,a,{clamp:s=!0,ease:l,mixer:u}={}){const d=n.length;if(Dd(d===a.length),d===1)return()=>a[0];if(d===2&&a[0]===a[1])return()=>a[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),a=[...a].reverse());const h=oA(a,l,u),m=h.length,p=g=>{if(f&&g<n[0])return a[0];let v=0;if(m>1)for(;v<n.length-2&&!(g<n[v+1]);v++);const b=Or(n[v],n[v+1],g);return h[v](b)};return s?g=>p(Fn(n[0],n[d-1],g)):p}function cA(n,a){const s=n[n.length-1];for(let l=1;l<=a;l++){const u=Or(0,a,l);n.push(Qt(s,1,u))}}function fA(n){const a=[0];return cA(a,n.length-1),a}function dA(n,a){return n.map(s=>s*a)}function hA(n,a){return n.map(()=>a||D0).splice(0,n.length-1)}function Mr({duration:n=300,keyframes:a,times:s,ease:l="easeInOut"}){const u=Aw(l)?l.map(by):by(l),d={done:!1,value:a[0]},f=dA(s&&s.length===a.length?s:fA(a),n),h=uA(f,a,{ease:Array.isArray(u)?u:hA(a,u)});return{calculatedDuration:n,next:m=>(d.value=h(m),d.done=m>=n,d)}}const mA=n=>n!==null;function Hd(n,{repeat:a,repeatType:s="loop"},l,u=1){const d=n.filter(mA),h=u<0||a&&s!=="loop"&&a%2===1?0:d.length-1;return!h||l===void 0?d[h]:l}const pA={decay:Zf,inertia:Zf,tween:Mr,keyframes:Mr,spring:Vo};function G0(n){typeof n.type=="string"&&(n.type=pA[n.type])}class Pd{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,s){return this.finished.then(a,s)}}const gA=n=>n/100;class Gd extends Pd{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var l,u;const{motionValue:s}=this.options;s&&s.updatedAt!==je.now()&&this.tick(je.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(u=(l=this.options).onStop)==null||u.call(l))},this.options=a,this.initAnimation(),this.play(),a.autoplay===!1&&this.pause()}initAnimation(){const{options:a}=this;G0(a);const{type:s=Mr,repeat:l=0,repeatDelay:u=0,repeatType:d,velocity:f=0}=a;let{keyframes:h}=a;const m=s||Mr;m!==Mr&&typeof h[0]!="number"&&(this.mixKeyframes=Hr(gA,k0(h[0],h[1])),h=[0,100]);const p=m(K(N({},a),{keyframes:h}));d==="mirror"&&(this.mirroredGenerator=m(K(N({},a),{keyframes:[...h].reverse(),velocity:-f}))),p.calculatedDuration===null&&(p.calculatedDuration=kd(p));const{calculatedDuration:g}=p;this.calculatedDuration=g,this.resolvedDuration=g+u,this.totalDuration=this.resolvedDuration*(l+1)-u,this.generator=p}updateTime(a){const s=Math.round(a-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(a,s=!1){const{generator:l,totalDuration:u,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:m}=this;if(this.startTime===null)return l.next(0);const{delay:p=0,keyframes:g,repeat:v,repeatType:b,repeatDelay:T,type:C,onUpdate:M,finalKeyframe:A}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-u/this.speed,this.startTime)),s?this.currentTime=a:this.updateTime(a);const E=this.currentTime-p*(this.playbackSpeed>=0?1:-1),j=this.playbackSpeed>=0?E<0:E>u;this.currentTime=Math.max(E,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let O=this.currentTime,z=l;if(v){const Y=Math.min(this.currentTime,u)/h;let W=Math.floor(Y),ft=Y%1;!ft&&Y>=1&&(ft=1),ft===1&&W--,W=Math.min(W,v+1),!!(W%2)&&(b==="reverse"?(ft=1-ft,T&&(ft-=T/h)):b==="mirror"&&(z=f)),O=Fn(0,1,ft)*h}const _=j?{done:!1,value:g[0]}:z.next(O);d&&(_.value=d(_.value));let{done:I}=_;!j&&m!==null&&(I=this.playbackSpeed>=0?this.currentTime>=u:this.currentTime<=0);const tt=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&I);return tt&&C!==Zf&&(_.value=Hd(g,this.options,A,this.speed)),M&&M(_.value),tt&&this.finish(),_}then(a,s){return this.finished.then(a,s)}get duration(){return Cn(this.calculatedDuration)}get time(){return Cn(this.currentTime)}set time(a){var s;a=Mn(a),this.currentTime=a,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(je.now());const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Cn(this.currentTime))}play(){var u,d;if(this.isStopped)return;const{driver:a=Jw,startTime:s}=this.options;this.driver||(this.driver=a(f=>this.tick(f))),(d=(u=this.options).onPlay)==null||d.call(u);const l=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=l):this.holdTime!==null?this.startTime=l-this.holdTime:this.startTime||(this.startTime=s!=null?s:l),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(je.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var a,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(a=this.options).onComplete)==null||s.call(a)}cancel(){var a,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(a=this.options).onCancel)==null||s.call(a)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),a.observe(this)}}function yA(n){var a;for(let s=1;s<n.length;s++)(a=n[s])!=null||(n[s]=n[s-1])}const ri=n=>n*180/Math.PI,Qf=n=>{const a=ri(Math.atan2(n[1],n[0]));return Ff(a)},vA={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Qf,rotateZ:Qf,skewX:n=>ri(Math.atan(n[1])),skewY:n=>ri(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},Ff=n=>(n=n%360,n<0&&(n+=360),n),Cy=Qf,Ry=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),Dy=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),bA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ry,scaleY:Dy,scale:n=>(Ry(n)+Dy(n))/2,rotateX:n=>Ff(ri(Math.atan2(n[6],n[5]))),rotateY:n=>Ff(ri(Math.atan2(-n[2],n[0]))),rotateZ:Cy,rotate:Cy,skewX:n=>ri(Math.atan(n[4])),skewY:n=>ri(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function If(n){return n.includes("scale")?1:0}function Wf(n,a){if(!n||n==="none")return If(a);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let l,u;if(s)l=bA,u=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);l=vA,u=h}if(!u)return If(a);const d=l[a],f=u[1].split(",").map(SA);return typeof d=="function"?d(f):f[d]}const xA=(n,a)=>{const{transform:s="none"}=getComputedStyle(n);return Wf(s,a)};function SA(n){return parseFloat(n.trim())}const ps=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gs=new Set(ps),Oy=n=>n===ms||n===yt,TA=new Set(["x","y","z"]),wA=ps.filter(n=>!TA.has(n));function AA(n){const a=[];return wA.forEach(s=>{const l=n.getValue(s);l!==void 0&&(a.push([s,l.get()]),l.set(s.startsWith("scale")?1:0))}),a}const li={width:({x:n},{paddingLeft:a="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),height:({y:n},{paddingTop:a="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),top:(n,{top:a})=>parseFloat(a),left:(n,{left:a})=>parseFloat(a),bottom:({y:n},{top:a})=>parseFloat(a)+(n.max-n.min),right:({x:n},{left:a})=>parseFloat(a)+(n.max-n.min),x:(n,{transform:a})=>Wf(a,"x"),y:(n,{transform:a})=>Wf(a,"y")};li.translateX=li.x;li.translateY=li.y;const oi=new Set;let $f=!1,Jf=!1,td=!1;function Y0(){if(Jf){const n=Array.from(oi).filter(l=>l.needsMeasurement),a=new Set(n.map(l=>l.element)),s=new Map;a.forEach(l=>{const u=AA(l);u.length&&(s.set(l,u),l.render())}),n.forEach(l=>l.measureInitialState()),a.forEach(l=>{l.render();const u=s.get(l);u&&u.forEach(([d,f])=>{var h;(h=l.getValue(d))==null||h.set(f)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}Jf=!1,$f=!1,oi.forEach(n=>n.complete(td)),oi.clear()}function q0(){oi.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Jf=!0)})}function EA(){td=!0,q0(),Y0(),td=!1}class Yd{constructor(a,s,l,u,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=l,this.motionValue=u,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(oi.add(this),$f||($f=!0,Ft.read(q0),Ft.resolveKeyframes(Y0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:l,motionValue:u}=this;if(a[0]===null){const d=u==null?void 0:u.get(),f=a[a.length-1];if(d!==void 0)a[0]=d;else if(l&&s){const h=l.readValue(s,f);h!=null&&(a[0]=h)}a[0]===void 0&&(a[0]=f),u&&d===void 0&&u.set(a[0])}yA(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),oi.delete(this)}cancel(){this.state==="scheduled"&&(oi.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const MA=n=>n.startsWith("--");function CA(n,a,s){MA(a)?n.style.setProperty(a,s):n.style[a]=s}const RA=Od(()=>window.ScrollTimeline!==void 0),DA={};function OA(n,a){const s=Od(n);return()=>{var l;return(l=DA[a])!=null?l:s()}}const X0=OA(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(n){return!1}return!0},"linearEasing"),wr=([n,a,s,l])=>`cubic-bezier(${n}, ${a}, ${s}, ${l})`,Ny={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wr([0,.65,.55,1]),circOut:wr([.55,0,1,.45]),backIn:wr([.31,.01,.66,-.59]),backOut:wr([.33,1.53,.69,.99])};function K0(n,a){if(n)return typeof n=="function"?X0()?H0(n,a):"ease-out":O0(n)?wr(n):Array.isArray(n)?n.map(s=>K0(s,a)||Ny.easeOut):Ny[n]}function NA(n,a,s,{delay:l=0,duration:u=300,repeat:d=0,repeatType:f="loop",ease:h="easeOut",times:m}={},p=void 0){const g={[a]:s};m&&(g.offset=m);const v=K0(h,u);Array.isArray(v)&&(g.easing=v);const b={delay:l,duration:u,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return p&&(b.pseudoElement=p),n.animate(g,b)}function Z0(n){return typeof n=="function"&&"applyToOptions"in n}function _A(s){var l=s,{type:n}=l,a=et(l,["type"]);var u,d;return Z0(n)&&X0()?n.applyToOptions(a):((u=a.duration)!=null||(a.duration=300),(d=a.ease)!=null||(a.ease="easeOut"),a)}class jA extends Pd{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;const{element:s,name:l,keyframes:u,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:h,onComplete:m}=a;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=a,Dd(typeof a.type!="string");const p=_A(a);this.animation=NA(s,l,u,p,d),p.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const g=Hd(u,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(g):CA(s,l,g),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var a,s;(s=(a=this.animation).finish)==null||s.call(a)}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:a}=this;a==="idle"||a==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var a,s;this.isPseudoElement||(s=(a=this.animation).commitStyles)==null||s.call(a)}get duration(){var s,l;const a=((l=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:l.call(s).duration)||0;return Cn(Number(a))}get time(){return Cn(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=Mn(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:s}){var l;return this.allowFlatten&&((l=this.animation.effect)==null||l.updateTiming({easing:"linear"})),this.animation.onfinish=null,a&&RA()?(this.animation.timeline=a,nn):s(this)}}const Q0={anticipate:M0,backInOut:E0,circInOut:R0};function VA(n){return n in Q0}function zA(n){typeof n.ease=="string"&&VA(n.ease)&&(n.ease=Q0[n.ease])}const _y=10;class LA extends jA{constructor(a){zA(a),G0(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){var g;const p=this.options,{motionValue:s,onUpdate:l,onComplete:u,element:d}=p,f=et(p,["motionValue","onUpdate","onComplete","element"]);if(!s)return;if(a!==void 0){s.set(a);return}const h=new Gd(K(N({},f),{autoplay:!1})),m=Mn((g=this.finishedTime)!=null?g:this.time);s.setWithVelocity(h.sample(m-_y).value,h.sample(m).value,_y),h.stop()}}const jy=(n,a)=>a==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Ra.test(n)||n==="0")&&!n.startsWith("url("));function BA(n){const a=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==a)return!0}function UA(n,a,s,l){const u=n[0];if(u===null)return!1;if(a==="display"||a==="visibility")return!0;const d=n[n.length-1],f=jy(u,a),h=jy(d,a);return!f||!h?!1:BA(n)||(s==="spring"||Z0(s))&&l}function F0(n){return v0(n)&&"offsetHeight"in n}const kA=new Set(["opacity","clipPath","filter","transform"]),HA=Od(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function PA(n){var p;const{motionValue:a,name:s,repeatDelay:l,repeatType:u,damping:d,type:f}=n;if(!F0((p=a==null?void 0:a.owner)==null?void 0:p.current))return!1;const{onUpdate:h,transformTemplate:m}=a.owner.getProps();return HA()&&s&&kA.has(s)&&(s!=="transform"||!m)&&!h&&!l&&u!=="mirror"&&d!==0&&f!=="inertia"}const GA=40;class YA extends Pd{constructor(b){var T=b,{autoplay:a=!0,delay:s=0,type:l="keyframes",repeat:u=0,repeatDelay:d=0,repeatType:f="loop",keyframes:h,name:m,motionValue:p,element:g}=T,v=et(T,["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"]);var A;super(),this.stop=()=>{var E,j;this._animation&&(this._animation.stop(),(E=this.stopTimeline)==null||E.call(this)),(j=this.keyframeResolver)==null||j.cancel()},this.createdAt=je.now();const C=N({autoplay:a,delay:s,type:l,repeat:u,repeatDelay:d,repeatType:f,name:m,motionValue:p,element:g},v),M=(g==null?void 0:g.KeyframeResolver)||Yd;this.keyframeResolver=new M(h,(E,j,O)=>this.onKeyframesResolved(E,j,C,!O),m,p,g),(A=this.keyframeResolver)==null||A.scheduleResolve()}onKeyframesResolved(a,s,l,u){this.keyframeResolver=void 0;const{name:d,type:f,velocity:h,delay:m,isHandoff:p,onUpdate:g}=l;this.resolvedAt=je.now(),UA(a,d,f,h)||((In.instantAnimations||!m)&&(g==null||g(Hd(a,l,s))),a[0]=a[a.length-1],l.duration=0,l.repeat=0);const v=u?this.resolvedAt?this.resolvedAt-this.createdAt>GA?this.resolvedAt:this.createdAt:this.createdAt:void 0,b=K(N({startTime:v,finalKeyframe:s},l),{keyframes:a}),T=!p&&PA(b)?new LA(K(N({},b),{element:b.motionValue.owner.current})):new Gd(b);T.finished.then(()=>this.notifyFinished()).catch(nn),this.pendingTimeline&&(this.stopTimeline=T.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=T}get finished(){return this._animation?this.animation.finished:this._finished}then(a,s){return this.finished.finally(a).then(()=>{})}get animation(){var a;return this._animation||((a=this.keyframeResolver)==null||a.resume(),EA()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var a;this._animation&&this.animation.cancel(),(a=this.keyframeResolver)==null||a.cancel()}}const qA=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function XA(n){const a=qA.exec(n);if(!a)return[,];const[,s,l,u]=a;return[`--${s!=null?s:l}`,u]}function I0(n,a,s=1){const[l,u]=XA(n);if(!l)return;const d=window.getComputedStyle(a).getPropertyValue(l);if(d){const f=d.trim();return y0(f)?parseFloat(f):f}return zd(u)?I0(u,a,s+1):u}function qd(n,a){var s,l;return(l=(s=n==null?void 0:n[a])!=null?s:n==null?void 0:n.default)!=null?l:n}const W0=new Set(["width","height","top","left","right","bottom",...ps]),KA={test:n=>n==="auto",parse:n=>n},$0=n=>a=>a.test(n),J0=[ms,yt,Rn,Ea,Bw,Lw,KA],Vy=n=>J0.find($0(n));function ZA(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||b0(n):!0}const QA=new Set(["brightness","contrast","saturate","opacity"]);function FA(n){const[a,s]=n.slice(0,-1).split("(");if(a==="drop-shadow")return n;const[l]=s.match(Ld)||[];if(!l)return n;const u=s.replace(l,"");let d=QA.has(a)?1:0;return l!==s&&(d*=100),a+"("+d+u+")"}const IA=/\b([a-z-]*)\(.*?\)/gu,ed=K(N({},Ra),{getAnimatableNone:n=>{const a=n.match(IA);return a?a.map(FA).join(" "):n}}),zy=K(N({},ms),{transform:Math.round}),WA={rotate:Ea,rotateX:Ea,rotateY:Ea,rotateZ:Ea,scale:ho,scaleX:ho,scaleY:ho,scaleZ:ho,skew:Ea,skewX:Ea,skewY:Ea,distance:yt,translateX:yt,translateY:yt,translateZ:yt,x:yt,y:yt,z:yt,perspective:yt,transformPerspective:yt,opacity:Nr,originX:Sy,originY:Sy,originZ:yt},Xd=K(N({borderWidth:yt,borderTopWidth:yt,borderRightWidth:yt,borderBottomWidth:yt,borderLeftWidth:yt,borderRadius:yt,radius:yt,borderTopLeftRadius:yt,borderTopRightRadius:yt,borderBottomRightRadius:yt,borderBottomLeftRadius:yt,width:yt,maxWidth:yt,height:yt,maxHeight:yt,top:yt,right:yt,bottom:yt,left:yt,padding:yt,paddingTop:yt,paddingRight:yt,paddingBottom:yt,paddingLeft:yt,margin:yt,marginTop:yt,marginRight:yt,marginBottom:yt,marginLeft:yt,backgroundPositionX:yt,backgroundPositionY:yt},WA),{zIndex:zy,fillOpacity:Nr,strokeOpacity:Nr,numOctaves:zy}),$A=K(N({},Xd),{color:Se,backgroundColor:Se,outlineColor:Se,fill:Se,stroke:Se,borderColor:Se,borderTopColor:Se,borderRightColor:Se,borderBottomColor:Se,borderLeftColor:Se,filter:ed,WebkitFilter:ed}),tb=n=>$A[n];function eb(n,a){let s=tb(n);return s!==ed&&(s=Ra),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const JA=new Set(["auto","none","0"]);function tE(n,a,s){let l=0,u;for(;l<n.length&&!u;){const d=n[l];typeof d=="string"&&!JA.has(d)&&_r(d).values.length&&(u=n[l]),l++}if(u&&s)for(const d of a)n[d]=eb(s,u)}class eE extends Yd{constructor(a,s,l,u,d){super(a,s,l,u,d,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:l}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<a.length;m++){let p=a[m];if(typeof p=="string"&&(p=p.trim(),zd(p))){const g=I0(p,s.current);g!==void 0&&(a[m]=g),m===a.length-1&&(this.finalKeyframe=p)}}if(this.resolveNoneKeyframes(),!W0.has(l)||a.length!==2)return;const[u,d]=a,f=Vy(u),h=Vy(d);if(f!==h)if(Oy(f)&&Oy(h))for(let m=0;m<a.length;m++){const p=a[m];typeof p=="string"&&(a[m]=parseFloat(p))}else li[l]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,l=[];for(let u=0;u<a.length;u++)(a[u]===null||ZA(a[u]))&&l.push(u);l.length&&tE(a,l,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:l}=this;if(!a||!a.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=li[l](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const u=s[s.length-1];u!==void 0&&a.getValue(l,u).jump(u,!1)}measureEndState(){var h;const{element:a,name:s,unresolvedKeyframes:l}=this;if(!a||!a.current)return;const u=a.getValue(s);u&&u.jump(this.measuredOrigin,!1);const d=l.length-1,f=l[d];l[d]=li[s](a.measureViewportBox(),window.getComputedStyle(a.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([m,p])=>{a.getValue(m).set(p)}),this.resolveNoneKeyframes()}}function nE(n,a,s){var l;if(n instanceof EventTarget)return[n];if(typeof n=="string"){let u=document;const d=(l=s==null?void 0:s[n])!=null?l:u.querySelectorAll(n);return d?Array.from(d):[]}return Array.from(n)}const nb=(n,a)=>a&&typeof n=="number"?a.transform(n):n,Ly=30,aE=n=>!isNaN(parseFloat(n));class iE{constructor(a,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,u=!0)=>{var f,h;const d=je.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();u&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=je.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=aE(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new Nd);const l=this.events[a].add(s);return a==="change"?()=>{l(),Ft.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,l){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-l}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var a;(a=this.events.change)==null||a.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=je.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>Ly)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Ly);return x0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var a,s;(a=this.dependents)==null||a.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function fs(n,a){return new iE(n,a)}const{schedule:Kd}=N0(queueMicrotask,!1),un={x:!1,y:!1};function ab(){return un.x||un.y}function sE(n){return n==="x"||n==="y"?un[n]?null:(un[n]=!0,()=>{un[n]=!1}):un.x||un.y?null:(un.x=un.y=!0,()=>{un.x=un.y=!1})}function ib(n,a){const s=nE(n),l=new AbortController,u=K(N({passive:!0},a),{signal:l.signal});return[s,u,()=>l.abort()]}function By(n){return!(n.pointerType==="touch"||ab())}function rE(n,a,s={}){const[l,u,d]=ib(n,s),f=h=>{if(!By(h))return;const{target:m}=h,p=a(m,h);if(typeof p!="function"||!m)return;const g=v=>{By(v)&&(p(v),m.removeEventListener("pointerleave",g))};m.addEventListener("pointerleave",g,u)};return l.forEach(h=>{h.addEventListener("pointerenter",f,u)}),d}const sb=(n,a)=>a?n===a?!0:sb(n,a.parentElement):!1,Zd=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,lE=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function oE(n){return lE.has(n.tagName)||n.tabIndex!==-1}const Mo=new WeakSet;function Uy(n){return a=>{a.key==="Enter"&&n(a)}}function wf(n,a){n.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const uE=(n,a)=>{const s=n.currentTarget;if(!s)return;const l=Uy(()=>{if(Mo.has(s))return;wf(s,"down");const u=Uy(()=>{wf(s,"up")}),d=()=>wf(s,"cancel");s.addEventListener("keyup",u,a),s.addEventListener("blur",d,a)});s.addEventListener("keydown",l,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",l),a)};function ky(n){return Zd(n)&&!ab()}function cE(n,a,s={}){const[l,u,d]=ib(n,s),f=h=>{const m=h.currentTarget;if(!ky(h))return;Mo.add(m);const p=a(m,h),g=(T,C)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",b),Mo.has(m)&&Mo.delete(m),ky(T)&&typeof p=="function"&&p(T,{success:C})},v=T=>{g(T,m===window||m===document||s.useGlobalTarget||sb(m,T.target))},b=T=>{g(T,!1)};window.addEventListener("pointerup",v,u),window.addEventListener("pointercancel",b,u)};return l.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",f,u),F0(h)&&(h.addEventListener("focus",p=>uE(p,u)),!oE(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function rb(n){return v0(n)&&"ownerSVGElement"in n}function fE(n){return rb(n)&&n.tagName==="svg"}const Te=n=>!!(n&&n.getVelocity),dE=[...J0,Se,Ra],hE=n=>dE.find($0(n)),lb=w.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function mE(n=!0){const a=w.useContext(Md);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:l,register:u}=a,d=w.useId();w.useEffect(()=>{if(n)return u(d)},[n]);const f=w.useCallback(()=>n&&l&&l(d),[d,l,n]);return!s&&l?[!1,f]:[!0]}const ob=w.createContext({strict:!1}),Hy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ds={};for(const n in Hy)ds[n]={isEnabled:a=>Hy[n].some(s=>!!a[s])};function pE(n){for(const a in n)ds[a]=N(N({},ds[a]),n[a])}const gE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function zo(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||gE.has(n)}let ub=n=>!zo(n);function yE(n){n&&(ub=a=>a.startsWith("on")?!zo(a):n(a))}try{yE(require("@emotion/is-prop-valid").default)}catch(n){}function vE(n,a,s){const l={};for(const u in n)u==="values"&&typeof n.values=="object"||(ub(u)||s===!0&&zo(u)||!a&&!zo(u)||n.draggable&&u.startsWith("onDrag"))&&(l[u]=n[u]);return l}function bE(n){if(typeof Proxy=="undefined")return n;const a=new Map,s=(...l)=>n(...l);return new Proxy(s,{get:(l,u)=>u==="create"?n:(a.has(u)||a.set(u,n(u)),a.get(u))})}const Yo=w.createContext({});function qo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function jr(n){return typeof n=="string"||Array.isArray(n)}const Qd=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Fd=["initial",...Qd];function Xo(n){return qo(n.animate)||Fd.some(a=>jr(n[a]))}function cb(n){return!!(Xo(n)||n.variants)}function xE(n,a){if(Xo(n)){const{initial:s,animate:l}=n;return{initial:s===!1||jr(s)?s:void 0,animate:jr(l)?l:void 0}}return n.inherit!==!1?a:{}}function SE(n){const{initial:a,animate:s}=xE(n,w.useContext(Yo));return w.useMemo(()=>({initial:a,animate:s}),[Py(a),Py(s)])}function Py(n){return Array.isArray(n)?n.join(" "):n}const TE=Symbol.for("motionComponentSymbol");function ss(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function wE(n,a,s){return w.useCallback(l=>{l&&n.onMount&&n.onMount(l),a&&(l?a.mount(l):a.unmount()),s&&(typeof s=="function"?s(l):ss(s)&&(s.current=l))},[a])}const Id=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),AE="framerAppearId",fb="data-"+Id(AE),db=w.createContext({});function EE(n,a,s,l,u){var M,A;const{visualElement:d}=w.useContext(Yo),f=w.useContext(ob),h=w.useContext(Md),m=w.useContext(lb).reducedMotion,p=w.useRef(null);l=l||f.renderer,!p.current&&l&&(p.current=l(n,{visualState:a,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:m}));const g=p.current,v=w.useContext(db);g&&!g.projection&&u&&(g.type==="html"||g.type==="svg")&&ME(p.current,s,u,v);const b=w.useRef(!1);w.useInsertionEffect(()=>{g&&b.current&&g.update(s,h)});const T=s[fb],C=w.useRef(!!T&&!((M=window.MotionHandoffIsComplete)!=null&&M.call(window,T))&&((A=window.MotionHasOptimisedAnimation)==null?void 0:A.call(window,T)));return yw(()=>{g&&(b.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),Kd.render(g.render),C.current&&g.animationState&&g.animationState.animateChanges())}),w.useEffect(()=>{g&&(!C.current&&g.animationState&&g.animationState.animateChanges(),C.current&&(queueMicrotask(()=>{var E;(E=window.MotionHandoffMarkAsComplete)==null||E.call(window,T)}),C.current=!1))}),g}function ME(n,a,s,l){const{layoutId:u,layout:d,drag:f,dragConstraints:h,layoutScroll:m,layoutRoot:p,layoutCrossfade:g}=a;n.projection=new s(n.latestValues,a["data-framer-portal-id"]?void 0:hb(n.parent)),n.projection.setOptions({layoutId:u,layout:d,alwaysMeasureLayout:!!f||h&&ss(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,crossfade:g,layoutScroll:m,layoutRoot:p})}function hb(n){if(n)return n.options.allowProjection!==!1?n.projection:hb(n.parent)}function CE({preloadedFeatures:n,createVisualElement:a,useRender:s,useVisualState:l,Component:u}){var h,m;n&&pE(n);function d(p,g){let v;const b=K(N(N({},w.useContext(lb)),p),{layoutId:RE(p)}),{isStatic:T}=b,C=SE(p),M=l(p,T);if(!T&&Ed){DE();const A=OE(b);v=A.MeasureLayout,C.visualElement=EE(u,M,b,a,A.ProjectionNode)}return S.jsxs(Yo.Provider,{value:C,children:[v&&C.visualElement?S.jsx(v,N({visualElement:C.visualElement},b)):null,s(u,p,wE(M,C.visualElement,g),M,T,C.visualElement)]})}d.displayName=`motion.${typeof u=="string"?u:`create(${(m=(h=u.displayName)!=null?h:u.name)!=null?m:""})`}`;const f=w.forwardRef(d);return f[TE]=u,f}function RE({layoutId:n}){const a=w.useContext(g0).id;return a&&n!==void 0?a+"-"+n:n}function DE(n,a){w.useContext(ob).strict}function OE(n){const{drag:a,layout:s}=ds;if(!a&&!s)return{};const l=N(N({},a),s);return{MeasureLayout:a!=null&&a.isEnabled(n)||s!=null&&s.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const Vr={};function NE(n){for(const a in n)Vr[a]=n[a],Vd(a)&&(Vr[a].isCSSVariable=!0)}function mb(n,{layout:a,layoutId:s}){return gs.has(n)||n.startsWith("origin")||(a||s!==void 0)&&(!!Vr[n]||n==="opacity")}const _E={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},jE=ps.length;function VE(n,a,s){let l="",u=!0;for(let d=0;d<jE;d++){const f=ps[d],h=n[f];if(h===void 0)continue;let m=!0;if(typeof h=="number"?m=h===(f.startsWith("scale")?1:0):m=parseFloat(h)===0,!m||s){const p=nb(h,Xd[f]);if(!m){u=!1;const g=_E[f]||f;l+=`${g}(${p}) `}s&&(a[f]=p)}}return l=l.trim(),s?l=s(a,u?"":l):u&&(l="none"),l}function Wd(n,a,s){const{style:l,vars:u,transformOrigin:d}=n;let f=!1,h=!1;for(const m in a){const p=a[m];if(gs.has(m)){f=!0;continue}else if(Vd(m)){u[m]=p;continue}else{const g=nb(p,Xd[m]);m.startsWith("origin")?(h=!0,d[m]=g):l[m]=g}}if(a.transform||(f||s?l.transform=VE(a,n.transform,s):l.transform&&(l.transform="none")),h){const{originX:m="50%",originY:p="50%",originZ:g=0}=d;l.transformOrigin=`${m} ${p} ${g}`}}const $d=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function pb(n,a,s){for(const l in a)!Te(a[l])&&!mb(l,s)&&(n[l]=a[l])}function zE({transformTemplate:n},a){return w.useMemo(()=>{const s=$d();return Wd(s,a,n),Object.assign({},s.vars,s.style)},[a])}function LE(n,a){const s=n.style||{},l={};return pb(l,s,n),Object.assign(l,zE(n,a)),l}function BE(n,a){const s={},l=LE(n,a);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=l,s}const UE={offset:"stroke-dashoffset",array:"stroke-dasharray"},kE={offset:"strokeDashoffset",array:"strokeDasharray"};function HE(n,a,s=1,l=0,u=!0){n.pathLength=1;const d=u?UE:kE;n[d.offset]=yt.transform(-l);const f=yt.transform(a),h=yt.transform(s);n[d.array]=`${f} ${h}`}function gb(n,v,m,p,g){var b=v,{attrX:a,attrY:s,attrScale:l,pathLength:u,pathSpacing:d=1,pathOffset:f=0}=b,h=et(b,["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"]);var M,A;if(Wd(n,h,p),m){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:T,style:C}=n;T.transform&&(C.transform=T.transform,delete T.transform),(C.transform||T.transformOrigin)&&(C.transformOrigin=(M=T.transformOrigin)!=null?M:"50% 50%",delete T.transformOrigin),C.transform&&(C.transformBox=(A=g==null?void 0:g.transformBox)!=null?A:"fill-box",delete T.transformBox),a!==void 0&&(T.x=a),s!==void 0&&(T.y=s),l!==void 0&&(T.scale=l),u!==void 0&&HE(T,u,d,f,!1)}const yb=()=>K(N({},$d()),{attrs:{}}),vb=n=>typeof n=="string"&&n.toLowerCase()==="svg";function PE(n,a,s,l){const u=w.useMemo(()=>{const d=yb();return gb(d,a,vb(l),n.transformTemplate,n.style),K(N({},d.attrs),{style:N({},d.style)})},[a]);if(n.style){const d={};pb(d,n.style,n),u.style=N(N({},d),u.style)}return u}const GE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Jd(n){return typeof n!="string"||n.includes("-")?!1:!!(GE.indexOf(n)>-1||/[A-Z]/u.test(n))}function YE(n=!1){return(s,l,u,{latestValues:d},f)=>{const m=(Jd(s)?PE:BE)(l,d,f,s),p=vE(l,typeof s=="string",n),g=s!==w.Fragment?K(N(N({},p),m),{ref:u}):{},{children:v}=l,b=w.useMemo(()=>Te(v)?v.get():v,[v]);return w.createElement(s,K(N({},g),{children:b}))}}function Gy(n){const a=[{},{}];return n==null||n.values.forEach((s,l)=>{a[0][l]=s.get(),a[1][l]=s.getVelocity()}),a}function th(n,a,s,l){if(typeof a=="function"){const[u,d]=Gy(l);a=a(s!==void 0?s:n.custom,u,d)}if(typeof a=="string"&&(a=n.variants&&n.variants[a]),typeof a=="function"){const[u,d]=Gy(l);a=a(s!==void 0?s:n.custom,u,d)}return a}function Co(n){return Te(n)?n.get():n}function qE({scrapeMotionValuesFromProps:n,createRenderState:a},s,l,u){return{latestValues:XE(s,l,u,n),renderState:a()}}const bb=n=>(a,s)=>{const l=w.useContext(Yo),u=w.useContext(Md),d=()=>qE(n,a,l,u);return s?d():gw(d)};function XE(n,a,s,l){const u={},d=l(n,{});for(const T in d)u[T]=Co(d[T]);let{initial:f,animate:h}=n;const m=Xo(n),p=cb(n);a&&p&&!m&&n.inherit!==!1&&(f===void 0&&(f=a.initial),h===void 0&&(h=a.animate));let g=s?s.initial===!1:!1;g=g||f===!1;const v=g?h:f;if(v&&typeof v!="boolean"&&!qo(v)){const T=Array.isArray(v)?v:[v];for(let C=0;C<T.length;C++){const M=th(n,T[C]);if(M){const b=M,{transitionEnd:A,transition:E}=b,j=et(b,["transitionEnd","transition"]);for(const O in j){let z=j[O];if(Array.isArray(z)){const _=g?z.length-1:0;z=z[_]}z!==null&&(u[O]=z)}for(const O in A)u[O]=A[O]}}}return u}function eh(n,a,s){var d;const{style:l}=n,u={};for(const f in l)(Te(l[f])||a.style&&Te(a.style[f])||mb(f,n)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(u[f]=l[f]);return u}const KE={useVisualState:bb({scrapeMotionValuesFromProps:eh,createRenderState:$d})};function xb(n,a,s){const l=eh(n,a,s);for(const u in n)if(Te(n[u])||Te(a[u])){const d=ps.indexOf(u)!==-1?"attr"+u.charAt(0).toUpperCase()+u.substring(1):u;l[d]=n[u]}return l}const ZE={useVisualState:bb({scrapeMotionValuesFromProps:xb,createRenderState:yb})};function QE(n,a){return function(l,{forwardMotionProps:u}={forwardMotionProps:!1}){const d=Jd(l)?ZE:KE,f=K(N({},d),{preloadedFeatures:n,useRender:YE(u),createVisualElement:a,Component:l});return CE(f)}}function zr(n,a,s){const l=n.getProps();return th(l,a,s!==void 0?s:l.custom,n)}const nd=n=>Array.isArray(n);function FE(n,a,s){n.hasValue(a)?n.getValue(a).set(s):n.addValue(a,fs(s))}function IE(n){return nd(n)?n[n.length-1]||0:n}function WE(n,a){let f=zr(n,a)||{},{transitionEnd:l={},transition:u={}}=f,d=et(f,["transitionEnd","transition"]);d=N(N({},d),l);for(const h in d){const m=IE(d[h]);FE(n,h,m)}}function $E(n){return!!(Te(n)&&n.add)}function ad(n,a){const s=n.getValue("willChange");if($E(s))return s.add(a);if(!s&&In.WillChange){const l=new In.WillChange("auto");n.addValue("willChange",l),l.add(a)}}function Sb(n){return n.props[fb]}const JE=n=>n!==null;function t2(n,{repeat:a,repeatType:s="loop"},l){const u=n.filter(JE),d=a&&s!=="loop"&&a%2===1?0:u.length-1;return u[d]}const e2={type:"spring",stiffness:500,damping:25,restSpeed:10},n2=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),a2={type:"keyframes",duration:.8},i2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},s2=(n,{keyframes:a})=>a.length>2?a2:gs.has(n)?n.startsWith("scale")?n2(a[1]):e2:i2;function r2(v){var b=v,{when:n,delay:a,delayChildren:s,staggerChildren:l,staggerDirection:u,repeat:d,repeatType:f,repeatDelay:h,from:m,elapsed:p}=b,g=et(b,["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"]);return!!Object.keys(g).length}const nh=(n,a,s,l={},u,d)=>f=>{const h=qd(l,n)||{},m=h.delay||l.delay||0;let{elapsed:p=0}=l;p=p-Mn(m);const g=K(N({keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity()},h),{delay:-p,onUpdate:b=>{a.set(b),h.onUpdate&&h.onUpdate(b)},onComplete:()=>{f(),h.onComplete&&h.onComplete()},name:n,motionValue:a,element:d?void 0:u});r2(h)||Object.assign(g,s2(n,g)),g.duration&&(g.duration=Mn(g.duration)),g.repeatDelay&&(g.repeatDelay=Mn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let v=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(v=!0)),(In.instantAnimations||In.skipAnimations)&&(v=!0,g.duration=0,g.delay=0),g.allowFlatten=!h.type&&!h.ease,v&&!d&&a.get()!==void 0){const b=t2(g.keyframes,h);if(b!==void 0){Ft.update(()=>{g.onUpdate(b),g.onComplete()});return}}return h.isSync?new Gd(g):new YA(g)};function l2({protectedKeys:n,needsAnimating:a},s){const l=n.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,l}function Tb(n,a,{delay:s=0,transitionOverride:l,type:u}={}){var v;let g=a,{transition:d=n.getDefaultTransition(),transitionEnd:f}=g,h=et(g,["transition","transitionEnd"]);l&&(d=l);const m=[],p=u&&n.animationState&&n.animationState.getState()[u];for(const b in h){const T=n.getValue(b,(v=n.latestValues[b])!=null?v:null),C=h[b];if(C===void 0||p&&l2(p,b))continue;const M=N({delay:s},qd(d||{},b)),A=T.get();if(A!==void 0&&!T.isAnimating&&!Array.isArray(C)&&C===A&&!M.velocity)continue;let E=!1;if(window.MotionHandoffAnimation){const O=Sb(n);if(O){const z=window.MotionHandoffAnimation(O,b,Ft);z!==null&&(M.startTime=z,E=!0)}}ad(n,b),T.start(nh(b,T,C,n.shouldReduceMotion&&W0.has(b)?{type:!1}:M,n,E));const j=T.animation;j&&m.push(j)}return f&&Promise.all(m).then(()=>{Ft.update(()=>{f&&WE(n,f)})}),m}function id(n,a,s={}){var m;const l=zr(n,a,s.type==="exit"?(m=n.presenceContext)==null?void 0:m.custom:void 0);let{transition:u=n.getDefaultTransition()||{}}=l||{};s.transitionOverride&&(u=s.transitionOverride);const d=l?()=>Promise.all(Tb(n,l,s)):()=>Promise.resolve(),f=n.variantChildren&&n.variantChildren.size?(p=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:b}=u;return o2(n,a,g+p,v,b,s)}:()=>Promise.resolve(),{when:h}=u;if(h){const[p,g]=h==="beforeChildren"?[d,f]:[f,d];return p().then(()=>g())}else return Promise.all([d(),f(s.delay)])}function o2(n,a,s=0,l=0,u=1,d){const f=[],h=(n.variantChildren.size-1)*l,m=u===1?(p=0)=>p*l:(p=0)=>h-p*l;return Array.from(n.variantChildren).sort(u2).forEach((p,g)=>{p.notify("AnimationStart",a),f.push(id(p,a,K(N({},d),{delay:s+m(g)})).then(()=>p.notify("AnimationComplete",a)))}),Promise.all(f)}function u2(n,a){return n.sortNodePosition(a)}function c2(n,a,s={}){n.notify("AnimationStart",a);let l;if(Array.isArray(a)){const u=a.map(d=>id(n,d,s));l=Promise.all(u)}else if(typeof a=="string")l=id(n,a,s);else{const u=typeof a=="function"?zr(n,a,s.custom):a;l=Promise.all(Tb(n,u,s))}return l.then(()=>{n.notify("AnimationComplete",a)})}function wb(n,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==n.length)return!1;for(let l=0;l<s;l++)if(a[l]!==n[l])return!1;return!0}const f2=Fd.length;function Ab(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?Ab(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const a={};for(let s=0;s<f2;s++){const l=Fd[s],u=n.props[l];(jr(u)||u===!1)&&(a[l]=u)}return a}const d2=[...Qd].reverse(),h2=Qd.length;function m2(n){return a=>Promise.all(a.map(({animation:s,options:l})=>c2(n,s,l)))}function p2(n){let a=m2(n),s=Yy(),l=!0;const u=m=>(p,g)=>{var b;const v=zr(n,g,m==="exit"?(b=n.presenceContext)==null?void 0:b.custom:void 0);if(v){const T=v,{transition:C,transitionEnd:M}=T,A=et(T,["transition","transitionEnd"]);p=N(N(N({},p),A),M)}return p};function d(m){a=m(n)}function f(m){const{props:p}=n,g=Ab(n.parent)||{},v=[],b=new Set;let T={},C=1/0;for(let A=0;A<h2;A++){const E=d2[A],j=s[E],O=p[E]!==void 0?p[E]:g[E],z=jr(O),_=E===m?j.isActive:null;_===!1&&(C=A);let I=O===g[E]&&O!==p[E]&&z;if(I&&l&&n.manuallyAnimateOnMount&&(I=!1),j.protectedKeys=N({},T),!j.isActive&&_===null||!O&&!j.prevProp||qo(O)||typeof O=="boolean")continue;const tt=g2(j.prevProp,O);let Y=tt||E===m&&j.isActive&&!I&&z||A>C&&z,W=!1;const ft=Array.isArray(O)?O:[O];let ht=ft.reduce(u(E),{});_===!1&&(ht={});const{prevResolvedValues:lt={}}=j,bt=N(N({},lt),ht),Et=q=>{Y=!0,b.has(q)&&(W=!0,b.delete(q)),j.needsAnimating[q]=!0;const P=n.getValue(q);P&&(P.liveStyle=!1)};for(const q in bt){const P=ht[q],ct=lt[q];if(T.hasOwnProperty(q))continue;let D=!1;nd(P)&&nd(ct)?D=!wb(P,ct):D=P!==ct,D?P!=null?Et(q):b.add(q):P!==void 0&&b.has(q)?Et(q):j.protectedKeys[q]=!0}j.prevProp=O,j.prevResolvedValues=ht,j.isActive&&(T=N(N({},T),ht)),l&&n.blockInitialAnimation&&(Y=!1),Y&&(!(I&&tt)||W)&&v.push(...ft.map(q=>({animation:q,options:{type:E}})))}if(b.size){const A={};if(typeof p.initial!="boolean"){const E=zr(n,Array.isArray(p.initial)?p.initial[0]:p.initial);E&&E.transition&&(A.transition=E.transition)}b.forEach(E=>{const j=n.getBaseTarget(E),O=n.getValue(E);O&&(O.liveStyle=!0),A[E]=j!=null?j:null}),v.push({animation:A})}let M=!!v.length;return l&&(p.initial===!1||p.initial===p.animate)&&!n.manuallyAnimateOnMount&&(M=!1),l=!1,M?a(v):Promise.resolve()}function h(m,p){var v;if(s[m].isActive===p)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(b=>{var T;return(T=b.animationState)==null?void 0:T.setActive(m,p)}),s[m].isActive=p;const g=f(m);for(const b in s)s[b].protectedKeys={};return g}return{animateChanges:f,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=Yy(),l=!0}}}function g2(n,a){return typeof a=="string"?a!==n:Array.isArray(a)?!wb(a,n):!1}function ti(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Yy(){return{animate:ti(!0),whileInView:ti(),whileHover:ti(),whileTap:ti(),whileDrag:ti(),whileFocus:ti(),exit:ti()}}class _a{constructor(a){this.isMounted=!1,this.node=a}update(){}}class y2 extends _a{constructor(a){super(a),a.animationState||(a.animationState=p2(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();qo(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)==null||a.call(this)}}let v2=0;class b2 extends _a{constructor(){super(...arguments),this.id=v2++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===l)return;const u=this.node.animationState.setActive("exit",!a);s&&!a&&u.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const x2={animation:{Feature:y2},exit:{Feature:b2}};function Lr(n,a,s,l={passive:!0}){return n.addEventListener(a,s,l),()=>n.removeEventListener(a,s)}function Yr(n){return{point:{x:n.pageX,y:n.pageY}}}const S2=n=>a=>Zd(a)&&n(a,Yr(a));function Cr(n,a,s,l){return Lr(n,a,S2(s),l)}function Eb({top:n,left:a,right:s,bottom:l}){return{x:{min:a,max:s},y:{min:n,max:l}}}function T2({x:n,y:a}){return{top:a.min,right:n.max,bottom:a.max,left:n.min}}function w2(n,a){if(!a)return n;const s=a({x:n.left,y:n.top}),l=a({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:l.y,right:l.x}}const Mb=1e-4,A2=1-Mb,E2=1+Mb,Cb=.01,M2=0-Cb,C2=0+Cb;function Me(n){return n.max-n.min}function R2(n,a,s){return Math.abs(n-a)<=s}function qy(n,a,s,l=.5){n.origin=l,n.originPoint=Qt(a.min,a.max,n.origin),n.scale=Me(s)/Me(a),n.translate=Qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=A2&&n.scale<=E2||isNaN(n.scale))&&(n.scale=1),(n.translate>=M2&&n.translate<=C2||isNaN(n.translate))&&(n.translate=0)}function Rr(n,a,s,l){qy(n.x,a.x,s.x,l?l.originX:void 0),qy(n.y,a.y,s.y,l?l.originY:void 0)}function Xy(n,a,s){n.min=s.min+a.min,n.max=n.min+Me(a)}function D2(n,a,s){Xy(n.x,a.x,s.x),Xy(n.y,a.y,s.y)}function Ky(n,a,s){n.min=a.min-s.min,n.max=n.min+Me(a)}function Dr(n,a,s){Ky(n.x,a.x,s.x),Ky(n.y,a.y,s.y)}const Zy=()=>({translate:0,scale:1,origin:0,originPoint:0}),rs=()=>({x:Zy(),y:Zy()}),Qy=()=>({min:0,max:0}),ee=()=>({x:Qy(),y:Qy()});function en(n){return[n("x"),n("y")]}function Af(n){return n===void 0||n===1}function sd({scale:n,scaleX:a,scaleY:s}){return!Af(n)||!Af(a)||!Af(s)}function ii(n){return sd(n)||Rb(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Rb(n){return Fy(n.x)||Fy(n.y)}function Fy(n){return n&&n!=="0%"}function Lo(n,a,s){const l=n-s,u=a*l;return s+u}function Iy(n,a,s,l,u){return u!==void 0&&(n=Lo(n,u,l)),Lo(n,s,l)+a}function rd(n,a=0,s=1,l,u){n.min=Iy(n.min,a,s,l,u),n.max=Iy(n.max,a,s,l,u)}function Db(n,{x:a,y:s}){rd(n.x,a.translate,a.scale,a.originPoint),rd(n.y,s.translate,s.scale,s.originPoint)}const Wy=.999999999999,$y=1.0000000000001;function O2(n,a,s,l=!1){const u=s.length;if(!u)return;a.x=a.y=1;let d,f;for(let h=0;h<u;h++){d=s[h],f=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&os(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(a.x*=f.x.scale,a.y*=f.y.scale,Db(n,f)),l&&ii(d.latestValues)&&os(n,d.latestValues))}a.x<$y&&a.x>Wy&&(a.x=1),a.y<$y&&a.y>Wy&&(a.y=1)}function ls(n,a){n.min=n.min+a,n.max=n.max+a}function Jy(n,a,s,l,u=.5){const d=Qt(n.min,n.max,u);rd(n,a,s,d,l)}function os(n,a){Jy(n.x,a.x,a.scaleX,a.scale,a.originX),Jy(n.y,a.y,a.scaleY,a.scale,a.originY)}function Ob(n,a){return Eb(w2(n.getBoundingClientRect(),a))}function N2(n,a,s){const l=Ob(n,s),{scroll:u}=a;return u&&(ls(l.x,u.offset.x),ls(l.y,u.offset.y)),l}const Nb=({current:n})=>n?n.ownerDocument.defaultView:null,tv=(n,a)=>Math.abs(n-a);function _2(n,a){const s=tv(n.x,a.x),l=tv(n.y,a.y);return Math.sqrt(df(s,2)+df(l,2))}class _b{constructor(a,s,{transformPagePoint:l,contextWindow:u,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=Mf(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,T=_2(v.offset,{x:0,y:0})>=3;if(!b&&!T)return;const{point:C}=v,{timestamp:M}=ye;this.history.push(K(N({},C),{timestamp:M}));const{onStart:A,onMove:E}=this.handlers;b||(A&&A(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),E&&E(this.lastMoveEvent,v)},this.handlePointerMove=(v,b)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=Ef(b,this.transformPagePoint),Ft.update(this.updatePoint,!0)},this.handlePointerUp=(v,b)=>{this.end();const{onEnd:T,onSessionEnd:C,resumeAnimation:M}=this.handlers;if(this.dragSnapToOrigin&&M&&M(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const A=Mf(v.type==="pointercancel"?this.lastMoveEventInfo:Ef(b,this.transformPagePoint),this.history);this.startEvent&&T&&T(v,A),C&&C(v,A)},!Zd(a))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=l,this.contextWindow=u||window;const f=Yr(a),h=Ef(f,this.transformPagePoint),{point:m}=h,{timestamp:p}=ye;this.history=[K(N({},m),{timestamp:p})];const{onSessionStart:g}=s;g&&g(a,Mf(h,this.history)),this.removeListeners=Hr(Cr(this.contextWindow,"pointermove",this.handlePointerMove),Cr(this.contextWindow,"pointerup",this.handlePointerUp),Cr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Ca(this.updatePoint)}}function Ef(n,a){return a?{point:a(n.point)}:n}function ev(n,a){return{x:n.x-a.x,y:n.y-a.y}}function Mf({point:n},a){return{point:n,delta:ev(n,jb(a)),offset:ev(n,j2(a)),velocity:V2(a,.1)}}function j2(n){return n[0]}function jb(n){return n[n.length-1]}function V2(n,a){if(n.length<2)return{x:0,y:0};let s=n.length-1,l=null;const u=jb(n);for(;s>=0&&(l=n[s],!(u.timestamp-l.timestamp>Mn(a)));)s--;if(!l)return{x:0,y:0};const d=Cn(u.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const f={x:(u.x-l.x)/d,y:(u.y-l.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function z2(n,{min:a,max:s},l){return a!==void 0&&n<a?n=l?Qt(a,n,l.min):Math.max(n,a):s!==void 0&&n>s&&(n=l?Qt(s,n,l.max):Math.min(n,s)),n}function nv(n,a,s){return{min:a!==void 0?n.min+a:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function L2(n,{top:a,left:s,bottom:l,right:u}){return{x:nv(n.x,s,u),y:nv(n.y,a,l)}}function av(n,a){let s=a.min-n.min,l=a.max-n.max;return a.max-a.min<n.max-n.min&&([s,l]=[l,s]),{min:s,max:l}}function B2(n,a){return{x:av(n.x,a.x),y:av(n.y,a.y)}}function U2(n,a){let s=.5;const l=Me(n),u=Me(a);return u>l?s=Or(a.min,a.max-l,n.min):l>u&&(s=Or(n.min,n.max-u,a.min)),Fn(0,1,s)}function k2(n,a){const s={};return a.min!==void 0&&(s.min=a.min-n.min),a.max!==void 0&&(s.max=a.max-n.min),s}const ld=.35;function H2(n=ld){return n===!1?n=0:n===!0&&(n=ld),{x:iv(n,"left","right"),y:iv(n,"top","bottom")}}function iv(n,a,s){return{min:sv(n,a),max:sv(n,s)}}function sv(n,a){return typeof n=="number"?n:n[a]||0}const P2=new WeakMap;class G2{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const u=g=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Yr(g).point)},d=(g,v)=>{const{drag:b,dragPropagation:T,onDragStart:C}=this.getProps();if(b&&!T&&(this.openDragLock&&this.openDragLock(),this.openDragLock=sE(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),en(A=>{let E=this.getAxisMotionValue(A).get()||0;if(Rn.test(E)){const{projection:j}=this.visualElement;if(j&&j.layout){const O=j.layout.layoutBox[A];O&&(E=Me(O)*(parseFloat(E)/100))}}this.originPoint[A]=E}),C&&Ft.postRender(()=>C(g,v)),ad(this.visualElement,"transform");const{animationState:M}=this.visualElement;M&&M.setActive("whileDrag",!0)},f=(g,v)=>{const{dragPropagation:b,dragDirectionLock:T,onDirectionLock:C,onDrag:M}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:A}=v;if(T&&this.currentDirection===null){this.currentDirection=Y2(A),this.currentDirection!==null&&C&&C(this.currentDirection);return}this.updateAxis("x",v.point,A),this.updateAxis("y",v.point,A),this.visualElement.render(),M&&M(g,v)},h=(g,v)=>this.stop(g,v),m=()=>en(g=>{var v;return this.getAnimationState(g)==="paused"&&((v=this.getAxisMotionValue(g).animation)==null?void 0:v.play())}),{dragSnapToOrigin:p}=this.getProps();this.panSession=new _b(a,{onSessionStart:u,onStart:d,onMove:f,onSessionEnd:h,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:p,contextWindow:Nb(this.visualElement)})}stop(a,s){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:u}=s;this.startAnimation(u);const{onDragEnd:d}=this.getProps();d&&Ft.postRender(()=>d(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,l){const{drag:u}=this.getProps();if(!l||!mo(a,u,this.currentDirection))return;const d=this.getAxisMotionValue(a);let f=this.originPoint[a]+l[a];this.constraints&&this.constraints[a]&&(f=z2(f,this.constraints[a],this.elastic[a])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:a,dragElastic:s}=this.getProps(),l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,u=this.constraints;a&&ss(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&l?this.constraints=L2(l.layoutBox,a):this.constraints=!1,this.elastic=H2(s),u!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&en(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=k2(l.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!ss(a))return!1;const l=a.current,{projection:u}=this.visualElement;if(!u||!u.layout)return!1;const d=N2(l,u.root,this.visualElement.getTransformPagePoint());let f=B2(u.layout.layoutBox,d);if(s){const h=s(T2(f));this.hasMutatedConstraints=!!h,h&&(f=Eb(h))}return f}startAnimation(a){const{drag:s,dragMomentum:l,dragElastic:u,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:h}=this.getProps(),m=this.constraints||{},p=en(g=>{if(!mo(g,s,this.currentDirection))return;let v=m&&m[g]||{};f&&(v={min:0,max:0});const b=u?200:1e6,T=u?40:1e7,C=N(N({type:"inertia",velocity:l?a[g]:0,bounceStiffness:b,bounceDamping:T,timeConstant:750,restDelta:1,restSpeed:10},d),v);return this.startAxisValueAnimation(g,C)});return Promise.all(p).then(h)}startAxisValueAnimation(a,s){const l=this.getAxisMotionValue(a);return ad(this.visualElement,a),l.start(nh(a,l,0,s,this.visualElement,!1))}stopAnimation(){en(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){en(a=>{var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,l=this.visualElement.getProps(),u=l[s];return u||this.visualElement.getValue(a,(l.initial?l.initial[a]:void 0)||0)}snapToCursor(a){en(s=>{const{drag:l}=this.getProps();if(!mo(s,l,this.currentDirection))return;const{projection:u}=this.visualElement,d=this.getAxisMotionValue(s);if(u&&u.layout){const{min:f,max:h}=u.layout.layoutBox[s];d.set(a[s]-Qt(f,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:l}=this.visualElement;if(!ss(s)||!l||!this.constraints)return;this.stopAnimation();const u={x:0,y:0};en(f=>{const h=this.getAxisMotionValue(f);if(h&&this.constraints!==!1){const m=h.get();u[f]=U2({min:m,max:m},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),en(f=>{if(!mo(f,a,null))return;const h=this.getAxisMotionValue(f),{min:m,max:p}=this.constraints[f];h.set(Qt(m,p,u[f]))})}addListeners(){if(!this.visualElement.current)return;P2.set(this.visualElement,this);const a=this.visualElement.current,s=Cr(a,"pointerdown",m=>{const{drag:p,dragListener:g=!0}=this.getProps();p&&g&&this.start(m)}),l=()=>{const{dragConstraints:m}=this.getProps();ss(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:u}=this.visualElement,d=u.addEventListener("measure",l);u&&!u.layout&&(u.root&&u.root.updateScroll(),u.updateLayout()),Ft.read(l);const f=Lr(window,"resize",()=>this.scalePositionWithinConstraints()),h=u.addEventListener("didUpdate",({delta:m,hasLayoutChanged:p})=>{this.isDragging&&p&&(en(g=>{const v=this.getAxisMotionValue(g);v&&(this.originPoint[g]+=m[g].translate,v.set(v.get()+m[g].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:l=!1,dragPropagation:u=!1,dragConstraints:d=!1,dragElastic:f=ld,dragMomentum:h=!0}=a;return K(N({},a),{drag:s,dragDirectionLock:l,dragPropagation:u,dragConstraints:d,dragElastic:f,dragMomentum:h})}}function mo(n,a,s){return(a===!0||a===n)&&(s===null||s===n)}function Y2(n,a=10){let s=null;return Math.abs(n.y)>a?s="y":Math.abs(n.x)>a&&(s="x"),s}class q2 extends _a{constructor(a){super(a),this.removeGroupControls=nn,this.removeListeners=nn,this.controls=new G2(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||nn}unmount(){this.removeGroupControls(),this.removeListeners()}}const rv=n=>(a,s)=>{n&&Ft.postRender(()=>n(a,s))};class X2 extends _a{constructor(){super(...arguments),this.removePointerDownListener=nn}onPointerDown(a){this.session=new _b(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Nb(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:l,onPanEnd:u}=this.node.getProps();return{onSessionStart:rv(a),onStart:rv(s),onMove:l,onEnd:(d,f)=>{delete this.session,u&&Ft.postRender(()=>u(d,f))}}}mount(){this.removePointerDownListener=Cr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function lv(n,a){return a.max===a.min?0:n/(a.max-a.min)*100}const Sr={correct:(n,a)=>{if(!a.target)return n;if(typeof n=="string")if(yt.test(n))n=parseFloat(n);else return n;const s=lv(n,a.target.x),l=lv(n,a.target.y);return`${s}% ${l}%`}},K2={correct:(n,{treeScale:a,projectionDelta:s})=>{const l=n,u=Ra.parse(n);if(u.length>5)return l;const d=Ra.createTransformer(n),f=typeof u[0]!="number"?1:0,h=s.x.scale*a.x,m=s.y.scale*a.y;u[0+f]/=h,u[1+f]/=m;const p=Qt(h,m,.5);return typeof u[2+f]=="number"&&(u[2+f]/=p),typeof u[3+f]=="number"&&(u[3+f]/=p),d(u)}};class Z2 extends w.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l,layoutId:u}=this.props,{projection:d}=a;NE(Q2),d&&(s.group&&s.group.add(d),l&&l.register&&u&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions(K(N({},d.options),{onExitComplete:()=>this.safeToRemove()}))),Ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:l,drag:u,isPresent:d}=this.props,{projection:f}=l;return f&&(f.isPresent=d,u||a.layoutDependency!==s||s===void 0||a.isPresent!==d?f.willUpdate():this.safeToRemove(),a.isPresent!==d&&(d?f.promote():f.relegate()||Ft.postRender(()=>{const h=f.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),Kd.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l}=this.props,{projection:u}=a;u&&(u.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(u),l&&l.deregister&&l.deregister(u))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function Vb(n){const[a,s]=mE(),l=w.useContext(g0);return S.jsx(Z2,K(N({},n),{layoutGroup:l,switchLayoutGroup:w.useContext(db),isPresent:a,safeToRemove:s}))}const Q2={borderRadius:K(N({},Sr),{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Sr,borderTopRightRadius:Sr,borderBottomLeftRadius:Sr,borderBottomRightRadius:Sr,boxShadow:K2};function F2(n,a,s){const l=Te(n)?n:fs(n);return l.start(nh("",l,a,s)),l.animation}const I2=(n,a)=>n.depth-a.depth;class W2{constructor(){this.children=[],this.isDirty=!1}add(a){Cd(this.children,a),this.isDirty=!0}remove(a){Rd(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(I2),this.isDirty=!1,this.children.forEach(a)}}function $2(n,a){const s=je.now(),l=({timestamp:u})=>{const d=u-s;d>=a&&(Ca(l),n(d-a))};return Ft.setup(l,!0),()=>Ca(l)}const zb=["TopLeft","TopRight","BottomLeft","BottomRight"],J2=zb.length,ov=n=>typeof n=="string"?parseFloat(n):n,uv=n=>typeof n=="number"||yt.test(n);function tM(n,a,s,l,u,d){var f,h,m,p;u?(n.opacity=Qt(0,(f=s.opacity)!=null?f:1,eM(l)),n.opacityExit=Qt((h=a.opacity)!=null?h:1,0,nM(l))):d&&(n.opacity=Qt((m=a.opacity)!=null?m:1,(p=s.opacity)!=null?p:1,l));for(let g=0;g<J2;g++){const v=`border${zb[g]}Radius`;let b=cv(a,v),T=cv(s,v);if(b===void 0&&T===void 0)continue;b||(b=0),T||(T=0),b===0||T===0||uv(b)===uv(T)?(n[v]=Math.max(Qt(ov(b),ov(T),l),0),(Rn.test(T)||Rn.test(b))&&(n[v]+="%")):n[v]=T}(a.rotate||s.rotate)&&(n.rotate=Qt(a.rotate||0,s.rotate||0,l))}function cv(n,a){return n[a]!==void 0?n[a]:n.borderRadius}const eM=Lb(0,.5,C0),nM=Lb(.5,.95,nn);function Lb(n,a,s){return l=>l<n?0:l>a?1:s(Or(n,a,l))}function fv(n,a){n.min=a.min,n.max=a.max}function tn(n,a){fv(n.x,a.x),fv(n.y,a.y)}function dv(n,a){n.translate=a.translate,n.scale=a.scale,n.originPoint=a.originPoint,n.origin=a.origin}function hv(n,a,s,l,u){return n-=a,n=Lo(n,1/s,l),u!==void 0&&(n=Lo(n,1/u,l)),n}function aM(n,a=0,s=1,l=.5,u,d=n,f=n){if(Rn.test(a)&&(a=parseFloat(a),a=Qt(f.min,f.max,a/100)-f.min),typeof a!="number")return;let h=Qt(d.min,d.max,l);n===d&&(h-=a),n.min=hv(n.min,a,s,h,u),n.max=hv(n.max,a,s,h,u)}function mv(n,a,[s,l,u],d,f){aM(n,a[s],a[l],a[u],a.scale,d,f)}const iM=["x","scaleX","originX"],sM=["y","scaleY","originY"];function pv(n,a,s,l){mv(n.x,a,iM,s?s.x:void 0,l?l.x:void 0),mv(n.y,a,sM,s?s.y:void 0,l?l.y:void 0)}function gv(n){return n.translate===0&&n.scale===1}function Bb(n){return gv(n.x)&&gv(n.y)}function yv(n,a){return n.min===a.min&&n.max===a.max}function rM(n,a){return yv(n.x,a.x)&&yv(n.y,a.y)}function vv(n,a){return Math.round(n.min)===Math.round(a.min)&&Math.round(n.max)===Math.round(a.max)}function Ub(n,a){return vv(n.x,a.x)&&vv(n.y,a.y)}function bv(n){return Me(n.x)/Me(n.y)}function xv(n,a){return n.translate===a.translate&&n.scale===a.scale&&n.originPoint===a.originPoint}class lM{constructor(){this.members=[]}add(a){Cd(this.members,a),a.scheduleRender()}remove(a){if(Rd(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(u=>a===u);if(s===0)return!1;let l;for(let u=s;u>=0;u--){const d=this.members[u];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(a,s){const l=this.lead;if(a!==l&&(this.prevLead=l,this.lead=a,a.show(),l)){l.instance&&l.scheduleRender(),a.scheduleRender(),a.resumeFrom=l,s&&(a.resumeFrom.preserveOpacity=!0),l.snapshot&&(a.snapshot=l.snapshot,a.snapshot.latestValues=l.animationValues||l.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:u}=a.options;u===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:l}=a;s.onExitComplete&&s.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function oM(n,a,s){let l="";const u=n.x.translate/a.x,d=n.y.translate/a.y,f=(s==null?void 0:s.z)||0;if((u||d||f)&&(l=`translate3d(${u}px, ${d}px, ${f}px) `),(a.x!==1||a.y!==1)&&(l+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:p,rotate:g,rotateX:v,rotateY:b,skewX:T,skewY:C}=s;p&&(l=`perspective(${p}px) ${l}`),g&&(l+=`rotate(${g}deg) `),v&&(l+=`rotateX(${v}deg) `),b&&(l+=`rotateY(${b}deg) `),T&&(l+=`skewX(${T}deg) `),C&&(l+=`skewY(${C}deg) `)}const h=n.x.scale*a.x,m=n.y.scale*a.y;return(h!==1||m!==1)&&(l+=`scale(${h}, ${m})`),l||"none"}const Cf=["","X","Y","Z"],uM={visibility:"hidden"},cM=1e3;let fM=0;function Rf(n,a,s,l){const{latestValues:u}=a;u[n]&&(s[n]=u[n],a.setStaticValue(n,0),l&&(l[n]=0))}function kb(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:a}=n.options;if(!a)return;const s=Sb(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:u,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Ft,!(u||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&kb(l)}function Hb({attachResizeListener:n,defaultParent:a,measureScroll:s,checkIsScrollRoot:l,resetTransform:u}){return class{constructor(f={},h=a==null?void 0:a()){this.id=fM++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(mM),this.nodes.forEach(bM),this.nodes.forEach(xM),this.nodes.forEach(pM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new W2)}addEventListener(f,h){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new Nd),this.eventHandlers.get(f).add(h)}notifyListeners(f,...h){const m=this.eventHandlers.get(f);m&&m.notify(...h)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=rb(f)&&!fE(f),this.instance=f;const{layoutId:h,layout:m,visualElement:p}=this.options;if(p&&!p.current&&p.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||h)&&(this.isLayoutDirty=!0),n){let g;const v=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=$2(v,250),Ro.hasAnimatedSinceResize&&(Ro.hasAnimatedSinceResize=!1,this.nodes.forEach(Tv))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&p&&(h||m)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:v,hasRelativeLayoutChanged:b,layout:T})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const C=this.options.transition||p.getDefaultTransition()||EM,{onLayoutAnimationStart:M,onLayoutAnimationComplete:A}=p.getProps(),E=!this.targetLayout||!Ub(this.targetLayout,T),j=!v&&b;if(this.options.layoutRoot||this.resumeFrom||j||v&&(E||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const O=K(N({},qd(C,"layout")),{onPlay:M,onComplete:A});(p.shouldReduceMotion||this.options.layoutRoot)&&(O.delay=0,O.type=!1),this.startAnimation(O),this.setAnimationOrigin(g,j)}else v||Tv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=T})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ca(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(SM),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&kb(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const v=this.path[g];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:m}=this.options;if(h===void 0&&!m)return;const p=this.getTransformTemplate();this.prevTransformTemplateValue=p?p(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Sv);return}this.isUpdating||this.nodes.forEach(yM),this.isUpdating=!1,this.nodes.forEach(vM),this.nodes.forEach(dM),this.nodes.forEach(hM),this.clearAllSnapshots();const h=je.now();ye.delta=Fn(0,1e3/60,h-ye.timestamp),ye.timestamp=h,ye.isProcessing=!0,bf.update.process(ye),bf.preRender.process(ye),bf.render.process(ye),ye.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Kd.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(gM),this.sharedNodes.forEach(TM)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ft.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ft.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Me(this.snapshot.measuredBox.x)&&!Me(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(h=!1),h&&this.instance){const m=l(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!u)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!Bb(this.projectionDelta),m=this.getTransformTemplate(),p=m?m(this.latestValues,""):void 0,g=p!==this.prevTransformTemplateValue;f&&this.instance&&(h||ii(this.latestValues)||g)&&(u(this.instance,p),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const h=this.measurePageBox();let m=this.removeElementScroll(h);return f&&(m=this.removeTransform(m)),MM(m),{animationId:this.root.animationId,measuredBox:h,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var p;const{visualElement:f}=this.options;if(!f)return ee();const h=f.measureViewportBox();if(!(((p=this.scroll)==null?void 0:p.wasRoot)||this.path.some(CM))){const{scroll:g}=this.root;g&&(ls(h.x,g.offset.x),ls(h.y,g.offset.y))}return h}removeElementScroll(f){var m;const h=ee();if(tn(h,f),(m=this.scroll)!=null&&m.wasRoot)return h;for(let p=0;p<this.path.length;p++){const g=this.path[p],{scroll:v,options:b}=g;g!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&tn(h,f),ls(h.x,v.offset.x),ls(h.y,v.offset.y))}return h}applyTransform(f,h=!1){const m=ee();tn(m,f);for(let p=0;p<this.path.length;p++){const g=this.path[p];!h&&g.options.layoutScroll&&g.scroll&&g!==g.root&&os(m,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),ii(g.latestValues)&&os(m,g.latestValues)}return ii(this.latestValues)&&os(m,this.latestValues),m}removeTransform(f){const h=ee();tn(h,f);for(let m=0;m<this.path.length;m++){const p=this.path[m];if(!p.instance||!ii(p.latestValues))continue;sd(p.latestValues)&&p.updateSnapshot();const g=ee(),v=p.measurePageBox();tn(g,v),pv(h,p.latestValues,p.snapshot?p.snapshot.layoutBox:void 0,g)}return ii(this.latestValues)&&pv(h,this.latestValues),h}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options=K(N(N({},this.options),f),{crossfade:f.crossfade!==void 0?f.crossfade:!0})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ye.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var b;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==h;if(!(f||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:v}=this.options;if(!(!this.layout||!(g||v))){if(this.resolvedRelativeTargetAt=ye.timestamp,!this.targetDelta&&!this.relativeTarget){const T=this.getClosestProjectingParent();T&&T.layout&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.layout.layoutBox,T.layout.layoutBox),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),D2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tn(this.target,this.layout.layoutBox),Db(this.target,this.targetDelta)):tn(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const T=this.getClosestProjectingParent();T&&!!T.resumingFrom==!!this.resumingFrom&&!T.options.layoutScroll&&T.target&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.target,T.target),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||sd(this.parent.latestValues)||Rb(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var C;const f=this.getLead(),h=!!this.resumingFrom||this!==f;let m=!0;if((this.isProjectionDirty||(C=this.parent)!=null&&C.isProjectionDirty)&&(m=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===ye.timestamp&&(m=!1),m)return;const{layout:p,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(p||g))return;tn(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,b=this.treeScale.y;O2(this.layoutCorrected,this.treeScale,this.path,h),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=ee());const{target:T}=f;if(!T){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(dv(this.prevProjectionDelta.x,this.projectionDelta.x),dv(this.prevProjectionDelta.y,this.projectionDelta.y)),Rr(this.projectionDelta,this.layoutCorrected,T,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==b||!xv(this.projectionDelta.x,this.prevProjectionDelta.x)||!xv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",T))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rs(),this.projectionDelta=rs(),this.projectionDeltaWithTransform=rs()}setAnimationOrigin(f,h=!1){const m=this.snapshot,p=m?m.latestValues:{},g=N({},this.latestValues),v=rs();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const b=ee(),T=m?m.source:void 0,C=this.layout?this.layout.source:void 0,M=T!==C,A=this.getStack(),E=!A||A.members.length<=1,j=!!(M&&!E&&this.options.crossfade===!0&&!this.path.some(AM));this.animationProgress=0;let O;this.mixTargetDelta=z=>{const _=z/1e3;wv(v.x,f.x,_),wv(v.y,f.y,_),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dr(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),wM(this.relativeTarget,this.relativeTargetOrigin,b,_),O&&rM(this.relativeTarget,O)&&(this.isProjectionDirty=!1),O||(O=ee()),tn(O,this.relativeTarget)),M&&(this.animationValues=g,tM(g,p,this.latestValues,_,j,E)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=_},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var h,m,p;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(p=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||p.stop(),this.pendingAnimation&&(Ca(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ft.update(()=>{Ro.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=fs(0)),this.currentAnimation=F2(this.motionValue,[0,1e3],K(N({},f),{isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),f.onUpdate&&f.onUpdate(g)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(cM),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:h,target:m,layout:p,latestValues:g}=f;if(!(!h||!m||!p)){if(this!==f&&this.layout&&p&&Pb(this.options.animationType,this.layout.layoutBox,p.layoutBox)){m=this.target||ee();const v=Me(this.layout.layoutBox.x);m.x.min=f.target.x.min,m.x.max=m.x.min+v;const b=Me(this.layout.layoutBox.y);m.y.min=f.target.y.min,m.y.max=m.y.min+b}tn(h,m),os(h,g),Rr(this.projectionDeltaWithTransform,this.layoutCorrected,h,g)}}registerSharedNode(f,h){this.sharedNodes.has(f)||this.sharedNodes.set(f,new lM),this.sharedNodes.get(f).add(h);const p=h.options.initialPromotionConfig;h.promote({transition:p?p.transition:void 0,preserveFollowOpacity:p&&p.shouldPreserveFollowOpacity?p.shouldPreserveFollowOpacity(h):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var h;const{layoutId:f}=this.options;return f?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:f}=this.options;return f?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:h,preserveFollowOpacity:m}={}){const p=this.getStack();p&&p.promote(this,m),f&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let h=!1;const{latestValues:m}=f;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(h=!0),!h)return;const p={};m.z&&Rf("z",f,p,this.animationValues);for(let g=0;g<Cf.length;g++)Rf(`rotate${Cf[g]}`,f,p,this.animationValues),Rf(`skew${Cf[g]}`,f,p,this.animationValues);f.render();for(const g in p)f.setStaticValue(g,p[g]),this.animationValues&&(this.animationValues[g]=p[g]);f.scheduleRender()}getProjectionStyles(f){var T,C;if(!this.instance||this.isSVG)return;if(!this.isVisible)return uM;const h={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=Co(f==null?void 0:f.pointerEvents)||"",h.transform=m?m(this.latestValues,""):"none",h;const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){const M={};return this.options.layoutId&&(M.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,M.pointerEvents=Co(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!ii(this.latestValues)&&(M.transform=m?m({},""):"none",this.hasProjected=!1),M}const g=p.animationValues||p.latestValues;this.applyTransformsToTarget(),h.transform=oM(this.projectionDeltaWithTransform,this.treeScale,g),m&&(h.transform=m(g,h.transform));const{x:v,y:b}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${b.origin*100}% 0`,p.animationValues?h.opacity=p===this?(C=(T=g.opacity)!=null?T:this.latestValues.opacity)!=null?C:1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:h.opacity=p===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const M in Vr){if(g[M]===void 0)continue;const{correct:A,applyTo:E,isCSSVariable:j}=Vr[M],O=h.transform==="none"?g[M]:A(g[M],p);if(E){const z=E.length;for(let _=0;_<z;_++)h[E[_]]=O}else j?this.options.visualElement.renderState.vars[M]=O:h[M]=O}return this.options.layoutId&&(h.pointerEvents=p===this?Co(f==null?void 0:f.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var h;return(h=f.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(Sv),this.root.sharedNodes.clear()}}}function dM(n){n.updateLayout()}function hM(n){var s;const a=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&a&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:u}=n.layout,{animationType:d}=n.options,f=a.source!==n.layout.source;d==="size"?en(v=>{const b=f?a.measuredBox[v]:a.layoutBox[v],T=Me(b);b.min=l[v].min,b.max=b.min+T}):Pb(d,a.layoutBox,l)&&en(v=>{const b=f?a.measuredBox[v]:a.layoutBox[v],T=Me(l[v]);b.max=b.min+T,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+T)});const h=rs();Rr(h,l,a.layoutBox);const m=rs();f?Rr(m,n.applyTransform(u,!0),a.measuredBox):Rr(m,l,a.layoutBox);const p=!Bb(h);let g=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:T}=v;if(b&&T){const C=ee();Dr(C,a.layoutBox,b.layoutBox);const M=ee();Dr(M,l,T.layoutBox),Ub(C,M)||(g=!0),v.options.layoutRoot&&(n.relativeTarget=M,n.relativeTargetOrigin=C,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:a,delta:m,layoutDelta:h,hasLayoutChanged:p,hasRelativeLayoutChanged:g})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function mM(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function pM(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function gM(n){n.clearSnapshot()}function Sv(n){n.clearMeasurements()}function yM(n){n.isLayoutDirty=!1}function vM(n){const{visualElement:a}=n.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),n.resetTransform()}function Tv(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function bM(n){n.resolveTargetDelta()}function xM(n){n.calcProjection()}function SM(n){n.resetSkewAndRotation()}function TM(n){n.removeLeadSnapshot()}function wv(n,a,s){n.translate=Qt(a.translate,0,s),n.scale=Qt(a.scale,1,s),n.origin=a.origin,n.originPoint=a.originPoint}function Av(n,a,s,l){n.min=Qt(a.min,s.min,l),n.max=Qt(a.max,s.max,l)}function wM(n,a,s,l){Av(n.x,a.x,s.x,l),Av(n.y,a.y,s.y,l)}function AM(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const EM={duration:.45,ease:[.4,0,.1,1]},Ev=n=>typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Mv=Ev("applewebkit/")&&!Ev("chrome/")?Math.round:nn;function Cv(n){n.min=Mv(n.min),n.max=Mv(n.max)}function MM(n){Cv(n.x),Cv(n.y)}function Pb(n,a,s){return n==="position"||n==="preserve-aspect"&&!R2(bv(a),bv(s),.2)}function CM(n){var a;return n!==n.root&&((a=n.scroll)==null?void 0:a.wasRoot)}const RM=Hb({attachResizeListener:(n,a)=>Lr(n,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Df={current:void 0},Gb=Hb({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Df.current){const n=new RM({});n.mount(window),n.setOptions({layoutScroll:!0}),Df.current=n}return Df.current},resetTransform:(n,a)=>{n.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),DM={pan:{Feature:X2},drag:{Feature:q2,ProjectionNode:Gb,MeasureLayout:Vb}};function Rv(n,a,s){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",s==="Start");const u="onHover"+s,d=l[u];d&&Ft.postRender(()=>d(a,Yr(a)))}class OM extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=rE(a,(s,l)=>(Rv(this.node,l,"Start"),u=>Rv(this.node,u,"End"))))}unmount(){}}class NM extends _a{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(s){a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Hr(Lr(this.node.current,"focus",()=>this.onFocus()),Lr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Dv(n,a,s){const{props:l}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",s==="Start");const u="onTap"+(s==="End"?"":s),d=l[u];d&&Ft.postRender(()=>d(a,Yr(a)))}class _M extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=cE(a,(s,l)=>(Dv(this.node,l,"Start"),(u,{success:d})=>Dv(this.node,u,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const od=new WeakMap,Of=new WeakMap,jM=n=>{const a=od.get(n.target);a&&a(n)},VM=n=>{n.forEach(jM)};function zM(s){var l=s,{root:n}=l,a=et(l,["root"]);const u=n||document;Of.has(u)||Of.set(u,{});const d=Of.get(u),f=JSON.stringify(a);return d[f]||(d[f]=new IntersectionObserver(VM,N({root:n},a))),d[f]}function LM(n,a,s){const l=zM(a);return od.set(n,s),l.observe(n),()=>{od.delete(n),l.unobserve(n)}}const BM={some:0,all:1};class UM extends _a{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:l,amount:u="some",once:d}=a,f={root:s?s.current:void 0,rootMargin:l,threshold:typeof u=="number"?u:BM[u]},h=m=>{const{isIntersecting:p}=m;if(this.isInView===p||(this.isInView=p,d&&!p&&this.hasEnteredView))return;p&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",p);const{onViewportEnter:g,onViewportLeave:v}=this.node.getProps(),b=p?g:v;b&&b(m)};return LM(this.node.current,f,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver=="undefined")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(kM(a,s))&&this.startObserver()}unmount(){}}function kM({viewport:n={}},{viewport:a={}}={}){return s=>n[s]!==a[s]}const HM={inView:{Feature:UM},tap:{Feature:_M},focus:{Feature:NM},hover:{Feature:OM}},PM={layout:{ProjectionNode:Gb,MeasureLayout:Vb}},ud={current:null},Yb={current:!1};function GM(){if(Yb.current=!0,!!Ed)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),a=()=>ud.current=n.matches;n.addListener(a),a()}else ud.current=!1}const YM=new WeakMap;function qM(n,a,s){for(const l in a){const u=a[l],d=s[l];if(Te(u))n.addValue(l,u);else if(Te(d))n.addValue(l,fs(u,{owner:n}));else if(d!==u)if(n.hasValue(l)){const f=n.getValue(l);f.liveStyle===!0?f.jump(u):f.hasAnimated||f.set(u)}else{const f=n.getStaticValue(l);n.addValue(l,fs(f!==void 0?f:u,{owner:n}))}}for(const l in s)a[l]===void 0&&n.removeValue(l);return a}const Ov=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class XM{scrapeMotionValuesFromProps(a,s,l){return{}}constructor({parent:a,props:s,presenceContext:l,reducedMotionConfig:u,blockInitialAnimation:d,visualState:f},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Yd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const T=je.now();this.renderScheduledAt<T&&(this.renderScheduledAt=T,Ft.render(this.render,!1,!0))};const{latestValues:m,renderState:p}=f;this.latestValues=m,this.baseTarget=N({},m),this.initialValues=s.initial?N({},m):{},this.renderState=p,this.parent=a,this.props=s,this.presenceContext=l,this.depth=a?a.depth+1:0,this.reducedMotionConfig=u,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=Xo(s),this.isVariantNode=cb(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const b=this.scrapeMotionValuesFromProps(s,{},this),{willChange:g}=b,v=et(b,["willChange"]);for(const T in v){const C=v[T];m[T]!==void 0&&Te(C)&&C.set(m[T],!1)}}mount(a){this.current=a,YM.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,l)=>this.bindToMotionValue(l,s)),Yb.current||GM(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ud.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ca(this.notifyUpdate),Ca(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const l=gs.has(a);l&&this.onBindTransform&&this.onBindTransform();const u=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Ft.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{u(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in ds){const s=ds[a];if(!s)continue;const{isEnabled:l,Feature:u}=s;if(!this.features[a]&&u&&l(this.props)&&(this.features[a]=new u(this)),this.features[a]){const d=this.features[a];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let l=0;l<Ov.length;l++){const u=Ov[l];this.propEventSubscriptions[u]&&(this.propEventSubscriptions[u](),delete this.propEventSubscriptions[u]);const d="on"+u,f=a[d];f&&(this.propEventSubscriptions[u]=this.on(u,f))}this.prevMotionValues=qM(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const l=this.values.get(a);s!==l&&(l&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let l=this.values.get(a);return l===void 0&&s!==void 0&&(l=fs(s===null?void 0:s,{owner:this}),this.addValue(a,l)),l}readValue(a,s){var u;let l=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:(u=this.getBaseTargetFromProps(this.props,a))!=null?u:this.readValueFromInstance(this.current,a,this.options);return l!=null&&(typeof l=="string"&&(y0(l)||b0(l))?l=parseFloat(l):!hE(l)&&Ra.test(s)&&(l=eb(a,s)),this.setBaseTarget(a,Te(l)?l.get():l)),Te(l)?l.get():l}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var d;const{initial:s}=this.props;let l;if(typeof s=="string"||typeof s=="object"){const f=th(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(l=f[a])}if(s&&l!==void 0)return l;const u=this.getBaseTargetFromProps(this.props,a);return u!==void 0&&!Te(u)?u:this.initialValues[a]!==void 0&&l===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new Nd),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class qb extends XM{constructor(){super(...arguments),this.KeyframeResolver=eE}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:l}){delete s[a],delete l[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;Te(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Xb(n,{style:a,vars:s},l,u){Object.assign(n.style,a,u&&u.getProjectionStyles(l));for(const d in s)n.style.setProperty(d,s[d])}function KM(n){return window.getComputedStyle(n)}class ZM extends qb{constructor(){super(...arguments),this.type="html",this.renderInstance=Xb}readValueFromInstance(a,s){var l;if(gs.has(s))return(l=this.projection)!=null&&l.isProjecting?If(s):xA(a,s);{const u=KM(a),d=(Vd(s)?u.getPropertyValue(s):u[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:s}){return Ob(a,s)}build(a,s,l){Wd(a,s,l.transformTemplate)}scrapeMotionValuesFromProps(a,s,l){return eh(a,s,l)}}const Kb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function QM(n,a,s,l){Xb(n,a,void 0,l);for(const u in a.attrs)n.setAttribute(Kb.has(u)?u:Id(u),a.attrs[u])}class FM extends qb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ee}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(gs.has(s)){const l=tb(s);return l&&l.default||0}return s=Kb.has(s)?s:Id(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,l){return xb(a,s,l)}build(a,s,l){gb(a,s,this.isSVGTag,l.transformTemplate,l.style)}renderInstance(a,s,l,u){QM(a,s,l,u)}mount(a){this.isSVGTag=vb(a.tagName),super.mount(a)}}const IM=(n,a)=>Jd(n)?new FM(a):new ZM(a,{allowProjection:n!==w.Fragment}),WM=QE(N(N(N(N({},x2),HM),DM),PM),IM),vn=bE(WM);function Nv(n,a){if(typeof n=="function")return n(a);n!=null&&(n.current=a)}function $M(...n){return a=>{let s=!1;const l=n.map(u=>{const d=Nv(u,a);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let u=0;u<l.length;u++){const d=l[u];typeof d=="function"?d():Nv(n[u],null)}}}}function ie(...n){return w.useCallback($M(...n),n)}function Br(n){const a=JM(n),s=w.forwardRef((l,u)=>{const p=l,{children:d}=p,f=et(p,["children"]),h=w.Children.toArray(d),m=h.find(eC);if(m){const g=m.props.children,v=h.map(b=>b===m?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:b);return S.jsx(a,K(N({},f),{ref:u,children:w.isValidElement(g)?w.cloneElement(g,void 0,v):null}))}return S.jsx(a,K(N({},f),{ref:u,children:d}))});return s.displayName=`${n}.Slot`,s}var Zb=Br("Slot");function JM(n){const a=w.forwardRef((s,l)=>{const m=s,{children:u}=m,d=et(m,["children"]),f=w.isValidElement(u)?aC(u):void 0,h=ie(f,l);if(w.isValidElement(u)){const p=nC(d,u.props);return u.type!==w.Fragment&&(p.ref=h),w.cloneElement(u,p)}return w.Children.count(u)>1?w.Children.only(null):null});return a.displayName=`${n}.SlotClone`,a}var tC=Symbol("radix.slottable");function eC(n){return w.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===tC}function nC(n,a){const s=N({},a);for(const l in a){const u=n[l],d=a[l];/^on[A-Z]/.test(l)?u&&d?s[l]=(...h)=>{const m=d(...h);return u(...h),m}:u&&(s[l]=u):l==="style"?s[l]=N(N({},u),d):l==="className"&&(s[l]=[u,d].filter(Boolean).join(" "))}return N(N({},n),s)}function aC(n){var l,u;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Qb(n){var a,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(a=0;a<u;a++)n[a]&&(s=Qb(n[a]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function Fb(){for(var n,a,s=0,l="",u=arguments.length;s<u;s++)(n=arguments[s])&&(a=Qb(n))&&(l&&(l+=" "),l+=a);return l}const _v=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,jv=Fb,Ib=(n,a)=>s=>{var l;if((a==null?void 0:a.variants)==null)return jv(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:u,defaultVariants:d}=a,f=Object.keys(u).map(p=>{const g=s==null?void 0:s[p],v=d==null?void 0:d[p];if(g===null)return null;const b=_v(g)||_v(v);return u[p][b]}),h=s&&Object.entries(s).reduce((p,g)=>{let[v,b]=g;return b===void 0||(p[v]=b),p},{}),m=a==null||(l=a.compoundVariants)===null||l===void 0?void 0:l.reduce((p,g)=>{let C=g,{class:v,className:b}=C,T=et(C,["class","className"]);return Object.entries(T).every(M=>{let[A,E]=M;return Array.isArray(E)?E.includes(N(N({},d),h)[A]):N(N({},d),h)[A]===E})?[...p,v,b]:p},[]);return jv(n,f,m,s==null?void 0:s.class,s==null?void 0:s.className)},ah="-",iC=n=>{const a=rC(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const h=f.split(ah);return h[0]===""&&h.length!==1&&h.shift(),Wb(h,a)||sC(f)},getConflictingClassGroupIds:(f,h)=>{const m=s[f]||[];return h&&l[f]?[...m,...l[f]]:m}}},Wb=(n,a)=>{var f;if(n.length===0)return a.classGroupId;const s=n[0],l=a.nextPart.get(s),u=l?Wb(n.slice(1),l):void 0;if(u)return u;if(a.validators.length===0)return;const d=n.join(ah);return(f=a.validators.find(({validator:h})=>h(d)))==null?void 0:f.classGroupId},Vv=/^\[(.+)\]$/,sC=n=>{if(Vv.test(n)){const a=Vv.exec(n)[1],s=a==null?void 0:a.substring(0,a.indexOf(":"));if(s)return"arbitrary.."+s}},rC=n=>{const{theme:a,classGroups:s}=n,l={nextPart:new Map,validators:[]};for(const u in s)cd(s[u],l,u,a);return l},cd=(n,a,s,l)=>{n.forEach(u=>{if(typeof u=="string"){const d=u===""?a:zv(a,u);d.classGroupId=s;return}if(typeof u=="function"){if(lC(u)){cd(u(l),a,s,l);return}a.validators.push({validator:u,classGroupId:s});return}Object.entries(u).forEach(([d,f])=>{cd(f,zv(a,d),s,l)})})},zv=(n,a)=>{let s=n;return a.split(ah).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},lC=n=>n.isThemeGetter,oC=n=>{if(n<1)return{get:()=>{},set:()=>{}};let a=0,s=new Map,l=new Map;const u=(d,f)=>{s.set(d,f),a++,a>n&&(a=0,l=s,s=new Map)};return{get(d){let f=s.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return u(d,f),f},set(d,f){s.has(d)?s.set(d,f):u(d,f)}}},fd="!",dd=":",uC=dd.length,cC=n=>{const{prefix:a,experimentalParseClassName:s}=n;let l=u=>{const d=[];let f=0,h=0,m=0,p;for(let C=0;C<u.length;C++){let M=u[C];if(f===0&&h===0){if(M===dd){d.push(u.slice(m,C)),m=C+uC;continue}if(M==="/"){p=C;continue}}M==="["?f++:M==="]"?f--:M==="("?h++:M===")"&&h--}const g=d.length===0?u:u.substring(m),v=fC(g),b=v!==g,T=p&&p>m?p-m:void 0;return{modifiers:d,hasImportantModifier:b,baseClassName:v,maybePostfixModifierPosition:T}};if(a){const u=a+dd,d=l;l=f=>f.startsWith(u)?d(f.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:f,maybePostfixModifierPosition:void 0}}if(s){const u=l;l=d=>s({className:d,parseClassName:u})}return l},fC=n=>n.endsWith(fd)?n.substring(0,n.length-1):n.startsWith(fd)?n.substring(1):n,dC=n=>{const a=Object.fromEntries(n.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;const u=[];let d=[];return l.forEach(f=>{f[0]==="["||a[f]?(u.push(...d.sort(),f),d=[]):d.push(f)}),u.push(...d.sort()),u}},hC=n=>N({cache:oC(n.cacheSize),parseClassName:cC(n),sortModifiers:dC(n)},iC(n)),mC=/\s+/,pC=(n,a)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:u,sortModifiers:d}=a,f=[],h=n.trim().split(mC);let m="";for(let p=h.length-1;p>=0;p-=1){const g=h[p],{isExternal:v,modifiers:b,hasImportantModifier:T,baseClassName:C,maybePostfixModifierPosition:M}=s(g);if(v){m=g+(m.length>0?" "+m:m);continue}let A=!!M,E=l(A?C.substring(0,M):C);if(!E){if(!A){m=g+(m.length>0?" "+m:m);continue}if(E=l(C),!E){m=g+(m.length>0?" "+m:m);continue}A=!1}const j=d(b).join(":"),O=T?j+fd:j,z=O+E;if(f.includes(z))continue;f.push(z);const _=u(E,A);for(let I=0;I<_.length;++I){const tt=_[I];f.push(O+tt)}m=g+(m.length>0?" "+m:m)}return m};function gC(){let n=0,a,s,l="";for(;n<arguments.length;)(a=arguments[n++])&&(s=$b(a))&&(l&&(l+=" "),l+=s);return l}const $b=n=>{if(typeof n=="string")return n;let a,s="";for(let l=0;l<n.length;l++)n[l]&&(a=$b(n[l]))&&(s&&(s+=" "),s+=a);return s};function yC(n,...a){let s,l,u,d=f;function f(m){const p=a.reduce((g,v)=>v(g),n());return s=hC(p),l=s.cache.get,u=s.cache.set,d=h,h(m)}function h(m){const p=l(m);if(p)return p;const g=pC(m,s);return u(m,g),g}return function(){return d(gC.apply(null,arguments))}}const le=n=>{const a=s=>s[n]||[];return a.isThemeGetter=!0,a},Jb=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,tx=/^\((?:(\w[\w-]*):)?(.+)\)$/i,vC=/^\d+\/\d+$/,bC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,SC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,TC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,wC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ji=n=>vC.test(n),At=n=>!!n&&!Number.isNaN(Number(n)),wa=n=>!!n&&Number.isInteger(Number(n)),Nf=n=>n.endsWith("%")&&At(n.slice(0,-1)),Zn=n=>bC.test(n),AC=()=>!0,EC=n=>xC.test(n)&&!SC.test(n),ex=()=>!1,MC=n=>TC.test(n),CC=n=>wC.test(n),RC=n=>!st(n)&&!rt(n),DC=n=>ys(n,ix,ex),st=n=>Jb.test(n),ei=n=>ys(n,sx,EC),_f=n=>ys(n,VC,At),Lv=n=>ys(n,nx,ex),OC=n=>ys(n,ax,CC),po=n=>ys(n,rx,MC),rt=n=>tx.test(n),Tr=n=>vs(n,sx),NC=n=>vs(n,zC),Bv=n=>vs(n,nx),_C=n=>vs(n,ix),jC=n=>vs(n,ax),go=n=>vs(n,rx,!0),ys=(n,a,s)=>{const l=Jb.exec(n);return l?l[1]?a(l[1]):s(l[2]):!1},vs=(n,a,s=!1)=>{const l=tx.exec(n);return l?l[1]?a(l[1]):s:!1},nx=n=>n==="position"||n==="percentage",ax=n=>n==="image"||n==="url",ix=n=>n==="length"||n==="size"||n==="bg-size",sx=n=>n==="length",VC=n=>n==="number",zC=n=>n==="family-name",rx=n=>n==="shadow",LC=()=>{const n=le("color"),a=le("font"),s=le("text"),l=le("font-weight"),u=le("tracking"),d=le("leading"),f=le("breakpoint"),h=le("container"),m=le("spacing"),p=le("radius"),g=le("shadow"),v=le("inset-shadow"),b=le("text-shadow"),T=le("drop-shadow"),C=le("blur"),M=le("perspective"),A=le("aspect"),E=le("ease"),j=le("animate"),O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],_=()=>[...z(),rt,st],I=()=>["auto","hidden","clip","visible","scroll"],tt=()=>["auto","contain","none"],Y=()=>[rt,st,m],W=()=>[Ji,"full","auto",...Y()],ft=()=>[wa,"none","subgrid",rt,st],ht=()=>["auto",{span:["full",wa,rt,st]},wa,rt,st],lt=()=>[wa,"auto",rt,st],bt=()=>["auto","min","max","fr",rt,st],Et=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ut=()=>["start","end","center","stretch","center-safe","end-safe"],B=()=>["auto",...Y()],q=()=>[Ji,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],P=()=>[n,rt,st],ct=()=>[...z(),Bv,Lv,{position:[rt,st]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],X=()=>["auto","cover","contain",_C,DC,{size:[rt,st]}],J=()=>[Nf,Tr,ei],$=()=>["","none","full",p,rt,st],at=()=>["",At,Tr,ei],vt=()=>["solid","dashed","dotted","double"],dt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],xt=()=>[At,Nf,Bv,Lv],nt=()=>["","none",C,rt,st],Tt=()=>["none",At,rt,st],qt=()=>["none",At,rt,st],Ot=()=>[At,rt,st],Ct=()=>[Ji,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Zn],breakpoint:[Zn],color:[AC],container:[Zn],"drop-shadow":[Zn],ease:["in","out","in-out"],font:[RC],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Zn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Zn],shadow:[Zn],spacing:["px",At],text:[Zn],"text-shadow":[Zn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ji,st,rt,A]}],container:["container"],columns:[{columns:[At,st,rt,h]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:_()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:tt()}],"overscroll-x":[{"overscroll-x":tt()}],"overscroll-y":[{"overscroll-y":tt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:W()}],"inset-x":[{"inset-x":W()}],"inset-y":[{"inset-y":W()}],start:[{start:W()}],end:[{end:W()}],top:[{top:W()}],right:[{right:W()}],bottom:[{bottom:W()}],left:[{left:W()}],visibility:["visible","invisible","collapse"],z:[{z:[wa,"auto",rt,st]}],basis:[{basis:[Ji,"full","auto",h,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[At,Ji,"auto","initial","none",st]}],grow:[{grow:["",At,rt,st]}],shrink:[{shrink:["",At,rt,st]}],order:[{order:[wa,"first","last","none",rt,st]}],"grid-cols":[{"grid-cols":ft()}],"col-start-end":[{col:ht()}],"col-start":[{"col-start":lt()}],"col-end":[{"col-end":lt()}],"grid-rows":[{"grid-rows":ft()}],"row-start-end":[{row:ht()}],"row-start":[{"row-start":lt()}],"row-end":[{"row-end":lt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":bt()}],"auto-rows":[{"auto-rows":bt()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...Et(),"normal"]}],"justify-items":[{"justify-items":[...ut(),"normal"]}],"justify-self":[{"justify-self":["auto",...ut()]}],"align-content":[{content:["normal",...Et()]}],"align-items":[{items:[...ut(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ut(),{baseline:["","last"]}]}],"place-content":[{"place-content":Et()}],"place-items":[{"place-items":[...ut(),"baseline"]}],"place-self":[{"place-self":["auto",...ut()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:B()}],mx:[{mx:B()}],my:[{my:B()}],ms:[{ms:B()}],me:[{me:B()}],mt:[{mt:B()}],mr:[{mr:B()}],mb:[{mb:B()}],ml:[{ml:B()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[h,"screen",...q()]}],"min-w":[{"min-w":[h,"screen","none",...q()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[f]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",s,Tr,ei]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,rt,_f]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Nf,st]}],"font-family":[{font:[NC,st,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,rt,st]}],"line-clamp":[{"line-clamp":[At,"none",rt,_f]}],leading:[{leading:[d,...Y()]}],"list-image":[{"list-image":["none",rt,st]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",rt,st]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...vt(),"wavy"]}],"text-decoration-thickness":[{decoration:[At,"from-font","auto",rt,ei]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[At,"auto",rt,st]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",rt,st]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",rt,st]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ct()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:X()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},wa,rt,st],radial:["",rt,st],conic:[wa,rt,st]},jC,OC]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:J()}],"gradient-via-pos":[{via:J()}],"gradient-to-pos":[{to:J()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:at()}],"border-w-x":[{"border-x":at()}],"border-w-y":[{"border-y":at()}],"border-w-s":[{"border-s":at()}],"border-w-e":[{"border-e":at()}],"border-w-t":[{"border-t":at()}],"border-w-r":[{"border-r":at()}],"border-w-b":[{"border-b":at()}],"border-w-l":[{"border-l":at()}],"divide-x":[{"divide-x":at()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":at()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...vt(),"hidden","none"]}],"divide-style":[{divide:[...vt(),"hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:[...vt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[At,rt,st]}],"outline-w":[{outline:["",At,Tr,ei]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",g,go,po]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",v,go,po]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:at()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[At,ei]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":at()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",b,go,po]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[At,rt,st]}],"mix-blend":[{"mix-blend":[...dt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":dt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[At]}],"mask-image-linear-from-pos":[{"mask-linear-from":xt()}],"mask-image-linear-to-pos":[{"mask-linear-to":xt()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":xt()}],"mask-image-t-to-pos":[{"mask-t-to":xt()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":xt()}],"mask-image-r-to-pos":[{"mask-r-to":xt()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":xt()}],"mask-image-b-to-pos":[{"mask-b-to":xt()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":xt()}],"mask-image-l-to-pos":[{"mask-l-to":xt()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":xt()}],"mask-image-x-to-pos":[{"mask-x-to":xt()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":xt()}],"mask-image-y-to-pos":[{"mask-y-to":xt()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[rt,st]}],"mask-image-radial-from-pos":[{"mask-radial-from":xt()}],"mask-image-radial-to-pos":[{"mask-radial-to":xt()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":z()}],"mask-image-conic-pos":[{"mask-conic":[At]}],"mask-image-conic-from-pos":[{"mask-conic-from":xt()}],"mask-image-conic-to-pos":[{"mask-conic-to":xt()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ct()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:X()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",rt,st]}],filter:[{filter:["","none",rt,st]}],blur:[{blur:nt()}],brightness:[{brightness:[At,rt,st]}],contrast:[{contrast:[At,rt,st]}],"drop-shadow":[{"drop-shadow":["","none",T,go,po]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",At,rt,st]}],"hue-rotate":[{"hue-rotate":[At,rt,st]}],invert:[{invert:["",At,rt,st]}],saturate:[{saturate:[At,rt,st]}],sepia:[{sepia:["",At,rt,st]}],"backdrop-filter":[{"backdrop-filter":["","none",rt,st]}],"backdrop-blur":[{"backdrop-blur":nt()}],"backdrop-brightness":[{"backdrop-brightness":[At,rt,st]}],"backdrop-contrast":[{"backdrop-contrast":[At,rt,st]}],"backdrop-grayscale":[{"backdrop-grayscale":["",At,rt,st]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[At,rt,st]}],"backdrop-invert":[{"backdrop-invert":["",At,rt,st]}],"backdrop-opacity":[{"backdrop-opacity":[At,rt,st]}],"backdrop-saturate":[{"backdrop-saturate":[At,rt,st]}],"backdrop-sepia":[{"backdrop-sepia":["",At,rt,st]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",rt,st]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[At,"initial",rt,st]}],ease:[{ease:["linear","initial",E,rt,st]}],delay:[{delay:[At,rt,st]}],animate:[{animate:["none",j,rt,st]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[M,rt,st]}],"perspective-origin":[{"perspective-origin":_()}],rotate:[{rotate:Tt()}],"rotate-x":[{"rotate-x":Tt()}],"rotate-y":[{"rotate-y":Tt()}],"rotate-z":[{"rotate-z":Tt()}],scale:[{scale:qt()}],"scale-x":[{"scale-x":qt()}],"scale-y":[{"scale-y":qt()}],"scale-z":[{"scale-z":qt()}],"scale-3d":["scale-3d"],skew:[{skew:Ot()}],"skew-x":[{"skew-x":Ot()}],"skew-y":[{"skew-y":Ot()}],transform:[{transform:[rt,st,"","none","gpu","cpu"]}],"transform-origin":[{origin:_()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ct()}],"translate-x":[{"translate-x":Ct()}],"translate-y":[{"translate-y":Ct()}],"translate-z":[{"translate-z":Ct()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",rt,st]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",rt,st]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[At,Tr,ei,_f]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},BC=yC(LC);function Kt(...n){return BC(Fb(n))}const UC=Ib("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ni(d){var f=d,{className:n,variant:a,size:s,asChild:l=!1}=f,u=et(f,["className","variant","size","asChild"]);const h=l?Zb:"button";return S.jsx(h,N({"data-slot":"button",className:Kt(UC({variant:a,size:s,className:n}))},u))}function bn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card",className:Kt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n)},a))}function xn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-header",className:Kt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n)},a))}function Sn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-title",className:Kt("leading-none font-semibold",n)},a))}function Tn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-content",className:Kt("px-6",n)},a))}function ts(l){var u=l,{className:n,type:a}=u,s=et(u,["className","type"]);return S.jsx("input",N({type:a,"data-slot":"input",className:Kt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n)},s))}var qr=p0();const kC=h0(qr);var HC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pt=HC.reduce((n,a)=>{const s=Br(`Primitive.${a}`),l=w.forwardRef((u,d)=>{const p=u,{asChild:f}=p,h=et(p,["asChild"]),m=f?s:a;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(m,K(N({},h),{ref:d}))});return l.displayName=`Primitive.${a}`,K(N({},n),{[a]:l})},{});function PC(n,a){n&&qr.flushSync(()=>n.dispatchEvent(a))}var GC="Label",lx=w.forwardRef((n,a)=>S.jsx(Pt.label,K(N({},n),{ref:a,onMouseDown:s=>{var u;s.target.closest("button, input, select, textarea")||((u=n.onMouseDown)==null||u.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}})));lx.displayName=GC;var YC=lx;function ai(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(YC,N({"data-slot":"label",className:Kt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",n)},a))}function Uv(n,[a,s]){return Math.min(s,Math.max(a,n))}function Bt(n,a,{checkForDefaultPrevented:s=!0}={}){return function(u){if(n==null||n(u),s===!1||!u.defaultPrevented)return a==null?void 0:a(u)}}function Xr(n,a=[]){let s=[];function l(d,f){const h=w.createContext(f),m=s.length;s=[...s,f];const p=v=>{var j;const E=v,{scope:b,children:T}=E,C=et(E,["scope","children"]),M=((j=b==null?void 0:b[n])==null?void 0:j[m])||h,A=w.useMemo(()=>C,Object.values(C));return S.jsx(M.Provider,{value:A,children:T})};p.displayName=d+"Provider";function g(v,b){var M;const T=((M=b==null?void 0:b[n])==null?void 0:M[m])||h,C=w.useContext(T);if(C)return C;if(f!==void 0)return f;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[p,g]}const u=()=>{const d=s.map(f=>w.createContext(f));return function(h){const m=(h==null?void 0:h[n])||d;return w.useMemo(()=>({[`__scope${n}`]:K(N({},h),{[n]:m})}),[h,m])}};return u.scopeName=n,[l,qC(u,...a)]}function qC(...n){const a=n[0];if(n.length===1)return a;const s=()=>{const l=n.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(d){const f=l.reduce((h,{useScope:m,scopeName:p})=>{const v=m(d)[`__scope${p}`];return N(N({},h),v)},{});return w.useMemo(()=>({[`__scope${a.scopeName}`]:f}),[f])}};return s.scopeName=a.scopeName,s}function ox(n){const a=n+"CollectionProvider",[s,l]=Xr(a),[u,d]=s(a,{collectionRef:{current:null},itemMap:new Map}),f=M=>{const{scope:A,children:E}=M,j=Aa.useRef(null),O=Aa.useRef(new Map).current;return S.jsx(u,{scope:A,itemMap:O,collectionRef:j,children:E})};f.displayName=a;const h=n+"CollectionSlot",m=Br(h),p=Aa.forwardRef((M,A)=>{const{scope:E,children:j}=M,O=d(h,E),z=ie(A,O.collectionRef);return S.jsx(m,{ref:z,children:j})});p.displayName=h;const g=n+"CollectionItemSlot",v="data-radix-collection-item",b=Br(g),T=Aa.forwardRef((M,A)=>{const tt=M,{scope:E,children:j}=tt,O=et(tt,["scope","children"]),z=Aa.useRef(null),_=ie(A,z),I=d(g,E);return Aa.useEffect(()=>(I.itemMap.set(z,N({ref:z},O)),()=>void I.itemMap.delete(z))),S.jsx(b,{[v]:"",ref:_,children:j})});T.displayName=g;function C(M){const A=d(n+"CollectionConsumer",M);return Aa.useCallback(()=>{const j=A.collectionRef.current;if(!j)return[];const O=Array.from(j.querySelectorAll(`[${v}]`));return Array.from(A.itemMap.values()).sort((I,tt)=>O.indexOf(I.ref.current)-O.indexOf(tt.ref.current))},[A.collectionRef,A.itemMap])}return[{Provider:f,Slot:p,ItemSlot:T},C,l]}var XC=w.createContext(void 0);function ih(n){const a=w.useContext(XC);return n||a||"ltr"}function Da(n){const a=w.useRef(n);return w.useEffect(()=>{a.current=n}),w.useMemo(()=>(...s)=>{var l;return(l=a.current)==null?void 0:l.call(a,...s)},[])}function KC(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n);w.useEffect(()=>{const l=u=>{u.key==="Escape"&&s(u)};return a.addEventListener("keydown",l,{capture:!0}),()=>a.removeEventListener("keydown",l,{capture:!0})},[s,a])}var ZC="DismissableLayer",hd="dismissableLayer.update",QC="dismissableLayer.pointerDownOutside",FC="dismissableLayer.focusOutside",kv,ux=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),cx=w.forwardRef((n,a)=>{var Y;const tt=n,{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:h}=tt,m=et(tt,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),p=w.useContext(ux),[g,v]=w.useState(null),b=(Y=g==null?void 0:g.ownerDocument)!=null?Y:globalThis==null?void 0:globalThis.document,[,T]=w.useState({}),C=ie(a,W=>v(W)),M=Array.from(p.layers),[A]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),E=M.indexOf(A),j=g?M.indexOf(g):-1,O=p.layersWithOutsidePointerEventsDisabled.size>0,z=j>=E,_=$C(W=>{const ft=W.target,ht=[...p.branches].some(lt=>lt.contains(ft));!z||ht||(u==null||u(W),f==null||f(W),W.defaultPrevented||h==null||h())},b),I=JC(W=>{const ft=W.target;[...p.branches].some(lt=>lt.contains(ft))||(d==null||d(W),f==null||f(W),W.defaultPrevented||h==null||h())},b);return KC(W=>{j===p.layers.size-1&&(l==null||l(W),!W.defaultPrevented&&h&&(W.preventDefault(),h()))},b),w.useEffect(()=>{if(g)return s&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(kv=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(g)),p.layers.add(g),Hv(),()=>{s&&p.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=kv)}},[g,b,s,p]),w.useEffect(()=>()=>{g&&(p.layers.delete(g),p.layersWithOutsidePointerEventsDisabled.delete(g),Hv())},[g,p]),w.useEffect(()=>{const W=()=>T({});return document.addEventListener(hd,W),()=>document.removeEventListener(hd,W)},[]),S.jsx(Pt.div,K(N({},m),{ref:C,style:N({pointerEvents:O?z?"auto":"none":void 0},n.style),onFocusCapture:Bt(n.onFocusCapture,I.onFocusCapture),onBlurCapture:Bt(n.onBlurCapture,I.onBlurCapture),onPointerDownCapture:Bt(n.onPointerDownCapture,_.onPointerDownCapture)}))});cx.displayName=ZC;var IC="DismissableLayerBranch",WC=w.forwardRef((n,a)=>{const s=w.useContext(ux),l=w.useRef(null),u=ie(a,l);return w.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),S.jsx(Pt.div,K(N({},n),{ref:u}))});WC.displayName=IC;function $C(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n),l=w.useRef(!1),u=w.useRef(()=>{});return w.useEffect(()=>{const d=h=>{if(h.target&&!l.current){let m=function(){fx(QC,s,p,{discrete:!0})};const p={originalEvent:h};h.pointerType==="touch"?(a.removeEventListener("click",u.current),u.current=m,a.addEventListener("click",u.current,{once:!0})):m()}else a.removeEventListener("click",u.current);l.current=!1},f=window.setTimeout(()=>{a.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(f),a.removeEventListener("pointerdown",d),a.removeEventListener("click",u.current)}},[a,s]),{onPointerDownCapture:()=>l.current=!0}}function JC(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n),l=w.useRef(!1);return w.useEffect(()=>{const u=d=>{d.target&&!l.current&&fx(FC,s,{originalEvent:d},{discrete:!1})};return a.addEventListener("focusin",u),()=>a.removeEventListener("focusin",u)},[a,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Hv(){const n=new CustomEvent(hd);document.dispatchEvent(n)}function fx(n,a,s,{discrete:l}){const u=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});a&&u.addEventListener(n,a,{once:!0}),l?PC(u,d):u.dispatchEvent(d)}var jf=0;function tR(){w.useEffect(()=>{var a,s;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(a=n[0])!=null?a:Pv()),document.body.insertAdjacentElement("beforeend",(s=n[1])!=null?s:Pv()),jf++,()=>{jf===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),jf--}},[])}function Pv(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Vf="focusScope.autoFocusOnMount",zf="focusScope.autoFocusOnUnmount",Gv={bubbles:!1,cancelable:!0},eR="FocusScope",dx=w.forwardRef((n,a)=>{const M=n,{loop:s=!1,trapped:l=!1,onMountAutoFocus:u,onUnmountAutoFocus:d}=M,f=et(M,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[h,m]=w.useState(null),p=Da(u),g=Da(d),v=w.useRef(null),b=ie(a,A=>m(A)),T=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(l){let A=function(z){if(T.paused||!h)return;const _=z.target;h.contains(_)?v.current=_:Ma(v.current,{select:!0})},E=function(z){if(T.paused||!h)return;const _=z.relatedTarget;_!==null&&(h.contains(_)||Ma(v.current,{select:!0}))},j=function(z){if(document.activeElement===document.body)for(const I of z)I.removedNodes.length>0&&Ma(h)};document.addEventListener("focusin",A),document.addEventListener("focusout",E);const O=new MutationObserver(j);return h&&O.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",A),document.removeEventListener("focusout",E),O.disconnect()}}},[l,h,T.paused]),w.useEffect(()=>{if(h){qv.add(T);const A=document.activeElement;if(!h.contains(A)){const j=new CustomEvent(Vf,Gv);h.addEventListener(Vf,p),h.dispatchEvent(j),j.defaultPrevented||(nR(lR(hx(h)),{select:!0}),document.activeElement===A&&Ma(h))}return()=>{h.removeEventListener(Vf,p),setTimeout(()=>{const j=new CustomEvent(zf,Gv);h.addEventListener(zf,g),h.dispatchEvent(j),j.defaultPrevented||Ma(A!=null?A:document.body,{select:!0}),h.removeEventListener(zf,g),qv.remove(T)},0)}}},[h,p,g,T]);const C=w.useCallback(A=>{if(!s&&!l||T.paused)return;const E=A.key==="Tab"&&!A.altKey&&!A.ctrlKey&&!A.metaKey,j=document.activeElement;if(E&&j){const O=A.currentTarget,[z,_]=aR(O);z&&_?!A.shiftKey&&j===_?(A.preventDefault(),s&&Ma(z,{select:!0})):A.shiftKey&&j===z&&(A.preventDefault(),s&&Ma(_,{select:!0})):j===O&&A.preventDefault()}},[s,l,T.paused]);return S.jsx(Pt.div,K(N({tabIndex:-1},f),{ref:b,onKeyDown:C}))});dx.displayName=eR;function nR(n,{select:a=!1}={}){const s=document.activeElement;for(const l of n)if(Ma(l,{select:a}),document.activeElement!==s)return}function aR(n){const a=hx(n),s=Yv(a,n),l=Yv(a.reverse(),n);return[s,l]}function hx(n){const a=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const u=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||u?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)a.push(s.currentNode);return a}function Yv(n,a){for(const s of n)if(!iR(s,{upTo:a}))return s}function iR(n,{upTo:a}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(a!==void 0&&n===a)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function sR(n){return n instanceof HTMLInputElement&&"select"in n}function Ma(n,{select:a=!1}={}){if(n&&n.focus){const s=document.activeElement;n.focus({preventScroll:!0}),n!==s&&sR(n)&&a&&n.select()}}var qv=rR();function rR(){let n=[];return{add(a){const s=n[0];a!==s&&(s==null||s.pause()),n=Xv(n,a),n.unshift(a)},remove(a){var s;n=Xv(n,a),(s=n[0])==null||s.resume()}}}function Xv(n,a){const s=[...n],l=s.indexOf(a);return l!==-1&&s.splice(l,1),s}function lR(n){return n.filter(a=>a.tagName!=="A")}var we=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},oR=m0[" useId ".trim().toString()]||(()=>{}),uR=0;function Kr(n){const[a,s]=w.useState(oR());return we(()=>{s(l=>l!=null?l:String(uR++))},[n]),n||(a?`radix-${a}`:"")}const cR=["top","right","bottom","left"],Oa=Math.min,Ye=Math.max,Bo=Math.round,yo=Math.floor,Dn=n=>({x:n,y:n}),fR={left:"right",right:"left",bottom:"top",top:"bottom"},dR={start:"end",end:"start"};function md(n,a,s){return Ye(n,Oa(a,s))}function Wn(n,a){return typeof n=="function"?n(a):n}function $n(n){return n.split("-")[0]}function bs(n){return n.split("-")[1]}function sh(n){return n==="x"?"y":"x"}function rh(n){return n==="y"?"height":"width"}function Qn(n){return["top","bottom"].includes($n(n))?"y":"x"}function lh(n){return sh(Qn(n))}function hR(n,a,s){s===void 0&&(s=!1);const l=bs(n),u=lh(n),d=rh(u);let f=u==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return a.reference[d]>a.floating[d]&&(f=Uo(f)),[f,Uo(f)]}function mR(n){const a=Uo(n);return[pd(n),a,pd(a)]}function pd(n){return n.replace(/start|end/g,a=>dR[a])}function pR(n,a,s){const l=["left","right"],u=["right","left"],d=["top","bottom"],f=["bottom","top"];switch(n){case"top":case"bottom":return s?a?u:l:a?l:u;case"left":case"right":return a?d:f;default:return[]}}function gR(n,a,s,l){const u=bs(n);let d=pR($n(n),s==="start",l);return u&&(d=d.map(f=>f+"-"+u),a&&(d=d.concat(d.map(pd)))),d}function Uo(n){return n.replace(/left|right|bottom|top/g,a=>fR[a])}function yR(n){return N({top:0,right:0,bottom:0,left:0},n)}function mx(n){return typeof n!="number"?yR(n):{top:n,right:n,bottom:n,left:n}}function ko(n){const{x:a,y:s,width:l,height:u}=n;return{width:l,height:u,top:s,left:a,right:a+l,bottom:s+u,x:a,y:s}}function Kv(n,a,s){let{reference:l,floating:u}=n;const d=Qn(a),f=lh(a),h=rh(f),m=$n(a),p=d==="y",g=l.x+l.width/2-u.width/2,v=l.y+l.height/2-u.height/2,b=l[h]/2-u[h]/2;let T;switch(m){case"top":T={x:g,y:l.y-u.height};break;case"bottom":T={x:g,y:l.y+l.height};break;case"right":T={x:l.x+l.width,y:v};break;case"left":T={x:l.x-u.width,y:v};break;default:T={x:l.x,y:l.y}}switch(bs(a)){case"start":T[f]-=b*(s&&p?-1:1);break;case"end":T[f]+=b*(s&&p?-1:1);break}return T}const vR=(n,a,s)=>on(null,null,function*(){const{placement:l="bottom",strategy:u="absolute",middleware:d=[],platform:f}=s,h=d.filter(Boolean),m=yield f.isRTL==null?void 0:f.isRTL(a);let p=yield f.getElementRects({reference:n,floating:a,strategy:u}),{x:g,y:v}=Kv(p,l,m),b=l,T={},C=0;for(let M=0;M<h.length;M++){const{name:A,fn:E}=h[M],{x:j,y:O,data:z,reset:_}=yield E({x:g,y:v,initialPlacement:l,placement:b,strategy:u,middlewareData:T,rects:p,platform:f,elements:{reference:n,floating:a}});g=j!=null?j:g,v=O!=null?O:v,T=K(N({},T),{[A]:N(N({},T[A]),z)}),_&&C<=50&&(C++,typeof _=="object"&&(_.placement&&(b=_.placement),_.rects&&(p=_.rects===!0?yield f.getElementRects({reference:n,floating:a,strategy:u}):_.rects),{x:g,y:v}=Kv(p,b,m)),M=-1)}return{x:g,y:v,placement:b,strategy:u,middlewareData:T}});function Ur(n,a){return on(this,null,function*(){var s;a===void 0&&(a={});const{x:l,y:u,platform:d,rects:f,elements:h,strategy:m}=n,{boundary:p="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:b=!1,padding:T=0}=Wn(a,n),C=mx(T),A=h[b?v==="floating"?"reference":"floating":v],E=ko(yield d.getClippingRect({element:(s=yield d.isElement==null?void 0:d.isElement(A))==null||s?A:A.contextElement||(yield d.getDocumentElement==null?void 0:d.getDocumentElement(h.floating)),boundary:p,rootBoundary:g,strategy:m})),j=v==="floating"?{x:l,y:u,width:f.floating.width,height:f.floating.height}:f.reference,O=yield d.getOffsetParent==null?void 0:d.getOffsetParent(h.floating),z=(yield d.isElement==null?void 0:d.isElement(O))?(yield d.getScale==null?void 0:d.getScale(O))||{x:1,y:1}:{x:1,y:1},_=ko(d.convertOffsetParentRelativeRectToViewportRelativeRect?yield d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:j,offsetParent:O,strategy:m}):j);return{top:(E.top-_.top+C.top)/z.y,bottom:(_.bottom-E.bottom+C.bottom)/z.y,left:(E.left-_.left+C.left)/z.x,right:(_.right-E.right+C.right)/z.x}})}const bR=n=>({name:"arrow",options:n,fn(s){return on(this,null,function*(){const{x:l,y:u,placement:d,rects:f,platform:h,elements:m,middlewareData:p}=s,{element:g,padding:v=0}=Wn(n,s)||{};if(g==null)return{};const b=mx(v),T={x:l,y:u},C=lh(d),M=rh(C),A=yield h.getDimensions(g),E=C==="y",j=E?"top":"left",O=E?"bottom":"right",z=E?"clientHeight":"clientWidth",_=f.reference[M]+f.reference[C]-T[C]-f.floating[M],I=T[C]-f.reference[C],tt=yield h.getOffsetParent==null?void 0:h.getOffsetParent(g);let Y=tt?tt[z]:0;(!Y||!(yield h.isElement==null?void 0:h.isElement(tt)))&&(Y=m.floating[z]||f.floating[M]);const W=_/2-I/2,ft=Y/2-A[M]/2-1,ht=Oa(b[j],ft),lt=Oa(b[O],ft),bt=ht,Et=Y-A[M]-lt,ut=Y/2-A[M]/2+W,B=md(bt,ut,Et),q=!p.arrow&&bs(d)!=null&&ut!==B&&f.reference[M]/2-(ut<bt?ht:lt)-A[M]/2<0,P=q?ut<bt?ut-bt:ut-Et:0;return{[C]:T[C]+P,data:N({[C]:B,centerOffset:ut-B-P},q&&{alignmentOffset:P}),reset:q}})}}),xR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,fn(s){return on(this,null,function*(){var l,u;const{placement:d,middlewareData:f,rects:h,initialPlacement:m,platform:p,elements:g}=s,B=Wn(n,s),{mainAxis:v=!0,crossAxis:b=!0,fallbackPlacements:T,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:A=!0}=B,E=et(B,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((l=f.arrow)!=null&&l.alignmentOffset)return{};const j=$n(d),O=Qn(m),z=$n(m)===m,_=yield p.isRTL==null?void 0:p.isRTL(g.floating),I=T||(z||!A?[Uo(m)]:mR(m)),tt=M!=="none";!T&&tt&&I.push(...gR(m,A,M,_));const Y=[m,...I],W=yield Ur(s,E),ft=[];let ht=((u=f.flip)==null?void 0:u.overflows)||[];if(v&&ft.push(W[j]),b){const q=hR(d,h,_);ft.push(W[q[0]],W[q[1]])}if(ht=[...ht,{placement:d,overflows:ft}],!ft.every(q=>q<=0)){var lt,bt;const q=(((lt=f.flip)==null?void 0:lt.index)||0)+1,P=Y[q];if(P){var Et;const D=b==="alignment"?O!==Qn(P):!1,X=((Et=ht[0])==null?void 0:Et.overflows[0])>0;if(!D||X)return{data:{index:q,overflows:ht},reset:{placement:P}}}let ct=(bt=ht.filter(D=>D.overflows[0]<=0).sort((D,X)=>D.overflows[1]-X.overflows[1])[0])==null?void 0:bt.placement;if(!ct)switch(C){case"bestFit":{var ut;const D=(ut=ht.filter(X=>{if(tt){const J=Qn(X.placement);return J===O||J==="y"}return!0}).map(X=>[X.placement,X.overflows.filter(J=>J>0).reduce((J,$)=>J+$,0)]).sort((X,J)=>X[1]-J[1])[0])==null?void 0:ut[0];D&&(ct=D);break}case"initialPlacement":ct=m;break}if(d!==ct)return{reset:{placement:ct}}}return{}})}}};function Zv(n,a){return{top:n.top-a.height,right:n.right-a.width,bottom:n.bottom-a.height,left:n.left-a.width}}function Qv(n){return cR.some(a=>n[a]>=0)}const SR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,fn(s){return on(this,null,function*(){const{rects:l}=s,f=Wn(n,s),{strategy:u="referenceHidden"}=f,d=et(f,["strategy"]);switch(u){case"referenceHidden":{const h=yield Ur(s,K(N({},d),{elementContext:"reference"})),m=Zv(h,l.reference);return{data:{referenceHiddenOffsets:m,referenceHidden:Qv(m)}}}case"escaped":{const h=yield Ur(s,K(N({},d),{altBoundary:!0})),m=Zv(h,l.floating);return{data:{escapedOffsets:m,escaped:Qv(m)}}}default:return{}}})}}};function TR(n,a){return on(this,null,function*(){const{placement:s,platform:l,elements:u}=n,d=yield l.isRTL==null?void 0:l.isRTL(u.floating),f=$n(s),h=bs(s),m=Qn(s)==="y",p=["left","top"].includes(f)?-1:1,g=d&&m?-1:1,v=Wn(a,n);let{mainAxis:b,crossAxis:T,alignmentAxis:C}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return h&&typeof C=="number"&&(T=h==="end"?C*-1:C),m?{x:T*g,y:b*p}:{x:b*p,y:T*g}})}const wR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,fn(s){return on(this,null,function*(){var l,u;const{x:d,y:f,placement:h,middlewareData:m}=s,p=yield TR(s,n);return h===((l=m.offset)==null?void 0:l.placement)&&(u=m.arrow)!=null&&u.alignmentOffset?{}:{x:d+p.x,y:f+p.y,data:K(N({},p),{placement:h})}})}}},AR=function(n){return n===void 0&&(n={}),{name:"shift",options:n,fn(s){return on(this,null,function*(){const{x:l,y:u,placement:d}=s,E=Wn(n,s),{mainAxis:f=!0,crossAxis:h=!1,limiter:m={fn:j=>{let{x:O,y:z}=j;return{x:O,y:z}}}}=E,p=et(E,["mainAxis","crossAxis","limiter"]),g={x:l,y:u},v=yield Ur(s,p),b=Qn($n(d)),T=sh(b);let C=g[T],M=g[b];if(f){const j=T==="y"?"top":"left",O=T==="y"?"bottom":"right",z=C+v[j],_=C-v[O];C=md(z,C,_)}if(h){const j=b==="y"?"top":"left",O=b==="y"?"bottom":"right",z=M+v[j],_=M-v[O];M=md(z,M,_)}const A=m.fn(K(N({},s),{[T]:C,[b]:M}));return K(N({},A),{data:{x:A.x-l,y:A.y-u,enabled:{[T]:f,[b]:h}}})})}}},ER=function(n){return n===void 0&&(n={}),{options:n,fn(a){const{x:s,y:l,placement:u,rects:d,middlewareData:f}=a,{offset:h=0,mainAxis:m=!0,crossAxis:p=!0}=Wn(n,a),g={x:s,y:l},v=Qn(u),b=sh(v);let T=g[b],C=g[v];const M=Wn(h,a),A=typeof M=="number"?{mainAxis:M,crossAxis:0}:N({mainAxis:0,crossAxis:0},M);if(m){const O=b==="y"?"height":"width",z=d.reference[b]-d.floating[O]+A.mainAxis,_=d.reference[b]+d.reference[O]-A.mainAxis;T<z?T=z:T>_&&(T=_)}if(p){var E,j;const O=b==="y"?"width":"height",z=["top","left"].includes($n(u)),_=d.reference[v]-d.floating[O]+(z&&((E=f.offset)==null?void 0:E[v])||0)+(z?0:A.crossAxis),I=d.reference[v]+d.reference[O]+(z?0:((j=f.offset)==null?void 0:j[v])||0)-(z?A.crossAxis:0);C<_?C=_:C>I&&(C=I)}return{[b]:T,[v]:C}}}},MR=function(n){return n===void 0&&(n={}),{name:"size",options:n,fn(s){return on(this,null,function*(){var l,u;const{placement:d,rects:f,platform:h,elements:m}=s,ht=Wn(n,s),{apply:p=()=>{}}=ht,g=et(ht,["apply"]),v=yield Ur(s,g),b=$n(d),T=bs(d),C=Qn(d)==="y",{width:M,height:A}=f.floating;let E,j;b==="top"||b==="bottom"?(E=b,j=T===((yield h.isRTL==null?void 0:h.isRTL(m.floating))?"start":"end")?"left":"right"):(j=b,E=T==="end"?"top":"bottom");const O=A-v.top-v.bottom,z=M-v.left-v.right,_=Oa(A-v[E],O),I=Oa(M-v[j],z),tt=!s.middlewareData.shift;let Y=_,W=I;if((l=s.middlewareData.shift)!=null&&l.enabled.x&&(W=z),(u=s.middlewareData.shift)!=null&&u.enabled.y&&(Y=O),tt&&!T){const lt=Ye(v.left,0),bt=Ye(v.right,0),Et=Ye(v.top,0),ut=Ye(v.bottom,0);C?W=M-2*(lt!==0||bt!==0?lt+bt:Ye(v.left,v.right)):Y=A-2*(Et!==0||ut!==0?Et+ut:Ye(v.top,v.bottom))}yield p(K(N({},s),{availableWidth:W,availableHeight:Y}));const ft=yield h.getDimensions(m.floating);return M!==ft.width||A!==ft.height?{reset:{rects:!0}}:{}})}}};function Ko(){return typeof window!="undefined"}function xs(n){return px(n)?(n.nodeName||"").toLowerCase():"#document"}function qe(n){var a;return(n==null||(a=n.ownerDocument)==null?void 0:a.defaultView)||window}function Nn(n){var a;return(a=(px(n)?n.ownerDocument:n.document)||window.document)==null?void 0:a.documentElement}function px(n){return Ko()?n instanceof Node||n instanceof qe(n).Node:!1}function fn(n){return Ko()?n instanceof Element||n instanceof qe(n).Element:!1}function On(n){return Ko()?n instanceof HTMLElement||n instanceof qe(n).HTMLElement:!1}function Fv(n){return!Ko()||typeof ShadowRoot=="undefined"?!1:n instanceof ShadowRoot||n instanceof qe(n).ShadowRoot}function Zr(n){const{overflow:a,overflowX:s,overflowY:l,display:u}=dn(n);return/auto|scroll|overlay|hidden|clip/.test(a+l+s)&&!["inline","contents"].includes(u)}function CR(n){return["table","td","th"].includes(xs(n))}function Zo(n){return[":popover-open",":modal"].some(a=>{try{return n.matches(a)}catch(s){return!1}})}function oh(n){const a=uh(),s=fn(n)?dn(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function RR(n){let a=Na(n);for(;On(a)&&!hs(a);){if(oh(a))return a;if(Zo(a))return null;a=Na(a)}return null}function uh(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function hs(n){return["html","body","#document"].includes(xs(n))}function dn(n){return qe(n).getComputedStyle(n)}function Qo(n){return fn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Na(n){if(xs(n)==="html")return n;const a=n.assignedSlot||n.parentNode||Fv(n)&&n.host||Nn(n);return Fv(a)?a.host:a}function gx(n){const a=Na(n);return hs(a)?n.ownerDocument?n.ownerDocument.body:n.body:On(a)&&Zr(a)?a:gx(a)}function kr(n,a,s){var l;a===void 0&&(a=[]),s===void 0&&(s=!0);const u=gx(n),d=u===((l=n.ownerDocument)==null?void 0:l.body),f=qe(u);if(d){const h=gd(f);return a.concat(f,f.visualViewport||[],Zr(u)?u:[],h&&s?kr(h):[])}return a.concat(u,kr(u,[],s))}function gd(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function yx(n){const a=dn(n);let s=parseFloat(a.width)||0,l=parseFloat(a.height)||0;const u=On(n),d=u?n.offsetWidth:s,f=u?n.offsetHeight:l,h=Bo(s)!==d||Bo(l)!==f;return h&&(s=d,l=f),{width:s,height:l,$:h}}function ch(n){return fn(n)?n:n.contextElement}function us(n){const a=ch(n);if(!On(a))return Dn(1);const s=a.getBoundingClientRect(),{width:l,height:u,$:d}=yx(a);let f=(d?Bo(s.width):s.width)/l,h=(d?Bo(s.height):s.height)/u;return(!f||!Number.isFinite(f))&&(f=1),(!h||!Number.isFinite(h))&&(h=1),{x:f,y:h}}const DR=Dn(0);function vx(n){const a=qe(n);return!uh()||!a.visualViewport?DR:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function OR(n,a,s){return a===void 0&&(a=!1),!s||a&&s!==qe(n)?!1:a}function ui(n,a,s,l){a===void 0&&(a=!1),s===void 0&&(s=!1);const u=n.getBoundingClientRect(),d=ch(n);let f=Dn(1);a&&(l?fn(l)&&(f=us(l)):f=us(n));const h=OR(d,s,l)?vx(d):Dn(0);let m=(u.left+h.x)/f.x,p=(u.top+h.y)/f.y,g=u.width/f.x,v=u.height/f.y;if(d){const b=qe(d),T=l&&fn(l)?qe(l):l;let C=b,M=gd(C);for(;M&&l&&T!==C;){const A=us(M),E=M.getBoundingClientRect(),j=dn(M),O=E.left+(M.clientLeft+parseFloat(j.paddingLeft))*A.x,z=E.top+(M.clientTop+parseFloat(j.paddingTop))*A.y;m*=A.x,p*=A.y,g*=A.x,v*=A.y,m+=O,p+=z,C=qe(M),M=gd(C)}}return ko({width:g,height:v,x:m,y:p})}function fh(n,a){const s=Qo(n).scrollLeft;return a?a.left+s:ui(Nn(n)).left+s}function bx(n,a,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),u=l.left+a.scrollLeft-(s?0:fh(n,l)),d=l.top+a.scrollTop;return{x:u,y:d}}function NR(n){let{elements:a,rect:s,offsetParent:l,strategy:u}=n;const d=u==="fixed",f=Nn(l),h=a?Zo(a.floating):!1;if(l===f||h&&d)return s;let m={scrollLeft:0,scrollTop:0},p=Dn(1);const g=Dn(0),v=On(l);if((v||!v&&!d)&&((xs(l)!=="body"||Zr(f))&&(m=Qo(l)),On(l))){const T=ui(l);p=us(l),g.x=T.x+l.clientLeft,g.y=T.y+l.clientTop}const b=f&&!v&&!d?bx(f,m,!0):Dn(0);return{width:s.width*p.x,height:s.height*p.y,x:s.x*p.x-m.scrollLeft*p.x+g.x+b.x,y:s.y*p.y-m.scrollTop*p.y+g.y+b.y}}function _R(n){return Array.from(n.getClientRects())}function jR(n){const a=Nn(n),s=Qo(n),l=n.ownerDocument.body,u=Ye(a.scrollWidth,a.clientWidth,l.scrollWidth,l.clientWidth),d=Ye(a.scrollHeight,a.clientHeight,l.scrollHeight,l.clientHeight);let f=-s.scrollLeft+fh(n);const h=-s.scrollTop;return dn(l).direction==="rtl"&&(f+=Ye(a.clientWidth,l.clientWidth)-u),{width:u,height:d,x:f,y:h}}function VR(n,a){const s=qe(n),l=Nn(n),u=s.visualViewport;let d=l.clientWidth,f=l.clientHeight,h=0,m=0;if(u){d=u.width,f=u.height;const p=uh();(!p||p&&a==="fixed")&&(h=u.offsetLeft,m=u.offsetTop)}return{width:d,height:f,x:h,y:m}}function zR(n,a){const s=ui(n,!0,a==="fixed"),l=s.top+n.clientTop,u=s.left+n.clientLeft,d=On(n)?us(n):Dn(1),f=n.clientWidth*d.x,h=n.clientHeight*d.y,m=u*d.x,p=l*d.y;return{width:f,height:h,x:m,y:p}}function Iv(n,a,s){let l;if(a==="viewport")l=VR(n,s);else if(a==="document")l=jR(Nn(n));else if(fn(a))l=zR(a,s);else{const u=vx(n);l={x:a.x-u.x,y:a.y-u.y,width:a.width,height:a.height}}return ko(l)}function xx(n,a){const s=Na(n);return s===a||!fn(s)||hs(s)?!1:dn(s).position==="fixed"||xx(s,a)}function LR(n,a){const s=a.get(n);if(s)return s;let l=kr(n,[],!1).filter(h=>fn(h)&&xs(h)!=="body"),u=null;const d=dn(n).position==="fixed";let f=d?Na(n):n;for(;fn(f)&&!hs(f);){const h=dn(f),m=oh(f);!m&&h.position==="fixed"&&(u=null),(d?!m&&!u:!m&&h.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||Zr(f)&&!m&&xx(n,f))?l=l.filter(g=>g!==f):u=h,f=Na(f)}return a.set(n,l),l}function BR(n){let{element:a,boundary:s,rootBoundary:l,strategy:u}=n;const f=[...s==="clippingAncestors"?Zo(a)?[]:LR(a,this._c):[].concat(s),l],h=f[0],m=f.reduce((p,g)=>{const v=Iv(a,g,u);return p.top=Ye(v.top,p.top),p.right=Oa(v.right,p.right),p.bottom=Oa(v.bottom,p.bottom),p.left=Ye(v.left,p.left),p},Iv(a,h,u));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function UR(n){const{width:a,height:s}=yx(n);return{width:a,height:s}}function kR(n,a,s){const l=On(a),u=Nn(a),d=s==="fixed",f=ui(n,!0,d,a);let h={scrollLeft:0,scrollTop:0};const m=Dn(0);function p(){m.x=fh(u)}if(l||!l&&!d)if((xs(a)!=="body"||Zr(u))&&(h=Qo(a)),l){const T=ui(a,!0,d,a);m.x=T.x+a.clientLeft,m.y=T.y+a.clientTop}else u&&p();d&&!l&&u&&p();const g=u&&!l&&!d?bx(u,h):Dn(0),v=f.left+h.scrollLeft-m.x-g.x,b=f.top+h.scrollTop-m.y-g.y;return{x:v,y:b,width:f.width,height:f.height}}function Lf(n){return dn(n).position==="static"}function Wv(n,a){if(!On(n)||dn(n).position==="fixed")return null;if(a)return a(n);let s=n.offsetParent;return Nn(n)===s&&(s=s.ownerDocument.body),s}function Sx(n,a){const s=qe(n);if(Zo(n))return s;if(!On(n)){let u=Na(n);for(;u&&!hs(u);){if(fn(u)&&!Lf(u))return u;u=Na(u)}return s}let l=Wv(n,a);for(;l&&CR(l)&&Lf(l);)l=Wv(l,a);return l&&hs(l)&&Lf(l)&&!oh(l)?s:l||RR(n)||s}const HR=function(n){return on(this,null,function*(){const a=this.getOffsetParent||Sx,s=this.getDimensions,l=yield s(n.floating);return{reference:kR(n.reference,yield a(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}})};function PR(n){return dn(n).direction==="rtl"}const GR={convertOffsetParentRelativeRectToViewportRelativeRect:NR,getDocumentElement:Nn,getClippingRect:BR,getOffsetParent:Sx,getElementRects:HR,getClientRects:_R,getDimensions:UR,getScale:us,isElement:fn,isRTL:PR};function Tx(n,a){return n.x===a.x&&n.y===a.y&&n.width===a.width&&n.height===a.height}function YR(n,a){let s=null,l;const u=Nn(n);function d(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function f(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),d();const p=n.getBoundingClientRect(),{left:g,top:v,width:b,height:T}=p;if(h||a(),!b||!T)return;const C=yo(v),M=yo(u.clientWidth-(g+b)),A=yo(u.clientHeight-(v+T)),E=yo(g),O={rootMargin:-C+"px "+-M+"px "+-A+"px "+-E+"px",threshold:Ye(0,Oa(1,m))||1};let z=!0;function _(I){const tt=I[0].intersectionRatio;if(tt!==m){if(!z)return f();tt?f(!1,tt):l=setTimeout(()=>{f(!1,1e-7)},1e3)}tt===1&&!Tx(p,n.getBoundingClientRect())&&f(),z=!1}try{s=new IntersectionObserver(_,K(N({},O),{root:u.ownerDocument}))}catch(I){s=new IntersectionObserver(_,O)}s.observe(n)}return f(!0),d}function qR(n,a,s,l){l===void 0&&(l={});const{ancestorScroll:u=!0,ancestorResize:d=!0,elementResize:f=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=l,p=ch(n),g=u||d?[...p?kr(p):[],...kr(a)]:[];g.forEach(E=>{u&&E.addEventListener("scroll",s,{passive:!0}),d&&E.addEventListener("resize",s)});const v=p&&h?YR(p,s):null;let b=-1,T=null;f&&(T=new ResizeObserver(E=>{let[j]=E;j&&j.target===p&&T&&(T.unobserve(a),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var O;(O=T)==null||O.observe(a)})),s()}),p&&!m&&T.observe(p),T.observe(a));let C,M=m?ui(n):null;m&&A();function A(){const E=ui(n);M&&!Tx(M,E)&&s(),M=E,C=requestAnimationFrame(A)}return s(),()=>{var E;g.forEach(j=>{u&&j.removeEventListener("scroll",s),d&&j.removeEventListener("resize",s)}),v==null||v(),(E=T)==null||E.disconnect(),T=null,m&&cancelAnimationFrame(C)}}const XR=wR,KR=AR,ZR=xR,QR=MR,FR=SR,$v=bR,IR=ER,WR=(n,a,s)=>{const l=new Map,u=N({platform:GR},s),d=K(N({},u.platform),{_c:l});return vR(n,a,K(N({},u),{platform:d}))};var Do=typeof document!="undefined"?w.useLayoutEffect:w.useEffect;function Ho(n,a){if(n===a)return!0;if(typeof n!=typeof a)return!1;if(typeof n=="function"&&n.toString()===a.toString())return!0;let s,l,u;if(n&&a&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==a.length)return!1;for(l=s;l--!==0;)if(!Ho(n[l],a[l]))return!1;return!0}if(u=Object.keys(n),s=u.length,s!==Object.keys(a).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(a,u[l]))return!1;for(l=s;l--!==0;){const d=u[l];if(!(d==="_owner"&&n.$$typeof)&&!Ho(n[d],a[d]))return!1}return!0}return n!==n&&a!==a}function wx(n){return typeof window=="undefined"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Jv(n,a){const s=wx(n);return Math.round(a*s)/s}function Bf(n){const a=w.useRef(n);return Do(()=>{a.current=n}),a}function $R(n){n===void 0&&(n={});const{placement:a="bottom",strategy:s="absolute",middleware:l=[],platform:u,elements:{reference:d,floating:f}={},transform:h=!0,whileElementsMounted:m,open:p}=n,[g,v]=w.useState({x:0,y:0,strategy:s,placement:a,middlewareData:{},isPositioned:!1}),[b,T]=w.useState(l);Ho(b,l)||T(l);const[C,M]=w.useState(null),[A,E]=w.useState(null),j=w.useCallback(P=>{P!==I.current&&(I.current=P,M(P))},[]),O=w.useCallback(P=>{P!==tt.current&&(tt.current=P,E(P))},[]),z=d||C,_=f||A,I=w.useRef(null),tt=w.useRef(null),Y=w.useRef(g),W=m!=null,ft=Bf(m),ht=Bf(u),lt=Bf(p),bt=w.useCallback(()=>{if(!I.current||!tt.current)return;const P={placement:a,strategy:s,middleware:b};ht.current&&(P.platform=ht.current),WR(I.current,tt.current,P).then(ct=>{const D=K(N({},ct),{isPositioned:lt.current!==!1});Et.current&&!Ho(Y.current,D)&&(Y.current=D,qr.flushSync(()=>{v(D)}))})},[b,a,s,ht,lt]);Do(()=>{p===!1&&Y.current.isPositioned&&(Y.current.isPositioned=!1,v(P=>K(N({},P),{isPositioned:!1})))},[p]);const Et=w.useRef(!1);Do(()=>(Et.current=!0,()=>{Et.current=!1}),[]),Do(()=>{if(z&&(I.current=z),_&&(tt.current=_),z&&_){if(ft.current)return ft.current(z,_,bt);bt()}},[z,_,bt,ft,W]);const ut=w.useMemo(()=>({reference:I,floating:tt,setReference:j,setFloating:O}),[j,O]),B=w.useMemo(()=>({reference:z,floating:_}),[z,_]),q=w.useMemo(()=>{const P={position:s,left:0,top:0};if(!B.floating)return P;const ct=Jv(B.floating,g.x),D=Jv(B.floating,g.y);return h?N(K(N({},P),{transform:"translate("+ct+"px, "+D+"px)"}),wx(B.floating)>=1.5&&{willChange:"transform"}):{position:s,left:ct,top:D}},[s,h,B.floating,g.x,g.y]);return w.useMemo(()=>K(N({},g),{update:bt,refs:ut,elements:B,floatingStyles:q}),[g,bt,ut,B,q])}const JR=n=>{function a(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:u}=typeof n=="function"?n(s):n;return l&&a(l)?l.current!=null?$v({element:l.current,padding:u}).fn(s):{}:l?$v({element:l,padding:u}).fn(s):{}}}},tD=(n,a)=>K(N({},XR(n)),{options:[n,a]}),eD=(n,a)=>K(N({},KR(n)),{options:[n,a]}),nD=(n,a)=>K(N({},IR(n)),{options:[n,a]}),aD=(n,a)=>K(N({},ZR(n)),{options:[n,a]}),iD=(n,a)=>K(N({},QR(n)),{options:[n,a]}),sD=(n,a)=>K(N({},FR(n)),{options:[n,a]}),rD=(n,a)=>K(N({},JR(n)),{options:[n,a]});var lD="Arrow",Ax=w.forwardRef((n,a)=>{const f=n,{children:s,width:l=10,height:u=5}=f,d=et(f,["children","width","height"]);return S.jsx(Pt.svg,K(N({},d),{ref:a,width:l,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:S.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Ax.displayName=lD;var oD=Ax;function uD(n){const[a,s]=w.useState(void 0);return we(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const d=u[0];let f,h;if("borderBoxSize"in d){const m=d.borderBoxSize,p=Array.isArray(m)?m[0]:m;f=p.inlineSize,h=p.blockSize}else f=n.offsetWidth,h=n.offsetHeight;s({width:f,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),a}var dh="Popper",[Ex,Mx]=Xr(dh),[cD,Cx]=Ex(dh),Rx=n=>{const{__scopePopper:a,children:s}=n,[l,u]=w.useState(null);return S.jsx(cD,{scope:a,anchor:l,onAnchorChange:u,children:s})};Rx.displayName=dh;var Dx="PopperAnchor",Ox=w.forwardRef((n,a)=>{const m=n,{__scopePopper:s,virtualRef:l}=m,u=et(m,["__scopePopper","virtualRef"]),d=Cx(Dx,s),f=w.useRef(null),h=ie(a,f);return w.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||f.current)}),l?null:S.jsx(Pt.div,K(N({},u),{ref:h}))});Ox.displayName=Dx;var hh="PopperContent",[fD,dD]=Ex(hh),Nx=w.forwardRef((n,a)=>{var nt,Tt,qt,Ot,Ct,_t,de,Ae;const xt=n,{__scopePopper:s,side:l="bottom",sideOffset:u=0,align:d="center",alignOffset:f=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:b=!1,updatePositionStrategy:T="optimized",onPlaced:C}=xt,M=et(xt,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),A=Cx(hh,s),[E,j]=w.useState(null),O=ie(a,an=>j(an)),[z,_]=w.useState(null),I=uD(z),tt=(nt=I==null?void 0:I.width)!=null?nt:0,Y=(Tt=I==null?void 0:I.height)!=null?Tt:0,W=l+(d!=="center"?"-"+d:""),ft=typeof g=="number"?g:N({top:0,right:0,bottom:0,left:0},g),ht=Array.isArray(p)?p:[p],lt=ht.length>0,bt={padding:ft,boundary:ht.filter(mD),altBoundary:lt},{refs:Et,floatingStyles:ut,placement:B,isPositioned:q,middlewareData:P}=$R({strategy:"fixed",placement:W,whileElementsMounted:(...an)=>qR(...an,{animationFrame:T==="always"}),elements:{reference:A.anchor},middleware:[tD({mainAxis:u+Y,alignmentAxis:f}),m&&eD(N({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?nD():void 0},bt)),m&&aD(N({},bt)),iD(K(N({},bt),{apply:({elements:an,rects:he,availableWidth:za,availableHeight:Fr})=>{const{width:Ir,height:di}=he.reference,hi=an.floating.style;hi.setProperty("--radix-popper-available-width",`${za}px`),hi.setProperty("--radix-popper-available-height",`${Fr}px`),hi.setProperty("--radix-popper-anchor-width",`${Ir}px`),hi.setProperty("--radix-popper-anchor-height",`${di}px`)}})),z&&rD({element:z,padding:h}),pD({arrowWidth:tt,arrowHeight:Y}),b&&sD(N({strategy:"referenceHidden"},bt))]}),[ct,D]=Vx(B),X=Da(C);we(()=>{q&&(X==null||X())},[q,X]);const J=(qt=P.arrow)==null?void 0:qt.x,$=(Ot=P.arrow)==null?void 0:Ot.y,at=((Ct=P.arrow)==null?void 0:Ct.centerOffset)!==0,[vt,dt]=w.useState();return we(()=>{E&&dt(window.getComputedStyle(E).zIndex)},[E]),S.jsx("div",{ref:Et.setFloating,"data-radix-popper-content-wrapper":"",style:N(K(N({},ut),{transform:q?ut.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:vt,"--radix-popper-transform-origin":[(_t=P.transformOrigin)==null?void 0:_t.x,(de=P.transformOrigin)==null?void 0:de.y].join(" ")}),((Ae=P.hide)==null?void 0:Ae.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:n.dir,children:S.jsx(fD,{scope:s,placedSide:ct,onArrowChange:_,arrowX:J,arrowY:$,shouldHideArrow:at,children:S.jsx(Pt.div,K(N({"data-side":ct,"data-align":D},M),{ref:O,style:K(N({},M.style),{animation:q?void 0:"none"})}))})})});Nx.displayName=hh;var _x="PopperArrow",hD={top:"bottom",right:"left",bottom:"top",left:"right"},jx=w.forwardRef(function(a,s){const h=a,{__scopePopper:l}=h,u=et(h,["__scopePopper"]),d=dD(_x,l),f=hD[d.placedSide];return S.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:S.jsx(oD,K(N({},u),{ref:s,style:K(N({},u.style),{display:"block"})}))})});jx.displayName=_x;function mD(n){return n!==null}var pD=n=>({name:"transformOrigin",options:n,fn(a){var A,E,j,O,z;const{placement:s,rects:l,middlewareData:u}=a,f=((A=u.arrow)==null?void 0:A.centerOffset)!==0,h=f?0:n.arrowWidth,m=f?0:n.arrowHeight,[p,g]=Vx(s),v={start:"0%",center:"50%",end:"100%"}[g],b=((j=(E=u.arrow)==null?void 0:E.x)!=null?j:0)+h/2,T=((z=(O=u.arrow)==null?void 0:O.y)!=null?z:0)+m/2;let C="",M="";return p==="bottom"?(C=f?v:`${b}px`,M=`${-m}px`):p==="top"?(C=f?v:`${b}px`,M=`${l.floating.height+m}px`):p==="right"?(C=`${-m}px`,M=f?v:`${T}px`):p==="left"&&(C=`${l.floating.width+m}px`,M=f?v:`${T}px`),{data:{x:C,y:M}}}});function Vx(n){const[a,s="center"]=n.split("-");return[a,s]}var gD=Rx,yD=Ox,vD=Nx,bD=jx,xD="Portal",zx=w.forwardRef((n,a)=>{var m;const h=n,{container:s}=h,l=et(h,["container"]),[u,d]=w.useState(!1);we(()=>d(!0),[]);const f=s||u&&((m=globalThis==null?void 0:globalThis.document)==null?void 0:m.body);return f?kC.createPortal(S.jsx(Pt.div,K(N({},l),{ref:a})),f):null});zx.displayName=xD;var SD=m0[" useInsertionEffect ".trim().toString()]||we;function Po({prop:n,defaultProp:a,onChange:s=()=>{},caller:l}){const[u,d,f]=TD({defaultProp:a,onChange:s}),h=n!==void 0,m=h?n:u;{const g=w.useRef(n!==void 0);w.useEffect(()=>{const v=g.current;v!==h&&console.warn(`${l} is changing from ${v?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=h},[h,l])}const p=w.useCallback(g=>{var v;if(h){const b=wD(g)?g(n):g;b!==n&&((v=f.current)==null||v.call(f,b))}else d(g)},[h,n,d,f]);return[m,p]}function TD({defaultProp:n,onChange:a}){const[s,l]=w.useState(n),u=w.useRef(s),d=w.useRef(a);return SD(()=>{d.current=a},[a]),w.useEffect(()=>{var f;u.current!==s&&((f=d.current)==null||f.call(d,s),u.current=s)},[s,u]),[s,l,d]}function wD(n){return typeof n=="function"}function AD(n){const a=w.useRef({value:n,previous:n});return w.useMemo(()=>(a.current.value!==n&&(a.current.previous=a.current.value,a.current.value=n),a.current.previous),[n])}var Lx=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ED="VisuallyHidden",MD=w.forwardRef((n,a)=>S.jsx(Pt.span,K(N({},n),{ref:a,style:N(N({},Lx),n.style)})));MD.displayName=ED;var CD=function(n){if(typeof document=="undefined")return null;var a=Array.isArray(n)?n[0]:n;return a.ownerDocument.body},es=new WeakMap,vo=new WeakMap,bo={},Uf=0,Bx=function(n){return n&&(n.host||Bx(n.parentNode))},RD=function(n,a){return a.map(function(s){if(n.contains(s))return s;var l=Bx(s);return l&&n.contains(l)?l:(console.error("aria-hidden",s,"in not contained inside",n,". Doing nothing"),null)}).filter(function(s){return!!s})},DD=function(n,a,s,l){var u=RD(a,Array.isArray(n)?n:[n]);bo[s]||(bo[s]=new WeakMap);var d=bo[s],f=[],h=new Set,m=new Set(u),p=function(v){!v||h.has(v)||(h.add(v),p(v.parentNode))};u.forEach(p);var g=function(v){!v||m.has(v)||Array.prototype.forEach.call(v.children,function(b){if(h.has(b))g(b);else try{var T=b.getAttribute(l),C=T!==null&&T!=="false",M=(es.get(b)||0)+1,A=(d.get(b)||0)+1;es.set(b,M),d.set(b,A),f.push(b),M===1&&C&&vo.set(b,!0),A===1&&b.setAttribute(s,"true"),C||b.setAttribute(l,"true")}catch(E){console.error("aria-hidden: cannot operate on ",b,E)}})};return g(a),h.clear(),Uf++,function(){f.forEach(function(v){var b=es.get(v)-1,T=d.get(v)-1;es.set(v,b),d.set(v,T),b||(vo.has(v)||v.removeAttribute(l),vo.delete(v)),T||v.removeAttribute(s)}),Uf--,Uf||(es=new WeakMap,es=new WeakMap,vo=new WeakMap,bo={})}},OD=function(n,a,s){s===void 0&&(s="data-aria-hidden");var l=Array.from(Array.isArray(n)?n:[n]),u=CD(n);return u?(l.push.apply(l,Array.from(u.querySelectorAll("[aria-live]"))),DD(l,u,s,"aria-hidden")):function(){return null}},En=function(){return En=Object.assign||function(a){for(var s,l=1,u=arguments.length;l<u;l++){s=arguments[l];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(a[d]=s[d])}return a},En.apply(this,arguments)};function Ux(n,a){var s={};for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,l=Object.getOwnPropertySymbols(n);u<l.length;u++)a.indexOf(l[u])<0&&Object.prototype.propertyIsEnumerable.call(n,l[u])&&(s[l[u]]=n[l[u]]);return s}function ND(n,a,s){if(s||arguments.length===2)for(var l=0,u=a.length,d;l<u;l++)(d||!(l in a))&&(d||(d=Array.prototype.slice.call(a,0,l)),d[l]=a[l]);return n.concat(d||Array.prototype.slice.call(a))}var Oo="right-scroll-bar-position",No="width-before-scroll-bar",_D="with-scroll-bars-hidden",jD="--removed-body-scroll-bar-size";function kf(n,a){return typeof n=="function"?n(a):n&&(n.current=a),n}function VD(n,a){var s=w.useState(function(){return{value:n,callback:a,facade:{get current(){return s.value},set current(l){var u=s.value;u!==l&&(s.value=l,s.callback(l,u))}}}})[0];return s.callback=a,s.facade}var zD=typeof window!="undefined"?w.useLayoutEffect:w.useEffect,t0=new WeakMap;function LD(n,a){var s=VD(null,function(l){return n.forEach(function(u){return kf(u,l)})});return zD(function(){var l=t0.get(s);if(l){var u=new Set(l),d=new Set(n),f=s.current;u.forEach(function(h){d.has(h)||kf(h,null)}),d.forEach(function(h){u.has(h)||kf(h,f)})}t0.set(s,n)},[n]),s}function BD(n){return n}function UD(n,a){a===void 0&&(a=BD);var s=[],l=!1,u={read:function(){if(l)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:n},useMedium:function(d){var f=a(d,l);return s.push(f),function(){s=s.filter(function(h){return h!==f})}},assignSyncMedium:function(d){for(l=!0;s.length;){var f=s;s=[],f.forEach(d)}s={push:function(h){return d(h)},filter:function(){return s}}},assignMedium:function(d){l=!0;var f=[];if(s.length){var h=s;s=[],h.forEach(d),f=s}var m=function(){var g=f;f=[],g.forEach(d)},p=function(){return Promise.resolve().then(m)};p(),s={push:function(g){f.push(g),p()},filter:function(g){return f=f.filter(g),s}}}};return u}function kD(n){n===void 0&&(n={});var a=UD(null);return a.options=En({async:!0,ssr:!1},n),a}var kx=function(n){var a=n.sideCar,s=Ux(n,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var l=a.read();if(!l)throw new Error("Sidecar medium not found");return w.createElement(l,En({},s))};kx.isSideCarExport=!0;function HD(n,a){return n.useMedium(a),kx}var Hx=kD(),Hf=function(){},Fo=w.forwardRef(function(n,a){var s=w.useRef(null),l=w.useState({onScrollCapture:Hf,onWheelCapture:Hf,onTouchMoveCapture:Hf}),u=l[0],d=l[1],f=n.forwardProps,h=n.children,m=n.className,p=n.removeScrollBar,g=n.enabled,v=n.shards,b=n.sideCar,T=n.noIsolation,C=n.inert,M=n.allowPinchZoom,A=n.as,E=A===void 0?"div":A,j=n.gapMode,O=Ux(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=b,_=LD([s,a]),I=En(En({},O),u);return w.createElement(w.Fragment,null,g&&w.createElement(z,{sideCar:Hx,removeScrollBar:p,shards:v,noIsolation:T,inert:C,setCallbacks:d,allowPinchZoom:!!M,lockRef:s,gapMode:j}),f?w.cloneElement(w.Children.only(h),En(En({},I),{ref:_})):w.createElement(E,En({},I,{className:m,ref:_}),h))});Fo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Fo.classNames={fullWidth:No,zeroRight:Oo};var PD=function(){if(typeof __webpack_nonce__!="undefined")return __webpack_nonce__};function GD(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var a=PD();return a&&n.setAttribute("nonce",a),n}function YD(n,a){n.styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a))}function qD(n){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(n)}var XD=function(){var n=0,a=null;return{add:function(s){n==0&&(a=GD())&&(YD(a,s),qD(a)),n++},remove:function(){n--,!n&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},KD=function(){var n=XD();return function(a,s){w.useEffect(function(){return n.add(a),function(){n.remove()}},[a&&s])}},Px=function(){var n=KD(),a=function(s){var l=s.styles,u=s.dynamic;return n(l,u),null};return a},ZD={left:0,top:0,right:0,gap:0},Pf=function(n){return parseInt(n||"",10)||0},QD=function(n){var a=window.getComputedStyle(document.body),s=a[n==="padding"?"paddingLeft":"marginLeft"],l=a[n==="padding"?"paddingTop":"marginTop"],u=a[n==="padding"?"paddingRight":"marginRight"];return[Pf(s),Pf(l),Pf(u)]},FD=function(n){if(n===void 0&&(n="margin"),typeof window=="undefined")return ZD;var a=QD(n),s=document.documentElement.clientWidth,l=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,l-s+a[2]-a[0])}},ID=Px(),cs="data-scroll-locked",WD=function(n,a,s,l){var u=n.left,d=n.top,f=n.right,h=n.gap;return s===void 0&&(s="margin"),`
  .`.concat(_D,` {
   overflow: hidden `).concat(l,`;
   padding-right: `).concat(h,"px ").concat(l,`;
  }
  body[`).concat(cs,`] {
    overflow: hidden `).concat(l,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(l,";"),s==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(f,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(l,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(l,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Oo,` {
    right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(No,` {
    margin-right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(Oo," .").concat(Oo,` {
    right: 0 `).concat(l,`;
  }
  
  .`).concat(No," .").concat(No,` {
    margin-right: 0 `).concat(l,`;
  }
  
  body[`).concat(cs,`] {
    `).concat(jD,": ").concat(h,`px;
  }
`)},e0=function(){var n=parseInt(document.body.getAttribute(cs)||"0",10);return isFinite(n)?n:0},$D=function(){w.useEffect(function(){return document.body.setAttribute(cs,(e0()+1).toString()),function(){var n=e0()-1;n<=0?document.body.removeAttribute(cs):document.body.setAttribute(cs,n.toString())}},[])},JD=function(n){var a=n.noRelative,s=n.noImportant,l=n.gapMode,u=l===void 0?"margin":l;$D();var d=w.useMemo(function(){return FD(u)},[u]);return w.createElement(ID,{styles:WD(d,!a,u,s?"":"!important")})},yd=!1;if(typeof window!="undefined")try{var xo=Object.defineProperty({},"passive",{get:function(){return yd=!0,!0}});window.addEventListener("test",xo,xo),window.removeEventListener("test",xo,xo)}catch(n){yd=!1}var ns=yd?{passive:!1}:!1,tO=function(n){return n.tagName==="TEXTAREA"},Gx=function(n,a){if(!(n instanceof Element))return!1;var s=window.getComputedStyle(n);return s[a]!=="hidden"&&!(s.overflowY===s.overflowX&&!tO(n)&&s[a]==="visible")},eO=function(n){return Gx(n,"overflowY")},nO=function(n){return Gx(n,"overflowX")},n0=function(n,a){var s=a.ownerDocument,l=a;do{typeof ShadowRoot!="undefined"&&l instanceof ShadowRoot&&(l=l.host);var u=Yx(n,l);if(u){var d=qx(n,l),f=d[1],h=d[2];if(f>h)return!0}l=l.parentNode}while(l&&l!==s.body);return!1},aO=function(n){var a=n.scrollTop,s=n.scrollHeight,l=n.clientHeight;return[a,s,l]},iO=function(n){var a=n.scrollLeft,s=n.scrollWidth,l=n.clientWidth;return[a,s,l]},Yx=function(n,a){return n==="v"?eO(a):nO(a)},qx=function(n,a){return n==="v"?aO(a):iO(a)},sO=function(n,a){return n==="h"&&a==="rtl"?-1:1},rO=function(n,a,s,l,u){var d=sO(n,window.getComputedStyle(a).direction),f=d*l,h=s.target,m=a.contains(h),p=!1,g=f>0,v=0,b=0;do{var T=qx(n,h),C=T[0],M=T[1],A=T[2],E=M-A-d*C;(C||E)&&Yx(n,h)&&(v+=E,b+=C),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!m&&h!==document.body||m&&(a.contains(h)||a===h));return(g&&Math.abs(v)<1||!g&&Math.abs(b)<1)&&(p=!0),p},So=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},a0=function(n){return[n.deltaX,n.deltaY]},i0=function(n){return n&&"current"in n?n.current:n},lO=function(n,a){return n[0]===a[0]&&n[1]===a[1]},oO=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},uO=0,as=[];function cO(n){var a=w.useRef([]),s=w.useRef([0,0]),l=w.useRef(),u=w.useState(uO++)[0],d=w.useState(Px)[0],f=w.useRef(n);w.useEffect(function(){f.current=n},[n]),w.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(u));var M=ND([n.lockRef.current],(n.shards||[]).map(i0),!0).filter(Boolean);return M.forEach(function(A){return A.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),M.forEach(function(A){return A.classList.remove("allow-interactivity-".concat(u))})}}},[n.inert,n.lockRef.current,n.shards]);var h=w.useCallback(function(M,A){if("touches"in M&&M.touches.length===2||M.type==="wheel"&&M.ctrlKey)return!f.current.allowPinchZoom;var E=So(M),j=s.current,O="deltaX"in M?M.deltaX:j[0]-E[0],z="deltaY"in M?M.deltaY:j[1]-E[1],_,I=M.target,tt=Math.abs(O)>Math.abs(z)?"h":"v";if("touches"in M&&tt==="h"&&I.type==="range")return!1;var Y=n0(tt,I);if(!Y)return!0;if(Y?_=tt:(_=tt==="v"?"h":"v",Y=n0(tt,I)),!Y)return!1;if(!l.current&&"changedTouches"in M&&(O||z)&&(l.current=_),!_)return!0;var W=l.current||_;return rO(W,A,M,W==="h"?O:z)},[]),m=w.useCallback(function(M){var A=M;if(!(!as.length||as[as.length-1]!==d)){var E="deltaY"in A?a0(A):So(A),j=a.current.filter(function(_){return _.name===A.type&&(_.target===A.target||A.target===_.shadowParent)&&lO(_.delta,E)})[0];if(j&&j.should){A.cancelable&&A.preventDefault();return}if(!j){var O=(f.current.shards||[]).map(i0).filter(Boolean).filter(function(_){return _.contains(A.target)}),z=O.length>0?h(A,O[0]):!f.current.noIsolation;z&&A.cancelable&&A.preventDefault()}}},[]),p=w.useCallback(function(M,A,E,j){var O={name:M,delta:A,target:E,should:j,shadowParent:fO(E)};a.current.push(O),setTimeout(function(){a.current=a.current.filter(function(z){return z!==O})},1)},[]),g=w.useCallback(function(M){s.current=So(M),l.current=void 0},[]),v=w.useCallback(function(M){p(M.type,a0(M),M.target,h(M,n.lockRef.current))},[]),b=w.useCallback(function(M){p(M.type,So(M),M.target,h(M,n.lockRef.current))},[]);w.useEffect(function(){return as.push(d),n.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:b}),document.addEventListener("wheel",m,ns),document.addEventListener("touchmove",m,ns),document.addEventListener("touchstart",g,ns),function(){as=as.filter(function(M){return M!==d}),document.removeEventListener("wheel",m,ns),document.removeEventListener("touchmove",m,ns),document.removeEventListener("touchstart",g,ns)}},[]);var T=n.removeScrollBar,C=n.inert;return w.createElement(w.Fragment,null,C?w.createElement(d,{styles:oO(u)}):null,T?w.createElement(JD,{gapMode:n.gapMode}):null)}function fO(n){for(var a=null;n!==null;)n instanceof ShadowRoot&&(a=n.host,n=n.host),n=n.parentNode;return a}const dO=HD(Hx,cO);var Xx=w.forwardRef(function(n,a){return w.createElement(Fo,En({},n,{ref:a,sideCar:dO}))});Xx.classNames=Fo.classNames;var hO=[" ","Enter","ArrowUp","ArrowDown"],mO=[" ","Enter"],ci="Select",[Io,Wo,pO]=ox(ci),[Ss,l3]=Xr(ci,[pO,Mx]),$o=Mx(),[gO,ja]=Ss(ci),[yO,vO]=Ss(ci),Kx=n=>{const{__scopeSelect:a,children:s,open:l,defaultOpen:u,onOpenChange:d,value:f,defaultValue:h,onValueChange:m,dir:p,name:g,autoComplete:v,disabled:b,required:T,form:C}=n,M=$o(a),[A,E]=w.useState(null),[j,O]=w.useState(null),[z,_]=w.useState(!1),I=ih(p),[tt,Y]=Po({prop:l,defaultProp:u!=null?u:!1,onChange:d,caller:ci}),[W,ft]=Po({prop:f,defaultProp:h,onChange:m,caller:ci}),ht=w.useRef(null),lt=A?C||!!A.closest("form"):!0,[bt,Et]=w.useState(new Set),ut=Array.from(bt).map(B=>B.props.value).join(";");return S.jsx(gD,K(N({},M),{children:S.jsxs(gO,{required:T,scope:a,trigger:A,onTriggerChange:E,valueNode:j,onValueNodeChange:O,valueNodeHasChildren:z,onValueNodeHasChildrenChange:_,contentId:Kr(),value:W,onValueChange:ft,open:tt,onOpenChange:Y,dir:I,triggerPointerDownPosRef:ht,disabled:b,children:[S.jsx(Io.Provider,{scope:a,children:S.jsx(yO,{scope:n.__scopeSelect,onNativeOptionAdd:w.useCallback(B=>{Et(q=>new Set(q).add(B))},[]),onNativeOptionRemove:w.useCallback(B=>{Et(q=>{const P=new Set(q);return P.delete(B),P})},[]),children:s})}),lt?S.jsxs(mS,{"aria-hidden":!0,required:T,tabIndex:-1,name:g,autoComplete:v,value:W,onChange:B=>ft(B.target.value),disabled:b,form:C,children:[W===void 0?S.jsx("option",{value:""}):null,Array.from(bt)]},ut):null]})}))};Kx.displayName=ci;var Zx="SelectTrigger",Qx=w.forwardRef((n,a)=>{const M=n,{__scopeSelect:s,disabled:l=!1}=M,u=et(M,["__scopeSelect","disabled"]),d=$o(s),f=ja(Zx,s),h=f.disabled||l,m=ie(a,f.onTriggerChange),p=Wo(s),g=w.useRef("touch"),[v,b,T]=gS(A=>{const E=p().filter(z=>!z.disabled),j=E.find(z=>z.value===f.value),O=yS(E,A,j);O!==void 0&&f.onValueChange(O.value)}),C=A=>{h||(f.onOpenChange(!0),T()),A&&(f.triggerPointerDownPosRef.current={x:Math.round(A.pageX),y:Math.round(A.pageY)})};return S.jsx(yD,K(N({asChild:!0},d),{children:S.jsx(Pt.button,K(N({type:"button",role:"combobox","aria-controls":f.contentId,"aria-expanded":f.open,"aria-required":f.required,"aria-autocomplete":"none",dir:f.dir,"data-state":f.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":pS(f.value)?"":void 0},u),{ref:m,onClick:Bt(u.onClick,A=>{A.currentTarget.focus(),g.current!=="mouse"&&C(A)}),onPointerDown:Bt(u.onPointerDown,A=>{g.current=A.pointerType;const E=A.target;E.hasPointerCapture(A.pointerId)&&E.releasePointerCapture(A.pointerId),A.button===0&&A.ctrlKey===!1&&A.pointerType==="mouse"&&(C(A),A.preventDefault())}),onKeyDown:Bt(u.onKeyDown,A=>{const E=v.current!=="";!(A.ctrlKey||A.altKey||A.metaKey)&&A.key.length===1&&b(A.key),!(E&&A.key===" ")&&hO.includes(A.key)&&(C(),A.preventDefault())})}))}))});Qx.displayName=Zx;var Fx="SelectValue",Ix=w.forwardRef((n,a)=>{const b=n,{__scopeSelect:s,className:l,style:u,children:d,placeholder:f=""}=b,h=et(b,["__scopeSelect","className","style","children","placeholder"]),m=ja(Fx,s),{onValueNodeHasChildrenChange:p}=m,g=d!==void 0,v=ie(a,m.onValueNodeChange);return we(()=>{p(g)},[p,g]),S.jsx(Pt.span,K(N({},h),{ref:v,style:{pointerEvents:"none"},children:pS(m.value)?S.jsx(S.Fragment,{children:f}):d}))});Ix.displayName=Fx;var bO="SelectIcon",Wx=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s,children:l}=d,u=et(d,["__scopeSelect","children"]);return S.jsx(Pt.span,K(N({"aria-hidden":!0},u),{ref:a,children:l||"▼"}))});Wx.displayName=bO;var xO="SelectPortal",$x=n=>S.jsx(zx,N({asChild:!0},n));$x.displayName=xO;var fi="SelectContent",Jx=w.forwardRef((n,a)=>{const s=ja(fi,n.__scopeSelect),[l,u]=w.useState();if(we(()=>{u(new DocumentFragment)},[]),!s.open){const d=l;return d?qr.createPortal(S.jsx(tS,{scope:n.__scopeSelect,children:S.jsx(Io.Slot,{scope:n.__scopeSelect,children:S.jsx("div",{children:n.children})})}),d):null}return S.jsx(eS,K(N({},n),{ref:a}))});Jx.displayName=fi;var cn=10,[tS,Va]=Ss(fi),SO="SelectContentImpl",TO=Br("SelectContent.RemoveScroll"),eS=w.forwardRef((n,a)=>{const xt=n,{__scopeSelect:s,position:l="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:d,onPointerDownOutside:f,side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:C,hideWhenDetached:M,avoidCollisions:A}=xt,E=et(xt,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),j=ja(fi,s),[O,z]=w.useState(null),[_,I]=w.useState(null),tt=ie(a,nt=>z(nt)),[Y,W]=w.useState(null),[ft,ht]=w.useState(null),lt=Wo(s),[bt,Et]=w.useState(!1),ut=w.useRef(!1);w.useEffect(()=>{if(O)return OD(O)},[O]),tR();const B=w.useCallback(nt=>{const[Tt,...qt]=lt().map(_t=>_t.ref.current),[Ot]=qt.slice(-1),Ct=document.activeElement;for(const _t of nt)if(_t===Ct||(_t==null||_t.scrollIntoView({block:"nearest"}),_t===Tt&&_&&(_.scrollTop=0),_t===Ot&&_&&(_.scrollTop=_.scrollHeight),_t==null||_t.focus(),document.activeElement!==Ct))return},[lt,_]),q=w.useCallback(()=>B([Y,O]),[B,Y,O]);w.useEffect(()=>{bt&&q()},[bt,q]);const{onOpenChange:P,triggerPointerDownPosRef:ct}=j;w.useEffect(()=>{if(O){let nt={x:0,y:0};const Tt=Ot=>{var Ct,_t,de,Ae;nt={x:Math.abs(Math.round(Ot.pageX)-((_t=(Ct=ct.current)==null?void 0:Ct.x)!=null?_t:0)),y:Math.abs(Math.round(Ot.pageY)-((Ae=(de=ct.current)==null?void 0:de.y)!=null?Ae:0))}},qt=Ot=>{nt.x<=10&&nt.y<=10?Ot.preventDefault():O.contains(Ot.target)||P(!1),document.removeEventListener("pointermove",Tt),ct.current=null};return ct.current!==null&&(document.addEventListener("pointermove",Tt),document.addEventListener("pointerup",qt,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",Tt),document.removeEventListener("pointerup",qt,{capture:!0})}}},[O,P,ct]),w.useEffect(()=>{const nt=()=>P(!1);return window.addEventListener("blur",nt),window.addEventListener("resize",nt),()=>{window.removeEventListener("blur",nt),window.removeEventListener("resize",nt)}},[P]);const[D,X]=gS(nt=>{const Tt=lt().filter(Ct=>!Ct.disabled),qt=Tt.find(Ct=>Ct.ref.current===document.activeElement),Ot=yS(Tt,nt,qt);Ot&&setTimeout(()=>Ot.ref.current.focus())}),J=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(j.value!==void 0&&j.value===Tt||Ot)&&(W(nt),Ot&&(ut.current=!0))},[j.value]),$=w.useCallback(()=>O==null?void 0:O.focus(),[O]),at=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(j.value!==void 0&&j.value===Tt||Ot)&&ht(nt)},[j.value]),vt=l==="popper"?vd:nS,dt=vt===vd?{side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:C,hideWhenDetached:M,avoidCollisions:A}:{};return S.jsx(tS,{scope:s,content:O,viewport:_,onViewportChange:I,itemRefCallback:J,selectedItem:Y,onItemLeave:$,itemTextRefCallback:at,focusSelectedItem:q,selectedItemText:ft,position:l,isPositioned:bt,searchRef:D,children:S.jsx(Xx,{as:TO,allowPinchZoom:!0,children:S.jsx(dx,{asChild:!0,trapped:j.open,onMountAutoFocus:nt=>{nt.preventDefault()},onUnmountAutoFocus:Bt(u,nt=>{var Tt;(Tt=j.trigger)==null||Tt.focus({preventScroll:!0}),nt.preventDefault()}),children:S.jsx(cx,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:nt=>nt.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:S.jsx(vt,K(N(N({role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:nt=>nt.preventDefault()},E),dt),{onPlaced:()=>Et(!0),ref:tt,style:N({display:"flex",flexDirection:"column",outline:"none"},E.style),onKeyDown:Bt(E.onKeyDown,nt=>{const Tt=nt.ctrlKey||nt.altKey||nt.metaKey;if(nt.key==="Tab"&&nt.preventDefault(),!Tt&&nt.key.length===1&&X(nt.key),["ArrowUp","ArrowDown","Home","End"].includes(nt.key)){let Ot=lt().filter(Ct=>!Ct.disabled).map(Ct=>Ct.ref.current);if(["ArrowUp","End"].includes(nt.key)&&(Ot=Ot.slice().reverse()),["ArrowUp","ArrowDown"].includes(nt.key)){const Ct=nt.target,_t=Ot.indexOf(Ct);Ot=Ot.slice(_t+1)}setTimeout(()=>B(Ot)),nt.preventDefault()}})}))})})})})});eS.displayName=SO;var wO="SelectItemAlignedPosition",nS=w.forwardRef((n,a)=>{const tt=n,{__scopeSelect:s,onPlaced:l}=tt,u=et(tt,["__scopeSelect","onPlaced"]),d=ja(fi,s),f=Va(fi,s),[h,m]=w.useState(null),[p,g]=w.useState(null),v=ie(a,Y=>g(Y)),b=Wo(s),T=w.useRef(!1),C=w.useRef(!0),{viewport:M,selectedItem:A,selectedItemText:E,focusSelectedItem:j}=f,O=w.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&p&&M&&A&&E){const Y=d.trigger.getBoundingClientRect(),W=p.getBoundingClientRect(),ft=d.valueNode.getBoundingClientRect(),ht=E.getBoundingClientRect();if(d.dir!=="rtl"){const Ct=ht.left-W.left,_t=ft.left-Ct,de=Y.left-_t,Ae=Y.width+de,an=Math.max(Ae,W.width),he=window.innerWidth-cn,za=Uv(_t,[cn,Math.max(cn,he-an)]);h.style.minWidth=Ae+"px",h.style.left=za+"px"}else{const Ct=W.right-ht.right,_t=window.innerWidth-ft.right-Ct,de=window.innerWidth-Y.right-_t,Ae=Y.width+de,an=Math.max(Ae,W.width),he=window.innerWidth-cn,za=Uv(_t,[cn,Math.max(cn,he-an)]);h.style.minWidth=Ae+"px",h.style.right=za+"px"}const lt=b(),bt=window.innerHeight-cn*2,Et=M.scrollHeight,ut=window.getComputedStyle(p),B=parseInt(ut.borderTopWidth,10),q=parseInt(ut.paddingTop,10),P=parseInt(ut.borderBottomWidth,10),ct=parseInt(ut.paddingBottom,10),D=B+q+Et+ct+P,X=Math.min(A.offsetHeight*5,D),J=window.getComputedStyle(M),$=parseInt(J.paddingTop,10),at=parseInt(J.paddingBottom,10),vt=Y.top+Y.height/2-cn,dt=bt-vt,xt=A.offsetHeight/2,nt=A.offsetTop+xt,Tt=B+q+nt,qt=D-Tt;if(Tt<=vt){const Ct=lt.length>0&&A===lt[lt.length-1].ref.current;h.style.bottom="0px";const _t=p.clientHeight-M.offsetTop-M.offsetHeight,de=Math.max(dt,xt+(Ct?at:0)+_t+P),Ae=Tt+de;h.style.height=Ae+"px"}else{const Ct=lt.length>0&&A===lt[0].ref.current;h.style.top="0px";const de=Math.max(vt,B+M.offsetTop+(Ct?$:0)+xt)+qt;h.style.height=de+"px",M.scrollTop=Tt-vt+M.offsetTop}h.style.margin=`${cn}px 0`,h.style.minHeight=X+"px",h.style.maxHeight=bt+"px",l==null||l(),requestAnimationFrame(()=>T.current=!0)}},[b,d.trigger,d.valueNode,h,p,M,A,E,d.dir,l]);we(()=>O(),[O]);const[z,_]=w.useState();we(()=>{p&&_(window.getComputedStyle(p).zIndex)},[p]);const I=w.useCallback(Y=>{Y&&C.current===!0&&(O(),j==null||j(),C.current=!1)},[O,j]);return S.jsx(EO,{scope:s,contentWrapper:h,shouldExpandOnScrollRef:T,onScrollButtonChange:I,children:S.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:S.jsx(Pt.div,K(N({},u),{ref:v,style:N({boxSizing:"border-box",maxHeight:"100%"},u.style)}))})})});nS.displayName=wO;var AO="SelectPopperPosition",vd=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s,align:l="start",collisionPadding:u=cn}=h,d=et(h,["__scopeSelect","align","collisionPadding"]),f=$o(s);return S.jsx(vD,K(N(N({},f),d),{ref:a,align:l,collisionPadding:u,style:K(N({boxSizing:"border-box"},d.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});vd.displayName=AO;var[EO,mh]=Ss(fi,{}),bd="SelectViewport",aS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,nonce:l}=p,u=et(p,["__scopeSelect","nonce"]),d=Va(bd,s),f=mh(bd,s),h=ie(a,d.onViewportChange),m=w.useRef(0);return S.jsxs(S.Fragment,{children:[S.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),S.jsx(Io.Slot,{scope:s,children:S.jsx(Pt.div,K(N({"data-radix-select-viewport":"",role:"presentation"},u),{ref:h,style:N({position:"relative",flex:1,overflow:"hidden auto"},u.style),onScroll:Bt(u.onScroll,g=>{const v=g.currentTarget,{contentWrapper:b,shouldExpandOnScrollRef:T}=f;if(T!=null&&T.current&&b){const C=Math.abs(m.current-v.scrollTop);if(C>0){const M=window.innerHeight-cn*2,A=parseFloat(b.style.minHeight),E=parseFloat(b.style.height),j=Math.max(A,E);if(j<M){const O=j+C,z=Math.min(M,O),_=O-z;b.style.height=z+"px",b.style.bottom==="0px"&&(v.scrollTop=_>0?_:0,b.style.justifyContent="flex-end")}}}m.current=v.scrollTop})}))})]})});aS.displayName=bd;var iS="SelectGroup",[MO,CO]=Ss(iS),RO=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),u=Kr();return S.jsx(MO,{scope:s,id:u,children:S.jsx(Pt.div,K(N({role:"group","aria-labelledby":u},l),{ref:a}))})});RO.displayName=iS;var sS="SelectLabel",DO=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),u=CO(sS,s);return S.jsx(Pt.div,K(N({id:u.id},l),{ref:a}))});DO.displayName=sS;var Go="SelectItem",[OO,rS]=Ss(Go),lS=w.forwardRef((n,a)=>{const j=n,{__scopeSelect:s,value:l,disabled:u=!1,textValue:d}=j,f=et(j,["__scopeSelect","value","disabled","textValue"]),h=ja(Go,s),m=Va(Go,s),p=h.value===l,[g,v]=w.useState(d!=null?d:""),[b,T]=w.useState(!1),C=ie(a,O=>{var z;return(z=m.itemRefCallback)==null?void 0:z.call(m,O,l,u)}),M=Kr(),A=w.useRef("touch"),E=()=>{u||(h.onValueChange(l),h.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return S.jsx(OO,{scope:s,value:l,disabled:u,textId:M,isSelected:p,onItemTextChange:w.useCallback(O=>{v(z=>{var _;return z||((_=O==null?void 0:O.textContent)!=null?_:"").trim()})},[]),children:S.jsx(Io.ItemSlot,{scope:s,value:l,disabled:u,textValue:g,children:S.jsx(Pt.div,K(N({role:"option","aria-labelledby":M,"data-highlighted":b?"":void 0,"aria-selected":p&&b,"data-state":p?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1},f),{ref:C,onFocus:Bt(f.onFocus,()=>T(!0)),onBlur:Bt(f.onBlur,()=>T(!1)),onClick:Bt(f.onClick,()=>{A.current!=="mouse"&&E()}),onPointerUp:Bt(f.onPointerUp,()=>{A.current==="mouse"&&E()}),onPointerDown:Bt(f.onPointerDown,O=>{A.current=O.pointerType}),onPointerMove:Bt(f.onPointerMove,O=>{var z;A.current=O.pointerType,u?(z=m.onItemLeave)==null||z.call(m):A.current==="mouse"&&O.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Bt(f.onPointerLeave,O=>{var z;O.currentTarget===document.activeElement&&((z=m.onItemLeave)==null||z.call(m))}),onKeyDown:Bt(f.onKeyDown,O=>{var _;((_=m.searchRef)==null?void 0:_.current)!==""&&O.key===" "||(mO.includes(O.key)&&E(),O.key===" "&&O.preventDefault())})}))})})});lS.displayName=Go;var Ar="SelectItemText",oS=w.forwardRef((n,a)=>{const E=n,{__scopeSelect:s,className:l,style:u}=E,d=et(E,["__scopeSelect","className","style"]),f=ja(Ar,s),h=Va(Ar,s),m=rS(Ar,s),p=vO(Ar,s),[g,v]=w.useState(null),b=ie(a,j=>v(j),m.onItemTextChange,j=>{var O;return(O=h.itemTextRefCallback)==null?void 0:O.call(h,j,m.value,m.disabled)}),T=g==null?void 0:g.textContent,C=w.useMemo(()=>S.jsx("option",{value:m.value,disabled:m.disabled,children:T},m.value),[m.disabled,m.value,T]),{onNativeOptionAdd:M,onNativeOptionRemove:A}=p;return we(()=>(M(C),()=>A(C)),[M,A,C]),S.jsxs(S.Fragment,{children:[S.jsx(Pt.span,K(N({id:m.textId},d),{ref:b})),m.isSelected&&f.valueNode&&!f.valueNodeHasChildren?qr.createPortal(d.children,f.valueNode):null]})});oS.displayName=Ar;var uS="SelectItemIndicator",cS=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]);return rS(uS,s).isSelected?S.jsx(Pt.span,K(N({"aria-hidden":!0},l),{ref:a})):null});cS.displayName=uS;var xd="SelectScrollUpButton",fS=w.forwardRef((n,a)=>{const s=Va(xd,n.__scopeSelect),l=mh(xd,n.__scopeSelect),[u,d]=w.useState(!1),f=ie(a,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollTop>0;d(p)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(hS,K(N({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop-m.offsetHeight)}})):null});fS.displayName=xd;var Sd="SelectScrollDownButton",dS=w.forwardRef((n,a)=>{const s=Va(Sd,n.__scopeSelect),l=mh(Sd,n.__scopeSelect),[u,d]=w.useState(!1),f=ie(a,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollHeight-m.clientHeight,g=Math.ceil(m.scrollTop)<p;d(g)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(hS,K(N({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop+m.offsetHeight)}})):null});dS.displayName=Sd;var hS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,onAutoScroll:l}=p,u=et(p,["__scopeSelect","onAutoScroll"]),d=Va("SelectScrollButton",s),f=w.useRef(null),h=Wo(s),m=w.useCallback(()=>{f.current!==null&&(window.clearInterval(f.current),f.current=null)},[]);return w.useEffect(()=>()=>m(),[m]),we(()=>{var v;const g=h().find(b=>b.ref.current===document.activeElement);(v=g==null?void 0:g.ref.current)==null||v.scrollIntoView({block:"nearest"})},[h]),S.jsx(Pt.div,K(N({"aria-hidden":!0},u),{ref:a,style:N({flexShrink:0},u.style),onPointerDown:Bt(u.onPointerDown,()=>{f.current===null&&(f.current=window.setInterval(l,50))}),onPointerMove:Bt(u.onPointerMove,()=>{var g;(g=d.onItemLeave)==null||g.call(d),f.current===null&&(f.current=window.setInterval(l,50))}),onPointerLeave:Bt(u.onPointerLeave,()=>{m()})}))}),NO="SelectSeparator",_O=w.forwardRef((n,a)=>{const u=n,{__scopeSelect:s}=u,l=et(u,["__scopeSelect"]);return S.jsx(Pt.div,K(N({"aria-hidden":!0},l),{ref:a}))});_O.displayName=NO;var Td="SelectArrow",jO=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s}=h,l=et(h,["__scopeSelect"]),u=$o(s),d=ja(Td,s),f=Va(Td,s);return d.open&&f.position==="popper"?S.jsx(bD,K(N(N({},u),l),{ref:a})):null});jO.displayName=Td;var VO="SelectBubbleInput",mS=w.forwardRef((u,l)=>{var d=u,{__scopeSelect:n,value:a}=d,s=et(d,["__scopeSelect","value"]);const f=w.useRef(null),h=ie(l,f),m=AD(a);return w.useEffect(()=>{const p=f.current;if(!p)return;const g=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(g,"value").set;if(m!==a&&b){const T=new Event("change",{bubbles:!0});b.call(p,a),p.dispatchEvent(T)}},[m,a]),S.jsx(Pt.select,K(N({},s),{style:N(N({},Lx),s.style),ref:h,defaultValue:a}))});mS.displayName=VO;function pS(n){return n===""||n===void 0}function gS(n){const a=Da(n),s=w.useRef(""),l=w.useRef(0),u=w.useCallback(f=>{const h=s.current+f;a(h),function m(p){s.current=p,window.clearTimeout(l.current),p!==""&&(l.current=window.setTimeout(()=>m(""),1e3))}(h)},[a]),d=w.useCallback(()=>{s.current="",window.clearTimeout(l.current)},[]);return w.useEffect(()=>()=>window.clearTimeout(l.current),[]),[s,u,d]}function yS(n,a,s){const u=a.length>1&&Array.from(a).every(p=>p===a[0])?a[0]:a,d=s?n.indexOf(s):-1;let f=zO(n,Math.max(d,0));u.length===1&&(f=f.filter(p=>p!==s));const m=f.find(p=>p.textValue.toLowerCase().startsWith(u.toLowerCase()));return m!==s?m:void 0}function zO(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var LO=Kx,BO=Qx,UO=Ix,kO=Wx,HO=$x,PO=Jx,GO=aS,YO=lS,qO=oS,XO=cS,KO=fS,ZO=dS;/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QO=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),FO=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,s,l)=>l?l.toUpperCase():s.toLowerCase()),s0=n=>{const a=FO(n);return a.charAt(0).toUpperCase()+a.slice(1)},vS=(...n)=>n.filter((a,s,l)=>!!a&&a.trim()!==""&&l.indexOf(a)===s).join(" ").trim(),IO=n=>{for(const a in n)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var WO={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $O=w.forwardRef((p,m)=>{var g=p,{color:n="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:f}=g,h=et(g,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return w.createElement("svg",N(N(K(N({ref:m},WO),{width:a,height:a,stroke:n,strokeWidth:l?Number(s)*24/Number(a):s,className:vS("lucide",u)}),!d&&!IO(h)&&{"aria-hidden":"true"}),h),[...f.map(([v,b])=>w.createElement(v,b)),...Array.isArray(d)?d:[d]])});/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=(n,a)=>{const s=w.forwardRef((f,d)=>{var h=f,{className:l}=h,u=et(h,["className"]);return w.createElement($O,N({ref:d,iconNode:a,className:vS(`lucide-${QO(s0(n))}`,`lucide-${n}`,l)},u))});return s.displayName=s0(n),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JO=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],tN=Ce("building",JO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],nN=Ce("calendar",eN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],iN=Ce("check",aN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],bS=Ce("chevron-down",sN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],lN=Ce("chevron-up",rN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],r0=Ce("circle-alert",oN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],l0=Ce("circle-check-big",uN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],fN=Ce("log-out",cN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dN=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],hN=Ce("refresh-cw",dN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],pN=Ce("search",mN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gN=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],yN=Ce("settings",gN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vN=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],bN=Ce("sparkles",vN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],SN=Ce("user-plus",xN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TN=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],wN=Ce("user",TN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],EN=Ce("users",AN);function MN(a){var n=et(a,[]);return S.jsx(LO,N({"data-slot":"select"},n))}function CN(a){var n=et(a,[]);return S.jsx(UO,N({"data-slot":"select-value"},n))}function RN(u){var d=u,{className:n,size:a="default",children:s}=d,l=et(d,["className","size","children"]);return S.jsxs(BO,K(N({"data-slot":"select-trigger","data-size":a,className:Kt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},l),{children:[s,S.jsx(kO,{asChild:!0,children:S.jsx(bS,{className:"size-4 opacity-50"})})]}))}function DN(u){var d=u,{className:n,children:a,position:s="popper"}=d,l=et(d,["className","children","position"]);return S.jsx(HO,{children:S.jsxs(PO,K(N({"data-slot":"select-content",className:Kt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:s},l),{children:[S.jsx(NN,{}),S.jsx(GO,{className:Kt("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),S.jsx(_N,{})]}))})}function ON(l){var u=l,{className:n,children:a}=u,s=et(u,["className","children"]);return S.jsxs(YO,K(N({"data-slot":"select-item",className:Kt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n)},s),{children:[S.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:S.jsx(XO,{children:S.jsx(iN,{className:"size-4"})})}),S.jsx(qO,{children:a})]}))}function NN(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(KO,K(N({"data-slot":"select-scroll-up-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:S.jsx(lN,{className:"size-4"})}))}function _N(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(ZO,K(N({"data-slot":"select-scroll-down-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:S.jsx(bS,{className:"size-4"})}))}function o0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:S.jsx("table",N({"data-slot":"table",className:Kt("w-full caption-bottom text-sm",n)},a))})}function u0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("thead",N({"data-slot":"table-header",className:Kt("[&_tr]:border-b",n)},a))}function c0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("tbody",N({"data-slot":"table-body",className:Kt("[&_tr:last-child]:border-0",n)},a))}function Gf(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("tr",N({"data-slot":"table-row",className:Kt("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",n)},a))}function wn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("th",N({"data-slot":"table-head",className:Kt("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}function An(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("td",N({"data-slot":"table-cell",className:Kt("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}const jN=Ib("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function f0(u){var d=u,{className:n,variant:a,asChild:s=!1}=d,l=et(d,["className","variant","asChild"]);const f=s?Zb:"span";return S.jsx(f,N({"data-slot":"badge",className:Kt(jN({variant:a}),n)},l))}var Yf="rovingFocusGroup.onEntryFocus",VN={bubbles:!1,cancelable:!0},Qr="RovingFocusGroup",[wd,xS,zN]=ox(Qr),[LN,SS]=Xr(Qr,[zN]),[BN,UN]=LN(Qr),TS=w.forwardRef((n,a)=>S.jsx(wd.Provider,{scope:n.__scopeRovingFocusGroup,children:S.jsx(wd.Slot,{scope:n.__scopeRovingFocusGroup,children:S.jsx(kN,K(N({},n),{ref:a}))})}));TS.displayName=Qr;var kN=w.forwardRef((n,a)=>{const Y=n,{__scopeRovingFocusGroup:s,orientation:l,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:g=!1}=Y,v=et(Y,["__scopeRovingFocusGroup","orientation","loop","dir","currentTabStopId","defaultCurrentTabStopId","onCurrentTabStopIdChange","onEntryFocus","preventScrollOnEntryFocus"]),b=w.useRef(null),T=ie(a,b),C=ih(d),[M,A]=Po({prop:f,defaultProp:h!=null?h:null,onChange:m,caller:Qr}),[E,j]=w.useState(!1),O=Da(p),z=xS(s),_=w.useRef(!1),[I,tt]=w.useState(0);return w.useEffect(()=>{const W=b.current;if(W)return W.addEventListener(Yf,O),()=>W.removeEventListener(Yf,O)},[O]),S.jsx(BN,{scope:s,orientation:l,dir:C,loop:u,currentTabStopId:M,onItemFocus:w.useCallback(W=>A(W),[A]),onItemShiftTab:w.useCallback(()=>j(!0),[]),onFocusableItemAdd:w.useCallback(()=>tt(W=>W+1),[]),onFocusableItemRemove:w.useCallback(()=>tt(W=>W-1),[]),children:S.jsx(Pt.div,K(N({tabIndex:E||I===0?-1:0,"data-orientation":l},v),{ref:T,style:N({outline:"none"},n.style),onMouseDown:Bt(n.onMouseDown,()=>{_.current=!0}),onFocus:Bt(n.onFocus,W=>{const ft=!_.current;if(W.target===W.currentTarget&&ft&&!E){const ht=new CustomEvent(Yf,VN);if(W.currentTarget.dispatchEvent(ht),!ht.defaultPrevented){const lt=z().filter(q=>q.focusable),bt=lt.find(q=>q.active),Et=lt.find(q=>q.id===M),B=[bt,Et,...lt].filter(Boolean).map(q=>q.ref.current);ES(B,g)}}_.current=!1}),onBlur:Bt(n.onBlur,()=>j(!1))}))})}),wS="RovingFocusGroupItem",AS=w.forwardRef((n,a)=>{const A=n,{__scopeRovingFocusGroup:s,focusable:l=!0,active:u=!1,tabStopId:d,children:f}=A,h=et(A,["__scopeRovingFocusGroup","focusable","active","tabStopId","children"]),m=Kr(),p=d||m,g=UN(wS,s),v=g.currentTabStopId===p,b=xS(s),{onFocusableItemAdd:T,onFocusableItemRemove:C,currentTabStopId:M}=g;return w.useEffect(()=>{if(l)return T(),()=>C()},[l,T,C]),S.jsx(wd.ItemSlot,{scope:s,id:p,focusable:l,active:u,children:S.jsx(Pt.span,K(N({tabIndex:v?0:-1,"data-orientation":g.orientation},h),{ref:a,onMouseDown:Bt(n.onMouseDown,E=>{l?g.onItemFocus(p):E.preventDefault()}),onFocus:Bt(n.onFocus,()=>g.onItemFocus(p)),onKeyDown:Bt(n.onKeyDown,E=>{if(E.key==="Tab"&&E.shiftKey){g.onItemShiftTab();return}if(E.target!==E.currentTarget)return;const j=GN(E,g.orientation,g.dir);if(j!==void 0){if(E.metaKey||E.ctrlKey||E.altKey||E.shiftKey)return;E.preventDefault();let z=b().filter(_=>_.focusable).map(_=>_.ref.current);if(j==="last")z.reverse();else if(j==="prev"||j==="next"){j==="prev"&&z.reverse();const _=z.indexOf(E.currentTarget);z=g.loop?YN(z,_+1):z.slice(_+1)}setTimeout(()=>ES(z))}}),children:typeof f=="function"?f({isCurrentTabStop:v,hasTabStop:M!=null}):f}))})});AS.displayName=wS;var HN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function PN(n,a){return a!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function GN(n,a,s){const l=PN(n.key,s);if(!(a==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(a==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return HN[l]}function ES(n,a=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:a}),document.activeElement!==s))return}function YN(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var qN=TS,XN=AS;function KN(n,a){return w.useReducer((s,l)=>{const u=a[s][l];return u!=null?u:s},n)}var MS=n=>{const{present:a,children:s}=n,l=ZN(a),u=typeof s=="function"?s({present:l.isPresent}):w.Children.only(s),d=ie(l.ref,QN(u));return typeof s=="function"||l.isPresent?w.cloneElement(u,{ref:d}):null};MS.displayName="Presence";function ZN(n){const[a,s]=w.useState(),l=w.useRef(null),u=w.useRef(n),d=w.useRef("none"),f=n?"mounted":"unmounted",[h,m]=KN(f,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const p=To(l.current);d.current=h==="mounted"?p:"none"},[h]),we(()=>{const p=l.current,g=u.current;if(g!==n){const b=d.current,T=To(p);n?m("MOUNT"):T==="none"||(p==null?void 0:p.display)==="none"?m("UNMOUNT"):m(g&&b!==T?"ANIMATION_OUT":"UNMOUNT"),u.current=n}},[n,m]),we(()=>{var p;if(a){let g;const v=(p=a.ownerDocument.defaultView)!=null?p:window,b=C=>{const A=To(l.current).includes(C.animationName);if(C.target===a&&A&&(m("ANIMATION_END"),!u.current)){const E=a.style.animationFillMode;a.style.animationFillMode="forwards",g=v.setTimeout(()=>{a.style.animationFillMode==="forwards"&&(a.style.animationFillMode=E)})}},T=C=>{C.target===a&&(d.current=To(l.current))};return a.addEventListener("animationstart",T),a.addEventListener("animationcancel",b),a.addEventListener("animationend",b),()=>{v.clearTimeout(g),a.removeEventListener("animationstart",T),a.removeEventListener("animationcancel",b),a.removeEventListener("animationend",b)}}else m("ANIMATION_END")},[a,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(p=>{l.current=p?getComputedStyle(p):null,s(p)},[])}}function To(n){return(n==null?void 0:n.animationName)||"none"}function QN(n){var l,u;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var Jo="Tabs",[FN,o3]=Xr(Jo,[SS]),CS=SS(),[IN,ph]=FN(Jo),RS=w.forwardRef((n,a)=>{const T=n,{__scopeTabs:s,value:l,onValueChange:u,defaultValue:d,orientation:f="horizontal",dir:h,activationMode:m="automatic"}=T,p=et(T,["__scopeTabs","value","onValueChange","defaultValue","orientation","dir","activationMode"]),g=ih(h),[v,b]=Po({prop:l,onChange:u,defaultProp:d!=null?d:"",caller:Jo});return S.jsx(IN,{scope:s,baseId:Kr(),value:v,onValueChange:b,orientation:f,dir:g,activationMode:m,children:S.jsx(Pt.div,K(N({dir:g,"data-orientation":f},p),{ref:a}))})});RS.displayName=Jo;var DS="TabsList",OS=w.forwardRef((n,a)=>{const h=n,{__scopeTabs:s,loop:l=!0}=h,u=et(h,["__scopeTabs","loop"]),d=ph(DS,s),f=CS(s);return S.jsx(qN,K(N({asChild:!0},f),{orientation:d.orientation,dir:d.dir,loop:l,children:S.jsx(Pt.div,K(N({role:"tablist","aria-orientation":d.orientation},u),{ref:a}))}))});OS.displayName=DS;var NS="TabsTrigger",_S=w.forwardRef((n,a)=>{const v=n,{__scopeTabs:s,value:l,disabled:u=!1}=v,d=et(v,["__scopeTabs","value","disabled"]),f=ph(NS,s),h=CS(s),m=zS(f.baseId,l),p=LS(f.baseId,l),g=l===f.value;return S.jsx(XN,K(N({asChild:!0},h),{focusable:!u,active:g,children:S.jsx(Pt.button,K(N({type:"button",role:"tab","aria-selected":g,"aria-controls":p,"data-state":g?"active":"inactive","data-disabled":u?"":void 0,disabled:u,id:m},d),{ref:a,onMouseDown:Bt(n.onMouseDown,b=>{!u&&b.button===0&&b.ctrlKey===!1?f.onValueChange(l):b.preventDefault()}),onKeyDown:Bt(n.onKeyDown,b=>{[" ","Enter"].includes(b.key)&&f.onValueChange(l)}),onFocus:Bt(n.onFocus,()=>{const b=f.activationMode!=="manual";!g&&!u&&b&&f.onValueChange(l)})}))}))});_S.displayName=NS;var jS="TabsContent",VS=w.forwardRef((n,a)=>{const b=n,{__scopeTabs:s,value:l,forceMount:u,children:d}=b,f=et(b,["__scopeTabs","value","forceMount","children"]),h=ph(jS,s),m=zS(h.baseId,l),p=LS(h.baseId,l),g=l===h.value,v=w.useRef(g);return w.useEffect(()=>{const T=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(T)},[]),S.jsx(MS,{present:u||g,children:({present:T})=>S.jsx(Pt.div,K(N({"data-state":g?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":m,hidden:!T,id:p,tabIndex:0},f),{ref:a,style:K(N({},n.style),{animationDuration:v.current?"0s":void 0}),children:T&&d}))})});VS.displayName=jS;function zS(n,a){return`${n}-trigger-${a}`}function LS(n,a){return`${n}-content-${a}`}var WN=RS,$N=OS,JN=_S,t3=VS;function e3(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(WN,N({"data-slot":"tabs",className:Kt("flex flex-col gap-2",n)},a))}function n3(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx($N,N({"data-slot":"tabs-list",className:Kt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n)},a))}function wo(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(JN,N({"data-slot":"tabs-trigger",className:Kt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},a))}function Ao(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(t3,N({"data-slot":"tabs-content",className:Kt("flex-1 outline-none",n)},a))}function d0(n,a){const[s,l]=w.useState(()=>{try{const d=window.localStorage.getItem(n);return d?JSON.parse(d):a}catch(d){return console.error(`خطأ في قراءة ${n} من localStorage:`,d),a}});return[s,d=>{try{const f=d instanceof Function?d(s):d;l(f),window.localStorage.setItem(n,JSON.stringify(f))}catch(f){console.error(`خطأ في حفظ ${n} في localStorage:`,f)}}]}function a3(){const[n,a]=w.useState(!0),[s,l]=d0("work-attendance-employees",[]),[u,d]=w.useState({name:"",department:"",startMonth:""}),[f,h]=d0("work-attendance-search-filters",{name:"",department:"",startMonth:"",status:""}),m=["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"];w.useEffect(()=>{const E=setTimeout(()=>{a(!1)},4e3);return()=>clearTimeout(E)},[]);const p=E=>{const j=new Date().getMonth(),O=m.indexOf(E);return(j-O+12)%12>=3},g=E=>{if(E.preventDefault(),u.name&&u.department&&u.startMonth){const j=K(N({id:Date.now()},u),{status:p(u.startMonth)});l([...s,j]),d({name:"",department:"",startMonth:""}),C("✅ تم حفظ البيانات بنجاح!")}},v=()=>{l(s.map(E=>K(N({},E),{status:p(E.startMonth)})))},b=()=>{const E={employees:s,exportDate:new Date().toISOString(),version:"1.0"},j=JSON.stringify(E,null,2),O=new Blob([j],{type:"application/json"}),z=document.createElement("a");z.href=URL.createObjectURL(O),z.download=`work-attendance-backup-${new Date().toISOString().split("T")[0]}.json`,z.click(),C("📁 تم تصدير البيانات بنجاح!","success")},T=E=>{const j=E.target.files[0];if(!j)return;const O=new FileReader;O.onload=z=>{try{const _=JSON.parse(z.target.result);_.employees&&Array.isArray(_.employees)?(l(_.employees),C("📥 تم استيراد البيانات بنجاح!","success")):C("❌ ملف غير صالح!","error")}catch(_){console.error("خطأ في استيراد البيانات:",_),C("❌ خطأ في قراءة الملف!","error")}},O.readAsText(j),E.target.value=""},C=(E,j="success")=>{const O=document.createElement("div"),z=j==="success"?"bg-green-500":"bg-red-500";O.className=`fixed top-4 right-4 ${z} text-white px-6 py-3 rounded-lg shadow-lg z-50`,O.textContent=E,document.body.appendChild(O),setTimeout(()=>{document.body.contains(O)&&document.body.removeChild(O)},3e3)},M=()=>{window.confirm("هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!")&&(l([]),h({name:"",department:"",startMonth:"",status:""}),C("🗑️ تم مسح جميع البيانات!","success"))},A=s.filter(E=>(!f.name||E.name.includes(f.name))&&(!f.department||E.department.includes(f.department))&&(!f.startMonth||E.startMonth.includes(f.startMonth))&&(!f.status||f.status.includes("مطلوب")&&E.status||f.status.includes("تم")&&!E.status));return n?S.jsx("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:S.jsxs(vn.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1},className:"text-center",children:[S.jsx(vn.h1,{className:"text-6xl font-bold text-white mb-8 typewriter",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"🌟 برنامج استحقاق كشف عمل 🌟"}),S.jsx(vn.p,{className:"text-2xl text-white mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:"👨‍💻 المبرمج: علي عاجل خشان المحنّة"}),S.jsx(vn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:S.jsx(ni,{onClick:()=>a(!1),className:"pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4",children:"ابدأ الآن"})})]})}):S.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900",children:S.jsxs("div",{className:"flex",children:[S.jsxs(vn.div,{initial:{x:-300},animate:{x:0},className:"w-80 min-h-screen glassmorphism p-6",children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-2xl font-bold gradient-text mb-2",children:"برنامج استحقاق كشف عمل"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة"})]}),S.jsxs(ni,{variant:"destructive",className:"w-full mb-6 hover-lift",onClick:()=>window.location.reload(),children:[S.jsx(fN,{className:"mr-2 h-4 w-4"}),"خروج"]})]}),S.jsx("div",{className:"flex-1 p-8",children:S.jsxs(e3,{defaultValue:"input",className:"w-full",children:[S.jsxs(n3,{className:"grid w-full grid-cols-4 mb-8",children:[S.jsxs(wo,{value:"input",className:"flex items-center gap-2",children:[S.jsx(SN,{className:"h-4 w-4"}),"إدخال البيانات"]}),S.jsxs(wo,{value:"employees",className:"flex items-center gap-2",children:[S.jsx(EN,{className:"h-4 w-4"}),"المستحقين للكشوفات"]}),S.jsxs(wo,{value:"search",className:"flex items-center gap-2",children:[S.jsx(pN,{className:"h-4 w-4"}),"البحث المتقدم"]}),S.jsxs(wo,{value:"settings",className:"flex items-center gap-2",children:[S.jsx(yN,{className:"h-4 w-4"}),"الإعدادات"]})]}),S.jsx(Ao,{value:"input",children:S.jsx(vn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(bn,{className:"max-w-2xl mx-auto neomorphism hover-lift",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-center gradient-text text-2xl",children:"📥 إدخال بيانات موظف جديد"})}),S.jsx(Tn,{children:S.jsxs("form",{onSubmit:g,className:"space-y-6",children:[S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"name",className:"flex items-center gap-2",children:[S.jsx(wN,{className:"h-4 w-4"}),"اسم الموظف"]}),S.jsx(ts,{id:"name",value:u.name,onChange:E=>d(K(N({},u),{name:E.target.value})),placeholder:"أدخل اسم الموظف",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"department",className:"flex items-center gap-2",children:[S.jsx(tN,{className:"h-4 w-4"}),"القسم"]}),S.jsx(ts,{id:"department",value:u.department,onChange:E=>d(K(N({},u),{department:E.target.value})),placeholder:"أدخل اسم القسم",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"startMonth",className:"flex items-center gap-2",children:[S.jsx(nN,{className:"h-4 w-4"}),"أول شهر للكشف 🌙"]}),S.jsxs(MN,{value:u.startMonth,onValueChange:E=>d(K(N({},u),{startMonth:E})),required:!0,children:[S.jsx(RN,{children:S.jsx(CN,{placeholder:"اختر الشهر"})}),S.jsx(DN,{children:m.map(E=>S.jsx(ON,{value:E,children:E},E))})]})]}),S.jsx(ni,{type:"submit",className:"w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6",children:"➕ حفظ البيانات"})]})})]})})}),S.jsx(Ao,{value:"employees",children:S.jsx(vn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(bn,{className:"neomorphism",children:[S.jsxs(xn,{className:"flex flex-row items-center justify-between",children:[S.jsx(Sn,{className:"gradient-text text-2xl",children:"📄 المستحقين للكشوفات"}),S.jsxs(ni,{onClick:v,className:"bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500",children:[S.jsx(hN,{className:"mr-2 h-4 w-4"}),"🔁 تحديث الكشفات"]})]}),S.jsx(Tn,{children:s.length===0?S.jsxs("div",{className:"text-center py-12",children:[S.jsx(bN,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),S.jsx("p",{className:"text-gray-500 text-lg",children:"لا توجد بيانات موظفين حتى الآن"})]}):S.jsxs(o0,{children:[S.jsx(u0,{children:S.jsxs(Gf,{children:[S.jsx(wn,{className:"text-right",children:"#"}),S.jsx(wn,{className:"text-right",children:"اسم الموظف"}),S.jsx(wn,{className:"text-right",children:"القسم"}),S.jsx(wn,{className:"text-right",children:"أول شهر"}),S.jsx(wn,{className:"text-right",children:"الحالة"})]})}),S.jsx(c0,{children:s.map((E,j)=>S.jsxs(vn.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:j*.1},className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[S.jsx(An,{children:j+1}),S.jsx(An,{className:"font-medium",children:E.name}),S.jsx(An,{children:E.department}),S.jsx(An,{children:E.startMonth}),S.jsx(An,{children:S.jsx(f0,{className:E.status?"status-required":"status-completed",children:E.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})})]},E.id))})]})})]})})}),S.jsx(Ao,{value:"search",children:S.jsxs(vn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[S.jsxs(bn,{className:"neomorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"gradient-text text-2xl",children:"🔍 البحث المتقدم"})}),S.jsx(Tn,{children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[S.jsxs("div",{children:[S.jsx(ai,{children:"البحث بالاسم"}),S.jsx(ts,{placeholder:"🔎 اسم الموظف",value:f.name,onChange:E=>h(K(N({},f),{name:E.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"القسم"}),S.jsx(ts,{placeholder:"🏢 اسم القسم",value:f.department,onChange:E=>h(K(N({},f),{department:E.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"أول شهر"}),S.jsx(ts,{placeholder:"🗓️ اسم الشهر",value:f.startMonth,onChange:E=>h(K(N({},f),{startMonth:E.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"الحالة"}),S.jsx(ts,{placeholder:"🔍 الحالة (مطلوب/تم)",value:f.status,onChange:E=>h(K(N({},f),{status:E.target.value})),className:"text-right"})]})]})})]}),S.jsxs(bn,{className:"neomorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{children:"نتائج البحث"})}),S.jsx(Tn,{children:A.length===0?S.jsx("div",{className:"text-center py-8",children:S.jsx("p",{className:"text-gray-500",children:"🚫 لا توجد بيانات مطابقة!"})}):S.jsxs(o0,{children:[S.jsx(u0,{children:S.jsxs(Gf,{children:[S.jsx(wn,{className:"text-right",children:"#"}),S.jsx(wn,{className:"text-right",children:"اسم الموظف"}),S.jsx(wn,{className:"text-right",children:"القسم"}),S.jsx(wn,{className:"text-right",children:"أول شهر"}),S.jsx(wn,{className:"text-right",children:"الحالة"})]})}),S.jsx(c0,{children:A.map((E,j)=>S.jsxs(Gf,{children:[S.jsx(An,{children:j+1}),S.jsx(An,{className:"font-medium",children:E.name}),S.jsx(An,{children:E.department}),S.jsx(An,{children:E.startMonth}),S.jsx(An,{children:S.jsx(f0,{className:E.status?"status-required":"status-completed",children:E.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})})]},E.id))})]})})]})]})}),S.jsx(Ao,{value:"settings",children:S.jsx(vn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto space-y-6",children:S.jsxs(bn,{className:"neomorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"gradient-text text-2xl",children:"⚙️ الإعدادات"})}),S.jsx(Tn,{className:"space-y-6",children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[S.jsxs(bn,{className:"glassmorphism md:col-span-2",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-lg",children:"💾 إدارة البيانات"})}),S.jsxs(Tn,{children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[S.jsx(ni,{onClick:b,className:"bg-blue-500 hover:bg-blue-600 text-white",children:"📁 تصدير البيانات"}),S.jsxs("div",{children:[S.jsx("input",{type:"file",accept:".json",onChange:T,style:{display:"none"},id:"import-file"}),S.jsx(ni,{onClick:()=>document.getElementById("import-file").click(),className:"bg-green-500 hover:bg-green-600 text-white w-full",children:"📥 استيراد البيانات"})]}),S.jsx(ni,{onClick:M,className:"bg-red-500 hover:bg-red-600 text-white",children:"🗑️ مسح جميع البيانات"})]}),S.jsxs("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[S.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 ",S.jsx("strong",{children:"نصائح:"})]}),S.jsxs("ul",{className:"text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1",children:[S.jsx("li",{children:"• يتم حفظ البيانات تلقائياً في متصفحك"}),S.jsx("li",{children:"• استخدم التصدير لإنشاء نسخة احتياطية"}),S.jsx("li",{children:"• يمكنك استيراد البيانات من ملف JSON"}),S.jsx("li",{children:"• البيانات محفوظة حتى لو أغلقت المتصفح"})]})]})]})]}),S.jsxs(bn,{className:"glassmorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-lg",children:"📝 نبذة عن البرنامج"})}),S.jsx(Tn,{children:S.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات."})})]}),S.jsxs(bn,{className:"glassmorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-lg",children:"👨‍💻 عن المبرمج"})}),S.jsx(Tn,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"علي عاجل خشان المحنّة"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"مبرمج محترف بخبرة واسعة في:"}),S.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[S.jsx("li",{children:"• VB.NET / C# / Java"}),S.jsx("li",{children:"• JavaScript / React / Node.js"}),S.jsx("li",{children:"• Microsoft Access و Excel VBA"}),S.jsx("li",{children:"• أنظمة الأرشفة الذكية والتحكم الآلي"})]})]})})]}),S.jsxs(bn,{className:"glassmorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-lg",children:"🆔 إصدار البرنامج"})}),S.jsx(Tn,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"v2.0.0"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات."})]})})]}),S.jsxs(bn,{className:"glassmorphism",children:[S.jsx(xn,{children:S.jsx(Sn,{className:"text-lg",children:"🎖️ حقوق التصميم"})}),S.jsx(Tn,{children:S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"جميع الحقوق محفوظة © 2025"})})]})]})})]})})})]})})]})})}pw.createRoot(document.getElementById("root")).render(S.jsx(w.StrictMode,{children:S.jsx(a3,{})}))});export default i3();
