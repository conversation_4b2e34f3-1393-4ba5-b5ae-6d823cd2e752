function KT(n,a){for(var s=0;s<a.length;s++){const l=a[s];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const d=Object.getOwnPropertyDescriptor(l,u);d&&Object.defineProperty(n,u,d.get?d:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function s(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(u){if(u.ep)return;u.ep=!0;const d=s(u);fetch(u.href,d)}})();function a0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var sf={exports:{}},hr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jg;function ZT(){if(Jg)return hr;Jg=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(l,u,d){var f=null;if(d!==void 0&&(f=""+d),u.key!==void 0&&(f=""+u.key),"key"in u){d={};for(var h in u)h!=="key"&&(d[h]=u[h])}else d=u;return u=d.ref,{$$typeof:n,type:l,key:f,ref:u!==void 0?u:null,props:d}}return hr.Fragment=a,hr.jsx=s,hr.jsxs=s,hr}var ty;function QT(){return ty||(ty=1,sf.exports=ZT()),sf.exports}var S=QT(),rf={exports:{}},bt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey;function FT(){if(ey)return bt;ey=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function x(R){return R===null||typeof R!="object"?null:(R=v&&R[v]||R["@@iterator"],typeof R=="function"?R:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,w={};function M(R,X,W){this.props=R,this.context=X,this.refs=w,this.updater=W||A}M.prototype.isReactComponent={},M.prototype.setState=function(R,X){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,X,"setState")},M.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function E(){}E.prototype=M.prototype;function _(R,X,W){this.props=R,this.context=X,this.refs=w,this.updater=W||A}var D=_.prototype=new E;D.constructor=_,O(D,M.prototype),D.isPureReactComponent=!0;var H=Array.isArray,z={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function Q(R,X,W,I,J,gt){return W=gt.ref,{$$typeof:n,type:R,key:X,ref:W!==void 0?W:null,props:gt}}function G(R,X){return Q(R.type,X,void 0,void 0,void 0,R.props)}function tt(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function rt(R){var X={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(W){return X[W]})}var ht=/\/+/g;function ct(R,X){return typeof R=="object"&&R!==null&&R.key!=null?rt(""+R.key):X.toString(36)}function yt(){}function vt(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(yt,yt):(R.status="pending",R.then(function(X){R.status==="pending"&&(R.status="fulfilled",R.value=X)},function(X){R.status==="pending"&&(R.status="rejected",R.reason=X)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function ot(R,X,W,I,J){var gt=typeof R;(gt==="undefined"||gt==="boolean")&&(R=null);var lt=!1;if(R===null)lt=!0;else switch(gt){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(R.$$typeof){case n:case a:lt=!0;break;case g:return lt=R._init,ot(lt(R._payload),X,W,I,J)}}if(lt)return J=J(R),lt=I===""?"."+ct(R,0):I,H(J)?(W="",lt!=null&&(W=lt.replace(ht,"$&/")+"/"),ot(J,X,W,"",function(Nt){return Nt})):J!=null&&(tt(J)&&(J=G(J,W+(J.key==null||R&&R.key===J.key?"":(""+J.key).replace(ht,"$&/")+"/")+lt)),X.push(J)),1;lt=0;var $=I===""?".":I+":";if(H(R))for(var ut=0;ut<R.length;ut++)I=R[ut],gt=$+ct(I,ut),lt+=ot(I,X,W,gt,J);else if(ut=x(R),typeof ut=="function")for(R=ut.call(R),ut=0;!(I=R.next()).done;)I=I.value,gt=$+ct(I,ut++),lt+=ot(I,X,W,gt,J);else if(gt==="object"){if(typeof R.then=="function")return ot(vt(R),X,W,I,J);throw X=String(R),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return lt}function V(R,X,W){if(R==null)return R;var I=[],J=0;return ot(R,I,"","",function(gt){return X.call(W,gt,J++)}),I}function Y(R){if(R._status===-1){var X=R._result;X=X(),X.then(function(W){(R._status===0||R._status===-1)&&(R._status=1,R._result=W)},function(W){(R._status===0||R._status===-1)&&(R._status=2,R._result=W)}),R._status===-1&&(R._status=0,R._result=X)}if(R._status===1)return R._result.default;throw R._result}var k=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function it(){}return bt.Children={map:V,forEach:function(R,X,W){V(R,function(){X.apply(this,arguments)},W)},count:function(R){var X=0;return V(R,function(){X++}),X},toArray:function(R){return V(R,function(X){return X})||[]},only:function(R){if(!tt(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},bt.Component=M,bt.Fragment=s,bt.Profiler=u,bt.PureComponent=_,bt.StrictMode=l,bt.Suspense=p,bt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=z,bt.__COMPILER_RUNTIME={__proto__:null,c:function(R){return z.H.useMemoCache(R)}},bt.cache=function(R){return function(){return R.apply(null,arguments)}},bt.cloneElement=function(R,X,W){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var I=O({},R.props),J=R.key,gt=void 0;if(X!=null)for(lt in X.ref!==void 0&&(gt=void 0),X.key!==void 0&&(J=""+X.key),X)!F.call(X,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&X.ref===void 0||(I[lt]=X[lt]);var lt=arguments.length-2;if(lt===1)I.children=W;else if(1<lt){for(var $=Array(lt),ut=0;ut<lt;ut++)$[ut]=arguments[ut+2];I.children=$}return Q(R.type,J,void 0,void 0,gt,I)},bt.createContext=function(R){return R={$$typeof:f,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:d,_context:R},R},bt.createElement=function(R,X,W){var I,J={},gt=null;if(X!=null)for(I in X.key!==void 0&&(gt=""+X.key),X)F.call(X,I)&&I!=="key"&&I!=="__self"&&I!=="__source"&&(J[I]=X[I]);var lt=arguments.length-2;if(lt===1)J.children=W;else if(1<lt){for(var $=Array(lt),ut=0;ut<lt;ut++)$[ut]=arguments[ut+2];J.children=$}if(R&&R.defaultProps)for(I in lt=R.defaultProps,lt)J[I]===void 0&&(J[I]=lt[I]);return Q(R,gt,void 0,void 0,null,J)},bt.createRef=function(){return{current:null}},bt.forwardRef=function(R){return{$$typeof:h,render:R}},bt.isValidElement=tt,bt.lazy=function(R){return{$$typeof:g,_payload:{_status:-1,_result:R},_init:Y}},bt.memo=function(R,X){return{$$typeof:m,type:R,compare:X===void 0?null:X}},bt.startTransition=function(R){var X=z.T,W={};z.T=W;try{var I=R(),J=z.S;J!==null&&J(W,I),typeof I=="object"&&I!==null&&typeof I.then=="function"&&I.then(it,k)}catch(gt){k(gt)}finally{z.T=X}},bt.unstable_useCacheRefresh=function(){return z.H.useCacheRefresh()},bt.use=function(R){return z.H.use(R)},bt.useActionState=function(R,X,W){return z.H.useActionState(R,X,W)},bt.useCallback=function(R,X){return z.H.useCallback(R,X)},bt.useContext=function(R){return z.H.useContext(R)},bt.useDebugValue=function(){},bt.useDeferredValue=function(R,X){return z.H.useDeferredValue(R,X)},bt.useEffect=function(R,X,W){var I=z.H;if(typeof W=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return I.useEffect(R,X)},bt.useId=function(){return z.H.useId()},bt.useImperativeHandle=function(R,X,W){return z.H.useImperativeHandle(R,X,W)},bt.useInsertionEffect=function(R,X){return z.H.useInsertionEffect(R,X)},bt.useLayoutEffect=function(R,X){return z.H.useLayoutEffect(R,X)},bt.useMemo=function(R,X){return z.H.useMemo(R,X)},bt.useOptimistic=function(R,X){return z.H.useOptimistic(R,X)},bt.useReducer=function(R,X,W){return z.H.useReducer(R,X,W)},bt.useRef=function(R){return z.H.useRef(R)},bt.useState=function(R){return z.H.useState(R)},bt.useSyncExternalStore=function(R,X,W){return z.H.useSyncExternalStore(R,X,W)},bt.useTransition=function(){return z.H.useTransition()},bt.version="19.1.0",bt}var ny;function gd(){return ny||(ny=1,rf.exports=FT()),rf.exports}var T=gd();const va=a0(T),i0=KT({__proto__:null,default:va},[T]);var lf={exports:{}},mr={},of={exports:{}},uf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ay;function IT(){return ay||(ay=1,function(n){function a(V,Y){var k=V.length;V.push(Y);t:for(;0<k;){var it=k-1>>>1,R=V[it];if(0<u(R,Y))V[it]=Y,V[k]=R,k=it;else break t}}function s(V){return V.length===0?null:V[0]}function l(V){if(V.length===0)return null;var Y=V[0],k=V.pop();if(k!==Y){V[0]=k;t:for(var it=0,R=V.length,X=R>>>1;it<X;){var W=2*(it+1)-1,I=V[W],J=W+1,gt=V[J];if(0>u(I,k))J<R&&0>u(gt,I)?(V[it]=gt,V[J]=k,it=J):(V[it]=I,V[W]=k,it=W);else if(J<R&&0>u(gt,k))V[it]=gt,V[J]=k,it=J;else break t}}return Y}function u(V,Y){var k=V.sortIndex-Y.sortIndex;return k!==0?k:V.id-Y.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();n.unstable_now=function(){return f.now()-h}}var p=[],m=[],g=1,v=null,x=3,A=!1,O=!1,w=!1,M=!1,E=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;function H(V){for(var Y=s(m);Y!==null;){if(Y.callback===null)l(m);else if(Y.startTime<=V)l(m),Y.sortIndex=Y.expirationTime,a(p,Y);else break;Y=s(m)}}function z(V){if(w=!1,H(V),!O)if(s(p)!==null)O=!0,F||(F=!0,ct());else{var Y=s(m);Y!==null&&ot(z,Y.startTime-V)}}var F=!1,Q=-1,G=5,tt=-1;function rt(){return M?!0:!(n.unstable_now()-tt<G)}function ht(){if(M=!1,F){var V=n.unstable_now();tt=V;var Y=!0;try{t:{O=!1,w&&(w=!1,_(Q),Q=-1),A=!0;var k=x;try{e:{for(H(V),v=s(p);v!==null&&!(v.expirationTime>V&&rt());){var it=v.callback;if(typeof it=="function"){v.callback=null,x=v.priorityLevel;var R=it(v.expirationTime<=V);if(V=n.unstable_now(),typeof R=="function"){v.callback=R,H(V),Y=!0;break e}v===s(p)&&l(p),H(V)}else l(p);v=s(p)}if(v!==null)Y=!0;else{var X=s(m);X!==null&&ot(z,X.startTime-V),Y=!1}}break t}finally{v=null,x=k,A=!1}Y=void 0}}finally{Y?ct():F=!1}}}var ct;if(typeof D=="function")ct=function(){D(ht)};else if(typeof MessageChannel<"u"){var yt=new MessageChannel,vt=yt.port2;yt.port1.onmessage=ht,ct=function(){vt.postMessage(null)}}else ct=function(){E(ht,0)};function ot(V,Y){Q=E(function(){V(n.unstable_now())},Y)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(V){V.callback=null},n.unstable_forceFrameRate=function(V){0>V||125<V?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<V?Math.floor(1e3/V):5},n.unstable_getCurrentPriorityLevel=function(){return x},n.unstable_next=function(V){switch(x){case 1:case 2:case 3:var Y=3;break;default:Y=x}var k=x;x=Y;try{return V()}finally{x=k}},n.unstable_requestPaint=function(){M=!0},n.unstable_runWithPriority=function(V,Y){switch(V){case 1:case 2:case 3:case 4:case 5:break;default:V=3}var k=x;x=V;try{return Y()}finally{x=k}},n.unstable_scheduleCallback=function(V,Y,k){var it=n.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?it+k:it):k=it,V){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=k+R,V={id:g++,callback:Y,priorityLevel:V,startTime:k,expirationTime:R,sortIndex:-1},k>it?(V.sortIndex=k,a(m,V),s(p)===null&&V===s(m)&&(w?(_(Q),Q=-1):w=!0,ot(z,k-it))):(V.sortIndex=R,a(p,V),O||A||(O=!0,F||(F=!0,ct()))),V},n.unstable_shouldYield=rt,n.unstable_wrapCallback=function(V){var Y=x;return function(){var k=x;x=Y;try{return V.apply(this,arguments)}finally{x=k}}}}(uf)),uf}var iy;function WT(){return iy||(iy=1,of.exports=IT()),of.exports}var cf={exports:{}},ge={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sy;function $T(){if(sy)return ge;sy=1;var n=gd();function a(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function d(p,m,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:v==null?null:""+v,children:p,containerInfo:m,implementation:g}}var f=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ge.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,ge.createPortal=function(p,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return d(p,m,null,g)},ge.flushSync=function(p){var m=f.T,g=l.p;try{if(f.T=null,l.p=2,p)return p()}finally{f.T=m,l.p=g,l.d.f()}},ge.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,l.d.C(p,m))},ge.prefetchDNS=function(p){typeof p=="string"&&l.d.D(p)},ge.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var g=m.as,v=h(g,m.crossOrigin),x=typeof m.integrity=="string"?m.integrity:void 0,A=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?l.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:v,integrity:x,fetchPriority:A}):g==="script"&&l.d.X(p,{crossOrigin:v,integrity:x,fetchPriority:A,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ge.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=h(m.as,m.crossOrigin);l.d.M(p,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&l.d.M(p)},ge.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,v=h(g,m.crossOrigin);l.d.L(p,g,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ge.preloadModule=function(p,m){if(typeof p=="string")if(m){var g=h(m.as,m.crossOrigin);l.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else l.d.m(p)},ge.requestFormReset=function(p){l.d.r(p)},ge.unstable_batchedUpdates=function(p,m){return p(m)},ge.useFormState=function(p,m,g){return f.H.useFormState(p,m,g)},ge.useFormStatus=function(){return f.H.useHostTransitionStatus()},ge.version="19.1.0",ge}var ry;function s0(){if(ry)return cf.exports;ry=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),cf.exports=$T(),cf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ly;function JT(){if(ly)return mr;ly=1;var n=WT(),a=gd(),s=s0();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(d(t)!==t)throw Error(l(188))}function p(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(l(188));return e!==t?null:t}for(var i=t,r=e;;){var o=i.return;if(o===null)break;var c=o.alternate;if(c===null){if(r=o.return,r!==null){i=r;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===i)return h(o),t;if(c===r)return h(o),e;c=c.sibling}throw Error(l(188))}if(i.return!==r.return)i=o,r=c;else{for(var y=!1,b=o.child;b;){if(b===i){y=!0,i=o,r=c;break}if(b===r){y=!0,r=o,i=c;break}b=b.sibling}if(!y){for(b=c.child;b;){if(b===i){y=!0,i=c,r=o;break}if(b===r){y=!0,r=c,i=o;break}b=b.sibling}if(!y)throw Error(l(189))}}if(i.alternate!==r)throw Error(l(190))}if(i.tag!==3)throw Error(l(188));return i.stateNode.current===i?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),A=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),w=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),_=Symbol.for("react.consumer"),D=Symbol.for("react.context"),H=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),tt=Symbol.for("react.activity"),rt=Symbol.for("react.memo_cache_sentinel"),ht=Symbol.iterator;function ct(t){return t===null||typeof t!="object"?null:(t=ht&&t[ht]||t["@@iterator"],typeof t=="function"?t:null)}var yt=Symbol.for("react.client.reference");function vt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===yt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case O:return"Fragment";case M:return"Profiler";case w:return"StrictMode";case z:return"Suspense";case F:return"SuspenseList";case tt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case A:return"Portal";case D:return(t.displayName||"Context")+".Provider";case _:return(t._context.displayName||"Context")+".Consumer";case H:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Q:return e=t.displayName||null,e!==null?e:vt(t.type)||"Memo";case G:e=t._payload,t=t._init;try{return vt(t(e))}catch{}}return null}var ot=Array.isArray,V=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k={pending:!1,data:null,method:null,action:null},it=[],R=-1;function X(t){return{current:t}}function W(t){0>R||(t.current=it[R],it[R]=null,R--)}function I(t,e){R++,it[R]=t.current,t.current=e}var J=X(null),gt=X(null),lt=X(null),$=X(null);function ut(t,e){switch(I(lt,e),I(gt,t),I(J,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Cg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Cg(e),t=Rg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}W(J),I(J,t)}function Nt(){W(J),W(gt),W(lt)}function Ct(t){t.memoizedState!==null&&I($,t);var e=J.current,i=Rg(e,t.type);e!==i&&(I(gt,t),I(J,i))}function wt(t){gt.current===t&&(W(J),W(gt)),$.current===t&&(W($),or._currentValue=k)}var At=Object.prototype.hasOwnProperty,oe=n.unstable_scheduleCallback,we=n.unstable_cancelCallback,Da=n.unstable_shouldYield,Oa=n.unstable_requestPaint,he=n.unstable_now,Ko=n.unstable_getCurrentPriorityLevel,Na=n.unstable_ImmediatePriority,oh=n.unstable_UserBlockingPriority,Yr=n.unstable_NormalPriority,CS=n.unstable_LowPriority,uh=n.unstable_IdlePriority,RS=n.log,DS=n.unstable_setDisableYieldValue,gs=null,Oe=null;function Zn(t){if(typeof RS=="function"&&DS(t),Oe&&typeof Oe.setStrictMode=="function")try{Oe.setStrictMode(gs,t)}catch{}}var Ne=Math.clz32?Math.clz32:_S,OS=Math.log,NS=Math.LN2;function _S(t){return t>>>=0,t===0?32:31-(OS(t)/NS|0)|0}var qr=256,Xr=4194304;function _a(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kr(t,e,i){var r=t.pendingLanes;if(r===0)return 0;var o=0,c=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var b=r&134217727;return b!==0?(r=b&~c,r!==0?o=_a(r):(y&=b,y!==0?o=_a(y):i||(i=b&~t,i!==0&&(o=_a(i))))):(b=r&~c,b!==0?o=_a(b):y!==0?o=_a(y):i||(i=r&~t,i!==0&&(o=_a(i)))),o===0?0:e!==0&&e!==o&&(e&c)===0&&(c=o&-o,i=e&-e,c>=i||c===32&&(i&4194048)!==0)?e:o}function ys(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function jS(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ch(){var t=qr;return qr<<=1,(qr&4194048)===0&&(qr=256),t}function fh(){var t=Xr;return Xr<<=1,(Xr&62914560)===0&&(Xr=4194304),t}function Zo(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function vs(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function VS(t,e,i,r,o,c){var y=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var b=t.entanglements,C=t.expirationTimes,B=t.hiddenUpdates;for(i=y&~i;0<i;){var q=31-Ne(i),Z=1<<q;b[q]=0,C[q]=-1;var U=B[q];if(U!==null)for(B[q]=null,q=0;q<U.length;q++){var P=U[q];P!==null&&(P.lane&=-536870913)}i&=~Z}r!==0&&dh(t,r,0),c!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=c&~(y&~e))}function dh(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-Ne(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|i&4194090}function hh(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var r=31-Ne(i),o=1<<r;o&e|t[r]&e&&(t[r]|=e),i&=~o}}function Qo(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Fo(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function mh(){var t=Y.p;return t!==0?t:(t=window.event,t===void 0?32:Zg(t.type))}function zS(t,e){var i=Y.p;try{return Y.p=t,e()}finally{Y.p=i}}var Qn=Math.random().toString(36).slice(2),me="__reactFiber$"+Qn,Ae="__reactProps$"+Qn,oi="__reactContainer$"+Qn,Io="__reactEvents$"+Qn,LS="__reactListeners$"+Qn,BS="__reactHandles$"+Qn,ph="__reactResources$"+Qn,bs="__reactMarker$"+Qn;function Wo(t){delete t[me],delete t[Ae],delete t[Io],delete t[LS],delete t[BS]}function ui(t){var e=t[me];if(e)return e;for(var i=t.parentNode;i;){if(e=i[oi]||i[me]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=_g(t);t!==null;){if(i=t[me])return i;t=_g(t)}return e}t=i,i=t.parentNode}return null}function ci(t){if(t=t[me]||t[oi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function xs(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function fi(t){var e=t[ph];return e||(e=t[ph]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ie(t){t[bs]=!0}var gh=new Set,yh={};function ja(t,e){di(t,e),di(t+"Capture",e)}function di(t,e){for(yh[t]=e,t=0;t<e.length;t++)gh.add(e[t])}var US=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),vh={},bh={};function kS(t){return At.call(bh,t)?!0:At.call(vh,t)?!1:US.test(t)?bh[t]=!0:(vh[t]=!0,!1)}function Zr(t,e,i){if(kS(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function Qr(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function Mn(t,e,i,r){if(r===null)t.removeAttribute(i);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+r)}}var $o,xh;function hi(t){if($o===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);$o=e&&e[1]||"",xh=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+$o+t+xh}var Jo=!1;function tu(t,e){if(!t||Jo)return"";Jo=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var Z=function(){throw Error()};if(Object.defineProperty(Z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Z,[])}catch(P){var U=P}Reflect.construct(t,[],Z)}else{try{Z.call()}catch(P){U=P}t.call(Z.prototype)}}else{try{throw Error()}catch(P){U=P}(Z=t())&&typeof Z.catch=="function"&&Z.catch(function(){})}}catch(P){if(P&&U&&typeof P.stack=="string")return[P.stack,U.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=r.DetermineComponentFrameRoot(),y=c[0],b=c[1];if(y&&b){var C=y.split(`
`),B=b.split(`
`);for(o=r=0;r<C.length&&!C[r].includes("DetermineComponentFrameRoot");)r++;for(;o<B.length&&!B[o].includes("DetermineComponentFrameRoot");)o++;if(r===C.length||o===B.length)for(r=C.length-1,o=B.length-1;1<=r&&0<=o&&C[r]!==B[o];)o--;for(;1<=r&&0<=o;r--,o--)if(C[r]!==B[o]){if(r!==1||o!==1)do if(r--,o--,0>o||C[r]!==B[o]){var q=`
`+C[r].replace(" at new "," at ");return t.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",t.displayName)),q}while(1<=r&&0<=o);break}}}finally{Jo=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?hi(i):""}function HS(t){switch(t.tag){case 26:case 27:case 5:return hi(t.type);case 16:return hi("Lazy");case 13:return hi("Suspense");case 19:return hi("SuspenseList");case 0:case 15:return tu(t.type,!1);case 11:return tu(t.type.render,!1);case 1:return tu(t.type,!0);case 31:return hi("Activity");default:return""}}function Sh(t){try{var e="";do e+=HS(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Pe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Th(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function PS(t){var e=Th(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var o=i.get,c=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){r=""+y,c.call(this,y)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fr(t){t._valueTracker||(t._valueTracker=PS(t))}function wh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),r="";return t&&(r=Th(t)?t.checked?"true":"false":t.value),t=r,t!==i?(e.setValue(t),!0):!1}function Ir(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var GS=/[\n"\\]/g;function Ge(t){return t.replace(GS,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function eu(t,e,i,r,o,c,y,b){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Pe(e)):t.value!==""+Pe(e)&&(t.value=""+Pe(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?nu(t,y,Pe(e)):i!=null?nu(t,y,Pe(i)):r!=null&&t.removeAttribute("value"),o==null&&c!=null&&(t.defaultChecked=!!c),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?t.name=""+Pe(b):t.removeAttribute("name")}function Ah(t,e,i,r,o,c,y,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||i!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;i=i!=null?""+Pe(i):"",e=e!=null?""+Pe(e):i,b||e===t.value||(t.value=e),t.defaultValue=e}r=r??o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=b?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function nu(t,e,i){e==="number"&&Ir(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function mi(t,e,i,r){if(t=t.options,e){e={};for(var o=0;o<i.length;o++)e["$"+i[o]]=!0;for(i=0;i<t.length;i++)o=e.hasOwnProperty("$"+t[i].value),t[i].selected!==o&&(t[i].selected=o),o&&r&&(t[i].defaultSelected=!0)}else{for(i=""+Pe(i),e=null,o=0;o<t.length;o++){if(t[o].value===i){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Eh(t,e,i){if(e!=null&&(e=""+Pe(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Pe(i):""}function Mh(t,e,i,r){if(e==null){if(r!=null){if(i!=null)throw Error(l(92));if(ot(r)){if(1<r.length)throw Error(l(93));r=r[0]}i=r}i==null&&(i=""),e=i}i=Pe(e),t.defaultValue=i,r=t.textContent,r===i&&r!==""&&r!==null&&(t.value=r)}function pi(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var YS=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ch(t,e,i){var r=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,i):typeof i!="number"||i===0||YS.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function Rh(t,e,i){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,i!=null){for(var r in i)!i.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var o in e)r=e[o],e.hasOwnProperty(o)&&i[o]!==r&&Ch(t,o,r)}else for(var c in e)e.hasOwnProperty(c)&&Ch(t,c,e[c])}function au(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qS=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),XS=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Wr(t){return XS.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var iu=null;function su(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var gi=null,yi=null;function Dh(t){var e=ci(t);if(e&&(t=e.stateNode)){var i=t[Ae]||null;t:switch(t=e.stateNode,e.type){case"input":if(eu(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Ge(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var r=i[e];if(r!==t&&r.form===t.form){var o=r[Ae]||null;if(!o)throw Error(l(90));eu(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<i.length;e++)r=i[e],r.form===t.form&&wh(r)}break t;case"textarea":Eh(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&mi(t,!!i.multiple,e,!1)}}}var ru=!1;function Oh(t,e,i){if(ru)return t(e,i);ru=!0;try{var r=t(e);return r}finally{if(ru=!1,(gi!==null||yi!==null)&&(Ll(),gi&&(e=gi,t=yi,yi=gi=null,Dh(e),t)))for(e=0;e<t.length;e++)Dh(t[e])}}function Ss(t,e){var i=t.stateNode;if(i===null)return null;var r=i[Ae]||null;if(r===null)return null;i=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(l(231,e,typeof i));return i}var Cn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),lu=!1;if(Cn)try{var Ts={};Object.defineProperty(Ts,"passive",{get:function(){lu=!0}}),window.addEventListener("test",Ts,Ts),window.removeEventListener("test",Ts,Ts)}catch{lu=!1}var Fn=null,ou=null,$r=null;function Nh(){if($r)return $r;var t,e=ou,i=e.length,r,o="value"in Fn?Fn.value:Fn.textContent,c=o.length;for(t=0;t<i&&e[t]===o[t];t++);var y=i-t;for(r=1;r<=y&&e[i-r]===o[c-r];r++);return $r=o.slice(t,1<r?1-r:void 0)}function Jr(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function tl(){return!0}function _h(){return!1}function Ee(t){function e(i,r,o,c,y){this._reactName=i,this._targetInst=o,this.type=r,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(i=t[b],this[b]=i?i(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?tl:_h,this.isPropagationStopped=_h,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=tl)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=tl)},persist:function(){},isPersistent:tl}),e}var Va={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},el=Ee(Va),ws=g({},Va,{view:0,detail:0}),KS=Ee(ws),uu,cu,As,nl=g({},ws,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:du,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==As&&(As&&t.type==="mousemove"?(uu=t.screenX-As.screenX,cu=t.screenY-As.screenY):cu=uu=0,As=t),uu)},movementY:function(t){return"movementY"in t?t.movementY:cu}}),jh=Ee(nl),ZS=g({},nl,{dataTransfer:0}),QS=Ee(ZS),FS=g({},ws,{relatedTarget:0}),fu=Ee(FS),IS=g({},Va,{animationName:0,elapsedTime:0,pseudoElement:0}),WS=Ee(IS),$S=g({},Va,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),JS=Ee($S),t1=g({},Va,{data:0}),Vh=Ee(t1),e1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},n1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},a1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function i1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=a1[t])?!!e[t]:!1}function du(){return i1}var s1=g({},ws,{key:function(t){if(t.key){var e=e1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Jr(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?n1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:du,charCode:function(t){return t.type==="keypress"?Jr(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Jr(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),r1=Ee(s1),l1=g({},nl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zh=Ee(l1),o1=g({},ws,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:du}),u1=Ee(o1),c1=g({},Va,{propertyName:0,elapsedTime:0,pseudoElement:0}),f1=Ee(c1),d1=g({},nl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),h1=Ee(d1),m1=g({},Va,{newState:0,oldState:0}),p1=Ee(m1),g1=[9,13,27,32],hu=Cn&&"CompositionEvent"in window,Es=null;Cn&&"documentMode"in document&&(Es=document.documentMode);var y1=Cn&&"TextEvent"in window&&!Es,Lh=Cn&&(!hu||Es&&8<Es&&11>=Es),Bh=" ",Uh=!1;function kh(t,e){switch(t){case"keyup":return g1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var vi=!1;function v1(t,e){switch(t){case"compositionend":return Hh(e);case"keypress":return e.which!==32?null:(Uh=!0,Bh);case"textInput":return t=e.data,t===Bh&&Uh?null:t;default:return null}}function b1(t,e){if(vi)return t==="compositionend"||!hu&&kh(t,e)?(t=Nh(),$r=ou=Fn=null,vi=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Lh&&e.locale!=="ko"?null:e.data;default:return null}}var x1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ph(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!x1[t.type]:e==="textarea"}function Gh(t,e,i,r){gi?yi?yi.push(r):yi=[r]:gi=r,e=Gl(e,"onChange"),0<e.length&&(i=new el("onChange","change",null,i,r),t.push({event:i,listeners:e}))}var Ms=null,Cs=null;function S1(t){Tg(t,0)}function al(t){var e=xs(t);if(wh(e))return t}function Yh(t,e){if(t==="change")return e}var qh=!1;if(Cn){var mu;if(Cn){var pu="oninput"in document;if(!pu){var Xh=document.createElement("div");Xh.setAttribute("oninput","return;"),pu=typeof Xh.oninput=="function"}mu=pu}else mu=!1;qh=mu&&(!document.documentMode||9<document.documentMode)}function Kh(){Ms&&(Ms.detachEvent("onpropertychange",Zh),Cs=Ms=null)}function Zh(t){if(t.propertyName==="value"&&al(Cs)){var e=[];Gh(e,Cs,t,su(t)),Oh(S1,e)}}function T1(t,e,i){t==="focusin"?(Kh(),Ms=e,Cs=i,Ms.attachEvent("onpropertychange",Zh)):t==="focusout"&&Kh()}function w1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return al(Cs)}function A1(t,e){if(t==="click")return al(e)}function E1(t,e){if(t==="input"||t==="change")return al(e)}function M1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var _e=typeof Object.is=="function"?Object.is:M1;function Rs(t,e){if(_e(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),r=Object.keys(e);if(i.length!==r.length)return!1;for(r=0;r<i.length;r++){var o=i[r];if(!At.call(e,o)||!_e(t[o],e[o]))return!1}return!0}function Qh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Fh(t,e){var i=Qh(t);t=0;for(var r;i;){if(i.nodeType===3){if(r=t+i.textContent.length,t<=e&&r>=e)return{node:i,offset:e-t};t=r}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=Qh(i)}}function Ih(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ih(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Wh(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ir(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch{i=!1}if(i)t=e.contentWindow;else break;e=Ir(t.document)}return e}function gu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var C1=Cn&&"documentMode"in document&&11>=document.documentMode,bi=null,yu=null,Ds=null,vu=!1;function $h(t,e,i){var r=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;vu||bi==null||bi!==Ir(r)||(r=bi,"selectionStart"in r&&gu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ds&&Rs(Ds,r)||(Ds=r,r=Gl(yu,"onSelect"),0<r.length&&(e=new el("onSelect","select",null,e,i),t.push({event:e,listeners:r}),e.target=bi)))}function za(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var xi={animationend:za("Animation","AnimationEnd"),animationiteration:za("Animation","AnimationIteration"),animationstart:za("Animation","AnimationStart"),transitionrun:za("Transition","TransitionRun"),transitionstart:za("Transition","TransitionStart"),transitioncancel:za("Transition","TransitionCancel"),transitionend:za("Transition","TransitionEnd")},bu={},Jh={};Cn&&(Jh=document.createElement("div").style,"AnimationEvent"in window||(delete xi.animationend.animation,delete xi.animationiteration.animation,delete xi.animationstart.animation),"TransitionEvent"in window||delete xi.transitionend.transition);function La(t){if(bu[t])return bu[t];if(!xi[t])return t;var e=xi[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in Jh)return bu[t]=e[i];return t}var tm=La("animationend"),em=La("animationiteration"),nm=La("animationstart"),R1=La("transitionrun"),D1=La("transitionstart"),O1=La("transitioncancel"),am=La("transitionend"),im=new Map,xu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");xu.push("scrollEnd");function Je(t,e){im.set(t,e),ja(e,[t])}var sm=new WeakMap;function Ye(t,e){if(typeof t=="object"&&t!==null){var i=sm.get(t);return i!==void 0?i:(e={value:t,source:e,stack:Sh(e)},sm.set(t,e),e)}return{value:t,source:e,stack:Sh(e)}}var qe=[],Si=0,Su=0;function il(){for(var t=Si,e=Su=Si=0;e<t;){var i=qe[e];qe[e++]=null;var r=qe[e];qe[e++]=null;var o=qe[e];qe[e++]=null;var c=qe[e];if(qe[e++]=null,r!==null&&o!==null){var y=r.pending;y===null?o.next=o:(o.next=y.next,y.next=o),r.pending=o}c!==0&&rm(i,o,c)}}function sl(t,e,i,r){qe[Si++]=t,qe[Si++]=e,qe[Si++]=i,qe[Si++]=r,Su|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function Tu(t,e,i,r){return sl(t,e,i,r),rl(t)}function Ti(t,e){return sl(t,null,null,e),rl(t)}function rm(t,e,i){t.lanes|=i;var r=t.alternate;r!==null&&(r.lanes|=i);for(var o=!1,c=t.return;c!==null;)c.childLanes|=i,r=c.alternate,r!==null&&(r.childLanes|=i),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(o=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,o&&e!==null&&(o=31-Ne(i),t=c.hiddenUpdates,r=t[o],r===null?t[o]=[e]:r.push(e),e.lane=i|536870912),c):null}function rl(t){if(50<tr)throw tr=0,Rc=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var wi={};function N1(t,e,i,r){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function je(t,e,i,r){return new N1(t,e,i,r)}function wu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Rn(t,e){var i=t.alternate;return i===null?(i=je(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function lm(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ll(t,e,i,r,o,c){var y=0;if(r=t,typeof t=="function")wu(t)&&(y=1);else if(typeof t=="string")y=jT(t,i,J.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case tt:return t=je(31,i,e,o),t.elementType=tt,t.lanes=c,t;case O:return Ba(i.children,o,c,e);case w:y=8,o|=24;break;case M:return t=je(12,i,e,o|2),t.elementType=M,t.lanes=c,t;case z:return t=je(13,i,e,o),t.elementType=z,t.lanes=c,t;case F:return t=je(19,i,e,o),t.elementType=F,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case E:case D:y=10;break t;case _:y=9;break t;case H:y=11;break t;case Q:y=14;break t;case G:y=16,r=null;break t}y=29,i=Error(l(130,t===null?"null":typeof t,"")),r=null}return e=je(y,i,e,o),e.elementType=t,e.type=r,e.lanes=c,e}function Ba(t,e,i,r){return t=je(7,t,r,e),t.lanes=i,t}function Au(t,e,i){return t=je(6,t,null,e),t.lanes=i,t}function Eu(t,e,i){return e=je(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ai=[],Ei=0,ol=null,ul=0,Xe=[],Ke=0,Ua=null,Dn=1,On="";function ka(t,e){Ai[Ei++]=ul,Ai[Ei++]=ol,ol=t,ul=e}function om(t,e,i){Xe[Ke++]=Dn,Xe[Ke++]=On,Xe[Ke++]=Ua,Ua=t;var r=Dn;t=On;var o=32-Ne(r)-1;r&=~(1<<o),i+=1;var c=32-Ne(e)+o;if(30<c){var y=o-o%5;c=(r&(1<<y)-1).toString(32),r>>=y,o-=y,Dn=1<<32-Ne(e)+o|i<<o|r,On=c+t}else Dn=1<<c|i<<o|r,On=t}function Mu(t){t.return!==null&&(ka(t,1),om(t,1,0))}function Cu(t){for(;t===ol;)ol=Ai[--Ei],Ai[Ei]=null,ul=Ai[--Ei],Ai[Ei]=null;for(;t===Ua;)Ua=Xe[--Ke],Xe[Ke]=null,On=Xe[--Ke],Xe[Ke]=null,Dn=Xe[--Ke],Xe[Ke]=null}var xe=null,Kt=null,Ot=!1,Ha=null,ln=!1,Ru=Error(l(519));function Pa(t){var e=Error(l(418,""));throw _s(Ye(e,t)),Ru}function um(t){var e=t.stateNode,i=t.type,r=t.memoizedProps;switch(e[me]=t,e[Ae]=r,i){case"dialog":Mt("cancel",e),Mt("close",e);break;case"iframe":case"object":case"embed":Mt("load",e);break;case"video":case"audio":for(i=0;i<nr.length;i++)Mt(nr[i],e);break;case"source":Mt("error",e);break;case"img":case"image":case"link":Mt("error",e),Mt("load",e);break;case"details":Mt("toggle",e);break;case"input":Mt("invalid",e),Ah(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Fr(e);break;case"select":Mt("invalid",e);break;case"textarea":Mt("invalid",e),Mh(e,r.value,r.defaultValue,r.children),Fr(e)}i=r.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||r.suppressHydrationWarning===!0||Mg(e.textContent,i)?(r.popover!=null&&(Mt("beforetoggle",e),Mt("toggle",e)),r.onScroll!=null&&Mt("scroll",e),r.onScrollEnd!=null&&Mt("scrollend",e),r.onClick!=null&&(e.onclick=Yl),e=!0):e=!1,e||Pa(t)}function cm(t){for(xe=t.return;xe;)switch(xe.tag){case 5:case 13:ln=!1;return;case 27:case 3:ln=!0;return;default:xe=xe.return}}function Os(t){if(t!==xe)return!1;if(!Ot)return cm(t),Ot=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||qc(t.type,t.memoizedProps)),i=!i),i&&Kt&&Pa(t),cm(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){Kt=en(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}Kt=null}}else e===27?(e=Kt,fa(t.type)?(t=Qc,Qc=null,Kt=t):Kt=e):Kt=xe?en(t.stateNode.nextSibling):null;return!0}function Ns(){Kt=xe=null,Ot=!1}function fm(){var t=Ha;return t!==null&&(Re===null?Re=t:Re.push.apply(Re,t),Ha=null),t}function _s(t){Ha===null?Ha=[t]:Ha.push(t)}var Du=X(null),Ga=null,Nn=null;function In(t,e,i){I(Du,e._currentValue),e._currentValue=i}function _n(t){t._currentValue=Du.current,W(Du)}function Ou(t,e,i){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===i)break;t=t.return}}function Nu(t,e,i,r){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var c=o.dependencies;if(c!==null){var y=o.child;c=c.firstContext;t:for(;c!==null;){var b=c;c=o;for(var C=0;C<e.length;C++)if(b.context===e[C]){c.lanes|=i,b=c.alternate,b!==null&&(b.lanes|=i),Ou(c.return,i,t),r||(y=null);break t}c=b.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(l(341));y.lanes|=i,c=y.alternate,c!==null&&(c.lanes|=i),Ou(y,i,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function js(t,e,i,r){t=null;for(var o=e,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var b=o.type;_e(o.pendingProps.value,y.value)||(t!==null?t.push(b):t=[b])}}else if(o===$.current){if(y=o.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(or):t=[or])}o=o.return}t!==null&&Nu(e,t,i,r),e.flags|=262144}function cl(t){for(t=t.firstContext;t!==null;){if(!_e(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ya(t){Ga=t,Nn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function pe(t){return dm(Ga,t)}function fl(t,e){return Ga===null&&Ya(t),dm(t,e)}function dm(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},Nn===null){if(t===null)throw Error(l(308));Nn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Nn=Nn.next=e;return i}var _1=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},j1=n.unstable_scheduleCallback,V1=n.unstable_NormalPriority,ee={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function _u(){return{controller:new _1,data:new Map,refCount:0}}function Vs(t){t.refCount--,t.refCount===0&&j1(V1,function(){t.controller.abort()})}var zs=null,ju=0,Mi=0,Ci=null;function z1(t,e){if(zs===null){var i=zs=[];ju=0,Mi=zc(),Ci={status:"pending",value:void 0,then:function(r){i.push(r)}}}return ju++,e.then(hm,hm),e}function hm(){if(--ju===0&&zs!==null){Ci!==null&&(Ci.status="fulfilled");var t=zs;zs=null,Mi=0,Ci=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function L1(t,e){var i=[],r={status:"pending",value:null,reason:null,then:function(o){i.push(o)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var o=0;o<i.length;o++)(0,i[o])(e)},function(o){for(r.status="rejected",r.reason=o,o=0;o<i.length;o++)(0,i[o])(void 0)}),r}var mm=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&z1(t,e),mm!==null&&mm(t,e)};var qa=X(null);function Vu(){var t=qa.current;return t!==null?t:Ht.pooledCache}function dl(t,e){e===null?I(qa,qa.current):I(qa,e.pool)}function pm(){var t=Vu();return t===null?null:{parent:ee._currentValue,pool:t}}var Ls=Error(l(460)),gm=Error(l(474)),hl=Error(l(542)),zu={then:function(){}};function ym(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ml(){}function vm(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(ml,ml),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,xm(t),t;default:if(typeof e.status=="string")e.then(ml,ml);else{if(t=Ht,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=r}},function(r){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,xm(t),t}throw Bs=e,Ls}}var Bs=null;function bm(){if(Bs===null)throw Error(l(459));var t=Bs;return Bs=null,t}function xm(t){if(t===Ls||t===hl)throw Error(l(483))}var Wn=!1;function Lu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Bu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function $n(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Jn(t,e,i){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(_t&2)!==0){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,e=rl(t),rm(t,null,i),e}return sl(t,r,e,i),rl(t)}function Us(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,hh(t,i)}}function Uu(t,e){var i=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,i===r)){var o=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var y={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};c===null?o=c=y:c=c.next=y,i=i.next}while(i!==null);c===null?o=c=e:c=c.next=e}else o=c=e;i={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:r.shared,callbacks:r.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var ku=!1;function ks(){if(ku){var t=Ci;if(t!==null)throw t}}function Hs(t,e,i,r){ku=!1;var o=t.updateQueue;Wn=!1;var c=o.firstBaseUpdate,y=o.lastBaseUpdate,b=o.shared.pending;if(b!==null){o.shared.pending=null;var C=b,B=C.next;C.next=null,y===null?c=B:y.next=B,y=C;var q=t.alternate;q!==null&&(q=q.updateQueue,b=q.lastBaseUpdate,b!==y&&(b===null?q.firstBaseUpdate=B:b.next=B,q.lastBaseUpdate=C))}if(c!==null){var Z=o.baseState;y=0,q=B=C=null,b=c;do{var U=b.lane&-536870913,P=U!==b.lane;if(P?(Rt&U)===U:(r&U)===U){U!==0&&U===Mi&&(ku=!0),q!==null&&(q=q.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var mt=t,ft=b;U=e;var Bt=i;switch(ft.tag){case 1:if(mt=ft.payload,typeof mt=="function"){Z=mt.call(Bt,Z,U);break t}Z=mt;break t;case 3:mt.flags=mt.flags&-65537|128;case 0:if(mt=ft.payload,U=typeof mt=="function"?mt.call(Bt,Z,U):mt,U==null)break t;Z=g({},Z,U);break t;case 2:Wn=!0}}U=b.callback,U!==null&&(t.flags|=64,P&&(t.flags|=8192),P=o.callbacks,P===null?o.callbacks=[U]:P.push(U))}else P={lane:U,tag:b.tag,payload:b.payload,callback:b.callback,next:null},q===null?(B=q=P,C=Z):q=q.next=P,y|=U;if(b=b.next,b===null){if(b=o.shared.pending,b===null)break;P=b,b=P.next,P.next=null,o.lastBaseUpdate=P,o.shared.pending=null}}while(!0);q===null&&(C=Z),o.baseState=C,o.firstBaseUpdate=B,o.lastBaseUpdate=q,c===null&&(o.shared.lanes=0),la|=y,t.lanes=y,t.memoizedState=Z}}function Sm(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Tm(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)Sm(i[t],e)}var Ri=X(null),pl=X(0);function wm(t,e){t=kn,I(pl,t),I(Ri,e),kn=t|e.baseLanes}function Hu(){I(pl,kn),I(Ri,Ri.current)}function Pu(){kn=pl.current,W(Ri),W(pl)}var ta=0,xt=null,zt=null,$t=null,gl=!1,Di=!1,Xa=!1,yl=0,Ps=0,Oi=null,B1=0;function Ft(){throw Error(l(321))}function Gu(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!_e(t[i],e[i]))return!1;return!0}function Yu(t,e,i,r,o,c){return ta=c,xt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?rp:lp,Xa=!1,c=i(r,o),Xa=!1,Di&&(c=Em(e,i,r,o)),Am(t),c}function Am(t){V.H=wl;var e=zt!==null&&zt.next!==null;if(ta=0,$t=zt=xt=null,gl=!1,Ps=0,Oi=null,e)throw Error(l(300));t===null||se||(t=t.dependencies,t!==null&&cl(t)&&(se=!0))}function Em(t,e,i,r){xt=t;var o=0;do{if(Di&&(Oi=null),Ps=0,Di=!1,25<=o)throw Error(l(301));if(o+=1,$t=zt=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}V.H=q1,c=e(i,r)}while(Di);return c}function U1(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?Gs(e):e,t=t.useState()[0],(zt!==null?zt.memoizedState:null)!==t&&(xt.flags|=1024),e}function qu(){var t=yl!==0;return yl=0,t}function Xu(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function Ku(t){if(gl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}gl=!1}ta=0,$t=zt=xt=null,Di=!1,Ps=yl=0,Oi=null}function Me(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $t===null?xt.memoizedState=$t=t:$t=$t.next=t,$t}function Jt(){if(zt===null){var t=xt.alternate;t=t!==null?t.memoizedState:null}else t=zt.next;var e=$t===null?xt.memoizedState:$t.next;if(e!==null)$t=e,zt=t;else{if(t===null)throw xt.alternate===null?Error(l(467)):Error(l(310));zt=t,t={memoizedState:zt.memoizedState,baseState:zt.baseState,baseQueue:zt.baseQueue,queue:zt.queue,next:null},$t===null?xt.memoizedState=$t=t:$t=$t.next=t}return $t}function Zu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Gs(t){var e=Ps;return Ps+=1,Oi===null&&(Oi=[]),t=vm(Oi,t,e),e=xt,($t===null?e.memoizedState:$t.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?rp:lp),t}function vl(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Gs(t);if(t.$$typeof===D)return pe(t)}throw Error(l(438,String(t)))}function Qu(t){var e=null,i=xt.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var r=xt.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=Zu(),xt.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),r=0;r<t;r++)i[r]=rt;return e.index++,i}function jn(t,e){return typeof e=="function"?e(t):e}function bl(t){var e=Jt();return Fu(e,zt,t)}function Fu(t,e,i){var r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=i;var o=t.baseQueue,c=r.pending;if(c!==null){if(o!==null){var y=o.next;o.next=c.next,c.next=y}e.baseQueue=o=c,r.pending=null}if(c=t.baseState,o===null)t.memoizedState=c;else{e=o.next;var b=y=null,C=null,B=e,q=!1;do{var Z=B.lane&-536870913;if(Z!==B.lane?(Rt&Z)===Z:(ta&Z)===Z){var U=B.revertLane;if(U===0)C!==null&&(C=C.next={lane:0,revertLane:0,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null}),Z===Mi&&(q=!0);else if((ta&U)===U){B=B.next,U===Mi&&(q=!0);continue}else Z={lane:0,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},C===null?(b=C=Z,y=c):C=C.next=Z,xt.lanes|=U,la|=U;Z=B.action,Xa&&i(c,Z),c=B.hasEagerState?B.eagerState:i(c,Z)}else U={lane:Z,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},C===null?(b=C=U,y=c):C=C.next=U,xt.lanes|=Z,la|=Z;B=B.next}while(B!==null&&B!==e);if(C===null?y=c:C.next=b,!_e(c,t.memoizedState)&&(se=!0,q&&(i=Ci,i!==null)))throw i;t.memoizedState=c,t.baseState=y,t.baseQueue=C,r.lastRenderedState=c}return o===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function Iu(t){var e=Jt(),i=e.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=t;var r=i.dispatch,o=i.pending,c=e.memoizedState;if(o!==null){i.pending=null;var y=o=o.next;do c=t(c,y.action),y=y.next;while(y!==o);_e(c,e.memoizedState)||(se=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),i.lastRenderedState=c}return[c,r]}function Mm(t,e,i){var r=xt,o=Jt(),c=Ot;if(c){if(i===void 0)throw Error(l(407));i=i()}else i=e();var y=!_e((zt||o).memoizedState,i);y&&(o.memoizedState=i,se=!0),o=o.queue;var b=Dm.bind(null,r,o,t);if(Ys(2048,8,b,[t]),o.getSnapshot!==e||y||$t!==null&&$t.memoizedState.tag&1){if(r.flags|=2048,Ni(9,xl(),Rm.bind(null,r,o,i,e),null),Ht===null)throw Error(l(349));c||(ta&124)!==0||Cm(r,e,i)}return i}function Cm(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=xt.updateQueue,e===null?(e=Zu(),xt.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function Rm(t,e,i,r){e.value=i,e.getSnapshot=r,Om(e)&&Nm(t)}function Dm(t,e,i){return i(function(){Om(e)&&Nm(t)})}function Om(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!_e(t,i)}catch{return!0}}function Nm(t){var e=Ti(t,2);e!==null&&Ue(e,t,2)}function Wu(t){var e=Me();if(typeof t=="function"){var i=t;if(t=i(),Xa){Zn(!0);try{i()}finally{Zn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:jn,lastRenderedState:t},e}function _m(t,e,i,r){return t.baseState=i,Fu(t,zt,typeof r=="function"?r:jn)}function k1(t,e,i,r,o){if(Tl(t))throw Error(l(485));if(t=e.action,t!==null){var c={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};V.T!==null?i(!0):c.isTransition=!1,r(c),i=e.pending,i===null?(c.next=e.pending=c,jm(e,c)):(c.next=i.next,e.pending=i.next=c)}}function jm(t,e){var i=e.action,r=e.payload,o=t.state;if(e.isTransition){var c=V.T,y={};V.T=y;try{var b=i(o,r),C=V.S;C!==null&&C(y,b),Vm(t,e,b)}catch(B){$u(t,e,B)}finally{V.T=c}}else try{c=i(o,r),Vm(t,e,c)}catch(B){$u(t,e,B)}}function Vm(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(r){zm(t,e,r)},function(r){return $u(t,e,r)}):zm(t,e,i)}function zm(t,e,i){e.status="fulfilled",e.value=i,Lm(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,jm(t,i)))}function $u(t,e,i){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=i,Lm(e),e=e.next;while(e!==r)}t.action=null}function Lm(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Bm(t,e){return e}function Um(t,e){if(Ot){var i=Ht.formState;if(i!==null){t:{var r=xt;if(Ot){if(Kt){e:{for(var o=Kt,c=ln;o.nodeType!==8;){if(!c){o=null;break e}if(o=en(o.nextSibling),o===null){o=null;break e}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){Kt=en(o.nextSibling),r=o.data==="F!";break t}}Pa(r)}r=!1}r&&(e=i[0])}}return i=Me(),i.memoizedState=i.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Bm,lastRenderedState:e},i.queue=r,i=ap.bind(null,xt,r),r.dispatch=i,r=Wu(!1),c=ac.bind(null,xt,!1,r.queue),r=Me(),o={state:e,dispatch:null,action:t,pending:null},r.queue=o,i=k1.bind(null,xt,o,c,i),o.dispatch=i,r.memoizedState=t,[e,i,!1]}function km(t){var e=Jt();return Hm(e,zt,t)}function Hm(t,e,i){if(e=Fu(t,e,Bm)[0],t=bl(jn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Gs(e)}catch(y){throw y===Ls?hl:y}else r=e;e=Jt();var o=e.queue,c=o.dispatch;return i!==e.memoizedState&&(xt.flags|=2048,Ni(9,xl(),H1.bind(null,o,i),null)),[r,c,t]}function H1(t,e){t.action=e}function Pm(t){var e=Jt(),i=zt;if(i!==null)return Hm(e,i,t);Jt(),e=e.memoizedState,i=Jt();var r=i.queue.dispatch;return i.memoizedState=t,[e,r,!1]}function Ni(t,e,i,r){return t={tag:t,create:i,deps:r,inst:e,next:null},e=xt.updateQueue,e===null&&(e=Zu(),xt.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(r=i.next,i.next=t,t.next=r,e.lastEffect=t),t}function xl(){return{destroy:void 0,resource:void 0}}function Gm(){return Jt().memoizedState}function Sl(t,e,i,r){var o=Me();r=r===void 0?null:r,xt.flags|=t,o.memoizedState=Ni(1|e,xl(),i,r)}function Ys(t,e,i,r){var o=Jt();r=r===void 0?null:r;var c=o.memoizedState.inst;zt!==null&&r!==null&&Gu(r,zt.memoizedState.deps)?o.memoizedState=Ni(e,c,i,r):(xt.flags|=t,o.memoizedState=Ni(1|e,c,i,r))}function Ym(t,e){Sl(8390656,8,t,e)}function qm(t,e){Ys(2048,8,t,e)}function Xm(t,e){return Ys(4,2,t,e)}function Km(t,e){return Ys(4,4,t,e)}function Zm(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Qm(t,e,i){i=i!=null?i.concat([t]):null,Ys(4,4,Zm.bind(null,e,t),i)}function Ju(){}function Fm(t,e){var i=Jt();e=e===void 0?null:e;var r=i.memoizedState;return e!==null&&Gu(e,r[1])?r[0]:(i.memoizedState=[t,e],t)}function Im(t,e){var i=Jt();e=e===void 0?null:e;var r=i.memoizedState;if(e!==null&&Gu(e,r[1]))return r[0];if(r=t(),Xa){Zn(!0);try{t()}finally{Zn(!1)}}return i.memoizedState=[r,e],r}function tc(t,e,i){return i===void 0||(ta&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=Jp(),xt.lanes|=t,la|=t,i)}function Wm(t,e,i,r){return _e(i,e)?i:Ri.current!==null?(t=tc(t,i,r),_e(t,e)||(se=!0),t):(ta&42)===0?(se=!0,t.memoizedState=i):(t=Jp(),xt.lanes|=t,la|=t,e)}function $m(t,e,i,r,o){var c=Y.p;Y.p=c!==0&&8>c?c:8;var y=V.T,b={};V.T=b,ac(t,!1,e,i);try{var C=o(),B=V.S;if(B!==null&&B(b,C),C!==null&&typeof C=="object"&&typeof C.then=="function"){var q=L1(C,r);qs(t,e,q,Be(t))}else qs(t,e,r,Be(t))}catch(Z){qs(t,e,{then:function(){},status:"rejected",reason:Z},Be())}finally{Y.p=c,V.T=y}}function P1(){}function ec(t,e,i,r){if(t.tag!==5)throw Error(l(476));var o=Jm(t).queue;$m(t,o,e,k,i===null?P1:function(){return tp(t),i(r)})}function Jm(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:k,baseState:k,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:jn,lastRenderedState:k},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:jn,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function tp(t){var e=Jm(t).next.queue;qs(t,e,{},Be())}function nc(){return pe(or)}function ep(){return Jt().memoizedState}function np(){return Jt().memoizedState}function G1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=Be();t=$n(i);var r=Jn(e,t,i);r!==null&&(Ue(r,e,i),Us(r,e,i)),e={cache:_u()},t.payload=e;return}e=e.return}}function Y1(t,e,i){var r=Be();i={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Tl(t)?ip(e,i):(i=Tu(t,e,i,r),i!==null&&(Ue(i,t,r),sp(i,e,r)))}function ap(t,e,i){var r=Be();qs(t,e,i,r)}function qs(t,e,i,r){var o={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Tl(t))ip(e,o);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var y=e.lastRenderedState,b=c(y,i);if(o.hasEagerState=!0,o.eagerState=b,_e(b,y))return sl(t,e,o,0),Ht===null&&il(),!1}catch{}finally{}if(i=Tu(t,e,o,r),i!==null)return Ue(i,t,r),sp(i,e,r),!0}return!1}function ac(t,e,i,r){if(r={lane:2,revertLane:zc(),action:r,hasEagerState:!1,eagerState:null,next:null},Tl(t)){if(e)throw Error(l(479))}else e=Tu(t,i,r,2),e!==null&&Ue(e,t,2)}function Tl(t){var e=t.alternate;return t===xt||e!==null&&e===xt}function ip(t,e){Di=gl=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function sp(t,e,i){if((i&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,hh(t,i)}}var wl={readContext:pe,use:vl,useCallback:Ft,useContext:Ft,useEffect:Ft,useImperativeHandle:Ft,useLayoutEffect:Ft,useInsertionEffect:Ft,useMemo:Ft,useReducer:Ft,useRef:Ft,useState:Ft,useDebugValue:Ft,useDeferredValue:Ft,useTransition:Ft,useSyncExternalStore:Ft,useId:Ft,useHostTransitionStatus:Ft,useFormState:Ft,useActionState:Ft,useOptimistic:Ft,useMemoCache:Ft,useCacheRefresh:Ft},rp={readContext:pe,use:vl,useCallback:function(t,e){return Me().memoizedState=[t,e===void 0?null:e],t},useContext:pe,useEffect:Ym,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Sl(4194308,4,Zm.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Sl(4194308,4,t,e)},useInsertionEffect:function(t,e){Sl(4,2,t,e)},useMemo:function(t,e){var i=Me();e=e===void 0?null:e;var r=t();if(Xa){Zn(!0);try{t()}finally{Zn(!1)}}return i.memoizedState=[r,e],r},useReducer:function(t,e,i){var r=Me();if(i!==void 0){var o=i(e);if(Xa){Zn(!0);try{i(e)}finally{Zn(!1)}}}else o=e;return r.memoizedState=r.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},r.queue=t,t=t.dispatch=Y1.bind(null,xt,t),[r.memoizedState,t]},useRef:function(t){var e=Me();return t={current:t},e.memoizedState=t},useState:function(t){t=Wu(t);var e=t.queue,i=ap.bind(null,xt,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:Ju,useDeferredValue:function(t,e){var i=Me();return tc(i,t,e)},useTransition:function(){var t=Wu(!1);return t=$m.bind(null,xt,t.queue,!0,!1),Me().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var r=xt,o=Me();if(Ot){if(i===void 0)throw Error(l(407));i=i()}else{if(i=e(),Ht===null)throw Error(l(349));(Rt&124)!==0||Cm(r,e,i)}o.memoizedState=i;var c={value:i,getSnapshot:e};return o.queue=c,Ym(Dm.bind(null,r,c,t),[t]),r.flags|=2048,Ni(9,xl(),Rm.bind(null,r,c,i,e),null),i},useId:function(){var t=Me(),e=Ht.identifierPrefix;if(Ot){var i=On,r=Dn;i=(r&~(1<<32-Ne(r)-1)).toString(32)+i,e="«"+e+"R"+i,i=yl++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=B1++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:nc,useFormState:Um,useActionState:Um,useOptimistic:function(t){var e=Me();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=ac.bind(null,xt,!0,i),i.dispatch=e,[t,e]},useMemoCache:Qu,useCacheRefresh:function(){return Me().memoizedState=G1.bind(null,xt)}},lp={readContext:pe,use:vl,useCallback:Fm,useContext:pe,useEffect:qm,useImperativeHandle:Qm,useInsertionEffect:Xm,useLayoutEffect:Km,useMemo:Im,useReducer:bl,useRef:Gm,useState:function(){return bl(jn)},useDebugValue:Ju,useDeferredValue:function(t,e){var i=Jt();return Wm(i,zt.memoizedState,t,e)},useTransition:function(){var t=bl(jn)[0],e=Jt().memoizedState;return[typeof t=="boolean"?t:Gs(t),e]},useSyncExternalStore:Mm,useId:ep,useHostTransitionStatus:nc,useFormState:km,useActionState:km,useOptimistic:function(t,e){var i=Jt();return _m(i,zt,t,e)},useMemoCache:Qu,useCacheRefresh:np},q1={readContext:pe,use:vl,useCallback:Fm,useContext:pe,useEffect:qm,useImperativeHandle:Qm,useInsertionEffect:Xm,useLayoutEffect:Km,useMemo:Im,useReducer:Iu,useRef:Gm,useState:function(){return Iu(jn)},useDebugValue:Ju,useDeferredValue:function(t,e){var i=Jt();return zt===null?tc(i,t,e):Wm(i,zt.memoizedState,t,e)},useTransition:function(){var t=Iu(jn)[0],e=Jt().memoizedState;return[typeof t=="boolean"?t:Gs(t),e]},useSyncExternalStore:Mm,useId:ep,useHostTransitionStatus:nc,useFormState:Pm,useActionState:Pm,useOptimistic:function(t,e){var i=Jt();return zt!==null?_m(i,zt,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:Qu,useCacheRefresh:np},_i=null,Xs=0;function Al(t){var e=Xs;return Xs+=1,_i===null&&(_i=[]),vm(_i,t,e)}function Ks(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function El(t,e){throw e.$$typeof===v?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function op(t){var e=t._init;return e(t._payload)}function up(t){function e(j,N){if(t){var L=j.deletions;L===null?(j.deletions=[N],j.flags|=16):L.push(N)}}function i(j,N){if(!t)return null;for(;N!==null;)e(j,N),N=N.sibling;return null}function r(j){for(var N=new Map;j!==null;)j.key!==null?N.set(j.key,j):N.set(j.index,j),j=j.sibling;return N}function o(j,N){return j=Rn(j,N),j.index=0,j.sibling=null,j}function c(j,N,L){return j.index=L,t?(L=j.alternate,L!==null?(L=L.index,L<N?(j.flags|=67108866,N):L):(j.flags|=67108866,N)):(j.flags|=1048576,N)}function y(j){return t&&j.alternate===null&&(j.flags|=67108866),j}function b(j,N,L,K){return N===null||N.tag!==6?(N=Au(L,j.mode,K),N.return=j,N):(N=o(N,L),N.return=j,N)}function C(j,N,L,K){var et=L.type;return et===O?q(j,N,L.props.children,K,L.key):N!==null&&(N.elementType===et||typeof et=="object"&&et!==null&&et.$$typeof===G&&op(et)===N.type)?(N=o(N,L.props),Ks(N,L),N.return=j,N):(N=ll(L.type,L.key,L.props,null,j.mode,K),Ks(N,L),N.return=j,N)}function B(j,N,L,K){return N===null||N.tag!==4||N.stateNode.containerInfo!==L.containerInfo||N.stateNode.implementation!==L.implementation?(N=Eu(L,j.mode,K),N.return=j,N):(N=o(N,L.children||[]),N.return=j,N)}function q(j,N,L,K,et){return N===null||N.tag!==7?(N=Ba(L,j.mode,K,et),N.return=j,N):(N=o(N,L),N.return=j,N)}function Z(j,N,L){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=Au(""+N,j.mode,L),N.return=j,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case x:return L=ll(N.type,N.key,N.props,null,j.mode,L),Ks(L,N),L.return=j,L;case A:return N=Eu(N,j.mode,L),N.return=j,N;case G:var K=N._init;return N=K(N._payload),Z(j,N,L)}if(ot(N)||ct(N))return N=Ba(N,j.mode,L,null),N.return=j,N;if(typeof N.then=="function")return Z(j,Al(N),L);if(N.$$typeof===D)return Z(j,fl(j,N),L);El(j,N)}return null}function U(j,N,L,K){var et=N!==null?N.key:null;if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return et!==null?null:b(j,N,""+L,K);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case x:return L.key===et?C(j,N,L,K):null;case A:return L.key===et?B(j,N,L,K):null;case G:return et=L._init,L=et(L._payload),U(j,N,L,K)}if(ot(L)||ct(L))return et!==null?null:q(j,N,L,K,null);if(typeof L.then=="function")return U(j,N,Al(L),K);if(L.$$typeof===D)return U(j,N,fl(j,L),K);El(j,L)}return null}function P(j,N,L,K,et){if(typeof K=="string"&&K!==""||typeof K=="number"||typeof K=="bigint")return j=j.get(L)||null,b(N,j,""+K,et);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case x:return j=j.get(K.key===null?L:K.key)||null,C(N,j,K,et);case A:return j=j.get(K.key===null?L:K.key)||null,B(N,j,K,et);case G:var Tt=K._init;return K=Tt(K._payload),P(j,N,L,K,et)}if(ot(K)||ct(K))return j=j.get(L)||null,q(N,j,K,et,null);if(typeof K.then=="function")return P(j,N,L,Al(K),et);if(K.$$typeof===D)return P(j,N,L,fl(N,K),et);El(N,K)}return null}function mt(j,N,L,K){for(var et=null,Tt=null,st=N,dt=N=0,le=null;st!==null&&dt<L.length;dt++){st.index>dt?(le=st,st=null):le=st.sibling;var Dt=U(j,st,L[dt],K);if(Dt===null){st===null&&(st=le);break}t&&st&&Dt.alternate===null&&e(j,st),N=c(Dt,N,dt),Tt===null?et=Dt:Tt.sibling=Dt,Tt=Dt,st=le}if(dt===L.length)return i(j,st),Ot&&ka(j,dt),et;if(st===null){for(;dt<L.length;dt++)st=Z(j,L[dt],K),st!==null&&(N=c(st,N,dt),Tt===null?et=st:Tt.sibling=st,Tt=st);return Ot&&ka(j,dt),et}for(st=r(st);dt<L.length;dt++)le=P(st,j,dt,L[dt],K),le!==null&&(t&&le.alternate!==null&&st.delete(le.key===null?dt:le.key),N=c(le,N,dt),Tt===null?et=le:Tt.sibling=le,Tt=le);return t&&st.forEach(function(ga){return e(j,ga)}),Ot&&ka(j,dt),et}function ft(j,N,L,K){if(L==null)throw Error(l(151));for(var et=null,Tt=null,st=N,dt=N=0,le=null,Dt=L.next();st!==null&&!Dt.done;dt++,Dt=L.next()){st.index>dt?(le=st,st=null):le=st.sibling;var ga=U(j,st,Dt.value,K);if(ga===null){st===null&&(st=le);break}t&&st&&ga.alternate===null&&e(j,st),N=c(ga,N,dt),Tt===null?et=ga:Tt.sibling=ga,Tt=ga,st=le}if(Dt.done)return i(j,st),Ot&&ka(j,dt),et;if(st===null){for(;!Dt.done;dt++,Dt=L.next())Dt=Z(j,Dt.value,K),Dt!==null&&(N=c(Dt,N,dt),Tt===null?et=Dt:Tt.sibling=Dt,Tt=Dt);return Ot&&ka(j,dt),et}for(st=r(st);!Dt.done;dt++,Dt=L.next())Dt=P(st,j,dt,Dt.value,K),Dt!==null&&(t&&Dt.alternate!==null&&st.delete(Dt.key===null?dt:Dt.key),N=c(Dt,N,dt),Tt===null?et=Dt:Tt.sibling=Dt,Tt=Dt);return t&&st.forEach(function(XT){return e(j,XT)}),Ot&&ka(j,dt),et}function Bt(j,N,L,K){if(typeof L=="object"&&L!==null&&L.type===O&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case x:t:{for(var et=L.key;N!==null;){if(N.key===et){if(et=L.type,et===O){if(N.tag===7){i(j,N.sibling),K=o(N,L.props.children),K.return=j,j=K;break t}}else if(N.elementType===et||typeof et=="object"&&et!==null&&et.$$typeof===G&&op(et)===N.type){i(j,N.sibling),K=o(N,L.props),Ks(K,L),K.return=j,j=K;break t}i(j,N);break}else e(j,N);N=N.sibling}L.type===O?(K=Ba(L.props.children,j.mode,K,L.key),K.return=j,j=K):(K=ll(L.type,L.key,L.props,null,j.mode,K),Ks(K,L),K.return=j,j=K)}return y(j);case A:t:{for(et=L.key;N!==null;){if(N.key===et)if(N.tag===4&&N.stateNode.containerInfo===L.containerInfo&&N.stateNode.implementation===L.implementation){i(j,N.sibling),K=o(N,L.children||[]),K.return=j,j=K;break t}else{i(j,N);break}else e(j,N);N=N.sibling}K=Eu(L,j.mode,K),K.return=j,j=K}return y(j);case G:return et=L._init,L=et(L._payload),Bt(j,N,L,K)}if(ot(L))return mt(j,N,L,K);if(ct(L)){if(et=ct(L),typeof et!="function")throw Error(l(150));return L=et.call(L),ft(j,N,L,K)}if(typeof L.then=="function")return Bt(j,N,Al(L),K);if(L.$$typeof===D)return Bt(j,N,fl(j,L),K);El(j,L)}return typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint"?(L=""+L,N!==null&&N.tag===6?(i(j,N.sibling),K=o(N,L),K.return=j,j=K):(i(j,N),K=Au(L,j.mode,K),K.return=j,j=K),y(j)):i(j,N)}return function(j,N,L,K){try{Xs=0;var et=Bt(j,N,L,K);return _i=null,et}catch(st){if(st===Ls||st===hl)throw st;var Tt=je(29,st,null,j.mode);return Tt.lanes=K,Tt.return=j,Tt}finally{}}}var ji=up(!0),cp=up(!1),Ze=X(null),on=null;function ea(t){var e=t.alternate;I(ne,ne.current&1),I(Ze,t),on===null&&(e===null||Ri.current!==null||e.memoizedState!==null)&&(on=t)}function fp(t){if(t.tag===22){if(I(ne,ne.current),I(Ze,t),on===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(on=t)}}else na()}function na(){I(ne,ne.current),I(Ze,Ze.current)}function Vn(t){W(Ze),on===t&&(on=null),W(ne)}var ne=X(0);function Ml(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||Zc(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function ic(t,e,i,r){e=t.memoizedState,i=i(r,e),i=i==null?e:g({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var sc={enqueueSetState:function(t,e,i){t=t._reactInternals;var r=Be(),o=$n(r);o.payload=e,i!=null&&(o.callback=i),e=Jn(t,o,r),e!==null&&(Ue(e,t,r),Us(e,t,r))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var r=Be(),o=$n(r);o.tag=1,o.payload=e,i!=null&&(o.callback=i),e=Jn(t,o,r),e!==null&&(Ue(e,t,r),Us(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=Be(),r=$n(i);r.tag=2,e!=null&&(r.callback=e),e=Jn(t,r,i),e!==null&&(Ue(e,t,i),Us(e,t,i))}};function dp(t,e,i,r,o,c,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,c,y):e.prototype&&e.prototype.isPureReactComponent?!Rs(i,r)||!Rs(o,c):!0}function hp(t,e,i,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,r),e.state!==t&&sc.enqueueReplaceState(e,e.state,null)}function Ka(t,e){var i=e;if("ref"in e){i={};for(var r in e)r!=="ref"&&(i[r]=e[r])}if(t=t.defaultProps){i===e&&(i=g({},i));for(var o in t)i[o]===void 0&&(i[o]=t[o])}return i}var Cl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function mp(t){Cl(t)}function pp(t){console.error(t)}function gp(t){Cl(t)}function Rl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function yp(t,e,i){try{var r=t.onCaughtError;r(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function rc(t,e,i){return i=$n(i),i.tag=3,i.payload={element:null},i.callback=function(){Rl(t,e)},i}function vp(t){return t=$n(t),t.tag=3,t}function bp(t,e,i,r){var o=i.type.getDerivedStateFromError;if(typeof o=="function"){var c=r.value;t.payload=function(){return o(c)},t.callback=function(){yp(e,i,r)}}var y=i.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){yp(e,i,r),typeof o!="function"&&(oa===null?oa=new Set([this]):oa.add(this));var b=r.stack;this.componentDidCatch(r.value,{componentStack:b!==null?b:""})})}function X1(t,e,i,r,o){if(i.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=i.alternate,e!==null&&js(e,i,o,!0),i=Ze.current,i!==null){switch(i.tag){case 13:return on===null?Oc():i.alternate===null&&Zt===0&&(Zt=3),i.flags&=-257,i.flags|=65536,i.lanes=o,r===zu?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([r]):e.add(r),_c(t,r,o)),!1;case 22:return i.flags|=65536,r===zu?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([r]):i.add(r)),_c(t,r,o)),!1}throw Error(l(435,i.tag))}return _c(t,r,o),Oc(),!1}if(Ot)return e=Ze.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,r!==Ru&&(t=Error(l(422),{cause:r}),_s(Ye(t,i)))):(r!==Ru&&(e=Error(l(423),{cause:r}),_s(Ye(e,i))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,r=Ye(r,i),o=rc(t.stateNode,r,o),Uu(t,o),Zt!==4&&(Zt=2)),!1;var c=Error(l(520),{cause:r});if(c=Ye(c,i),Js===null?Js=[c]:Js.push(c),Zt!==4&&(Zt=2),e===null)return!0;r=Ye(r,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=o&-o,i.lanes|=t,t=rc(i.stateNode,r,t),Uu(i,t),!1;case 1:if(e=i.type,c=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(oa===null||!oa.has(c))))return i.flags|=65536,o&=-o,i.lanes|=o,o=vp(o),bp(o,t,i,r),Uu(i,o),!1}i=i.return}while(i!==null);return!1}var xp=Error(l(461)),se=!1;function ue(t,e,i,r){e.child=t===null?cp(e,null,i,r):ji(e,t.child,i,r)}function Sp(t,e,i,r,o){i=i.render;var c=e.ref;if("ref"in r){var y={};for(var b in r)b!=="ref"&&(y[b]=r[b])}else y=r;return Ya(e),r=Yu(t,e,i,y,c,o),b=qu(),t!==null&&!se?(Xu(t,e,o),zn(t,e,o)):(Ot&&b&&Mu(e),e.flags|=1,ue(t,e,r,o),e.child)}function Tp(t,e,i,r,o){if(t===null){var c=i.type;return typeof c=="function"&&!wu(c)&&c.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=c,wp(t,e,c,r,o)):(t=ll(i.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!mc(t,o)){var y=c.memoizedProps;if(i=i.compare,i=i!==null?i:Rs,i(y,r)&&t.ref===e.ref)return zn(t,e,o)}return e.flags|=1,t=Rn(c,r),t.ref=e.ref,t.return=e,e.child=t}function wp(t,e,i,r,o){if(t!==null){var c=t.memoizedProps;if(Rs(c,r)&&t.ref===e.ref)if(se=!1,e.pendingProps=r=c,mc(t,o))(t.flags&131072)!==0&&(se=!0);else return e.lanes=t.lanes,zn(t,e,o)}return lc(t,e,i,r,o)}function Ap(t,e,i){var r=e.pendingProps,o=r.children,c=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=c!==null?c.baseLanes|i:i,t!==null){for(o=e.child=t.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;e.childLanes=c&~r}else e.childLanes=0,e.child=null;return Ep(t,e,r,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&dl(e,c!==null?c.cachePool:null),c!==null?wm(e,c):Hu(),fp(e);else return e.lanes=e.childLanes=536870912,Ep(t,e,c!==null?c.baseLanes|i:i,i)}else c!==null?(dl(e,c.cachePool),wm(e,c),na(),e.memoizedState=null):(t!==null&&dl(e,null),Hu(),na());return ue(t,e,o,i),e.child}function Ep(t,e,i,r){var o=Vu();return o=o===null?null:{parent:ee._currentValue,pool:o},e.memoizedState={baseLanes:i,cachePool:o},t!==null&&dl(e,null),Hu(),fp(e),t!==null&&js(t,e,r,!0),null}function Dl(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(l(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function lc(t,e,i,r,o){return Ya(e),i=Yu(t,e,i,r,void 0,o),r=qu(),t!==null&&!se?(Xu(t,e,o),zn(t,e,o)):(Ot&&r&&Mu(e),e.flags|=1,ue(t,e,i,o),e.child)}function Mp(t,e,i,r,o,c){return Ya(e),e.updateQueue=null,i=Em(e,r,i,o),Am(t),r=qu(),t!==null&&!se?(Xu(t,e,c),zn(t,e,c)):(Ot&&r&&Mu(e),e.flags|=1,ue(t,e,i,c),e.child)}function Cp(t,e,i,r,o){if(Ya(e),e.stateNode===null){var c=wi,y=i.contextType;typeof y=="object"&&y!==null&&(c=pe(y)),c=new i(r,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=sc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=r,c.state=e.memoizedState,c.refs={},Lu(e),y=i.contextType,c.context=typeof y=="object"&&y!==null?pe(y):wi,c.state=e.memoizedState,y=i.getDerivedStateFromProps,typeof y=="function"&&(ic(e,i,y,r),c.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&sc.enqueueReplaceState(c,c.state,null),Hs(e,r,c,o),ks(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){c=e.stateNode;var b=e.memoizedProps,C=Ka(i,b);c.props=C;var B=c.context,q=i.contextType;y=wi,typeof q=="object"&&q!==null&&(y=pe(q));var Z=i.getDerivedStateFromProps;q=typeof Z=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=e.pendingProps!==b,q||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||B!==y)&&hp(e,c,r,y),Wn=!1;var U=e.memoizedState;c.state=U,Hs(e,r,c,o),ks(),B=e.memoizedState,b||U!==B||Wn?(typeof Z=="function"&&(ic(e,i,Z,r),B=e.memoizedState),(C=Wn||dp(e,i,C,r,U,B,y))?(q||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=B),c.props=r,c.state=B,c.context=y,r=C):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{c=e.stateNode,Bu(t,e),y=e.memoizedProps,q=Ka(i,y),c.props=q,Z=e.pendingProps,U=c.context,B=i.contextType,C=wi,typeof B=="object"&&B!==null&&(C=pe(B)),b=i.getDerivedStateFromProps,(B=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==Z||U!==C)&&hp(e,c,r,C),Wn=!1,U=e.memoizedState,c.state=U,Hs(e,r,c,o),ks();var P=e.memoizedState;y!==Z||U!==P||Wn||t!==null&&t.dependencies!==null&&cl(t.dependencies)?(typeof b=="function"&&(ic(e,i,b,r),P=e.memoizedState),(q=Wn||dp(e,i,q,r,U,P,C)||t!==null&&t.dependencies!==null&&cl(t.dependencies))?(B||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(r,P,C),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(r,P,C)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&U===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&U===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=P),c.props=r,c.state=P,c.context=C,r=q):(typeof c.componentDidUpdate!="function"||y===t.memoizedProps&&U===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&U===t.memoizedState||(e.flags|=1024),r=!1)}return c=r,Dl(t,e),r=(e.flags&128)!==0,c||r?(c=e.stateNode,i=r&&typeof i.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&r?(e.child=ji(e,t.child,null,o),e.child=ji(e,null,i,o)):ue(t,e,i,o),e.memoizedState=c.state,t=e.child):t=zn(t,e,o),t}function Rp(t,e,i,r){return Ns(),e.flags|=256,ue(t,e,i,r),e.child}var oc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function uc(t){return{baseLanes:t,cachePool:pm()}}function cc(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=Qe),t}function Dp(t,e,i){var r=e.pendingProps,o=!1,c=(e.flags&128)!==0,y;if((y=c)||(y=t!==null&&t.memoizedState===null?!1:(ne.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(Ot){if(o?ea(e):na(),Ot){var b=Kt,C;if(C=b){t:{for(C=b,b=ln;C.nodeType!==8;){if(!b){b=null;break t}if(C=en(C.nextSibling),C===null){b=null;break t}}b=C}b!==null?(e.memoizedState={dehydrated:b,treeContext:Ua!==null?{id:Dn,overflow:On}:null,retryLane:536870912,hydrationErrors:null},C=je(18,null,null,0),C.stateNode=b,C.return=e,e.child=C,xe=e,Kt=null,C=!0):C=!1}C||Pa(e)}if(b=e.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Zc(b)?e.lanes=32:e.lanes=536870912,null;Vn(e)}return b=r.children,r=r.fallback,o?(na(),o=e.mode,b=Ol({mode:"hidden",children:b},o),r=Ba(r,o,i,null),b.return=e,r.return=e,b.sibling=r,e.child=b,o=e.child,o.memoizedState=uc(i),o.childLanes=cc(t,y,i),e.memoizedState=oc,r):(ea(e),fc(e,b))}if(C=t.memoizedState,C!==null&&(b=C.dehydrated,b!==null)){if(c)e.flags&256?(ea(e),e.flags&=-257,e=dc(t,e,i)):e.memoizedState!==null?(na(),e.child=t.child,e.flags|=128,e=null):(na(),o=r.fallback,b=e.mode,r=Ol({mode:"visible",children:r.children},b),o=Ba(o,b,i,null),o.flags|=2,r.return=e,o.return=e,r.sibling=o,e.child=r,ji(e,t.child,null,i),r=e.child,r.memoizedState=uc(i),r.childLanes=cc(t,y,i),e.memoizedState=oc,e=o);else if(ea(e),Zc(b)){if(y=b.nextSibling&&b.nextSibling.dataset,y)var B=y.dgst;y=B,r=Error(l(419)),r.stack="",r.digest=y,_s({value:r,source:null,stack:null}),e=dc(t,e,i)}else if(se||js(t,e,i,!1),y=(i&t.childLanes)!==0,se||y){if(y=Ht,y!==null&&(r=i&-i,r=(r&42)!==0?1:Qo(r),r=(r&(y.suspendedLanes|i))!==0?0:r,r!==0&&r!==C.retryLane))throw C.retryLane=r,Ti(t,r),Ue(y,t,r),xp;b.data==="$?"||Oc(),e=dc(t,e,i)}else b.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=C.treeContext,Kt=en(b.nextSibling),xe=e,Ot=!0,Ha=null,ln=!1,t!==null&&(Xe[Ke++]=Dn,Xe[Ke++]=On,Xe[Ke++]=Ua,Dn=t.id,On=t.overflow,Ua=e),e=fc(e,r.children),e.flags|=4096);return e}return o?(na(),o=r.fallback,b=e.mode,C=t.child,B=C.sibling,r=Rn(C,{mode:"hidden",children:r.children}),r.subtreeFlags=C.subtreeFlags&65011712,B!==null?o=Rn(B,o):(o=Ba(o,b,i,null),o.flags|=2),o.return=e,r.return=e,r.sibling=o,e.child=r,r=o,o=e.child,b=t.child.memoizedState,b===null?b=uc(i):(C=b.cachePool,C!==null?(B=ee._currentValue,C=C.parent!==B?{parent:B,pool:B}:C):C=pm(),b={baseLanes:b.baseLanes|i,cachePool:C}),o.memoizedState=b,o.childLanes=cc(t,y,i),e.memoizedState=oc,r):(ea(e),i=t.child,t=i.sibling,i=Rn(i,{mode:"visible",children:r.children}),i.return=e,i.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=i,e.memoizedState=null,i)}function fc(t,e){return e=Ol({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ol(t,e){return t=je(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function dc(t,e,i){return ji(e,t.child,null,i),t=fc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Op(t,e,i){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Ou(t.return,e,i)}function hc(t,e,i,r,o){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:i,tailMode:o}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=i,c.tailMode=o)}function Np(t,e,i){var r=e.pendingProps,o=r.revealOrder,c=r.tail;if(ue(t,e,r.children,i),r=ne.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Op(t,i,e);else if(t.tag===19)Op(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(I(ne,r),o){case"forwards":for(i=e.child,o=null;i!==null;)t=i.alternate,t!==null&&Ml(t)===null&&(o=i),i=i.sibling;i=o,i===null?(o=e.child,e.child=null):(o=i.sibling,i.sibling=null),hc(e,!1,o,i,c);break;case"backwards":for(i=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Ml(t)===null){e.child=o;break}t=o.sibling,o.sibling=i,i=o,o=t}hc(e,!0,i,null,c);break;case"together":hc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function zn(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),la|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(js(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,i=Rn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=Rn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function mc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&cl(t)))}function K1(t,e,i){switch(e.tag){case 3:ut(e,e.stateNode.containerInfo),In(e,ee,t.memoizedState.cache),Ns();break;case 27:case 5:Ct(e);break;case 4:ut(e,e.stateNode.containerInfo);break;case 10:In(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(ea(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Dp(t,e,i):(ea(e),t=zn(t,e,i),t!==null?t.sibling:null);ea(e);break;case 19:var o=(t.flags&128)!==0;if(r=(i&e.childLanes)!==0,r||(js(t,e,i,!1),r=(i&e.childLanes)!==0),o){if(r)return Np(t,e,i);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),I(ne,ne.current),r)break;return null;case 22:case 23:return e.lanes=0,Ap(t,e,i);case 24:In(e,ee,t.memoizedState.cache)}return zn(t,e,i)}function _p(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)se=!0;else{if(!mc(t,i)&&(e.flags&128)===0)return se=!1,K1(t,e,i);se=(t.flags&131072)!==0}else se=!1,Ot&&(e.flags&1048576)!==0&&om(e,ul,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,o=r._init;if(r=o(r._payload),e.type=r,typeof r=="function")wu(r)?(t=Ka(r,t),e.tag=1,e=Cp(null,e,r,t,i)):(e.tag=0,e=lc(null,e,r,t,i));else{if(r!=null){if(o=r.$$typeof,o===H){e.tag=11,e=Sp(null,e,r,t,i);break t}else if(o===Q){e.tag=14,e=Tp(null,e,r,t,i);break t}}throw e=vt(r)||r,Error(l(306,e,""))}}return e;case 0:return lc(t,e,e.type,e.pendingProps,i);case 1:return r=e.type,o=Ka(r,e.pendingProps),Cp(t,e,r,o,i);case 3:t:{if(ut(e,e.stateNode.containerInfo),t===null)throw Error(l(387));r=e.pendingProps;var c=e.memoizedState;o=c.element,Bu(t,e),Hs(e,r,null,i);var y=e.memoizedState;if(r=y.cache,In(e,ee,r),r!==c.cache&&Nu(e,[ee],i,!0),ks(),r=y.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Rp(t,e,r,i);break t}else if(r!==o){o=Ye(Error(l(424)),e),_s(o),e=Rp(t,e,r,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Kt=en(t.firstChild),xe=e,Ot=!0,Ha=null,ln=!0,i=cp(e,null,r,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(Ns(),r===o){e=zn(t,e,i);break t}ue(t,e,r,i)}e=e.child}return e;case 26:return Dl(t,e),t===null?(i=Lg(e.type,null,e.pendingProps,null))?e.memoizedState=i:Ot||(i=e.type,t=e.pendingProps,r=ql(lt.current).createElement(i),r[me]=e,r[Ae]=t,fe(r,i,t),ie(r),e.stateNode=r):e.memoizedState=Lg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Ct(e),t===null&&Ot&&(r=e.stateNode=jg(e.type,e.pendingProps,lt.current),xe=e,ln=!0,o=Kt,fa(e.type)?(Qc=o,Kt=en(r.firstChild)):Kt=o),ue(t,e,e.pendingProps.children,i),Dl(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Ot&&((o=r=Kt)&&(r=xT(r,e.type,e.pendingProps,ln),r!==null?(e.stateNode=r,xe=e,Kt=en(r.firstChild),ln=!1,o=!0):o=!1),o||Pa(e)),Ct(e),o=e.type,c=e.pendingProps,y=t!==null?t.memoizedProps:null,r=c.children,qc(o,c)?r=null:y!==null&&qc(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Yu(t,e,U1,null,null,i),or._currentValue=o),Dl(t,e),ue(t,e,r,i),e.child;case 6:return t===null&&Ot&&((t=i=Kt)&&(i=ST(i,e.pendingProps,ln),i!==null?(e.stateNode=i,xe=e,Kt=null,t=!0):t=!1),t||Pa(e)),null;case 13:return Dp(t,e,i);case 4:return ut(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=ji(e,null,r,i):ue(t,e,r,i),e.child;case 11:return Sp(t,e,e.type,e.pendingProps,i);case 7:return ue(t,e,e.pendingProps,i),e.child;case 8:return ue(t,e,e.pendingProps.children,i),e.child;case 12:return ue(t,e,e.pendingProps.children,i),e.child;case 10:return r=e.pendingProps,In(e,e.type,r.value),ue(t,e,r.children,i),e.child;case 9:return o=e.type._context,r=e.pendingProps.children,Ya(e),o=pe(o),r=r(o),e.flags|=1,ue(t,e,r,i),e.child;case 14:return Tp(t,e,e.type,e.pendingProps,i);case 15:return wp(t,e,e.type,e.pendingProps,i);case 19:return Np(t,e,i);case 31:return r=e.pendingProps,i=e.mode,r={mode:r.mode,children:r.children},t===null?(i=Ol(r,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=Rn(t.child,r),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Ap(t,e,i);case 24:return Ya(e),r=pe(ee),t===null?(o=Vu(),o===null&&(o=Ht,c=_u(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=i),o=c),e.memoizedState={parent:r,cache:o},Lu(e),In(e,ee,o)):((t.lanes&i)!==0&&(Bu(t,e),Hs(e,null,null,i),ks()),o=t.memoizedState,c=e.memoizedState,o.parent!==r?(o={parent:r,cache:r},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),In(e,ee,r)):(r=c.cache,In(e,ee,r),r!==o.cache&&Nu(e,[ee],i,!0))),ue(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function Ln(t){t.flags|=4}function jp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Pg(e)){if(e=Ze.current,e!==null&&((Rt&4194048)===Rt?on!==null:(Rt&62914560)!==Rt&&(Rt&536870912)===0||e!==on))throw Bs=zu,gm;t.flags|=8192}}function Nl(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?fh():536870912,t.lanes|=e,Bi|=e)}function Zs(t,e){if(!Ot)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var r=null;i!==null;)i.alternate!==null&&(r=i),i=i.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Yt(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,r=0;if(e)for(var o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=i,e}function Z1(t,e,i){var r=e.pendingProps;switch(Cu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Yt(e),null;case 1:return Yt(e),null;case 3:return i=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),_n(ee),Nt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(Os(e)?Ln(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,fm())),Yt(e),null;case 26:return i=e.memoizedState,t===null?(Ln(e),i!==null?(Yt(e),jp(e,i)):(Yt(e),e.flags&=-16777217)):i?i!==t.memoizedState?(Ln(e),Yt(e),jp(e,i)):(Yt(e),e.flags&=-16777217):(t.memoizedProps!==r&&Ln(e),Yt(e),e.flags&=-16777217),null;case 27:wt(e),i=lt.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Ln(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Yt(e),null}t=J.current,Os(e)?um(e):(t=jg(o,r,i),e.stateNode=t,Ln(e))}return Yt(e),null;case 5:if(wt(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Ln(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Yt(e),null}if(t=J.current,Os(e))um(e);else{switch(o=ql(lt.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?o.createElement(i,{is:r.is}):o.createElement(i)}}t[me]=e,t[Ae]=r;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(fe(t,i,r),i){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ln(e)}}return Yt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&Ln(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(l(166));if(t=lt.current,Os(e)){if(t=e.stateNode,i=e.memoizedProps,r=null,o=xe,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}t[me]=e,t=!!(t.nodeValue===i||r!==null&&r.suppressHydrationWarning===!0||Mg(t.nodeValue,i)),t||Pa(e)}else t=ql(t).createTextNode(r),t[me]=e,e.stateNode=t}return Yt(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Os(e),r!==null&&r.dehydrated!==null){if(t===null){if(!o)throw Error(l(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(l(317));o[me]=e}else Ns(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Yt(e),o=!1}else o=fm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Vn(e),e):(Vn(e),null)}if(Vn(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=r!==null,t=t!==null&&t.memoizedState!==null,i){r=e.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var c=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(c=r.memoizedState.cachePool.pool),c!==o&&(r.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Nl(e,e.updateQueue),Yt(e),null;case 4:return Nt(),t===null&&kc(e.stateNode.containerInfo),Yt(e),null;case 10:return _n(e.type),Yt(e),null;case 19:if(W(ne),o=e.memoizedState,o===null)return Yt(e),null;if(r=(e.flags&128)!==0,c=o.rendering,c===null)if(r)Zs(o,!1);else{if(Zt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=Ml(t),c!==null){for(e.flags|=128,Zs(o,!1),t=c.updateQueue,e.updateQueue=t,Nl(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)lm(i,t),i=i.sibling;return I(ne,ne.current&1|2),e.child}t=t.sibling}o.tail!==null&&he()>Vl&&(e.flags|=128,r=!0,Zs(o,!1),e.lanes=4194304)}else{if(!r)if(t=Ml(c),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Nl(e,t),Zs(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!Ot)return Yt(e),null}else 2*he()-o.renderingStartTime>Vl&&i!==536870912&&(e.flags|=128,r=!0,Zs(o,!1),e.lanes=4194304);o.isBackwards?(c.sibling=e.child,e.child=c):(t=o.last,t!==null?t.sibling=c:e.child=c,o.last=c)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=he(),e.sibling=null,t=ne.current,I(ne,r?t&1|2:t&1),e):(Yt(e),null);case 22:case 23:return Vn(e),Pu(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(i&536870912)!==0&&(e.flags&128)===0&&(Yt(e),e.subtreeFlags&6&&(e.flags|=8192)):Yt(e),i=e.updateQueue,i!==null&&Nl(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==i&&(e.flags|=2048),t!==null&&W(qa),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),_n(ee),Yt(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function Q1(t,e){switch(Cu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return _n(ee),Nt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return wt(e),null;case 13:if(Vn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));Ns()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return W(ne),null;case 4:return Nt(),null;case 10:return _n(e.type),null;case 22:case 23:return Vn(e),Pu(),t!==null&&W(qa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return _n(ee),null;case 25:return null;default:return null}}function Vp(t,e){switch(Cu(e),e.tag){case 3:_n(ee),Nt();break;case 26:case 27:case 5:wt(e);break;case 4:Nt();break;case 13:Vn(e);break;case 19:W(ne);break;case 10:_n(e.type);break;case 22:case 23:Vn(e),Pu(),t!==null&&W(qa);break;case 24:_n(ee)}}function Qs(t,e){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var o=r.next;i=o;do{if((i.tag&t)===t){r=void 0;var c=i.create,y=i.inst;r=c(),y.destroy=r}i=i.next}while(i!==o)}}catch(b){kt(e,e.return,b)}}function aa(t,e,i){try{var r=e.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var c=o.next;r=c;do{if((r.tag&t)===t){var y=r.inst,b=y.destroy;if(b!==void 0){y.destroy=void 0,o=e;var C=i,B=b;try{B()}catch(q){kt(o,C,q)}}}r=r.next}while(r!==c)}}catch(q){kt(e,e.return,q)}}function zp(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{Tm(e,i)}catch(r){kt(t,t.return,r)}}}function Lp(t,e,i){i.props=Ka(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(r){kt(t,e,r)}}function Fs(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof i=="function"?t.refCleanup=i(r):i.current=r}}catch(o){kt(t,e,o)}}function un(t,e){var i=t.ref,r=t.refCleanup;if(i!==null)if(typeof r=="function")try{r()}catch(o){kt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(o){kt(t,e,o)}else i.current=null}function Bp(t){var e=t.type,i=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break t;case"img":i.src?r.src=i.src:i.srcSet&&(r.srcset=i.srcSet)}}catch(o){kt(t,t.return,o)}}function pc(t,e,i){try{var r=t.stateNode;pT(r,t.type,i,e),r[Ae]=e}catch(o){kt(t,t.return,o)}}function Up(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&fa(t.type)||t.tag===4}function gc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Up(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&fa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function yc(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=Yl));else if(r!==4&&(r===27&&fa(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(yc(t,e,i),t=t.sibling;t!==null;)yc(t,e,i),t=t.sibling}function _l(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(r!==4&&(r===27&&fa(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(_l(t,e,i),t=t.sibling;t!==null;)_l(t,e,i),t=t.sibling}function kp(t){var e=t.stateNode,i=t.memoizedProps;try{for(var r=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);fe(e,r,i),e[me]=t,e[Ae]=i}catch(c){kt(t,t.return,c)}}var Bn=!1,It=!1,vc=!1,Hp=typeof WeakSet=="function"?WeakSet:Set,re=null;function F1(t,e){if(t=t.containerInfo,Gc=Il,t=Wh(t),gu(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var r=i.getSelection&&i.getSelection();if(r&&r.rangeCount!==0){i=r.anchorNode;var o=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{i.nodeType,c.nodeType}catch{i=null;break t}var y=0,b=-1,C=-1,B=0,q=0,Z=t,U=null;e:for(;;){for(var P;Z!==i||o!==0&&Z.nodeType!==3||(b=y+o),Z!==c||r!==0&&Z.nodeType!==3||(C=y+r),Z.nodeType===3&&(y+=Z.nodeValue.length),(P=Z.firstChild)!==null;)U=Z,Z=P;for(;;){if(Z===t)break e;if(U===i&&++B===o&&(b=y),U===c&&++q===r&&(C=y),(P=Z.nextSibling)!==null)break;Z=U,U=Z.parentNode}Z=P}i=b===-1||C===-1?null:{start:b,end:C}}else i=null}i=i||{start:0,end:0}}else i=null;for(Yc={focusedElem:t,selectionRange:i},Il=!1,re=e;re!==null;)if(e=re,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,re=t;else for(;re!==null;){switch(e=re,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,i=e,o=c.memoizedProps,c=c.memoizedState,r=i.stateNode;try{var mt=Ka(i.type,o,i.elementType===i.type);t=r.getSnapshotBeforeUpdate(mt,c),r.__reactInternalSnapshotBeforeUpdate=t}catch(ft){kt(i,i.return,ft)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)Kc(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Kc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,re=t;break}re=e.return}}function Pp(t,e,i){var r=i.flags;switch(i.tag){case 0:case 11:case 15:ia(t,i),r&4&&Qs(5,i);break;case 1:if(ia(t,i),r&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(y){kt(i,i.return,y)}else{var o=Ka(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){kt(i,i.return,y)}}r&64&&zp(i),r&512&&Fs(i,i.return);break;case 3:if(ia(t,i),r&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Tm(t,e)}catch(y){kt(i,i.return,y)}}break;case 27:e===null&&r&4&&kp(i);case 26:case 5:ia(t,i),e===null&&r&4&&Bp(i),r&512&&Fs(i,i.return);break;case 12:ia(t,i);break;case 13:ia(t,i),r&4&&qp(t,i),r&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=iT.bind(null,i),TT(t,i))));break;case 22:if(r=i.memoizedState!==null||Bn,!r){e=e!==null&&e.memoizedState!==null||It,o=Bn;var c=It;Bn=r,(It=e)&&!c?sa(t,i,(i.subtreeFlags&8772)!==0):ia(t,i),Bn=o,It=c}break;case 30:break;default:ia(t,i)}}function Gp(t){var e=t.alternate;e!==null&&(t.alternate=null,Gp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Wo(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Pt=null,Ce=!1;function Un(t,e,i){for(i=i.child;i!==null;)Yp(t,e,i),i=i.sibling}function Yp(t,e,i){if(Oe&&typeof Oe.onCommitFiberUnmount=="function")try{Oe.onCommitFiberUnmount(gs,i)}catch{}switch(i.tag){case 26:It||un(i,e),Un(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:It||un(i,e);var r=Pt,o=Ce;fa(i.type)&&(Pt=i.stateNode,Ce=!1),Un(t,e,i),ir(i.stateNode),Pt=r,Ce=o;break;case 5:It||un(i,e);case 6:if(r=Pt,o=Ce,Pt=null,Un(t,e,i),Pt=r,Ce=o,Pt!==null)if(Ce)try{(Pt.nodeType===9?Pt.body:Pt.nodeName==="HTML"?Pt.ownerDocument.body:Pt).removeChild(i.stateNode)}catch(c){kt(i,e,c)}else try{Pt.removeChild(i.stateNode)}catch(c){kt(i,e,c)}break;case 18:Pt!==null&&(Ce?(t=Pt,Ng(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),dr(t)):Ng(Pt,i.stateNode));break;case 4:r=Pt,o=Ce,Pt=i.stateNode.containerInfo,Ce=!0,Un(t,e,i),Pt=r,Ce=o;break;case 0:case 11:case 14:case 15:It||aa(2,i,e),It||aa(4,i,e),Un(t,e,i);break;case 1:It||(un(i,e),r=i.stateNode,typeof r.componentWillUnmount=="function"&&Lp(i,e,r)),Un(t,e,i);break;case 21:Un(t,e,i);break;case 22:It=(r=It)||i.memoizedState!==null,Un(t,e,i),It=r;break;default:Un(t,e,i)}}function qp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{dr(t)}catch(i){kt(e,e.return,i)}}function I1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Hp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Hp),e;default:throw Error(l(435,t.tag))}}function bc(t,e){var i=I1(t);e.forEach(function(r){var o=sT.bind(null,t,r);i.has(r)||(i.add(r),r.then(o,o))})}function Ve(t,e){var i=e.deletions;if(i!==null)for(var r=0;r<i.length;r++){var o=i[r],c=t,y=e,b=y;t:for(;b!==null;){switch(b.tag){case 27:if(fa(b.type)){Pt=b.stateNode,Ce=!1;break t}break;case 5:Pt=b.stateNode,Ce=!1;break t;case 3:case 4:Pt=b.stateNode.containerInfo,Ce=!0;break t}b=b.return}if(Pt===null)throw Error(l(160));Yp(c,y,o),Pt=null,Ce=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Xp(e,t),e=e.sibling}var tn=null;function Xp(t,e){var i=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ve(e,t),ze(t),r&4&&(aa(3,t,t.return),Qs(3,t),aa(5,t,t.return));break;case 1:Ve(e,t),ze(t),r&512&&(It||i===null||un(i,i.return)),r&64&&Bn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?r:i.concat(r))));break;case 26:var o=tn;if(Ve(e,t),ze(t),r&512&&(It||i===null||un(i,i.return)),r&4){var c=i!==null?i.memoizedState:null;if(r=t.memoizedState,i===null)if(r===null)if(t.stateNode===null){t:{r=t.type,i=t.memoizedProps,o=o.ownerDocument||o;e:switch(r){case"title":c=o.getElementsByTagName("title")[0],(!c||c[bs]||c[me]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(r),o.head.insertBefore(c,o.querySelector("head > title"))),fe(c,r,i),c[me]=t,ie(c),r=c;break t;case"link":var y=kg("link","href",o).get(r+(i.href||""));if(y){for(var b=0;b<y.length;b++)if(c=y[b],c.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&c.getAttribute("rel")===(i.rel==null?null:i.rel)&&c.getAttribute("title")===(i.title==null?null:i.title)&&c.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){y.splice(b,1);break e}}c=o.createElement(r),fe(c,r,i),o.head.appendChild(c);break;case"meta":if(y=kg("meta","content",o).get(r+(i.content||""))){for(b=0;b<y.length;b++)if(c=y[b],c.getAttribute("content")===(i.content==null?null:""+i.content)&&c.getAttribute("name")===(i.name==null?null:i.name)&&c.getAttribute("property")===(i.property==null?null:i.property)&&c.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&c.getAttribute("charset")===(i.charSet==null?null:i.charSet)){y.splice(b,1);break e}}c=o.createElement(r),fe(c,r,i),o.head.appendChild(c);break;default:throw Error(l(468,r))}c[me]=t,ie(c),r=c}t.stateNode=r}else Hg(o,t.type,t.stateNode);else t.stateNode=Ug(o,r,t.memoizedProps);else c!==r?(c===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):c.count--,r===null?Hg(o,t.type,t.stateNode):Ug(o,r,t.memoizedProps)):r===null&&t.stateNode!==null&&pc(t,t.memoizedProps,i.memoizedProps)}break;case 27:Ve(e,t),ze(t),r&512&&(It||i===null||un(i,i.return)),i!==null&&r&4&&pc(t,t.memoizedProps,i.memoizedProps);break;case 5:if(Ve(e,t),ze(t),r&512&&(It||i===null||un(i,i.return)),t.flags&32){o=t.stateNode;try{pi(o,"")}catch(P){kt(t,t.return,P)}}r&4&&t.stateNode!=null&&(o=t.memoizedProps,pc(t,o,i!==null?i.memoizedProps:o)),r&1024&&(vc=!0);break;case 6:if(Ve(e,t),ze(t),r&4){if(t.stateNode===null)throw Error(l(162));r=t.memoizedProps,i=t.stateNode;try{i.nodeValue=r}catch(P){kt(t,t.return,P)}}break;case 3:if(Zl=null,o=tn,tn=Xl(e.containerInfo),Ve(e,t),tn=o,ze(t),r&4&&i!==null&&i.memoizedState.isDehydrated)try{dr(e.containerInfo)}catch(P){kt(t,t.return,P)}vc&&(vc=!1,Kp(t));break;case 4:r=tn,tn=Xl(t.stateNode.containerInfo),Ve(e,t),ze(t),tn=r;break;case 12:Ve(e,t),ze(t);break;case 13:Ve(e,t),ze(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Ec=he()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,bc(t,r)));break;case 22:o=t.memoizedState!==null;var C=i!==null&&i.memoizedState!==null,B=Bn,q=It;if(Bn=B||o,It=q||C,Ve(e,t),It=q,Bn=B,ze(t),r&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(i===null||C||Bn||It||Za(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){C=i=e;try{if(c=C.stateNode,o)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{b=C.stateNode;var Z=C.memoizedProps.style,U=Z!=null&&Z.hasOwnProperty("display")?Z.display:null;b.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(P){kt(C,C.return,P)}}}else if(e.tag===6){if(i===null){C=e;try{C.stateNode.nodeValue=o?"":C.memoizedProps}catch(P){kt(C,C.return,P)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(i=r.retryQueue,i!==null&&(r.retryQueue=null,bc(t,i))));break;case 19:Ve(e,t),ze(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,bc(t,r)));break;case 30:break;case 21:break;default:Ve(e,t),ze(t)}}function ze(t){var e=t.flags;if(e&2){try{for(var i,r=t.return;r!==null;){if(Up(r)){i=r;break}r=r.return}if(i==null)throw Error(l(160));switch(i.tag){case 27:var o=i.stateNode,c=gc(t);_l(t,c,o);break;case 5:var y=i.stateNode;i.flags&32&&(pi(y,""),i.flags&=-33);var b=gc(t);_l(t,b,y);break;case 3:case 4:var C=i.stateNode.containerInfo,B=gc(t);yc(t,B,C);break;default:throw Error(l(161))}}catch(q){kt(t,t.return,q)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Kp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Kp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ia(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Pp(t,e.alternate,e),e=e.sibling}function Za(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:aa(4,e,e.return),Za(e);break;case 1:un(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Lp(e,e.return,i),Za(e);break;case 27:ir(e.stateNode);case 26:case 5:un(e,e.return),Za(e);break;case 22:e.memoizedState===null&&Za(e);break;case 30:Za(e);break;default:Za(e)}t=t.sibling}}function sa(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,o=t,c=e,y=c.flags;switch(c.tag){case 0:case 11:case 15:sa(o,c,i),Qs(4,c);break;case 1:if(sa(o,c,i),r=c,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(B){kt(r,r.return,B)}if(r=c,o=r.updateQueue,o!==null){var b=r.stateNode;try{var C=o.shared.hiddenCallbacks;if(C!==null)for(o.shared.hiddenCallbacks=null,o=0;o<C.length;o++)Sm(C[o],b)}catch(B){kt(r,r.return,B)}}i&&y&64&&zp(c),Fs(c,c.return);break;case 27:kp(c);case 26:case 5:sa(o,c,i),i&&r===null&&y&4&&Bp(c),Fs(c,c.return);break;case 12:sa(o,c,i);break;case 13:sa(o,c,i),i&&y&4&&qp(o,c);break;case 22:c.memoizedState===null&&sa(o,c,i),Fs(c,c.return);break;case 30:break;default:sa(o,c,i)}e=e.sibling}}function xc(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&Vs(i))}function Sc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Vs(t))}function cn(t,e,i,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Zp(t,e,i,r),e=e.sibling}function Zp(t,e,i,r){var o=e.flags;switch(e.tag){case 0:case 11:case 15:cn(t,e,i,r),o&2048&&Qs(9,e);break;case 1:cn(t,e,i,r);break;case 3:cn(t,e,i,r),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Vs(t)));break;case 12:if(o&2048){cn(t,e,i,r),t=e.stateNode;try{var c=e.memoizedProps,y=c.id,b=c.onPostCommit;typeof b=="function"&&b(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(C){kt(e,e.return,C)}}else cn(t,e,i,r);break;case 13:cn(t,e,i,r);break;case 23:break;case 22:c=e.stateNode,y=e.alternate,e.memoizedState!==null?c._visibility&2?cn(t,e,i,r):Is(t,e):c._visibility&2?cn(t,e,i,r):(c._visibility|=2,Vi(t,e,i,r,(e.subtreeFlags&10256)!==0)),o&2048&&xc(y,e);break;case 24:cn(t,e,i,r),o&2048&&Sc(e.alternate,e);break;default:cn(t,e,i,r)}}function Vi(t,e,i,r,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,y=e,b=i,C=r,B=y.flags;switch(y.tag){case 0:case 11:case 15:Vi(c,y,b,C,o),Qs(8,y);break;case 23:break;case 22:var q=y.stateNode;y.memoizedState!==null?q._visibility&2?Vi(c,y,b,C,o):Is(c,y):(q._visibility|=2,Vi(c,y,b,C,o)),o&&B&2048&&xc(y.alternate,y);break;case 24:Vi(c,y,b,C,o),o&&B&2048&&Sc(y.alternate,y);break;default:Vi(c,y,b,C,o)}e=e.sibling}}function Is(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,r=e,o=r.flags;switch(r.tag){case 22:Is(i,r),o&2048&&xc(r.alternate,r);break;case 24:Is(i,r),o&2048&&Sc(r.alternate,r);break;default:Is(i,r)}e=e.sibling}}var Ws=8192;function zi(t){if(t.subtreeFlags&Ws)for(t=t.child;t!==null;)Qp(t),t=t.sibling}function Qp(t){switch(t.tag){case 26:zi(t),t.flags&Ws&&t.memoizedState!==null&&zT(tn,t.memoizedState,t.memoizedProps);break;case 5:zi(t);break;case 3:case 4:var e=tn;tn=Xl(t.stateNode.containerInfo),zi(t),tn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ws,Ws=16777216,zi(t),Ws=e):zi(t));break;default:zi(t)}}function Fp(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function $s(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];re=r,Wp(r,t)}Fp(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ip(t),t=t.sibling}function Ip(t){switch(t.tag){case 0:case 11:case 15:$s(t),t.flags&2048&&aa(9,t,t.return);break;case 3:$s(t);break;case 12:$s(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,jl(t)):$s(t);break;default:$s(t)}}function jl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];re=r,Wp(r,t)}Fp(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:aa(8,e,e.return),jl(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,jl(e));break;default:jl(e)}t=t.sibling}}function Wp(t,e){for(;re!==null;){var i=re;switch(i.tag){case 0:case 11:case 15:aa(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var r=i.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Vs(i.memoizedState.cache)}if(r=i.child,r!==null)r.return=i,re=r;else t:for(i=t;re!==null;){r=re;var o=r.sibling,c=r.return;if(Gp(r),r===i){re=null;break t}if(o!==null){o.return=c,re=o;break t}re=c}}}var W1={getCacheForType:function(t){var e=pe(ee),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},$1=typeof WeakMap=="function"?WeakMap:Map,_t=0,Ht=null,Et=null,Rt=0,jt=0,Le=null,ra=!1,Li=!1,Tc=!1,kn=0,Zt=0,la=0,Qa=0,wc=0,Qe=0,Bi=0,Js=null,Re=null,Ac=!1,Ec=0,Vl=1/0,zl=null,oa=null,ce=0,ua=null,Ui=null,ki=0,Mc=0,Cc=null,$p=null,tr=0,Rc=null;function Be(){if((_t&2)!==0&&Rt!==0)return Rt&-Rt;if(V.T!==null){var t=Mi;return t!==0?t:zc()}return mh()}function Jp(){Qe===0&&(Qe=(Rt&536870912)===0||Ot?ch():536870912);var t=Ze.current;return t!==null&&(t.flags|=32),Qe}function Ue(t,e,i){(t===Ht&&(jt===2||jt===9)||t.cancelPendingCommit!==null)&&(Hi(t,0),ca(t,Rt,Qe,!1)),vs(t,i),((_t&2)===0||t!==Ht)&&(t===Ht&&((_t&2)===0&&(Qa|=i),Zt===4&&ca(t,Rt,Qe,!1)),fn(t))}function tg(t,e,i){if((_t&6)!==0)throw Error(l(327));var r=!i&&(e&124)===0&&(e&t.expiredLanes)===0||ys(t,e),o=r?eT(t,e):Nc(t,e,!0),c=r;do{if(o===0){Li&&!r&&ca(t,e,0,!1);break}else{if(i=t.current.alternate,c&&!J1(i)){o=Nc(t,e,!1),c=!1;continue}if(o===2){if(c=e,t.errorRecoveryDisabledLanes&c)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var b=t;o=Js;var C=b.current.memoizedState.isDehydrated;if(C&&(Hi(b,y).flags|=256),y=Nc(b,y,!1),y!==2){if(Tc&&!C){b.errorRecoveryDisabledLanes|=c,Qa|=c,o=4;break t}c=Re,Re=o,c!==null&&(Re===null?Re=c:Re.push.apply(Re,c))}o=y}if(c=!1,o!==2)continue}}if(o===1){Hi(t,0),ca(t,e,0,!0);break}t:{switch(r=t,c=o,c){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:ca(r,e,Qe,!ra);break t;case 2:Re=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(o=Ec+300-he(),10<o)){if(ca(r,e,Qe,!ra),Kr(r,0,!0)!==0)break t;r.timeoutHandle=Dg(eg.bind(null,r,i,Re,zl,Ac,e,Qe,Qa,Bi,ra,c,2,-0,0),o);break t}eg(r,i,Re,zl,Ac,e,Qe,Qa,Bi,ra,c,0,-0,0)}}break}while(!0);fn(t)}function eg(t,e,i,r,o,c,y,b,C,B,q,Z,U,P){if(t.timeoutHandle=-1,Z=e.subtreeFlags,(Z&8192||(Z&16785408)===16785408)&&(lr={stylesheets:null,count:0,unsuspend:VT},Qp(e),Z=LT(),Z!==null)){t.cancelPendingCommit=Z(og.bind(null,t,e,c,i,r,o,y,b,C,q,1,U,P)),ca(t,c,y,!B);return}og(t,e,c,i,r,o,y,b,C)}function J1(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var r=0;r<i.length;r++){var o=i[r],c=o.getSnapshot;o=o.value;try{if(!_e(c(),o))return!1}catch{return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ca(t,e,i,r){e&=~wc,e&=~Qa,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var o=e;0<o;){var c=31-Ne(o),y=1<<c;r[c]=-1,o&=~y}i!==0&&dh(t,i,e)}function Ll(){return(_t&6)===0?(er(0),!1):!0}function Dc(){if(Et!==null){if(jt===0)var t=Et.return;else t=Et,Nn=Ga=null,Ku(t),_i=null,Xs=0,t=Et;for(;t!==null;)Vp(t.alternate,t),t=t.return;Et=null}}function Hi(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,yT(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Dc(),Ht=t,Et=i=Rn(t.current,null),Rt=e,jt=0,Le=null,ra=!1,Li=ys(t,e),Tc=!1,Bi=Qe=wc=Qa=la=Zt=0,Re=Js=null,Ac=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var o=31-Ne(r),c=1<<o;e|=t[o],r&=~c}return kn=e,il(),i}function ng(t,e){xt=null,V.H=wl,e===Ls||e===hl?(e=bm(),jt=3):e===gm?(e=bm(),jt=4):jt=e===xp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Le=e,Et===null&&(Zt=1,Rl(t,Ye(e,t.current)))}function ag(){var t=V.H;return V.H=wl,t===null?wl:t}function ig(){var t=V.A;return V.A=W1,t}function Oc(){Zt=4,ra||(Rt&4194048)!==Rt&&Ze.current!==null||(Li=!0),(la&134217727)===0&&(Qa&134217727)===0||Ht===null||ca(Ht,Rt,Qe,!1)}function Nc(t,e,i){var r=_t;_t|=2;var o=ag(),c=ig();(Ht!==t||Rt!==e)&&(zl=null,Hi(t,e)),e=!1;var y=Zt;t:do try{if(jt!==0&&Et!==null){var b=Et,C=Le;switch(jt){case 8:Dc(),y=6;break t;case 3:case 2:case 9:case 6:Ze.current===null&&(e=!0);var B=jt;if(jt=0,Le=null,Pi(t,b,C,B),i&&Li){y=0;break t}break;default:B=jt,jt=0,Le=null,Pi(t,b,C,B)}}tT(),y=Zt;break}catch(q){ng(t,q)}while(!0);return e&&t.shellSuspendCounter++,Nn=Ga=null,_t=r,V.H=o,V.A=c,Et===null&&(Ht=null,Rt=0,il()),y}function tT(){for(;Et!==null;)sg(Et)}function eT(t,e){var i=_t;_t|=2;var r=ag(),o=ig();Ht!==t||Rt!==e?(zl=null,Vl=he()+500,Hi(t,e)):Li=ys(t,e);t:do try{if(jt!==0&&Et!==null){e=Et;var c=Le;e:switch(jt){case 1:jt=0,Le=null,Pi(t,e,c,1);break;case 2:case 9:if(ym(c)){jt=0,Le=null,rg(e);break}e=function(){jt!==2&&jt!==9||Ht!==t||(jt=7),fn(t)},c.then(e,e);break t;case 3:jt=7;break t;case 4:jt=5;break t;case 7:ym(c)?(jt=0,Le=null,rg(e)):(jt=0,Le=null,Pi(t,e,c,7));break;case 5:var y=null;switch(Et.tag){case 26:y=Et.memoizedState;case 5:case 27:var b=Et;if(!y||Pg(y)){jt=0,Le=null;var C=b.sibling;if(C!==null)Et=C;else{var B=b.return;B!==null?(Et=B,Bl(B)):Et=null}break e}}jt=0,Le=null,Pi(t,e,c,5);break;case 6:jt=0,Le=null,Pi(t,e,c,6);break;case 8:Dc(),Zt=6;break t;default:throw Error(l(462))}}nT();break}catch(q){ng(t,q)}while(!0);return Nn=Ga=null,V.H=r,V.A=o,_t=i,Et!==null?0:(Ht=null,Rt=0,il(),Zt)}function nT(){for(;Et!==null&&!Da();)sg(Et)}function sg(t){var e=_p(t.alternate,t,kn);t.memoizedProps=t.pendingProps,e===null?Bl(t):Et=e}function rg(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=Mp(i,e,e.pendingProps,e.type,void 0,Rt);break;case 11:e=Mp(i,e,e.pendingProps,e.type.render,e.ref,Rt);break;case 5:Ku(e);default:Vp(i,e),e=Et=lm(e,kn),e=_p(i,e,kn)}t.memoizedProps=t.pendingProps,e===null?Bl(t):Et=e}function Pi(t,e,i,r){Nn=Ga=null,Ku(e),_i=null,Xs=0;var o=e.return;try{if(X1(t,o,e,i,Rt)){Zt=1,Rl(t,Ye(i,t.current)),Et=null;return}}catch(c){if(o!==null)throw Et=o,c;Zt=1,Rl(t,Ye(i,t.current)),Et=null;return}e.flags&32768?(Ot||r===1?t=!0:Li||(Rt&536870912)!==0?t=!1:(ra=t=!0,(r===2||r===9||r===3||r===6)&&(r=Ze.current,r!==null&&r.tag===13&&(r.flags|=16384))),lg(e,t)):Bl(e)}function Bl(t){var e=t;do{if((e.flags&32768)!==0){lg(e,ra);return}t=e.return;var i=Z1(e.alternate,e,kn);if(i!==null){Et=i;return}if(e=e.sibling,e!==null){Et=e;return}Et=e=t}while(e!==null);Zt===0&&(Zt=5)}function lg(t,e){do{var i=Q1(t.alternate,t);if(i!==null){i.flags&=32767,Et=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){Et=t;return}Et=t=i}while(t!==null);Zt=6,Et=null}function og(t,e,i,r,o,c,y,b,C){t.cancelPendingCommit=null;do Ul();while(ce!==0);if((_t&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(c=e.lanes|e.childLanes,c|=Su,VS(t,i,c,y,b,C),t===Ht&&(Et=Ht=null,Rt=0),Ui=e,ua=t,ki=i,Mc=c,Cc=o,$p=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,rT(Yr,function(){return hg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=V.T,V.T=null,o=Y.p,Y.p=2,y=_t,_t|=4;try{F1(t,e,i)}finally{_t=y,Y.p=o,V.T=r}}ce=1,ug(),cg(),fg()}}function ug(){if(ce===1){ce=0;var t=ua,e=Ui,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=V.T,V.T=null;var r=Y.p;Y.p=2;var o=_t;_t|=4;try{Xp(e,t);var c=Yc,y=Wh(t.containerInfo),b=c.focusedElem,C=c.selectionRange;if(y!==b&&b&&b.ownerDocument&&Ih(b.ownerDocument.documentElement,b)){if(C!==null&&gu(b)){var B=C.start,q=C.end;if(q===void 0&&(q=B),"selectionStart"in b)b.selectionStart=B,b.selectionEnd=Math.min(q,b.value.length);else{var Z=b.ownerDocument||document,U=Z&&Z.defaultView||window;if(U.getSelection){var P=U.getSelection(),mt=b.textContent.length,ft=Math.min(C.start,mt),Bt=C.end===void 0?ft:Math.min(C.end,mt);!P.extend&&ft>Bt&&(y=Bt,Bt=ft,ft=y);var j=Fh(b,ft),N=Fh(b,Bt);if(j&&N&&(P.rangeCount!==1||P.anchorNode!==j.node||P.anchorOffset!==j.offset||P.focusNode!==N.node||P.focusOffset!==N.offset)){var L=Z.createRange();L.setStart(j.node,j.offset),P.removeAllRanges(),ft>Bt?(P.addRange(L),P.extend(N.node,N.offset)):(L.setEnd(N.node,N.offset),P.addRange(L))}}}}for(Z=[],P=b;P=P.parentNode;)P.nodeType===1&&Z.push({element:P,left:P.scrollLeft,top:P.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<Z.length;b++){var K=Z[b];K.element.scrollLeft=K.left,K.element.scrollTop=K.top}}Il=!!Gc,Yc=Gc=null}finally{_t=o,Y.p=r,V.T=i}}t.current=e,ce=2}}function cg(){if(ce===2){ce=0;var t=ua,e=Ui,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=V.T,V.T=null;var r=Y.p;Y.p=2;var o=_t;_t|=4;try{Pp(t,e.alternate,e)}finally{_t=o,Y.p=r,V.T=i}}ce=3}}function fg(){if(ce===4||ce===3){ce=0,Oa();var t=ua,e=Ui,i=ki,r=$p;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ce=5:(ce=0,Ui=ua=null,dg(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(oa=null),Fo(i),e=e.stateNode,Oe&&typeof Oe.onCommitFiberRoot=="function")try{Oe.onCommitFiberRoot(gs,e,void 0,(e.current.flags&128)===128)}catch{}if(r!==null){e=V.T,o=Y.p,Y.p=2,V.T=null;try{for(var c=t.onRecoverableError,y=0;y<r.length;y++){var b=r[y];c(b.value,{componentStack:b.stack})}}finally{V.T=e,Y.p=o}}(ki&3)!==0&&Ul(),fn(t),o=t.pendingLanes,(i&4194090)!==0&&(o&42)!==0?t===Rc?tr++:(tr=0,Rc=t):tr=0,er(0)}}function dg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Vs(e)))}function Ul(t){return ug(),cg(),fg(),hg()}function hg(){if(ce!==5)return!1;var t=ua,e=Mc;Mc=0;var i=Fo(ki),r=V.T,o=Y.p;try{Y.p=32>i?32:i,V.T=null,i=Cc,Cc=null;var c=ua,y=ki;if(ce=0,Ui=ua=null,ki=0,(_t&6)!==0)throw Error(l(331));var b=_t;if(_t|=4,Ip(c.current),Zp(c,c.current,y,i),_t=b,er(0,!1),Oe&&typeof Oe.onPostCommitFiberRoot=="function")try{Oe.onPostCommitFiberRoot(gs,c)}catch{}return!0}finally{Y.p=o,V.T=r,dg(t,e)}}function mg(t,e,i){e=Ye(i,e),e=rc(t.stateNode,e,2),t=Jn(t,e,2),t!==null&&(vs(t,2),fn(t))}function kt(t,e,i){if(t.tag===3)mg(t,t,i);else for(;e!==null;){if(e.tag===3){mg(e,t,i);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(oa===null||!oa.has(r))){t=Ye(i,t),i=vp(2),r=Jn(e,i,2),r!==null&&(bp(i,r,e,t),vs(r,2),fn(r));break}}e=e.return}}function _c(t,e,i){var r=t.pingCache;if(r===null){r=t.pingCache=new $1;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(i)||(Tc=!0,o.add(i),t=aT.bind(null,t,e,i),e.then(t,t))}function aT(t,e,i){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,Ht===t&&(Rt&i)===i&&(Zt===4||Zt===3&&(Rt&62914560)===Rt&&300>he()-Ec?(_t&2)===0&&Hi(t,0):wc|=i,Bi===Rt&&(Bi=0)),fn(t)}function pg(t,e){e===0&&(e=fh()),t=Ti(t,e),t!==null&&(vs(t,e),fn(t))}function iT(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),pg(t,i)}function sT(t,e){var i=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(i=o.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(l(314))}r!==null&&r.delete(e),pg(t,i)}function rT(t,e){return oe(t,e)}var kl=null,Gi=null,jc=!1,Hl=!1,Vc=!1,Fa=0;function fn(t){t!==Gi&&t.next===null&&(Gi===null?kl=Gi=t:Gi=Gi.next=t),Hl=!0,jc||(jc=!0,oT())}function er(t,e){if(!Vc&&Hl){Vc=!0;do for(var i=!1,r=kl;r!==null;){if(t!==0){var o=r.pendingLanes;if(o===0)var c=0;else{var y=r.suspendedLanes,b=r.pingedLanes;c=(1<<31-Ne(42|t)+1)-1,c&=o&~(y&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(i=!0,bg(r,c))}else c=Rt,c=Kr(r,r===Ht?c:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(c&3)===0||ys(r,c)||(i=!0,bg(r,c));r=r.next}while(i);Vc=!1}}function lT(){gg()}function gg(){Hl=jc=!1;var t=0;Fa!==0&&(gT()&&(t=Fa),Fa=0);for(var e=he(),i=null,r=kl;r!==null;){var o=r.next,c=yg(r,e);c===0?(r.next=null,i===null?kl=o:i.next=o,o===null&&(Gi=i)):(i=r,(t!==0||(c&3)!==0)&&(Hl=!0)),r=o}er(t)}function yg(t,e){for(var i=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var y=31-Ne(c),b=1<<y,C=o[y];C===-1?((b&i)===0||(b&r)!==0)&&(o[y]=jS(b,e)):C<=e&&(t.expiredLanes|=b),c&=~b}if(e=Ht,i=Rt,i=Kr(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,i===0||t===e&&(jt===2||jt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&we(r),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||ys(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(r!==null&&we(r),Fo(i)){case 2:case 8:i=oh;break;case 32:i=Yr;break;case 268435456:i=uh;break;default:i=Yr}return r=vg.bind(null,t),i=oe(i,r),t.callbackPriority=e,t.callbackNode=i,e}return r!==null&&r!==null&&we(r),t.callbackPriority=2,t.callbackNode=null,2}function vg(t,e){if(ce!==0&&ce!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Ul()&&t.callbackNode!==i)return null;var r=Rt;return r=Kr(t,t===Ht?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(tg(t,r,e),yg(t,he()),t.callbackNode!=null&&t.callbackNode===i?vg.bind(null,t):null)}function bg(t,e){if(Ul())return null;tg(t,e,!0)}function oT(){vT(function(){(_t&6)!==0?oe(Na,lT):gg()})}function zc(){return Fa===0&&(Fa=ch()),Fa}function xg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Wr(""+t)}function Sg(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function uT(t,e,i,r,o){if(e==="submit"&&i&&i.stateNode===o){var c=xg((o[Ae]||null).action),y=r.submitter;y&&(e=(e=y[Ae]||null)?xg(e.formAction):y.getAttribute("formAction"),e!==null&&(c=e,y=null));var b=new el("action","action",null,r,o);t.push({event:b,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(Fa!==0){var C=y?Sg(o,y):new FormData(o);ec(i,{pending:!0,data:C,method:o.method,action:c},null,C)}}else typeof c=="function"&&(b.preventDefault(),C=y?Sg(o,y):new FormData(o),ec(i,{pending:!0,data:C,method:o.method,action:c},c,C))},currentTarget:o}]})}}for(var Lc=0;Lc<xu.length;Lc++){var Bc=xu[Lc],cT=Bc.toLowerCase(),fT=Bc[0].toUpperCase()+Bc.slice(1);Je(cT,"on"+fT)}Je(tm,"onAnimationEnd"),Je(em,"onAnimationIteration"),Je(nm,"onAnimationStart"),Je("dblclick","onDoubleClick"),Je("focusin","onFocus"),Je("focusout","onBlur"),Je(R1,"onTransitionRun"),Je(D1,"onTransitionStart"),Je(O1,"onTransitionCancel"),Je(am,"onTransitionEnd"),di("onMouseEnter",["mouseout","mouseover"]),di("onMouseLeave",["mouseout","mouseover"]),di("onPointerEnter",["pointerout","pointerover"]),di("onPointerLeave",["pointerout","pointerover"]),ja("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ja("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ja("onBeforeInput",["compositionend","keypress","textInput","paste"]),ja("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ja("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ja("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var nr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dT=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(nr));function Tg(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var r=t[i],o=r.event;r=r.listeners;t:{var c=void 0;if(e)for(var y=r.length-1;0<=y;y--){var b=r[y],C=b.instance,B=b.currentTarget;if(b=b.listener,C!==c&&o.isPropagationStopped())break t;c=b,o.currentTarget=B;try{c(o)}catch(q){Cl(q)}o.currentTarget=null,c=C}else for(y=0;y<r.length;y++){if(b=r[y],C=b.instance,B=b.currentTarget,b=b.listener,C!==c&&o.isPropagationStopped())break t;c=b,o.currentTarget=B;try{c(o)}catch(q){Cl(q)}o.currentTarget=null,c=C}}}}function Mt(t,e){var i=e[Io];i===void 0&&(i=e[Io]=new Set);var r=t+"__bubble";i.has(r)||(wg(e,t,2,!1),i.add(r))}function Uc(t,e,i){var r=0;e&&(r|=4),wg(i,t,r,e)}var Pl="_reactListening"+Math.random().toString(36).slice(2);function kc(t){if(!t[Pl]){t[Pl]=!0,gh.forEach(function(i){i!=="selectionchange"&&(dT.has(i)||Uc(i,!1,t),Uc(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Pl]||(e[Pl]=!0,Uc("selectionchange",!1,e))}}function wg(t,e,i,r){switch(Zg(e)){case 2:var o=kT;break;case 8:o=HT;break;default:o=Jc}i=o.bind(null,e,i,t),o=void 0,!lu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,i,{capture:!0,passive:o}):t.addEventListener(e,i,!0):o!==void 0?t.addEventListener(e,i,{passive:o}):t.addEventListener(e,i,!1)}function Hc(t,e,i,r,o){var c=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var b=r.stateNode.containerInfo;if(b===o)break;if(y===4)for(y=r.return;y!==null;){var C=y.tag;if((C===3||C===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;b!==null;){if(y=ui(b),y===null)return;if(C=y.tag,C===5||C===6||C===26||C===27){r=c=y;continue t}b=b.parentNode}}r=r.return}Oh(function(){var B=c,q=su(i),Z=[];t:{var U=im.get(t);if(U!==void 0){var P=el,mt=t;switch(t){case"keypress":if(Jr(i)===0)break t;case"keydown":case"keyup":P=r1;break;case"focusin":mt="focus",P=fu;break;case"focusout":mt="blur",P=fu;break;case"beforeblur":case"afterblur":P=fu;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":P=jh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":P=QS;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":P=u1;break;case tm:case em:case nm:P=WS;break;case am:P=f1;break;case"scroll":case"scrollend":P=KS;break;case"wheel":P=h1;break;case"copy":case"cut":case"paste":P=JS;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":P=zh;break;case"toggle":case"beforetoggle":P=p1}var ft=(e&4)!==0,Bt=!ft&&(t==="scroll"||t==="scrollend"),j=ft?U!==null?U+"Capture":null:U;ft=[];for(var N=B,L;N!==null;){var K=N;if(L=K.stateNode,K=K.tag,K!==5&&K!==26&&K!==27||L===null||j===null||(K=Ss(N,j),K!=null&&ft.push(ar(N,K,L))),Bt)break;N=N.return}0<ft.length&&(U=new P(U,mt,null,i,q),Z.push({event:U,listeners:ft}))}}if((e&7)===0){t:{if(U=t==="mouseover"||t==="pointerover",P=t==="mouseout"||t==="pointerout",U&&i!==iu&&(mt=i.relatedTarget||i.fromElement)&&(ui(mt)||mt[oi]))break t;if((P||U)&&(U=q.window===q?q:(U=q.ownerDocument)?U.defaultView||U.parentWindow:window,P?(mt=i.relatedTarget||i.toElement,P=B,mt=mt?ui(mt):null,mt!==null&&(Bt=d(mt),ft=mt.tag,mt!==Bt||ft!==5&&ft!==27&&ft!==6)&&(mt=null)):(P=null,mt=B),P!==mt)){if(ft=jh,K="onMouseLeave",j="onMouseEnter",N="mouse",(t==="pointerout"||t==="pointerover")&&(ft=zh,K="onPointerLeave",j="onPointerEnter",N="pointer"),Bt=P==null?U:xs(P),L=mt==null?U:xs(mt),U=new ft(K,N+"leave",P,i,q),U.target=Bt,U.relatedTarget=L,K=null,ui(q)===B&&(ft=new ft(j,N+"enter",mt,i,q),ft.target=L,ft.relatedTarget=Bt,K=ft),Bt=K,P&&mt)e:{for(ft=P,j=mt,N=0,L=ft;L;L=Yi(L))N++;for(L=0,K=j;K;K=Yi(K))L++;for(;0<N-L;)ft=Yi(ft),N--;for(;0<L-N;)j=Yi(j),L--;for(;N--;){if(ft===j||j!==null&&ft===j.alternate)break e;ft=Yi(ft),j=Yi(j)}ft=null}else ft=null;P!==null&&Ag(Z,U,P,ft,!1),mt!==null&&Bt!==null&&Ag(Z,Bt,mt,ft,!0)}}t:{if(U=B?xs(B):window,P=U.nodeName&&U.nodeName.toLowerCase(),P==="select"||P==="input"&&U.type==="file")var et=Yh;else if(Ph(U))if(qh)et=E1;else{et=w1;var Tt=T1}else P=U.nodeName,!P||P.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?B&&au(B.elementType)&&(et=Yh):et=A1;if(et&&(et=et(t,B))){Gh(Z,et,i,q);break t}Tt&&Tt(t,U,B),t==="focusout"&&B&&U.type==="number"&&B.memoizedProps.value!=null&&nu(U,"number",U.value)}switch(Tt=B?xs(B):window,t){case"focusin":(Ph(Tt)||Tt.contentEditable==="true")&&(bi=Tt,yu=B,Ds=null);break;case"focusout":Ds=yu=bi=null;break;case"mousedown":vu=!0;break;case"contextmenu":case"mouseup":case"dragend":vu=!1,$h(Z,i,q);break;case"selectionchange":if(C1)break;case"keydown":case"keyup":$h(Z,i,q)}var st;if(hu)t:{switch(t){case"compositionstart":var dt="onCompositionStart";break t;case"compositionend":dt="onCompositionEnd";break t;case"compositionupdate":dt="onCompositionUpdate";break t}dt=void 0}else vi?kh(t,i)&&(dt="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(dt="onCompositionStart");dt&&(Lh&&i.locale!=="ko"&&(vi||dt!=="onCompositionStart"?dt==="onCompositionEnd"&&vi&&(st=Nh()):(Fn=q,ou="value"in Fn?Fn.value:Fn.textContent,vi=!0)),Tt=Gl(B,dt),0<Tt.length&&(dt=new Vh(dt,t,null,i,q),Z.push({event:dt,listeners:Tt}),st?dt.data=st:(st=Hh(i),st!==null&&(dt.data=st)))),(st=y1?v1(t,i):b1(t,i))&&(dt=Gl(B,"onBeforeInput"),0<dt.length&&(Tt=new Vh("onBeforeInput","beforeinput",null,i,q),Z.push({event:Tt,listeners:dt}),Tt.data=st)),uT(Z,t,B,i,q)}Tg(Z,e)})}function ar(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Gl(t,e){for(var i=e+"Capture",r=[];t!==null;){var o=t,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Ss(t,i),o!=null&&r.unshift(ar(t,o,c)),o=Ss(t,e),o!=null&&r.push(ar(t,o,c))),t.tag===3)return r;t=t.return}return[]}function Yi(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Ag(t,e,i,r,o){for(var c=e._reactName,y=[];i!==null&&i!==r;){var b=i,C=b.alternate,B=b.stateNode;if(b=b.tag,C!==null&&C===r)break;b!==5&&b!==26&&b!==27||B===null||(C=B,o?(B=Ss(i,c),B!=null&&y.unshift(ar(i,B,C))):o||(B=Ss(i,c),B!=null&&y.push(ar(i,B,C)))),i=i.return}y.length!==0&&t.push({event:e,listeners:y})}var hT=/\r\n?/g,mT=/\u0000|\uFFFD/g;function Eg(t){return(typeof t=="string"?t:""+t).replace(hT,`
`).replace(mT,"")}function Mg(t,e){return e=Eg(e),Eg(t)===e}function Yl(){}function Lt(t,e,i,r,o,c){switch(i){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||pi(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&pi(t,""+r);break;case"className":Qr(t,"class",r);break;case"tabIndex":Qr(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Qr(t,i,r);break;case"style":Rh(t,r,c);break;case"data":if(e!=="object"){Qr(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=Wr(""+r),t.setAttribute(i,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(i==="formAction"?(e!=="input"&&Lt(t,e,"name",o.name,o,null),Lt(t,e,"formEncType",o.formEncType,o,null),Lt(t,e,"formMethod",o.formMethod,o,null),Lt(t,e,"formTarget",o.formTarget,o,null)):(Lt(t,e,"encType",o.encType,o,null),Lt(t,e,"method",o.method,o,null),Lt(t,e,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=Wr(""+r),t.setAttribute(i,r);break;case"onClick":r!=null&&(t.onclick=Yl);break;case"onScroll":r!=null&&Mt("scroll",t);break;case"onScrollEnd":r!=null&&Mt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}i=Wr(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""+r):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":r===!0?t.setAttribute(i,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,r):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(i,r):t.removeAttribute(i);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(i):t.setAttribute(i,r);break;case"popover":Mt("beforetoggle",t),Mt("toggle",t),Zr(t,"popover",r);break;case"xlinkActuate":Mn(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":Mn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":Mn(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":Mn(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":Mn(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":Mn(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":Mn(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":Mn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":Mn(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Zr(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=qS.get(i)||i,Zr(t,i,r))}}function Pc(t,e,i,r,o,c){switch(i){case"style":Rh(t,r,c);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"children":typeof r=="string"?pi(t,r):(typeof r=="number"||typeof r=="bigint")&&pi(t,""+r);break;case"onScroll":r!=null&&Mt("scroll",t);break;case"onScrollEnd":r!=null&&Mt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Yl);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!yh.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(o=i.endsWith("Capture"),e=i.slice(2,o?i.length-7:void 0),c=t[Ae]||null,c=c!=null?c[i]:null,typeof c=="function"&&t.removeEventListener(e,c,o),typeof r=="function")){typeof c!="function"&&c!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,r,o);break t}i in t?t[i]=r:r===!0?t.setAttribute(i,""):Zr(t,i,r)}}}function fe(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Mt("error",t),Mt("load",t);var r=!1,o=!1,c;for(c in i)if(i.hasOwnProperty(c)){var y=i[c];if(y!=null)switch(c){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:Lt(t,e,c,y,i,null)}}o&&Lt(t,e,"srcSet",i.srcSet,i,null),r&&Lt(t,e,"src",i.src,i,null);return;case"input":Mt("invalid",t);var b=c=y=o=null,C=null,B=null;for(r in i)if(i.hasOwnProperty(r)){var q=i[r];if(q!=null)switch(r){case"name":o=q;break;case"type":y=q;break;case"checked":C=q;break;case"defaultChecked":B=q;break;case"value":c=q;break;case"defaultValue":b=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(l(137,e));break;default:Lt(t,e,r,q,i,null)}}Ah(t,c,b,C,B,y,o,!1),Fr(t);return;case"select":Mt("invalid",t),r=y=c=null;for(o in i)if(i.hasOwnProperty(o)&&(b=i[o],b!=null))switch(o){case"value":c=b;break;case"defaultValue":y=b;break;case"multiple":r=b;default:Lt(t,e,o,b,i,null)}e=c,i=y,t.multiple=!!r,e!=null?mi(t,!!r,e,!1):i!=null&&mi(t,!!r,i,!0);return;case"textarea":Mt("invalid",t),c=o=r=null;for(y in i)if(i.hasOwnProperty(y)&&(b=i[y],b!=null))switch(y){case"value":r=b;break;case"defaultValue":o=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(l(91));break;default:Lt(t,e,y,b,i,null)}Mh(t,r,o,c),Fr(t);return;case"option":for(C in i)if(i.hasOwnProperty(C)&&(r=i[C],r!=null))switch(C){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:Lt(t,e,C,r,i,null)}return;case"dialog":Mt("beforetoggle",t),Mt("toggle",t),Mt("cancel",t),Mt("close",t);break;case"iframe":case"object":Mt("load",t);break;case"video":case"audio":for(r=0;r<nr.length;r++)Mt(nr[r],t);break;case"image":Mt("error",t),Mt("load",t);break;case"details":Mt("toggle",t);break;case"embed":case"source":case"link":Mt("error",t),Mt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(B in i)if(i.hasOwnProperty(B)&&(r=i[B],r!=null))switch(B){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:Lt(t,e,B,r,i,null)}return;default:if(au(e)){for(q in i)i.hasOwnProperty(q)&&(r=i[q],r!==void 0&&Pc(t,e,q,r,i,void 0));return}}for(b in i)i.hasOwnProperty(b)&&(r=i[b],r!=null&&Lt(t,e,b,r,i,null))}function pT(t,e,i,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,y=null,b=null,C=null,B=null,q=null;for(P in i){var Z=i[P];if(i.hasOwnProperty(P)&&Z!=null)switch(P){case"checked":break;case"value":break;case"defaultValue":C=Z;default:r.hasOwnProperty(P)||Lt(t,e,P,null,r,Z)}}for(var U in r){var P=r[U];if(Z=i[U],r.hasOwnProperty(U)&&(P!=null||Z!=null))switch(U){case"type":c=P;break;case"name":o=P;break;case"checked":B=P;break;case"defaultChecked":q=P;break;case"value":y=P;break;case"defaultValue":b=P;break;case"children":case"dangerouslySetInnerHTML":if(P!=null)throw Error(l(137,e));break;default:P!==Z&&Lt(t,e,U,P,r,Z)}}eu(t,y,b,C,B,q,c,o);return;case"select":P=y=b=U=null;for(c in i)if(C=i[c],i.hasOwnProperty(c)&&C!=null)switch(c){case"value":break;case"multiple":P=C;default:r.hasOwnProperty(c)||Lt(t,e,c,null,r,C)}for(o in r)if(c=r[o],C=i[o],r.hasOwnProperty(o)&&(c!=null||C!=null))switch(o){case"value":U=c;break;case"defaultValue":b=c;break;case"multiple":y=c;default:c!==C&&Lt(t,e,o,c,r,C)}e=b,i=y,r=P,U!=null?mi(t,!!i,U,!1):!!r!=!!i&&(e!=null?mi(t,!!i,e,!0):mi(t,!!i,i?[]:"",!1));return;case"textarea":P=U=null;for(b in i)if(o=i[b],i.hasOwnProperty(b)&&o!=null&&!r.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Lt(t,e,b,null,r,o)}for(y in r)if(o=r[y],c=i[y],r.hasOwnProperty(y)&&(o!=null||c!=null))switch(y){case"value":U=o;break;case"defaultValue":P=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(l(91));break;default:o!==c&&Lt(t,e,y,o,r,c)}Eh(t,U,P);return;case"option":for(var mt in i)if(U=i[mt],i.hasOwnProperty(mt)&&U!=null&&!r.hasOwnProperty(mt))switch(mt){case"selected":t.selected=!1;break;default:Lt(t,e,mt,null,r,U)}for(C in r)if(U=r[C],P=i[C],r.hasOwnProperty(C)&&U!==P&&(U!=null||P!=null))switch(C){case"selected":t.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Lt(t,e,C,U,r,P)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ft in i)U=i[ft],i.hasOwnProperty(ft)&&U!=null&&!r.hasOwnProperty(ft)&&Lt(t,e,ft,null,r,U);for(B in r)if(U=r[B],P=i[B],r.hasOwnProperty(B)&&U!==P&&(U!=null||P!=null))switch(B){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(l(137,e));break;default:Lt(t,e,B,U,r,P)}return;default:if(au(e)){for(var Bt in i)U=i[Bt],i.hasOwnProperty(Bt)&&U!==void 0&&!r.hasOwnProperty(Bt)&&Pc(t,e,Bt,void 0,r,U);for(q in r)U=r[q],P=i[q],!r.hasOwnProperty(q)||U===P||U===void 0&&P===void 0||Pc(t,e,q,U,r,P);return}}for(var j in i)U=i[j],i.hasOwnProperty(j)&&U!=null&&!r.hasOwnProperty(j)&&Lt(t,e,j,null,r,U);for(Z in r)U=r[Z],P=i[Z],!r.hasOwnProperty(Z)||U===P||U==null&&P==null||Lt(t,e,Z,U,r,P)}var Gc=null,Yc=null;function ql(t){return t.nodeType===9?t:t.ownerDocument}function Cg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Rg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function qc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Xc=null;function gT(){var t=window.event;return t&&t.type==="popstate"?t===Xc?!1:(Xc=t,!0):(Xc=null,!1)}var Dg=typeof setTimeout=="function"?setTimeout:void 0,yT=typeof clearTimeout=="function"?clearTimeout:void 0,Og=typeof Promise=="function"?Promise:void 0,vT=typeof queueMicrotask=="function"?queueMicrotask:typeof Og<"u"?function(t){return Og.resolve(null).then(t).catch(bT)}:Dg;function bT(t){setTimeout(function(){throw t})}function fa(t){return t==="head"}function Ng(t,e){var i=e,r=0,o=0;do{var c=i.nextSibling;if(t.removeChild(i),c&&c.nodeType===8)if(i=c.data,i==="/$"){if(0<r&&8>r){i=r;var y=t.ownerDocument;if(i&1&&ir(y.documentElement),i&2&&ir(y.body),i&4)for(i=y.head,ir(i),y=i.firstChild;y;){var b=y.nextSibling,C=y.nodeName;y[bs]||C==="SCRIPT"||C==="STYLE"||C==="LINK"&&y.rel.toLowerCase()==="stylesheet"||i.removeChild(y),y=b}}if(o===0){t.removeChild(c),dr(e);return}o--}else i==="$"||i==="$?"||i==="$!"?o++:r=i.charCodeAt(0)-48;else r=0;i=c}while(i);dr(e)}function Kc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Kc(i),Wo(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function xT(t,e,i,r){for(;t.nodeType===1;){var o=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[bs])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=en(t.nextSibling),t===null)break}return null}function ST(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=en(t.nextSibling),t===null))return null;return t}function Zc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function TT(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var r=function(){e(),i.removeEventListener("DOMContentLoaded",r)};i.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function en(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Qc=null;function _g(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function jg(t,e,i){switch(e=ql(i),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function ir(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Wo(t)}var Fe=new Map,Vg=new Set;function Xl(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Hn=Y.d;Y.d={f:wT,r:AT,D:ET,C:MT,L:CT,m:RT,X:OT,S:DT,M:NT};function wT(){var t=Hn.f(),e=Ll();return t||e}function AT(t){var e=ci(t);e!==null&&e.tag===5&&e.type==="form"?tp(e):Hn.r(t)}var qi=typeof document>"u"?null:document;function zg(t,e,i){var r=qi;if(r&&typeof e=="string"&&e){var o=Ge(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof i=="string"&&(o+='[crossorigin="'+i+'"]'),Vg.has(o)||(Vg.add(o),t={rel:t,crossOrigin:i,href:e},r.querySelector(o)===null&&(e=r.createElement("link"),fe(e,"link",t),ie(e),r.head.appendChild(e)))}}function ET(t){Hn.D(t),zg("dns-prefetch",t,null)}function MT(t,e){Hn.C(t,e),zg("preconnect",t,e)}function CT(t,e,i){Hn.L(t,e,i);var r=qi;if(r&&t&&e){var o='link[rel="preload"][as="'+Ge(e)+'"]';e==="image"&&i&&i.imageSrcSet?(o+='[imagesrcset="'+Ge(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(o+='[imagesizes="'+Ge(i.imageSizes)+'"]')):o+='[href="'+Ge(t)+'"]';var c=o;switch(e){case"style":c=Xi(t);break;case"script":c=Ki(t)}Fe.has(c)||(t=g({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),Fe.set(c,t),r.querySelector(o)!==null||e==="style"&&r.querySelector(sr(c))||e==="script"&&r.querySelector(rr(c))||(e=r.createElement("link"),fe(e,"link",t),ie(e),r.head.appendChild(e)))}}function RT(t,e){Hn.m(t,e);var i=qi;if(i&&t){var r=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ge(r)+'"][href="'+Ge(t)+'"]',c=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Ki(t)}if(!Fe.has(c)&&(t=g({rel:"modulepreload",href:t},e),Fe.set(c,t),i.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(rr(c)))return}r=i.createElement("link"),fe(r,"link",t),ie(r),i.head.appendChild(r)}}}function DT(t,e,i){Hn.S(t,e,i);var r=qi;if(r&&t){var o=fi(r).hoistableStyles,c=Xi(t);e=e||"default";var y=o.get(c);if(!y){var b={loading:0,preload:null};if(y=r.querySelector(sr(c)))b.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},i),(i=Fe.get(c))&&Fc(t,i);var C=y=r.createElement("link");ie(C),fe(C,"link",t),C._p=new Promise(function(B,q){C.onload=B,C.onerror=q}),C.addEventListener("load",function(){b.loading|=1}),C.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Kl(y,e,r)}y={type:"stylesheet",instance:y,count:1,state:b},o.set(c,y)}}}function OT(t,e){Hn.X(t,e);var i=qi;if(i&&t){var r=fi(i).hoistableScripts,o=Ki(t),c=r.get(o);c||(c=i.querySelector(rr(o)),c||(t=g({src:t,async:!0},e),(e=Fe.get(o))&&Ic(t,e),c=i.createElement("script"),ie(c),fe(c,"link",t),i.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function NT(t,e){Hn.M(t,e);var i=qi;if(i&&t){var r=fi(i).hoistableScripts,o=Ki(t),c=r.get(o);c||(c=i.querySelector(rr(o)),c||(t=g({src:t,async:!0,type:"module"},e),(e=Fe.get(o))&&Ic(t,e),c=i.createElement("script"),ie(c),fe(c,"link",t),i.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Lg(t,e,i,r){var o=(o=lt.current)?Xl(o):null;if(!o)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=Xi(i.href),i=fi(o).hoistableStyles,r=i.get(e),r||(r={type:"style",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=Xi(i.href);var c=fi(o).hoistableStyles,y=c.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,y),(c=o.querySelector(sr(t)))&&!c._p&&(y.instance=c,y.state.loading=5),Fe.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Fe.set(t,i),c||_T(o,t,i,y.state))),e&&r===null)throw Error(l(528,""));return y}if(e&&r!==null)throw Error(l(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ki(i),i=fi(o).hoistableScripts,r=i.get(e),r||(r={type:"script",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function Xi(t){return'href="'+Ge(t)+'"'}function sr(t){return'link[rel="stylesheet"]['+t+"]"}function Bg(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function _T(t,e,i,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),fe(e,"link",i),ie(e),t.head.appendChild(e))}function Ki(t){return'[src="'+Ge(t)+'"]'}function rr(t){return"script[async]"+t}function Ug(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Ge(i.href)+'"]');if(r)return e.instance=r,ie(r),r;var o=g({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),ie(r),fe(r,"style",o),Kl(r,i.precedence,t),e.instance=r;case"stylesheet":o=Xi(i.href);var c=t.querySelector(sr(o));if(c)return e.state.loading|=4,e.instance=c,ie(c),c;r=Bg(i),(o=Fe.get(o))&&Fc(r,o),c=(t.ownerDocument||t).createElement("link"),ie(c);var y=c;return y._p=new Promise(function(b,C){y.onload=b,y.onerror=C}),fe(c,"link",r),e.state.loading|=4,Kl(c,i.precedence,t),e.instance=c;case"script":return c=Ki(i.src),(o=t.querySelector(rr(c)))?(e.instance=o,ie(o),o):(r=i,(o=Fe.get(c))&&(r=g({},i),Ic(r,o)),t=t.ownerDocument||t,o=t.createElement("script"),ie(o),fe(o,"link",r),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,Kl(r,i.precedence,t));return e.instance}function Kl(t,e,i){for(var r=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,c=o,y=0;y<r.length;y++){var b=r[y];if(b.dataset.precedence===e)c=b;else if(c!==o)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function Fc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Ic(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Zl=null;function kg(t,e,i){if(Zl===null){var r=new Map,o=Zl=new Map;o.set(i,r)}else o=Zl,r=o.get(i),r||(r=new Map,o.set(i,r));if(r.has(t))return r;for(r.set(t,null),i=i.getElementsByTagName(t),o=0;o<i.length;o++){var c=i[o];if(!(c[bs]||c[me]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(e)||"";y=t+y;var b=r.get(y);b?b.push(c):r.set(y,[c])}}return r}function Hg(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function jT(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Pg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var lr=null;function VT(){}function zT(t,e,i){if(lr===null)throw Error(l(475));var r=lr;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Xi(i.href),c=t.querySelector(sr(o));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=Ql.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=c,ie(c);return}c=t.ownerDocument||t,i=Bg(i),(o=Fe.get(o))&&Fc(i,o),c=c.createElement("link"),ie(c);var y=c;y._p=new Promise(function(b,C){y.onload=b,y.onerror=C}),fe(c,"link",i),e.instance=c}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=Ql.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function LT(){if(lr===null)throw Error(l(475));var t=lr;return t.stylesheets&&t.count===0&&Wc(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&Wc(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function Ql(){if(this.count--,this.count===0){if(this.stylesheets)Wc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Fl=null;function Wc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Fl=new Map,e.forEach(BT,t),Fl=null,Ql.call(t))}function BT(t,e){if(!(e.state.loading&4)){var i=Fl.get(t);if(i)var r=i.get(null);else{i=new Map,Fl.set(t,i);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var y=o[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(i.set(y.dataset.precedence,y),r=y)}r&&i.set(null,r)}o=e.instance,y=o.getAttribute("data-precedence"),c=i.get(y)||r,c===r&&i.set(null,o),i.set(y,o),this.count++,r=Ql.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),c?c.parentNode.insertBefore(o,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var or={$$typeof:D,Provider:null,Consumer:null,_currentValue:k,_currentValue2:k,_threadCount:0};function UT(t,e,i,r,o,c,y,b){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zo(0),this.hiddenUpdates=Zo(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Gg(t,e,i,r,o,c,y,b,C,B,q,Z){return t=new UT(t,e,i,y,b,C,B,Z),e=1,c===!0&&(e|=24),c=je(3,null,null,e),t.current=c,c.stateNode=t,e=_u(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:r,isDehydrated:i,cache:e},Lu(c),t}function Yg(t){return t?(t=wi,t):wi}function qg(t,e,i,r,o,c){o=Yg(o),r.context===null?r.context=o:r.pendingContext=o,r=$n(e),r.payload={element:i},c=c===void 0?null:c,c!==null&&(r.callback=c),i=Jn(t,r,e),i!==null&&(Ue(i,t,e),Us(i,t,e))}function Xg(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function $c(t,e){Xg(t,e),(t=t.alternate)&&Xg(t,e)}function Kg(t){if(t.tag===13){var e=Ti(t,67108864);e!==null&&Ue(e,t,67108864),$c(t,67108864)}}var Il=!0;function kT(t,e,i,r){var o=V.T;V.T=null;var c=Y.p;try{Y.p=2,Jc(t,e,i,r)}finally{Y.p=c,V.T=o}}function HT(t,e,i,r){var o=V.T;V.T=null;var c=Y.p;try{Y.p=8,Jc(t,e,i,r)}finally{Y.p=c,V.T=o}}function Jc(t,e,i,r){if(Il){var o=tf(r);if(o===null)Hc(t,e,r,Wl,i),Qg(t,r);else if(GT(o,t,e,i,r))r.stopPropagation();else if(Qg(t,r),e&4&&-1<PT.indexOf(t)){for(;o!==null;){var c=ci(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=_a(c.pendingLanes);if(y!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;y;){var C=1<<31-Ne(y);b.entanglements[1]|=C,y&=~C}fn(c),(_t&6)===0&&(Vl=he()+500,er(0))}}break;case 13:b=Ti(c,2),b!==null&&Ue(b,c,2),Ll(),$c(c,2)}if(c=tf(r),c===null&&Hc(t,e,r,Wl,i),c===o)break;o=c}o!==null&&r.stopPropagation()}else Hc(t,e,r,null,i)}}function tf(t){return t=su(t),ef(t)}var Wl=null;function ef(t){if(Wl=null,t=ui(t),t!==null){var e=d(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=f(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Wl=t,null}function Zg(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ko()){case Na:return 2;case oh:return 8;case Yr:case CS:return 32;case uh:return 268435456;default:return 32}default:return 32}}var nf=!1,da=null,ha=null,ma=null,ur=new Map,cr=new Map,pa=[],PT="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Qg(t,e){switch(t){case"focusin":case"focusout":da=null;break;case"dragenter":case"dragleave":ha=null;break;case"mouseover":case"mouseout":ma=null;break;case"pointerover":case"pointerout":ur.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":cr.delete(e.pointerId)}}function fr(t,e,i,r,o,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:i,eventSystemFlags:r,nativeEvent:c,targetContainers:[o]},e!==null&&(e=ci(e),e!==null&&Kg(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function GT(t,e,i,r,o){switch(e){case"focusin":return da=fr(da,t,e,i,r,o),!0;case"dragenter":return ha=fr(ha,t,e,i,r,o),!0;case"mouseover":return ma=fr(ma,t,e,i,r,o),!0;case"pointerover":var c=o.pointerId;return ur.set(c,fr(ur.get(c)||null,t,e,i,r,o)),!0;case"gotpointercapture":return c=o.pointerId,cr.set(c,fr(cr.get(c)||null,t,e,i,r,o)),!0}return!1}function Fg(t){var e=ui(t.target);if(e!==null){var i=d(e);if(i!==null){if(e=i.tag,e===13){if(e=f(i),e!==null){t.blockedOn=e,zS(t.priority,function(){if(i.tag===13){var r=Be();r=Qo(r);var o=Ti(i,r);o!==null&&Ue(o,i,r),$c(i,r)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function $l(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=tf(t.nativeEvent);if(i===null){i=t.nativeEvent;var r=new i.constructor(i.type,i);iu=r,i.target.dispatchEvent(r),iu=null}else return e=ci(i),e!==null&&Kg(e),t.blockedOn=i,!1;e.shift()}return!0}function Ig(t,e,i){$l(t)&&i.delete(e)}function YT(){nf=!1,da!==null&&$l(da)&&(da=null),ha!==null&&$l(ha)&&(ha=null),ma!==null&&$l(ma)&&(ma=null),ur.forEach(Ig),cr.forEach(Ig)}function Jl(t,e){t.blockedOn===e&&(t.blockedOn=null,nf||(nf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,YT)))}var to=null;function Wg(t){to!==t&&(to=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){to===t&&(to=null);for(var e=0;e<t.length;e+=3){var i=t[e],r=t[e+1],o=t[e+2];if(typeof r!="function"){if(ef(r||i)===null)continue;break}var c=ci(i);c!==null&&(t.splice(e,3),e-=3,ec(c,{pending:!0,data:o,method:i.method,action:r},r,o))}}))}function dr(t){function e(C){return Jl(C,t)}da!==null&&Jl(da,t),ha!==null&&Jl(ha,t),ma!==null&&Jl(ma,t),ur.forEach(e),cr.forEach(e);for(var i=0;i<pa.length;i++){var r=pa[i];r.blockedOn===t&&(r.blockedOn=null)}for(;0<pa.length&&(i=pa[0],i.blockedOn===null);)Fg(i),i.blockedOn===null&&pa.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(r=0;r<i.length;r+=3){var o=i[r],c=i[r+1],y=o[Ae]||null;if(typeof c=="function")y||Wg(i);else if(y){var b=null;if(c&&c.hasAttribute("formAction")){if(o=c,y=c[Ae]||null)b=y.formAction;else if(ef(o)!==null)continue}else b=y.action;typeof b=="function"?i[r+1]=b:(i.splice(r,3),r-=3),Wg(i)}}}function af(t){this._internalRoot=t}eo.prototype.render=af.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var i=e.current,r=Be();qg(i,r,t,e,null,null)},eo.prototype.unmount=af.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;qg(t.current,2,null,t,null,null),Ll(),e[oi]=null}};function eo(t){this._internalRoot=t}eo.prototype.unstable_scheduleHydration=function(t){if(t){var e=mh();t={blockedOn:null,target:t,priority:e};for(var i=0;i<pa.length&&e!==0&&e<pa[i].priority;i++);pa.splice(i,0,t),i===0&&Fg(t)}};var $g=a.version;if($g!=="19.1.0")throw Error(l(527,$g,"19.1.0"));Y.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=p(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var qT={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var no=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!no.isDisabled&&no.supportsFiber)try{gs=no.inject(qT),Oe=no}catch{}}return mr.createRoot=function(t,e){if(!u(t))throw Error(l(299));var i=!1,r="",o=mp,c=pp,y=gp,b=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(b=e.unstable_transitionCallbacks)),e=Gg(t,1,!1,null,null,i,r,o,c,y,b,null),t[oi]=e.current,kc(t),new af(e)},mr.hydrateRoot=function(t,e,i){if(!u(t))throw Error(l(299));var r=!1,o="",c=mp,y=pp,b=gp,C=null,B=null;return i!=null&&(i.unstable_strictMode===!0&&(r=!0),i.identifierPrefix!==void 0&&(o=i.identifierPrefix),i.onUncaughtError!==void 0&&(c=i.onUncaughtError),i.onCaughtError!==void 0&&(y=i.onCaughtError),i.onRecoverableError!==void 0&&(b=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(C=i.unstable_transitionCallbacks),i.formState!==void 0&&(B=i.formState)),e=Gg(t,1,!0,e,i??null,r,o,c,y,b,C,B),e.context=Yg(null),i=e.current,r=Be(),r=Qo(r),o=$n(r),o.callback=null,Jn(i,o,r),i=r,e.current.lanes=i,vs(e,i),fn(e),t[oi]=e.current,kc(t),new eo(e)},mr.version="19.1.0",mr}var oy;function tw(){if(oy)return lf.exports;oy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),lf.exports=JT(),lf.exports}var ew=tw();const r0=T.createContext({});function nw(n){const a=T.useRef(null);return a.current===null&&(a.current=n()),a.current}const yd=typeof window<"u",aw=yd?T.useLayoutEffect:T.useEffect,vd=T.createContext(null);function bd(n,a){n.indexOf(a)===-1&&n.push(a)}function xd(n,a){const s=n.indexOf(a);s>-1&&n.splice(s,1)}const Yn=(n,a,s)=>s>a?a:s<n?n:s;let Sd=()=>{};const qn={},l0=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function o0(n){return typeof n=="object"&&n!==null}const u0=n=>/^0[^.\s]+$/u.test(n);function Td(n){let a;return()=>(a===void 0&&(a=n()),a)}const $e=n=>n,iw=(n,a)=>s=>a(n(s)),Vr=(...n)=>n.reduce(iw),Ar=(n,a,s)=>{const l=a-n;return l===0?1:(s-n)/l};class wd{constructor(){this.subscriptions=[]}add(a){return bd(this.subscriptions,a),()=>xd(this.subscriptions,a)}notify(a,s,l){const u=this.subscriptions.length;if(u)if(u===1)this.subscriptions[0](a,s,l);else for(let d=0;d<u;d++){const f=this.subscriptions[d];f&&f(a,s,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const xn=n=>n*1e3,Sn=n=>n/1e3;function c0(n,a){return a?n*(1e3/a):0}const f0=(n,a,s)=>(((1-3*s+3*a)*n+(3*s-6*a))*n+3*a)*n,sw=1e-7,rw=12;function lw(n,a,s,l,u){let d,f,h=0;do f=a+(s-a)/2,d=f0(f,l,u)-n,d>0?s=f:a=f;while(Math.abs(d)>sw&&++h<rw);return f}function zr(n,a,s,l){if(n===a&&s===l)return $e;const u=d=>lw(d,0,1,n,s);return d=>d===0||d===1?d:f0(u(d),a,l)}const d0=n=>a=>a<=.5?n(2*a)/2:(2-n(2*(1-a)))/2,h0=n=>a=>1-n(1-a),m0=zr(.33,1.53,.69,.99),Ad=h0(m0),p0=d0(Ad),g0=n=>(n*=2)<1?.5*Ad(n):.5*(2-Math.pow(2,-10*(n-1))),Ed=n=>1-Math.sin(Math.acos(n)),y0=h0(Ed),v0=d0(Ed),ow=zr(.42,0,1,1),uw=zr(0,0,.58,1),b0=zr(.42,0,.58,1),cw=n=>Array.isArray(n)&&typeof n[0]!="number",x0=n=>Array.isArray(n)&&typeof n[0]=="number",fw={linear:$e,easeIn:ow,easeInOut:b0,easeOut:uw,circIn:Ed,circInOut:v0,circOut:y0,backIn:Ad,backInOut:p0,backOut:m0,anticipate:g0},dw=n=>typeof n=="string",uy=n=>{if(x0(n)){Sd(n.length===4);const[a,s,l,u]=n;return zr(a,s,l,u)}else if(dw(n))return fw[n];return n},ao=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],cy={value:null};function hw(n,a){let s=new Set,l=new Set,u=!1,d=!1;const f=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},p=0;function m(v){f.has(v)&&(g.schedule(v),n()),p++,v(h)}const g={schedule:(v,x=!1,A=!1)=>{const w=A&&u?s:l;return x&&f.add(v),w.has(v)||w.add(v),v},cancel:v=>{l.delete(v),f.delete(v)},process:v=>{if(h=v,u){d=!0;return}u=!0,[s,l]=[l,s],s.forEach(m),a&&cy.value&&cy.value.frameloop[a].push(p),p=0,s.clear(),u=!1,d&&(d=!1,g.process(v))}};return g}const mw=40;function S0(n,a){let s=!1,l=!0;const u={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=ao.reduce((D,H)=>(D[H]=hw(d,a?H:void 0),D),{}),{setup:h,read:p,resolveKeyframes:m,preUpdate:g,update:v,preRender:x,render:A,postRender:O}=f,w=()=>{const D=qn.useManualTiming?u.timestamp:performance.now();s=!1,qn.useManualTiming||(u.delta=l?1e3/60:Math.max(Math.min(D-u.timestamp,mw),1)),u.timestamp=D,u.isProcessing=!0,h.process(u),p.process(u),m.process(u),g.process(u),v.process(u),x.process(u),A.process(u),O.process(u),u.isProcessing=!1,s&&a&&(l=!1,n(w))},M=()=>{s=!0,l=!0,u.isProcessing||n(w)};return{schedule:ao.reduce((D,H)=>{const z=f[H];return D[H]=(F,Q=!1,G=!1)=>(s||M(),z.schedule(F,Q,G)),D},{}),cancel:D=>{for(let H=0;H<ao.length;H++)f[ao[H]].cancel(D)},state:u,steps:f}}const{schedule:Xt,cancel:Sa,state:de,steps:ff}=S0(typeof requestAnimationFrame<"u"?requestAnimationFrame:$e,!0);let yo;function pw(){yo=void 0}const De={now:()=>(yo===void 0&&De.set(de.isProcessing||qn.useManualTiming?de.timestamp:performance.now()),yo),set:n=>{yo=n,queueMicrotask(pw)}},T0=n=>a=>typeof a=="string"&&a.startsWith(n),Md=T0("--"),gw=T0("var(--"),Cd=n=>gw(n)?yw.test(n.split("/*")[0].trim()):!1,yw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,os={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Er={...os,transform:n=>Yn(0,1,n)},io={...os,default:1},br=n=>Math.round(n*1e5)/1e5,Rd=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function vw(n){return n==null}const bw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Dd=(n,a)=>s=>!!(typeof s=="string"&&bw.test(s)&&s.startsWith(n)||a&&!vw(s)&&Object.prototype.hasOwnProperty.call(s,a)),w0=(n,a,s)=>l=>{if(typeof l!="string")return l;const[u,d,f,h]=l.match(Rd);return{[n]:parseFloat(u),[a]:parseFloat(d),[s]:parseFloat(f),alpha:h!==void 0?parseFloat(h):1}},xw=n=>Yn(0,255,n),df={...os,transform:n=>Math.round(xw(n))},ei={test:Dd("rgb","red"),parse:w0("red","green","blue"),transform:({red:n,green:a,blue:s,alpha:l=1})=>"rgba("+df.transform(n)+", "+df.transform(a)+", "+df.transform(s)+", "+br(Er.transform(l))+")"};function Sw(n){let a="",s="",l="",u="";return n.length>5?(a=n.substring(1,3),s=n.substring(3,5),l=n.substring(5,7),u=n.substring(7,9)):(a=n.substring(1,2),s=n.substring(2,3),l=n.substring(3,4),u=n.substring(4,5),a+=a,s+=s,l+=l,u+=u),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(l,16),alpha:u?parseInt(u,16)/255:1}}const Lf={test:Dd("#"),parse:Sw,transform:ei.transform},Lr=n=>({test:a=>typeof a=="string"&&a.endsWith(n)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${n}`}),ba=Lr("deg"),Tn=Lr("%"),pt=Lr("px"),Tw=Lr("vh"),ww=Lr("vw"),fy={...Tn,parse:n=>Tn.parse(n)/100,transform:n=>Tn.transform(n*100)},$i={test:Dd("hsl","hue"),parse:w0("hue","saturation","lightness"),transform:({hue:n,saturation:a,lightness:s,alpha:l=1})=>"hsla("+Math.round(n)+", "+Tn.transform(br(a))+", "+Tn.transform(br(s))+", "+br(Er.transform(l))+")"},ye={test:n=>ei.test(n)||Lf.test(n)||$i.test(n),parse:n=>ei.test(n)?ei.parse(n):$i.test(n)?$i.parse(n):Lf.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?ei.transform(n):$i.transform(n)},Aw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Ew(n){var a,s;return isNaN(n)&&typeof n=="string"&&(((a=n.match(Rd))==null?void 0:a.length)||0)+(((s=n.match(Aw))==null?void 0:s.length)||0)>0}const A0="number",E0="color",Mw="var",Cw="var(",dy="${}",Rw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Mr(n){const a=n.toString(),s=[],l={color:[],number:[],var:[]},u=[];let d=0;const h=a.replace(Rw,p=>(ye.test(p)?(l.color.push(d),u.push(E0),s.push(ye.parse(p))):p.startsWith(Cw)?(l.var.push(d),u.push(Mw),s.push(p)):(l.number.push(d),u.push(A0),s.push(parseFloat(p))),++d,dy)).split(dy);return{values:s,split:h,indexes:l,types:u}}function M0(n){return Mr(n).values}function C0(n){const{split:a,types:s}=Mr(n),l=a.length;return u=>{let d="";for(let f=0;f<l;f++)if(d+=a[f],u[f]!==void 0){const h=s[f];h===A0?d+=br(u[f]):h===E0?d+=ye.transform(u[f]):d+=u[f]}return d}}const Dw=n=>typeof n=="number"?0:n;function Ow(n){const a=M0(n);return C0(n)(a.map(Dw))}const Ta={test:Ew,parse:M0,createTransformer:C0,getAnimatableNone:Ow};function hf(n,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(a-n)*6*s:s<1/2?a:s<2/3?n+(a-n)*(2/3-s)*6:n}function Nw({hue:n,saturation:a,lightness:s,alpha:l}){n/=360,a/=100,s/=100;let u=0,d=0,f=0;if(!a)u=d=f=s;else{const h=s<.5?s*(1+a):s+a-s*a,p=2*s-h;u=hf(p,h,n+1/3),d=hf(p,h,n),f=hf(p,h,n-1/3)}return{red:Math.round(u*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:l}}function Ao(n,a){return s=>s>0?a:n}const qt=(n,a,s)=>n+(a-n)*s,mf=(n,a,s)=>{const l=n*n,u=s*(a*a-l)+l;return u<0?0:Math.sqrt(u)},_w=[Lf,ei,$i],jw=n=>_w.find(a=>a.test(n));function hy(n){const a=jw(n);if(!a)return!1;let s=a.parse(n);return a===$i&&(s=Nw(s)),s}const my=(n,a)=>{const s=hy(n),l=hy(a);if(!s||!l)return Ao(n,a);const u={...s};return d=>(u.red=mf(s.red,l.red,d),u.green=mf(s.green,l.green,d),u.blue=mf(s.blue,l.blue,d),u.alpha=qt(s.alpha,l.alpha,d),ei.transform(u))},Bf=new Set(["none","hidden"]);function Vw(n,a){return Bf.has(n)?s=>s<=0?n:a:s=>s>=1?a:n}function zw(n,a){return s=>qt(n,a,s)}function Od(n){return typeof n=="number"?zw:typeof n=="string"?Cd(n)?Ao:ye.test(n)?my:Uw:Array.isArray(n)?R0:typeof n=="object"?ye.test(n)?my:Lw:Ao}function R0(n,a){const s=[...n],l=s.length,u=n.map((d,f)=>Od(d)(d,a[f]));return d=>{for(let f=0;f<l;f++)s[f]=u[f](d);return s}}function Lw(n,a){const s={...n,...a},l={};for(const u in s)n[u]!==void 0&&a[u]!==void 0&&(l[u]=Od(n[u])(n[u],a[u]));return u=>{for(const d in l)s[d]=l[d](u);return s}}function Bw(n,a){const s=[],l={color:0,var:0,number:0};for(let u=0;u<a.values.length;u++){const d=a.types[u],f=n.indexes[d][l[d]],h=n.values[f]??0;s[u]=h,l[d]++}return s}const Uw=(n,a)=>{const s=Ta.createTransformer(a),l=Mr(n),u=Mr(a);return l.indexes.var.length===u.indexes.var.length&&l.indexes.color.length===u.indexes.color.length&&l.indexes.number.length>=u.indexes.number.length?Bf.has(n)&&!u.values.length||Bf.has(a)&&!l.values.length?Vw(n,a):Vr(R0(Bw(l,u),u.values),s):Ao(n,a)};function D0(n,a,s){return typeof n=="number"&&typeof a=="number"&&typeof s=="number"?qt(n,a,s):Od(n)(n,a)}const kw=n=>{const a=({timestamp:s})=>n(s);return{start:(s=!0)=>Xt.update(a,s),stop:()=>Sa(a),now:()=>de.isProcessing?de.timestamp:De.now()}},O0=(n,a,s=10)=>{let l="";const u=Math.max(Math.round(a/s),2);for(let d=0;d<u;d++)l+=n(d/(u-1))+", ";return`linear(${l.substring(0,l.length-2)})`},Eo=2e4;function Nd(n){let a=0;const s=50;let l=n.next(a);for(;!l.done&&a<Eo;)a+=s,l=n.next(a);return a>=Eo?1/0:a}function Hw(n,a=100,s){const l=s({...n,keyframes:[0,a]}),u=Math.min(Nd(l),Eo);return{type:"keyframes",ease:d=>l.next(u*d).value/a,duration:Sn(u)}}const Pw=5;function N0(n,a,s){const l=Math.max(a-Pw,0);return c0(s-n(l),a-l)}const Qt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},py=.001;function Gw({duration:n=Qt.duration,bounce:a=Qt.bounce,velocity:s=Qt.velocity,mass:l=Qt.mass}){let u,d,f=1-a;f=Yn(Qt.minDamping,Qt.maxDamping,f),n=Yn(Qt.minDuration,Qt.maxDuration,Sn(n)),f<1?(u=m=>{const g=m*f,v=g*n,x=g-s,A=Uf(m,f),O=Math.exp(-v);return py-x/A*O},d=m=>{const v=m*f*n,x=v*s+s,A=Math.pow(f,2)*Math.pow(m,2)*n,O=Math.exp(-v),w=Uf(Math.pow(m,2),f);return(-u(m)+py>0?-1:1)*((x-A)*O)/w}):(u=m=>{const g=Math.exp(-m*n),v=(m-s)*n+1;return-.001+g*v},d=m=>{const g=Math.exp(-m*n),v=(s-m)*(n*n);return g*v});const h=5/n,p=qw(u,d,h);if(n=xn(n),isNaN(p))return{stiffness:Qt.stiffness,damping:Qt.damping,duration:n};{const m=Math.pow(p,2)*l;return{stiffness:m,damping:f*2*Math.sqrt(l*m),duration:n}}}const Yw=12;function qw(n,a,s){let l=s;for(let u=1;u<Yw;u++)l=l-n(l)/a(l);return l}function Uf(n,a){return n*Math.sqrt(1-a*a)}const Xw=["duration","bounce"],Kw=["stiffness","damping","mass"];function gy(n,a){return a.some(s=>n[s]!==void 0)}function Zw(n){let a={velocity:Qt.velocity,stiffness:Qt.stiffness,damping:Qt.damping,mass:Qt.mass,isResolvedFromDuration:!1,...n};if(!gy(n,Kw)&&gy(n,Xw))if(n.visualDuration){const s=n.visualDuration,l=2*Math.PI/(s*1.2),u=l*l,d=2*Yn(.05,1,1-(n.bounce||0))*Math.sqrt(u);a={...a,mass:Qt.mass,stiffness:u,damping:d}}else{const s=Gw(n);a={...a,...s,mass:Qt.mass},a.isResolvedFromDuration=!0}return a}function Mo(n=Qt.visualDuration,a=Qt.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:a}:n;let{restSpeed:l,restDelta:u}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:p,damping:m,mass:g,duration:v,velocity:x,isResolvedFromDuration:A}=Zw({...s,velocity:-Sn(s.velocity||0)}),O=x||0,w=m/(2*Math.sqrt(p*g)),M=f-d,E=Sn(Math.sqrt(p/g)),_=Math.abs(M)<5;l||(l=_?Qt.restSpeed.granular:Qt.restSpeed.default),u||(u=_?Qt.restDelta.granular:Qt.restDelta.default);let D;if(w<1){const z=Uf(E,w);D=F=>{const Q=Math.exp(-w*E*F);return f-Q*((O+w*E*M)/z*Math.sin(z*F)+M*Math.cos(z*F))}}else if(w===1)D=z=>f-Math.exp(-E*z)*(M+(O+E*M)*z);else{const z=E*Math.sqrt(w*w-1);D=F=>{const Q=Math.exp(-w*E*F),G=Math.min(z*F,300);return f-Q*((O+w*E*M)*Math.sinh(G)+z*M*Math.cosh(G))/z}}const H={calculatedDuration:A&&v||null,next:z=>{const F=D(z);if(A)h.done=z>=v;else{let Q=z===0?O:0;w<1&&(Q=z===0?xn(O):N0(D,z,F));const G=Math.abs(Q)<=l,tt=Math.abs(f-F)<=u;h.done=G&&tt}return h.value=h.done?f:F,h},toString:()=>{const z=Math.min(Nd(H),Eo),F=O0(Q=>H.next(z*Q).value,z,30);return z+"ms "+F},toTransition:()=>{}};return H}Mo.applyToOptions=n=>{const a=Hw(n,100,Mo);return n.ease=a.ease,n.duration=xn(a.duration),n.type="keyframes",n};function kf({keyframes:n,velocity:a=0,power:s=.8,timeConstant:l=325,bounceDamping:u=10,bounceStiffness:d=500,modifyTarget:f,min:h,max:p,restDelta:m=.5,restSpeed:g}){const v=n[0],x={done:!1,value:v},A=G=>h!==void 0&&G<h||p!==void 0&&G>p,O=G=>h===void 0?p:p===void 0||Math.abs(h-G)<Math.abs(p-G)?h:p;let w=s*a;const M=v+w,E=f===void 0?M:f(M);E!==M&&(w=E-v);const _=G=>-w*Math.exp(-G/l),D=G=>E+_(G),H=G=>{const tt=_(G),rt=D(G);x.done=Math.abs(tt)<=m,x.value=x.done?E:rt};let z,F;const Q=G=>{A(x.value)&&(z=G,F=Mo({keyframes:[x.value,O(x.value)],velocity:N0(D,G,x.value),damping:u,stiffness:d,restDelta:m,restSpeed:g}))};return Q(0),{calculatedDuration:null,next:G=>{let tt=!1;return!F&&z===void 0&&(tt=!0,H(G),Q(G)),z!==void 0&&G>=z?F.next(G-z):(!tt&&H(G),x)}}}function Qw(n,a,s){const l=[],u=s||qn.mix||D0,d=n.length-1;for(let f=0;f<d;f++){let h=u(n[f],n[f+1]);if(a){const p=Array.isArray(a)?a[f]||$e:a;h=Vr(p,h)}l.push(h)}return l}function Fw(n,a,{clamp:s=!0,ease:l,mixer:u}={}){const d=n.length;if(Sd(d===a.length),d===1)return()=>a[0];if(d===2&&a[0]===a[1])return()=>a[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),a=[...a].reverse());const h=Qw(a,l,u),p=h.length,m=g=>{if(f&&g<n[0])return a[0];let v=0;if(p>1)for(;v<n.length-2&&!(g<n[v+1]);v++);const x=Ar(n[v],n[v+1],g);return h[v](x)};return s?g=>m(Yn(n[0],n[d-1],g)):m}function Iw(n,a){const s=n[n.length-1];for(let l=1;l<=a;l++){const u=Ar(0,a,l);n.push(qt(s,1,u))}}function Ww(n){const a=[0];return Iw(a,n.length-1),a}function $w(n,a){return n.map(s=>s*a)}function Jw(n,a){return n.map(()=>a||b0).splice(0,n.length-1)}function xr({duration:n=300,keyframes:a,times:s,ease:l="easeInOut"}){const u=cw(l)?l.map(uy):uy(l),d={done:!1,value:a[0]},f=$w(s&&s.length===a.length?s:Ww(a),n),h=Fw(f,a,{ease:Array.isArray(u)?u:Jw(a,u)});return{calculatedDuration:n,next:p=>(d.value=h(p),d.done=p>=n,d)}}const tA=n=>n!==null;function _d(n,{repeat:a,repeatType:s="loop"},l,u=1){const d=n.filter(tA),h=u<0||a&&s!=="loop"&&a%2===1?0:d.length-1;return!h||l===void 0?d[h]:l}const eA={decay:kf,inertia:kf,tween:xr,keyframes:xr,spring:Mo};function _0(n){typeof n.type=="string"&&(n.type=eA[n.type])}class jd{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,s){return this.finished.then(a,s)}}const nA=n=>n/100;class Vd extends jd{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var l,u;const{motionValue:s}=this.options;s&&s.updatedAt!==De.now()&&this.tick(De.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(u=(l=this.options).onStop)==null||u.call(l))},this.options=a,this.initAnimation(),this.play(),a.autoplay===!1&&this.pause()}initAnimation(){const{options:a}=this;_0(a);const{type:s=xr,repeat:l=0,repeatDelay:u=0,repeatType:d,velocity:f=0}=a;let{keyframes:h}=a;const p=s||xr;p!==xr&&typeof h[0]!="number"&&(this.mixKeyframes=Vr(nA,D0(h[0],h[1])),h=[0,100]);const m=p({...a,keyframes:h});d==="mirror"&&(this.mirroredGenerator=p({...a,keyframes:[...h].reverse(),velocity:-f})),m.calculatedDuration===null&&(m.calculatedDuration=Nd(m));const{calculatedDuration:g}=m;this.calculatedDuration=g,this.resolvedDuration=g+u,this.totalDuration=this.resolvedDuration*(l+1)-u,this.generator=m}updateTime(a){const s=Math.round(a-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(a,s=!1){const{generator:l,totalDuration:u,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:p}=this;if(this.startTime===null)return l.next(0);const{delay:m=0,keyframes:g,repeat:v,repeatType:x,repeatDelay:A,type:O,onUpdate:w,finalKeyframe:M}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-u/this.speed,this.startTime)),s?this.currentTime=a:this.updateTime(a);const E=this.currentTime-m*(this.playbackSpeed>=0?1:-1),_=this.playbackSpeed>=0?E<0:E>u;this.currentTime=Math.max(E,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let D=this.currentTime,H=l;if(v){const G=Math.min(this.currentTime,u)/h;let tt=Math.floor(G),rt=G%1;!rt&&G>=1&&(rt=1),rt===1&&tt--,tt=Math.min(tt,v+1),!!(tt%2)&&(x==="reverse"?(rt=1-rt,A&&(rt-=A/h)):x==="mirror"&&(H=f)),D=Yn(0,1,rt)*h}const z=_?{done:!1,value:g[0]}:H.next(D);d&&(z.value=d(z.value));let{done:F}=z;!_&&p!==null&&(F=this.playbackSpeed>=0?this.currentTime>=u:this.currentTime<=0);const Q=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&F);return Q&&O!==kf&&(z.value=_d(g,this.options,M,this.speed)),w&&w(z.value),Q&&this.finish(),z}then(a,s){return this.finished.then(a,s)}get duration(){return Sn(this.calculatedDuration)}get time(){return Sn(this.currentTime)}set time(a){var s;a=xn(a),this.currentTime=a,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(De.now());const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Sn(this.currentTime))}play(){var u,d;if(this.isStopped)return;const{driver:a=kw,startTime:s}=this.options;this.driver||(this.driver=a(f=>this.tick(f))),(d=(u=this.options).onPlay)==null||d.call(u);const l=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=l):this.holdTime!==null?this.startTime=l-this.holdTime:this.startTime||(this.startTime=s??l),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(De.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var a,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(a=this.options).onComplete)==null||s.call(a)}cancel(){var a,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(a=this.options).onCancel)==null||s.call(a)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),a.observe(this)}}function aA(n){for(let a=1;a<n.length;a++)n[a]??(n[a]=n[a-1])}const ni=n=>n*180/Math.PI,Hf=n=>{const a=ni(Math.atan2(n[1],n[0]));return Pf(a)},iA={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Hf,rotateZ:Hf,skewX:n=>ni(Math.atan(n[1])),skewY:n=>ni(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},Pf=n=>(n=n%360,n<0&&(n+=360),n),yy=Hf,vy=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),by=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),sA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:vy,scaleY:by,scale:n=>(vy(n)+by(n))/2,rotateX:n=>Pf(ni(Math.atan2(n[6],n[5]))),rotateY:n=>Pf(ni(Math.atan2(-n[2],n[0]))),rotateZ:yy,rotate:yy,skewX:n=>ni(Math.atan(n[4])),skewY:n=>ni(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function Gf(n){return n.includes("scale")?1:0}function Yf(n,a){if(!n||n==="none")return Gf(a);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let l,u;if(s)l=sA,u=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);l=iA,u=h}if(!u)return Gf(a);const d=l[a],f=u[1].split(",").map(lA);return typeof d=="function"?d(f):f[d]}const rA=(n,a)=>{const{transform:s="none"}=getComputedStyle(n);return Yf(s,a)};function lA(n){return parseFloat(n.trim())}const us=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],cs=new Set(us),xy=n=>n===os||n===pt,oA=new Set(["x","y","z"]),uA=us.filter(n=>!oA.has(n));function cA(n){const a=[];return uA.forEach(s=>{const l=n.getValue(s);l!==void 0&&(a.push([s,l.get()]),l.set(s.startsWith("scale")?1:0))}),a}const ai={width:({x:n},{paddingLeft:a="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),height:({y:n},{paddingTop:a="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),top:(n,{top:a})=>parseFloat(a),left:(n,{left:a})=>parseFloat(a),bottom:({y:n},{top:a})=>parseFloat(a)+(n.max-n.min),right:({x:n},{left:a})=>parseFloat(a)+(n.max-n.min),x:(n,{transform:a})=>Yf(a,"x"),y:(n,{transform:a})=>Yf(a,"y")};ai.translateX=ai.x;ai.translateY=ai.y;const ii=new Set;let qf=!1,Xf=!1,Kf=!1;function j0(){if(Xf){const n=Array.from(ii).filter(l=>l.needsMeasurement),a=new Set(n.map(l=>l.element)),s=new Map;a.forEach(l=>{const u=cA(l);u.length&&(s.set(l,u),l.render())}),n.forEach(l=>l.measureInitialState()),a.forEach(l=>{l.render();const u=s.get(l);u&&u.forEach(([d,f])=>{var h;(h=l.getValue(d))==null||h.set(f)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}Xf=!1,qf=!1,ii.forEach(n=>n.complete(Kf)),ii.clear()}function V0(){ii.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Xf=!0)})}function fA(){Kf=!0,V0(),j0(),Kf=!1}class zd{constructor(a,s,l,u,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=l,this.motionValue=u,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(ii.add(this),qf||(qf=!0,Xt.read(V0),Xt.resolveKeyframes(j0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:l,motionValue:u}=this;if(a[0]===null){const d=u==null?void 0:u.get(),f=a[a.length-1];if(d!==void 0)a[0]=d;else if(l&&s){const h=l.readValue(s,f);h!=null&&(a[0]=h)}a[0]===void 0&&(a[0]=f),u&&d===void 0&&u.set(a[0])}aA(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),ii.delete(this)}cancel(){this.state==="scheduled"&&(ii.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const dA=n=>n.startsWith("--");function hA(n,a,s){dA(a)?n.style.setProperty(a,s):n.style[a]=s}const mA=Td(()=>window.ScrollTimeline!==void 0),pA={};function gA(n,a){const s=Td(n);return()=>pA[a]??s()}const z0=gA(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),yr=([n,a,s,l])=>`cubic-bezier(${n}, ${a}, ${s}, ${l})`,Sy={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yr([0,.65,.55,1]),circOut:yr([.55,0,1,.45]),backIn:yr([.31,.01,.66,-.59]),backOut:yr([.33,1.53,.69,.99])};function L0(n,a){if(n)return typeof n=="function"?z0()?O0(n,a):"ease-out":x0(n)?yr(n):Array.isArray(n)?n.map(s=>L0(s,a)||Sy.easeOut):Sy[n]}function yA(n,a,s,{delay:l=0,duration:u=300,repeat:d=0,repeatType:f="loop",ease:h="easeOut",times:p}={},m=void 0){const g={[a]:s};p&&(g.offset=p);const v=L0(h,u);Array.isArray(v)&&(g.easing=v);const x={delay:l,duration:u,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return m&&(x.pseudoElement=m),n.animate(g,x)}function B0(n){return typeof n=="function"&&"applyToOptions"in n}function vA({type:n,...a}){return B0(n)&&z0()?n.applyToOptions(a):(a.duration??(a.duration=300),a.ease??(a.ease="easeOut"),a)}class bA extends jd{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;const{element:s,name:l,keyframes:u,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:h,onComplete:p}=a;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=a,Sd(typeof a.type!="string");const m=vA(a);this.animation=yA(s,l,u,m,d),m.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const g=_d(u,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(g):hA(s,l,g),this.animation.cancel()}p==null||p(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var a,s;(s=(a=this.animation).finish)==null||s.call(a)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:a}=this;a==="idle"||a==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var a,s;this.isPseudoElement||(s=(a=this.animation).commitStyles)==null||s.call(a)}get duration(){var s,l;const a=((l=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:l.call(s).duration)||0;return Sn(Number(a))}get time(){return Sn(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=xn(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:s}){var l;return this.allowFlatten&&((l=this.animation.effect)==null||l.updateTiming({easing:"linear"})),this.animation.onfinish=null,a&&mA()?(this.animation.timeline=a,$e):s(this)}}const U0={anticipate:g0,backInOut:p0,circInOut:v0};function xA(n){return n in U0}function SA(n){typeof n.ease=="string"&&xA(n.ease)&&(n.ease=U0[n.ease])}const Ty=10;class TA extends bA{constructor(a){SA(a),_0(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){const{motionValue:s,onUpdate:l,onComplete:u,element:d,...f}=this.options;if(!s)return;if(a!==void 0){s.set(a);return}const h=new Vd({...f,autoplay:!1}),p=xn(this.finishedTime??this.time);s.setWithVelocity(h.sample(p-Ty).value,h.sample(p).value,Ty),h.stop()}}const wy=(n,a)=>a==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Ta.test(n)||n==="0")&&!n.startsWith("url("));function wA(n){const a=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==a)return!0}function AA(n,a,s,l){const u=n[0];if(u===null)return!1;if(a==="display"||a==="visibility")return!0;const d=n[n.length-1],f=wy(u,a),h=wy(d,a);return!f||!h?!1:wA(n)||(s==="spring"||B0(s))&&l}function k0(n){return o0(n)&&"offsetHeight"in n}const EA=new Set(["opacity","clipPath","filter","transform"]),MA=Td(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function CA(n){var m;const{motionValue:a,name:s,repeatDelay:l,repeatType:u,damping:d,type:f}=n;if(!k0((m=a==null?void 0:a.owner)==null?void 0:m.current))return!1;const{onUpdate:h,transformTemplate:p}=a.owner.getProps();return MA()&&s&&EA.has(s)&&(s!=="transform"||!p)&&!h&&!l&&u!=="mirror"&&d!==0&&f!=="inertia"}const RA=40;class DA extends jd{constructor({autoplay:a=!0,delay:s=0,type:l="keyframes",repeat:u=0,repeatDelay:d=0,repeatType:f="loop",keyframes:h,name:p,motionValue:m,element:g,...v}){var O;super(),this.stop=()=>{var w,M;this._animation&&(this._animation.stop(),(w=this.stopTimeline)==null||w.call(this)),(M=this.keyframeResolver)==null||M.cancel()},this.createdAt=De.now();const x={autoplay:a,delay:s,type:l,repeat:u,repeatDelay:d,repeatType:f,name:p,motionValue:m,element:g,...v},A=(g==null?void 0:g.KeyframeResolver)||zd;this.keyframeResolver=new A(h,(w,M,E)=>this.onKeyframesResolved(w,M,x,!E),p,m,g),(O=this.keyframeResolver)==null||O.scheduleResolve()}onKeyframesResolved(a,s,l,u){this.keyframeResolver=void 0;const{name:d,type:f,velocity:h,delay:p,isHandoff:m,onUpdate:g}=l;this.resolvedAt=De.now(),AA(a,d,f,h)||((qn.instantAnimations||!p)&&(g==null||g(_d(a,l,s))),a[0]=a[a.length-1],l.duration=0,l.repeat=0);const x={startTime:u?this.resolvedAt?this.resolvedAt-this.createdAt>RA?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...l,keyframes:a},A=!m&&CA(x)?new TA({...x,element:x.motionValue.owner.current}):new Vd(x);A.finished.then(()=>this.notifyFinished()).catch($e),this.pendingTimeline&&(this.stopTimeline=A.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=A}get finished(){return this._animation?this.animation.finished:this._finished}then(a,s){return this.finished.finally(a).then(()=>{})}get animation(){var a;return this._animation||((a=this.keyframeResolver)==null||a.resume(),fA()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var a;this._animation&&this.animation.cancel(),(a=this.keyframeResolver)==null||a.cancel()}}const OA=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function NA(n){const a=OA.exec(n);if(!a)return[,];const[,s,l,u]=a;return[`--${s??l}`,u]}function H0(n,a,s=1){const[l,u]=NA(n);if(!l)return;const d=window.getComputedStyle(a).getPropertyValue(l);if(d){const f=d.trim();return l0(f)?parseFloat(f):f}return Cd(u)?H0(u,a,s+1):u}function Ld(n,a){return(n==null?void 0:n[a])??(n==null?void 0:n.default)??n}const P0=new Set(["width","height","top","left","right","bottom",...us]),_A={test:n=>n==="auto",parse:n=>n},G0=n=>a=>a.test(n),Y0=[os,pt,Tn,ba,ww,Tw,_A],Ay=n=>Y0.find(G0(n));function jA(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||u0(n):!0}const VA=new Set(["brightness","contrast","saturate","opacity"]);function zA(n){const[a,s]=n.slice(0,-1).split("(");if(a==="drop-shadow")return n;const[l]=s.match(Rd)||[];if(!l)return n;const u=s.replace(l,"");let d=VA.has(a)?1:0;return l!==s&&(d*=100),a+"("+d+u+")"}const LA=/\b([a-z-]*)\(.*?\)/gu,Zf={...Ta,getAnimatableNone:n=>{const a=n.match(LA);return a?a.map(zA).join(" "):n}},Ey={...os,transform:Math.round},BA={rotate:ba,rotateX:ba,rotateY:ba,rotateZ:ba,scale:io,scaleX:io,scaleY:io,scaleZ:io,skew:ba,skewX:ba,skewY:ba,distance:pt,translateX:pt,translateY:pt,translateZ:pt,x:pt,y:pt,z:pt,perspective:pt,transformPerspective:pt,opacity:Er,originX:fy,originY:fy,originZ:pt},Bd={borderWidth:pt,borderTopWidth:pt,borderRightWidth:pt,borderBottomWidth:pt,borderLeftWidth:pt,borderRadius:pt,radius:pt,borderTopLeftRadius:pt,borderTopRightRadius:pt,borderBottomRightRadius:pt,borderBottomLeftRadius:pt,width:pt,maxWidth:pt,height:pt,maxHeight:pt,top:pt,right:pt,bottom:pt,left:pt,padding:pt,paddingTop:pt,paddingRight:pt,paddingBottom:pt,paddingLeft:pt,margin:pt,marginTop:pt,marginRight:pt,marginBottom:pt,marginLeft:pt,backgroundPositionX:pt,backgroundPositionY:pt,...BA,zIndex:Ey,fillOpacity:Er,strokeOpacity:Er,numOctaves:Ey},UA={...Bd,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Zf,WebkitFilter:Zf},q0=n=>UA[n];function X0(n,a){let s=q0(n);return s!==Zf&&(s=Ta),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const kA=new Set(["auto","none","0"]);function HA(n,a,s){let l=0,u;for(;l<n.length&&!u;){const d=n[l];typeof d=="string"&&!kA.has(d)&&Mr(d).values.length&&(u=n[l]),l++}if(u&&s)for(const d of a)n[d]=X0(s,u)}class PA extends zd{constructor(a,s,l,u,d){super(a,s,l,u,d,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:l}=this;if(!s||!s.current)return;super.readKeyframes();for(let p=0;p<a.length;p++){let m=a[p];if(typeof m=="string"&&(m=m.trim(),Cd(m))){const g=H0(m,s.current);g!==void 0&&(a[p]=g),p===a.length-1&&(this.finalKeyframe=m)}}if(this.resolveNoneKeyframes(),!P0.has(l)||a.length!==2)return;const[u,d]=a,f=Ay(u),h=Ay(d);if(f!==h)if(xy(f)&&xy(h))for(let p=0;p<a.length;p++){const m=a[p];typeof m=="string"&&(a[p]=parseFloat(m))}else ai[l]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,l=[];for(let u=0;u<a.length;u++)(a[u]===null||jA(a[u]))&&l.push(u);l.length&&HA(a,l,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:l}=this;if(!a||!a.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ai[l](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const u=s[s.length-1];u!==void 0&&a.getValue(l,u).jump(u,!1)}measureEndState(){var h;const{element:a,name:s,unresolvedKeyframes:l}=this;if(!a||!a.current)return;const u=a.getValue(s);u&&u.jump(this.measuredOrigin,!1);const d=l.length-1,f=l[d];l[d]=ai[s](a.measureViewportBox(),window.getComputedStyle(a.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([p,m])=>{a.getValue(p).set(m)}),this.resolveNoneKeyframes()}}function GA(n,a,s){if(n instanceof EventTarget)return[n];if(typeof n=="string"){let l=document;const u=(s==null?void 0:s[n])??l.querySelectorAll(n);return u?Array.from(u):[]}return Array.from(n)}const K0=(n,a)=>a&&typeof n=="number"?a.transform(n):n,My=30,YA=n=>!isNaN(parseFloat(n));class qA{constructor(a,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,u=!0)=>{var f,h;const d=De.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const p of this.dependents)p.dirty();u&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=De.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=YA(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new wd);const l=this.events[a].add(s);return a==="change"?()=>{l(),Xt.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,l){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-l}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var a;(a=this.events.change)==null||a.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=De.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>My)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,My);return c0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var a,s;(a=this.dependents)==null||a.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ss(n,a){return new qA(n,a)}const{schedule:Ud}=S0(queueMicrotask,!1),nn={x:!1,y:!1};function Z0(){return nn.x||nn.y}function XA(n){return n==="x"||n==="y"?nn[n]?null:(nn[n]=!0,()=>{nn[n]=!1}):nn.x||nn.y?null:(nn.x=nn.y=!0,()=>{nn.x=nn.y=!1})}function Q0(n,a){const s=GA(n),l=new AbortController,u={passive:!0,...a,signal:l.signal};return[s,u,()=>l.abort()]}function Cy(n){return!(n.pointerType==="touch"||Z0())}function KA(n,a,s={}){const[l,u,d]=Q0(n,s),f=h=>{if(!Cy(h))return;const{target:p}=h,m=a(p,h);if(typeof m!="function"||!p)return;const g=v=>{Cy(v)&&(m(v),p.removeEventListener("pointerleave",g))};p.addEventListener("pointerleave",g,u)};return l.forEach(h=>{h.addEventListener("pointerenter",f,u)}),d}const F0=(n,a)=>a?n===a?!0:F0(n,a.parentElement):!1,kd=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,ZA=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function QA(n){return ZA.has(n.tagName)||n.tabIndex!==-1}const vo=new WeakSet;function Ry(n){return a=>{a.key==="Enter"&&n(a)}}function pf(n,a){n.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const FA=(n,a)=>{const s=n.currentTarget;if(!s)return;const l=Ry(()=>{if(vo.has(s))return;pf(s,"down");const u=Ry(()=>{pf(s,"up")}),d=()=>pf(s,"cancel");s.addEventListener("keyup",u,a),s.addEventListener("blur",d,a)});s.addEventListener("keydown",l,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",l),a)};function Dy(n){return kd(n)&&!Z0()}function IA(n,a,s={}){const[l,u,d]=Q0(n,s),f=h=>{const p=h.currentTarget;if(!Dy(h))return;vo.add(p);const m=a(p,h),g=(A,O)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",x),vo.has(p)&&vo.delete(p),Dy(A)&&typeof m=="function"&&m(A,{success:O})},v=A=>{g(A,p===window||p===document||s.useGlobalTarget||F0(p,A.target))},x=A=>{g(A,!1)};window.addEventListener("pointerup",v,u),window.addEventListener("pointercancel",x,u)};return l.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",f,u),k0(h)&&(h.addEventListener("focus",m=>FA(m,u)),!QA(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function I0(n){return o0(n)&&"ownerSVGElement"in n}function WA(n){return I0(n)&&n.tagName==="svg"}const ve=n=>!!(n&&n.getVelocity),$A=[...Y0,ye,Ta],JA=n=>$A.find(G0(n)),W0=T.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function tE(n=!0){const a=T.useContext(vd);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:l,register:u}=a,d=T.useId();T.useEffect(()=>{if(n)return u(d)},[n]);const f=T.useCallback(()=>n&&l&&l(d),[d,l,n]);return!s&&l?[!1,f]:[!0]}const $0=T.createContext({strict:!1}),Oy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rs={};for(const n in Oy)rs[n]={isEnabled:a=>Oy[n].some(s=>!!a[s])};function eE(n){for(const a in n)rs[a]={...rs[a],...n[a]}}const nE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Co(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||nE.has(n)}let J0=n=>!Co(n);function aE(n){n&&(J0=a=>a.startsWith("on")?!Co(a):n(a))}try{aE(require("@emotion/is-prop-valid").default)}catch{}function iE(n,a,s){const l={};for(const u in n)u==="values"&&typeof n.values=="object"||(J0(u)||s===!0&&Co(u)||!a&&!Co(u)||n.draggable&&u.startsWith("onDrag"))&&(l[u]=n[u]);return l}function sE(n){if(typeof Proxy>"u")return n;const a=new Map,s=(...l)=>n(...l);return new Proxy(s,{get:(l,u)=>u==="create"?n:(a.has(u)||a.set(u,n(u)),a.get(u))})}const zo=T.createContext({});function Lo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function Cr(n){return typeof n=="string"||Array.isArray(n)}const Hd=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Pd=["initial",...Hd];function Bo(n){return Lo(n.animate)||Pd.some(a=>Cr(n[a]))}function tb(n){return!!(Bo(n)||n.variants)}function rE(n,a){if(Bo(n)){const{initial:s,animate:l}=n;return{initial:s===!1||Cr(s)?s:void 0,animate:Cr(l)?l:void 0}}return n.inherit!==!1?a:{}}function lE(n){const{initial:a,animate:s}=rE(n,T.useContext(zo));return T.useMemo(()=>({initial:a,animate:s}),[Ny(a),Ny(s)])}function Ny(n){return Array.isArray(n)?n.join(" "):n}const oE=Symbol.for("motionComponentSymbol");function Ji(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function uE(n,a,s){return T.useCallback(l=>{l&&n.onMount&&n.onMount(l),a&&(l?a.mount(l):a.unmount()),s&&(typeof s=="function"?s(l):Ji(s)&&(s.current=l))},[a])}const Gd=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),cE="framerAppearId",eb="data-"+Gd(cE),nb=T.createContext({});function fE(n,a,s,l,u){var w,M;const{visualElement:d}=T.useContext(zo),f=T.useContext($0),h=T.useContext(vd),p=T.useContext(W0).reducedMotion,m=T.useRef(null);l=l||f.renderer,!m.current&&l&&(m.current=l(n,{visualState:a,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:p}));const g=m.current,v=T.useContext(nb);g&&!g.projection&&u&&(g.type==="html"||g.type==="svg")&&dE(m.current,s,u,v);const x=T.useRef(!1);T.useInsertionEffect(()=>{g&&x.current&&g.update(s,h)});const A=s[eb],O=T.useRef(!!A&&!((w=window.MotionHandoffIsComplete)!=null&&w.call(window,A))&&((M=window.MotionHasOptimisedAnimation)==null?void 0:M.call(window,A)));return aw(()=>{g&&(x.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),Ud.render(g.render),O.current&&g.animationState&&g.animationState.animateChanges())}),T.useEffect(()=>{g&&(!O.current&&g.animationState&&g.animationState.animateChanges(),O.current&&(queueMicrotask(()=>{var E;(E=window.MotionHandoffMarkAsComplete)==null||E.call(window,A)}),O.current=!1))}),g}function dE(n,a,s,l){const{layoutId:u,layout:d,drag:f,dragConstraints:h,layoutScroll:p,layoutRoot:m,layoutCrossfade:g}=a;n.projection=new s(n.latestValues,a["data-framer-portal-id"]?void 0:ab(n.parent)),n.projection.setOptions({layoutId:u,layout:d,alwaysMeasureLayout:!!f||h&&Ji(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,crossfade:g,layoutScroll:p,layoutRoot:m})}function ab(n){if(n)return n.options.allowProjection!==!1?n.projection:ab(n.parent)}function hE({preloadedFeatures:n,createVisualElement:a,useRender:s,useVisualState:l,Component:u}){n&&eE(n);function d(h,p){let m;const g={...T.useContext(W0),...h,layoutId:mE(h)},{isStatic:v}=g,x=lE(h),A=l(h,v);if(!v&&yd){pE();const O=gE(g);m=O.MeasureLayout,x.visualElement=fE(u,A,g,a,O.ProjectionNode)}return S.jsxs(zo.Provider,{value:x,children:[m&&x.visualElement?S.jsx(m,{visualElement:x.visualElement,...g}):null,s(u,h,uE(A,x.visualElement,p),A,v,x.visualElement)]})}d.displayName=`motion.${typeof u=="string"?u:`create(${u.displayName??u.name??""})`}`;const f=T.forwardRef(d);return f[oE]=u,f}function mE({layoutId:n}){const a=T.useContext(r0).id;return a&&n!==void 0?a+"-"+n:n}function pE(n,a){T.useContext($0).strict}function gE(n){const{drag:a,layout:s}=rs;if(!a&&!s)return{};const l={...a,...s};return{MeasureLayout:a!=null&&a.isEnabled(n)||s!=null&&s.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const Rr={};function yE(n){for(const a in n)Rr[a]=n[a],Md(a)&&(Rr[a].isCSSVariable=!0)}function ib(n,{layout:a,layoutId:s}){return cs.has(n)||n.startsWith("origin")||(a||s!==void 0)&&(!!Rr[n]||n==="opacity")}const vE={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bE=us.length;function xE(n,a,s){let l="",u=!0;for(let d=0;d<bE;d++){const f=us[d],h=n[f];if(h===void 0)continue;let p=!0;if(typeof h=="number"?p=h===(f.startsWith("scale")?1:0):p=parseFloat(h)===0,!p||s){const m=K0(h,Bd[f]);if(!p){u=!1;const g=vE[f]||f;l+=`${g}(${m}) `}s&&(a[f]=m)}}return l=l.trim(),s?l=s(a,u?"":l):u&&(l="none"),l}function Yd(n,a,s){const{style:l,vars:u,transformOrigin:d}=n;let f=!1,h=!1;for(const p in a){const m=a[p];if(cs.has(p)){f=!0;continue}else if(Md(p)){u[p]=m;continue}else{const g=K0(m,Bd[p]);p.startsWith("origin")?(h=!0,d[p]=g):l[p]=g}}if(a.transform||(f||s?l.transform=xE(a,n.transform,s):l.transform&&(l.transform="none")),h){const{originX:p="50%",originY:m="50%",originZ:g=0}=d;l.transformOrigin=`${p} ${m} ${g}`}}const qd=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sb(n,a,s){for(const l in a)!ve(a[l])&&!ib(l,s)&&(n[l]=a[l])}function SE({transformTemplate:n},a){return T.useMemo(()=>{const s=qd();return Yd(s,a,n),Object.assign({},s.vars,s.style)},[a])}function TE(n,a){const s=n.style||{},l={};return sb(l,s,n),Object.assign(l,SE(n,a)),l}function wE(n,a){const s={},l=TE(n,a);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=l,s}const AE={offset:"stroke-dashoffset",array:"stroke-dasharray"},EE={offset:"strokeDashoffset",array:"strokeDasharray"};function ME(n,a,s=1,l=0,u=!0){n.pathLength=1;const d=u?AE:EE;n[d.offset]=pt.transform(-l);const f=pt.transform(a),h=pt.transform(s);n[d.array]=`${f} ${h}`}function rb(n,{attrX:a,attrY:s,attrScale:l,pathLength:u,pathSpacing:d=1,pathOffset:f=0,...h},p,m,g){if(Yd(n,h,m),p){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:v,style:x}=n;v.transform&&(x.transform=v.transform,delete v.transform),(x.transform||v.transformOrigin)&&(x.transformOrigin=v.transformOrigin??"50% 50%",delete v.transformOrigin),x.transform&&(x.transformBox=(g==null?void 0:g.transformBox)??"fill-box",delete v.transformBox),a!==void 0&&(v.x=a),s!==void 0&&(v.y=s),l!==void 0&&(v.scale=l),u!==void 0&&ME(v,u,d,f,!1)}const lb=()=>({...qd(),attrs:{}}),ob=n=>typeof n=="string"&&n.toLowerCase()==="svg";function CE(n,a,s,l){const u=T.useMemo(()=>{const d=lb();return rb(d,a,ob(l),n.transformTemplate,n.style),{...d.attrs,style:{...d.style}}},[a]);if(n.style){const d={};sb(d,n.style,n),u.style={...d,...u.style}}return u}const RE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Xd(n){return typeof n!="string"||n.includes("-")?!1:!!(RE.indexOf(n)>-1||/[A-Z]/u.test(n))}function DE(n=!1){return(s,l,u,{latestValues:d},f)=>{const p=(Xd(s)?CE:wE)(l,d,f,s),m=iE(l,typeof s=="string",n),g=s!==T.Fragment?{...m,...p,ref:u}:{},{children:v}=l,x=T.useMemo(()=>ve(v)?v.get():v,[v]);return T.createElement(s,{...g,children:x})}}function _y(n){const a=[{},{}];return n==null||n.values.forEach((s,l)=>{a[0][l]=s.get(),a[1][l]=s.getVelocity()}),a}function Kd(n,a,s,l){if(typeof a=="function"){const[u,d]=_y(l);a=a(s!==void 0?s:n.custom,u,d)}if(typeof a=="string"&&(a=n.variants&&n.variants[a]),typeof a=="function"){const[u,d]=_y(l);a=a(s!==void 0?s:n.custom,u,d)}return a}function bo(n){return ve(n)?n.get():n}function OE({scrapeMotionValuesFromProps:n,createRenderState:a},s,l,u){return{latestValues:NE(s,l,u,n),renderState:a()}}const ub=n=>(a,s)=>{const l=T.useContext(zo),u=T.useContext(vd),d=()=>OE(n,a,l,u);return s?d():nw(d)};function NE(n,a,s,l){const u={},d=l(n,{});for(const x in d)u[x]=bo(d[x]);let{initial:f,animate:h}=n;const p=Bo(n),m=tb(n);a&&m&&!p&&n.inherit!==!1&&(f===void 0&&(f=a.initial),h===void 0&&(h=a.animate));let g=s?s.initial===!1:!1;g=g||f===!1;const v=g?h:f;if(v&&typeof v!="boolean"&&!Lo(v)){const x=Array.isArray(v)?v:[v];for(let A=0;A<x.length;A++){const O=Kd(n,x[A]);if(O){const{transitionEnd:w,transition:M,...E}=O;for(const _ in E){let D=E[_];if(Array.isArray(D)){const H=g?D.length-1:0;D=D[H]}D!==null&&(u[_]=D)}for(const _ in w)u[_]=w[_]}}}return u}function Zd(n,a,s){var d;const{style:l}=n,u={};for(const f in l)(ve(l[f])||a.style&&ve(a.style[f])||ib(f,n)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(u[f]=l[f]);return u}const _E={useVisualState:ub({scrapeMotionValuesFromProps:Zd,createRenderState:qd})};function cb(n,a,s){const l=Zd(n,a,s);for(const u in n)if(ve(n[u])||ve(a[u])){const d=us.indexOf(u)!==-1?"attr"+u.charAt(0).toUpperCase()+u.substring(1):u;l[d]=n[u]}return l}const jE={useVisualState:ub({scrapeMotionValuesFromProps:cb,createRenderState:lb})};function VE(n,a){return function(l,{forwardMotionProps:u}={forwardMotionProps:!1}){const f={...Xd(l)?jE:_E,preloadedFeatures:n,useRender:DE(u),createVisualElement:a,Component:l};return hE(f)}}function Dr(n,a,s){const l=n.getProps();return Kd(l,a,s!==void 0?s:l.custom,n)}const Qf=n=>Array.isArray(n);function zE(n,a,s){n.hasValue(a)?n.getValue(a).set(s):n.addValue(a,ss(s))}function LE(n){return Qf(n)?n[n.length-1]||0:n}function BE(n,a){const s=Dr(n,a);let{transitionEnd:l={},transition:u={},...d}=s||{};d={...d,...l};for(const f in d){const h=LE(d[f]);zE(n,f,h)}}function UE(n){return!!(ve(n)&&n.add)}function Ff(n,a){const s=n.getValue("willChange");if(UE(s))return s.add(a);if(!s&&qn.WillChange){const l=new qn.WillChange("auto");n.addValue("willChange",l),l.add(a)}}function fb(n){return n.props[eb]}const kE=n=>n!==null;function HE(n,{repeat:a,repeatType:s="loop"},l){const u=n.filter(kE),d=a&&s!=="loop"&&a%2===1?0:u.length-1;return u[d]}const PE={type:"spring",stiffness:500,damping:25,restSpeed:10},GE=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),YE={type:"keyframes",duration:.8},qE={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},XE=(n,{keyframes:a})=>a.length>2?YE:cs.has(n)?n.startsWith("scale")?GE(a[1]):PE:qE;function KE({when:n,delay:a,delayChildren:s,staggerChildren:l,staggerDirection:u,repeat:d,repeatType:f,repeatDelay:h,from:p,elapsed:m,...g}){return!!Object.keys(g).length}const Qd=(n,a,s,l={},u,d)=>f=>{const h=Ld(l,n)||{},p=h.delay||l.delay||0;let{elapsed:m=0}=l;m=m-xn(p);const g={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity(),...h,delay:-m,onUpdate:x=>{a.set(x),h.onUpdate&&h.onUpdate(x)},onComplete:()=>{f(),h.onComplete&&h.onComplete()},name:n,motionValue:a,element:d?void 0:u};KE(h)||Object.assign(g,XE(n,g)),g.duration&&(g.duration=xn(g.duration)),g.repeatDelay&&(g.repeatDelay=xn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let v=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(v=!0)),(qn.instantAnimations||qn.skipAnimations)&&(v=!0,g.duration=0,g.delay=0),g.allowFlatten=!h.type&&!h.ease,v&&!d&&a.get()!==void 0){const x=HE(g.keyframes,h);if(x!==void 0){Xt.update(()=>{g.onUpdate(x),g.onComplete()});return}}return h.isSync?new Vd(g):new DA(g)};function ZE({protectedKeys:n,needsAnimating:a},s){const l=n.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,l}function db(n,a,{delay:s=0,transitionOverride:l,type:u}={}){let{transition:d=n.getDefaultTransition(),transitionEnd:f,...h}=a;l&&(d=l);const p=[],m=u&&n.animationState&&n.animationState.getState()[u];for(const g in h){const v=n.getValue(g,n.latestValues[g]??null),x=h[g];if(x===void 0||m&&ZE(m,g))continue;const A={delay:s,...Ld(d||{},g)},O=v.get();if(O!==void 0&&!v.isAnimating&&!Array.isArray(x)&&x===O&&!A.velocity)continue;let w=!1;if(window.MotionHandoffAnimation){const E=fb(n);if(E){const _=window.MotionHandoffAnimation(E,g,Xt);_!==null&&(A.startTime=_,w=!0)}}Ff(n,g),v.start(Qd(g,v,x,n.shouldReduceMotion&&P0.has(g)?{type:!1}:A,n,w));const M=v.animation;M&&p.push(M)}return f&&Promise.all(p).then(()=>{Xt.update(()=>{f&&BE(n,f)})}),p}function If(n,a,s={}){var p;const l=Dr(n,a,s.type==="exit"?(p=n.presenceContext)==null?void 0:p.custom:void 0);let{transition:u=n.getDefaultTransition()||{}}=l||{};s.transitionOverride&&(u=s.transitionOverride);const d=l?()=>Promise.all(db(n,l,s)):()=>Promise.resolve(),f=n.variantChildren&&n.variantChildren.size?(m=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:x}=u;return QE(n,a,g+m,v,x,s)}:()=>Promise.resolve(),{when:h}=u;if(h){const[m,g]=h==="beforeChildren"?[d,f]:[f,d];return m().then(()=>g())}else return Promise.all([d(),f(s.delay)])}function QE(n,a,s=0,l=0,u=1,d){const f=[],h=(n.variantChildren.size-1)*l,p=u===1?(m=0)=>m*l:(m=0)=>h-m*l;return Array.from(n.variantChildren).sort(FE).forEach((m,g)=>{m.notify("AnimationStart",a),f.push(If(m,a,{...d,delay:s+p(g)}).then(()=>m.notify("AnimationComplete",a)))}),Promise.all(f)}function FE(n,a){return n.sortNodePosition(a)}function IE(n,a,s={}){n.notify("AnimationStart",a);let l;if(Array.isArray(a)){const u=a.map(d=>If(n,d,s));l=Promise.all(u)}else if(typeof a=="string")l=If(n,a,s);else{const u=typeof a=="function"?Dr(n,a,s.custom):a;l=Promise.all(db(n,u,s))}return l.then(()=>{n.notify("AnimationComplete",a)})}function hb(n,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==n.length)return!1;for(let l=0;l<s;l++)if(a[l]!==n[l])return!1;return!0}const WE=Pd.length;function mb(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?mb(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const a={};for(let s=0;s<WE;s++){const l=Pd[s],u=n.props[l];(Cr(u)||u===!1)&&(a[l]=u)}return a}const $E=[...Hd].reverse(),JE=Hd.length;function t2(n){return a=>Promise.all(a.map(({animation:s,options:l})=>IE(n,s,l)))}function e2(n){let a=t2(n),s=jy(),l=!0;const u=p=>(m,g)=>{var x;const v=Dr(n,g,p==="exit"?(x=n.presenceContext)==null?void 0:x.custom:void 0);if(v){const{transition:A,transitionEnd:O,...w}=v;m={...m,...w,...O}}return m};function d(p){a=p(n)}function f(p){const{props:m}=n,g=mb(n.parent)||{},v=[],x=new Set;let A={},O=1/0;for(let M=0;M<JE;M++){const E=$E[M],_=s[E],D=m[E]!==void 0?m[E]:g[E],H=Cr(D),z=E===p?_.isActive:null;z===!1&&(O=M);let F=D===g[E]&&D!==m[E]&&H;if(F&&l&&n.manuallyAnimateOnMount&&(F=!1),_.protectedKeys={...A},!_.isActive&&z===null||!D&&!_.prevProp||Lo(D)||typeof D=="boolean")continue;const Q=n2(_.prevProp,D);let G=Q||E===p&&_.isActive&&!F&&H||M>O&&H,tt=!1;const rt=Array.isArray(D)?D:[D];let ht=rt.reduce(u(E),{});z===!1&&(ht={});const{prevResolvedValues:ct={}}=_,yt={...ct,...ht},vt=Y=>{G=!0,x.has(Y)&&(tt=!0,x.delete(Y)),_.needsAnimating[Y]=!0;const k=n.getValue(Y);k&&(k.liveStyle=!1)};for(const Y in yt){const k=ht[Y],it=ct[Y];if(A.hasOwnProperty(Y))continue;let R=!1;Qf(k)&&Qf(it)?R=!hb(k,it):R=k!==it,R?k!=null?vt(Y):x.add(Y):k!==void 0&&x.has(Y)?vt(Y):_.protectedKeys[Y]=!0}_.prevProp=D,_.prevResolvedValues=ht,_.isActive&&(A={...A,...ht}),l&&n.blockInitialAnimation&&(G=!1),G&&(!(F&&Q)||tt)&&v.push(...rt.map(Y=>({animation:Y,options:{type:E}})))}if(x.size){const M={};if(typeof m.initial!="boolean"){const E=Dr(n,Array.isArray(m.initial)?m.initial[0]:m.initial);E&&E.transition&&(M.transition=E.transition)}x.forEach(E=>{const _=n.getBaseTarget(E),D=n.getValue(E);D&&(D.liveStyle=!0),M[E]=_??null}),v.push({animation:M})}let w=!!v.length;return l&&(m.initial===!1||m.initial===m.animate)&&!n.manuallyAnimateOnMount&&(w=!1),l=!1,w?a(v):Promise.resolve()}function h(p,m){var v;if(s[p].isActive===m)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(x=>{var A;return(A=x.animationState)==null?void 0:A.setActive(p,m)}),s[p].isActive=m;const g=f(p);for(const x in s)s[x].protectedKeys={};return g}return{animateChanges:f,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=jy(),l=!0}}}function n2(n,a){return typeof a=="string"?a!==n:Array.isArray(a)?!hb(a,n):!1}function Ia(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function jy(){return{animate:Ia(!0),whileInView:Ia(),whileHover:Ia(),whileTap:Ia(),whileDrag:Ia(),whileFocus:Ia(),exit:Ia()}}class Ma{constructor(a){this.isMounted=!1,this.node=a}update(){}}class a2 extends Ma{constructor(a){super(a),a.animationState||(a.animationState=e2(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();Lo(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)==null||a.call(this)}}let i2=0;class s2 extends Ma{constructor(){super(...arguments),this.id=i2++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===l)return;const u=this.node.animationState.setActive("exit",!a);s&&!a&&u.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const r2={animation:{Feature:a2},exit:{Feature:s2}};function Or(n,a,s,l={passive:!0}){return n.addEventListener(a,s,l),()=>n.removeEventListener(a,s)}function Br(n){return{point:{x:n.pageX,y:n.pageY}}}const l2=n=>a=>kd(a)&&n(a,Br(a));function Sr(n,a,s,l){return Or(n,a,l2(s),l)}function pb({top:n,left:a,right:s,bottom:l}){return{x:{min:a,max:s},y:{min:n,max:l}}}function o2({x:n,y:a}){return{top:a.min,right:n.max,bottom:a.max,left:n.min}}function u2(n,a){if(!a)return n;const s=a({x:n.left,y:n.top}),l=a({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:l.y,right:l.x}}const gb=1e-4,c2=1-gb,f2=1+gb,yb=.01,d2=0-yb,h2=0+yb;function Se(n){return n.max-n.min}function m2(n,a,s){return Math.abs(n-a)<=s}function Vy(n,a,s,l=.5){n.origin=l,n.originPoint=qt(a.min,a.max,n.origin),n.scale=Se(s)/Se(a),n.translate=qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=c2&&n.scale<=f2||isNaN(n.scale))&&(n.scale=1),(n.translate>=d2&&n.translate<=h2||isNaN(n.translate))&&(n.translate=0)}function Tr(n,a,s,l){Vy(n.x,a.x,s.x,l?l.originX:void 0),Vy(n.y,a.y,s.y,l?l.originY:void 0)}function zy(n,a,s){n.min=s.min+a.min,n.max=n.min+Se(a)}function p2(n,a,s){zy(n.x,a.x,s.x),zy(n.y,a.y,s.y)}function Ly(n,a,s){n.min=a.min-s.min,n.max=n.min+Se(a)}function wr(n,a,s){Ly(n.x,a.x,s.x),Ly(n.y,a.y,s.y)}const By=()=>({translate:0,scale:1,origin:0,originPoint:0}),ts=()=>({x:By(),y:By()}),Uy=()=>({min:0,max:0}),Wt=()=>({x:Uy(),y:Uy()});function We(n){return[n("x"),n("y")]}function gf(n){return n===void 0||n===1}function Wf({scale:n,scaleX:a,scaleY:s}){return!gf(n)||!gf(a)||!gf(s)}function ti(n){return Wf(n)||vb(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function vb(n){return ky(n.x)||ky(n.y)}function ky(n){return n&&n!=="0%"}function Ro(n,a,s){const l=n-s,u=a*l;return s+u}function Hy(n,a,s,l,u){return u!==void 0&&(n=Ro(n,u,l)),Ro(n,s,l)+a}function $f(n,a=0,s=1,l,u){n.min=Hy(n.min,a,s,l,u),n.max=Hy(n.max,a,s,l,u)}function bb(n,{x:a,y:s}){$f(n.x,a.translate,a.scale,a.originPoint),$f(n.y,s.translate,s.scale,s.originPoint)}const Py=.999999999999,Gy=1.0000000000001;function g2(n,a,s,l=!1){const u=s.length;if(!u)return;a.x=a.y=1;let d,f;for(let h=0;h<u;h++){d=s[h],f=d.projectionDelta;const{visualElement:p}=d.options;p&&p.props.style&&p.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ns(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(a.x*=f.x.scale,a.y*=f.y.scale,bb(n,f)),l&&ti(d.latestValues)&&ns(n,d.latestValues))}a.x<Gy&&a.x>Py&&(a.x=1),a.y<Gy&&a.y>Py&&(a.y=1)}function es(n,a){n.min=n.min+a,n.max=n.max+a}function Yy(n,a,s,l,u=.5){const d=qt(n.min,n.max,u);$f(n,a,s,d,l)}function ns(n,a){Yy(n.x,a.x,a.scaleX,a.scale,a.originX),Yy(n.y,a.y,a.scaleY,a.scale,a.originY)}function xb(n,a){return pb(u2(n.getBoundingClientRect(),a))}function y2(n,a,s){const l=xb(n,s),{scroll:u}=a;return u&&(es(l.x,u.offset.x),es(l.y,u.offset.y)),l}const Sb=({current:n})=>n?n.ownerDocument.defaultView:null,qy=(n,a)=>Math.abs(n-a);function v2(n,a){const s=qy(n.x,a.x),l=qy(n.y,a.y);return Math.sqrt(s**2+l**2)}class Tb{constructor(a,s,{transformPagePoint:l,contextWindow:u,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=vf(this.lastMoveEventInfo,this.history),x=this.startEvent!==null,A=v2(v.offset,{x:0,y:0})>=3;if(!x&&!A)return;const{point:O}=v,{timestamp:w}=de;this.history.push({...O,timestamp:w});const{onStart:M,onMove:E}=this.handlers;x||(M&&M(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),E&&E(this.lastMoveEvent,v)},this.handlePointerMove=(v,x)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=yf(x,this.transformPagePoint),Xt.update(this.updatePoint,!0)},this.handlePointerUp=(v,x)=>{this.end();const{onEnd:A,onSessionEnd:O,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const M=vf(v.type==="pointercancel"?this.lastMoveEventInfo:yf(x,this.transformPagePoint),this.history);this.startEvent&&A&&A(v,M),O&&O(v,M)},!kd(a))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=l,this.contextWindow=u||window;const f=Br(a),h=yf(f,this.transformPagePoint),{point:p}=h,{timestamp:m}=de;this.history=[{...p,timestamp:m}];const{onSessionStart:g}=s;g&&g(a,vf(h,this.history)),this.removeListeners=Vr(Sr(this.contextWindow,"pointermove",this.handlePointerMove),Sr(this.contextWindow,"pointerup",this.handlePointerUp),Sr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Sa(this.updatePoint)}}function yf(n,a){return a?{point:a(n.point)}:n}function Xy(n,a){return{x:n.x-a.x,y:n.y-a.y}}function vf({point:n},a){return{point:n,delta:Xy(n,wb(a)),offset:Xy(n,b2(a)),velocity:x2(a,.1)}}function b2(n){return n[0]}function wb(n){return n[n.length-1]}function x2(n,a){if(n.length<2)return{x:0,y:0};let s=n.length-1,l=null;const u=wb(n);for(;s>=0&&(l=n[s],!(u.timestamp-l.timestamp>xn(a)));)s--;if(!l)return{x:0,y:0};const d=Sn(u.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const f={x:(u.x-l.x)/d,y:(u.y-l.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function S2(n,{min:a,max:s},l){return a!==void 0&&n<a?n=l?qt(a,n,l.min):Math.max(n,a):s!==void 0&&n>s&&(n=l?qt(s,n,l.max):Math.min(n,s)),n}function Ky(n,a,s){return{min:a!==void 0?n.min+a:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function T2(n,{top:a,left:s,bottom:l,right:u}){return{x:Ky(n.x,s,u),y:Ky(n.y,a,l)}}function Zy(n,a){let s=a.min-n.min,l=a.max-n.max;return a.max-a.min<n.max-n.min&&([s,l]=[l,s]),{min:s,max:l}}function w2(n,a){return{x:Zy(n.x,a.x),y:Zy(n.y,a.y)}}function A2(n,a){let s=.5;const l=Se(n),u=Se(a);return u>l?s=Ar(a.min,a.max-l,n.min):l>u&&(s=Ar(n.min,n.max-u,a.min)),Yn(0,1,s)}function E2(n,a){const s={};return a.min!==void 0&&(s.min=a.min-n.min),a.max!==void 0&&(s.max=a.max-n.min),s}const Jf=.35;function M2(n=Jf){return n===!1?n=0:n===!0&&(n=Jf),{x:Qy(n,"left","right"),y:Qy(n,"top","bottom")}}function Qy(n,a,s){return{min:Fy(n,a),max:Fy(n,s)}}function Fy(n,a){return typeof n=="number"?n:n[a]||0}const C2=new WeakMap;class R2{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Wt(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const u=g=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Br(g).point)},d=(g,v)=>{const{drag:x,dragPropagation:A,onDragStart:O}=this.getProps();if(x&&!A&&(this.openDragLock&&this.openDragLock(),this.openDragLock=XA(x),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),We(M=>{let E=this.getAxisMotionValue(M).get()||0;if(Tn.test(E)){const{projection:_}=this.visualElement;if(_&&_.layout){const D=_.layout.layoutBox[M];D&&(E=Se(D)*(parseFloat(E)/100))}}this.originPoint[M]=E}),O&&Xt.postRender(()=>O(g,v)),Ff(this.visualElement,"transform");const{animationState:w}=this.visualElement;w&&w.setActive("whileDrag",!0)},f=(g,v)=>{const{dragPropagation:x,dragDirectionLock:A,onDirectionLock:O,onDrag:w}=this.getProps();if(!x&&!this.openDragLock)return;const{offset:M}=v;if(A&&this.currentDirection===null){this.currentDirection=D2(M),this.currentDirection!==null&&O&&O(this.currentDirection);return}this.updateAxis("x",v.point,M),this.updateAxis("y",v.point,M),this.visualElement.render(),w&&w(g,v)},h=(g,v)=>this.stop(g,v),p=()=>We(g=>{var v;return this.getAnimationState(g)==="paused"&&((v=this.getAxisMotionValue(g).animation)==null?void 0:v.play())}),{dragSnapToOrigin:m}=this.getProps();this.panSession=new Tb(a,{onSessionStart:u,onStart:d,onMove:f,onSessionEnd:h,resumeAnimation:p},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:m,contextWindow:Sb(this.visualElement)})}stop(a,s){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:u}=s;this.startAnimation(u);const{onDragEnd:d}=this.getProps();d&&Xt.postRender(()=>d(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,l){const{drag:u}=this.getProps();if(!l||!so(a,u,this.currentDirection))return;const d=this.getAxisMotionValue(a);let f=this.originPoint[a]+l[a];this.constraints&&this.constraints[a]&&(f=S2(f,this.constraints[a],this.elastic[a])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:a,dragElastic:s}=this.getProps(),l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,u=this.constraints;a&&Ji(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&l?this.constraints=T2(l.layoutBox,a):this.constraints=!1,this.elastic=M2(s),u!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&We(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=E2(l.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!Ji(a))return!1;const l=a.current,{projection:u}=this.visualElement;if(!u||!u.layout)return!1;const d=y2(l,u.root,this.visualElement.getTransformPagePoint());let f=w2(u.layout.layoutBox,d);if(s){const h=s(o2(f));this.hasMutatedConstraints=!!h,h&&(f=pb(h))}return f}startAnimation(a){const{drag:s,dragMomentum:l,dragElastic:u,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:h}=this.getProps(),p=this.constraints||{},m=We(g=>{if(!so(g,s,this.currentDirection))return;let v=p&&p[g]||{};f&&(v={min:0,max:0});const x=u?200:1e6,A=u?40:1e7,O={type:"inertia",velocity:l?a[g]:0,bounceStiffness:x,bounceDamping:A,timeConstant:750,restDelta:1,restSpeed:10,...d,...v};return this.startAxisValueAnimation(g,O)});return Promise.all(m).then(h)}startAxisValueAnimation(a,s){const l=this.getAxisMotionValue(a);return Ff(this.visualElement,a),l.start(Qd(a,l,0,s,this.visualElement,!1))}stopAnimation(){We(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){We(a=>{var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,l=this.visualElement.getProps(),u=l[s];return u||this.visualElement.getValue(a,(l.initial?l.initial[a]:void 0)||0)}snapToCursor(a){We(s=>{const{drag:l}=this.getProps();if(!so(s,l,this.currentDirection))return;const{projection:u}=this.visualElement,d=this.getAxisMotionValue(s);if(u&&u.layout){const{min:f,max:h}=u.layout.layoutBox[s];d.set(a[s]-qt(f,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:l}=this.visualElement;if(!Ji(s)||!l||!this.constraints)return;this.stopAnimation();const u={x:0,y:0};We(f=>{const h=this.getAxisMotionValue(f);if(h&&this.constraints!==!1){const p=h.get();u[f]=A2({min:p,max:p},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),We(f=>{if(!so(f,a,null))return;const h=this.getAxisMotionValue(f),{min:p,max:m}=this.constraints[f];h.set(qt(p,m,u[f]))})}addListeners(){if(!this.visualElement.current)return;C2.set(this.visualElement,this);const a=this.visualElement.current,s=Sr(a,"pointerdown",p=>{const{drag:m,dragListener:g=!0}=this.getProps();m&&g&&this.start(p)}),l=()=>{const{dragConstraints:p}=this.getProps();Ji(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:u}=this.visualElement,d=u.addEventListener("measure",l);u&&!u.layout&&(u.root&&u.root.updateScroll(),u.updateLayout()),Xt.read(l);const f=Or(window,"resize",()=>this.scalePositionWithinConstraints()),h=u.addEventListener("didUpdate",({delta:p,hasLayoutChanged:m})=>{this.isDragging&&m&&(We(g=>{const v=this.getAxisMotionValue(g);v&&(this.originPoint[g]+=p[g].translate,v.set(v.get()+p[g].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:l=!1,dragPropagation:u=!1,dragConstraints:d=!1,dragElastic:f=Jf,dragMomentum:h=!0}=a;return{...a,drag:s,dragDirectionLock:l,dragPropagation:u,dragConstraints:d,dragElastic:f,dragMomentum:h}}}function so(n,a,s){return(a===!0||a===n)&&(s===null||s===n)}function D2(n,a=10){let s=null;return Math.abs(n.y)>a?s="y":Math.abs(n.x)>a&&(s="x"),s}class O2 extends Ma{constructor(a){super(a),this.removeGroupControls=$e,this.removeListeners=$e,this.controls=new R2(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$e}unmount(){this.removeGroupControls(),this.removeListeners()}}const Iy=n=>(a,s)=>{n&&Xt.postRender(()=>n(a,s))};class N2 extends Ma{constructor(){super(...arguments),this.removePointerDownListener=$e}onPointerDown(a){this.session=new Tb(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Sb(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:l,onPanEnd:u}=this.node.getProps();return{onSessionStart:Iy(a),onStart:Iy(s),onMove:l,onEnd:(d,f)=>{delete this.session,u&&Xt.postRender(()=>u(d,f))}}}mount(){this.removePointerDownListener=Sr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const xo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Wy(n,a){return a.max===a.min?0:n/(a.max-a.min)*100}const pr={correct:(n,a)=>{if(!a.target)return n;if(typeof n=="string")if(pt.test(n))n=parseFloat(n);else return n;const s=Wy(n,a.target.x),l=Wy(n,a.target.y);return`${s}% ${l}%`}},_2={correct:(n,{treeScale:a,projectionDelta:s})=>{const l=n,u=Ta.parse(n);if(u.length>5)return l;const d=Ta.createTransformer(n),f=typeof u[0]!="number"?1:0,h=s.x.scale*a.x,p=s.y.scale*a.y;u[0+f]/=h,u[1+f]/=p;const m=qt(h,p,.5);return typeof u[2+f]=="number"&&(u[2+f]/=m),typeof u[3+f]=="number"&&(u[3+f]/=m),d(u)}};class j2 extends T.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l,layoutId:u}=this.props,{projection:d}=a;yE(V2),d&&(s.group&&s.group.add(d),l&&l.register&&u&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),xo.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:l,drag:u,isPresent:d}=this.props,{projection:f}=l;return f&&(f.isPresent=d,u||a.layoutDependency!==s||s===void 0||a.isPresent!==d?f.willUpdate():this.safeToRemove(),a.isPresent!==d&&(d?f.promote():f.relegate()||Xt.postRender(()=>{const h=f.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),Ud.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l}=this.props,{projection:u}=a;u&&(u.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(u),l&&l.deregister&&l.deregister(u))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function Ab(n){const[a,s]=tE(),l=T.useContext(r0);return S.jsx(j2,{...n,layoutGroup:l,switchLayoutGroup:T.useContext(nb),isPresent:a,safeToRemove:s})}const V2={borderRadius:{...pr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:pr,borderTopRightRadius:pr,borderBottomLeftRadius:pr,borderBottomRightRadius:pr,boxShadow:_2};function z2(n,a,s){const l=ve(n)?n:ss(n);return l.start(Qd("",l,a,s)),l.animation}const L2=(n,a)=>n.depth-a.depth;class B2{constructor(){this.children=[],this.isDirty=!1}add(a){bd(this.children,a),this.isDirty=!0}remove(a){xd(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(L2),this.isDirty=!1,this.children.forEach(a)}}function U2(n,a){const s=De.now(),l=({timestamp:u})=>{const d=u-s;d>=a&&(Sa(l),n(d-a))};return Xt.setup(l,!0),()=>Sa(l)}const Eb=["TopLeft","TopRight","BottomLeft","BottomRight"],k2=Eb.length,$y=n=>typeof n=="string"?parseFloat(n):n,Jy=n=>typeof n=="number"||pt.test(n);function H2(n,a,s,l,u,d){u?(n.opacity=qt(0,s.opacity??1,P2(l)),n.opacityExit=qt(a.opacity??1,0,G2(l))):d&&(n.opacity=qt(a.opacity??1,s.opacity??1,l));for(let f=0;f<k2;f++){const h=`border${Eb[f]}Radius`;let p=tv(a,h),m=tv(s,h);if(p===void 0&&m===void 0)continue;p||(p=0),m||(m=0),p===0||m===0||Jy(p)===Jy(m)?(n[h]=Math.max(qt($y(p),$y(m),l),0),(Tn.test(m)||Tn.test(p))&&(n[h]+="%")):n[h]=m}(a.rotate||s.rotate)&&(n.rotate=qt(a.rotate||0,s.rotate||0,l))}function tv(n,a){return n[a]!==void 0?n[a]:n.borderRadius}const P2=Mb(0,.5,y0),G2=Mb(.5,.95,$e);function Mb(n,a,s){return l=>l<n?0:l>a?1:s(Ar(n,a,l))}function ev(n,a){n.min=a.min,n.max=a.max}function Ie(n,a){ev(n.x,a.x),ev(n.y,a.y)}function nv(n,a){n.translate=a.translate,n.scale=a.scale,n.originPoint=a.originPoint,n.origin=a.origin}function av(n,a,s,l,u){return n-=a,n=Ro(n,1/s,l),u!==void 0&&(n=Ro(n,1/u,l)),n}function Y2(n,a=0,s=1,l=.5,u,d=n,f=n){if(Tn.test(a)&&(a=parseFloat(a),a=qt(f.min,f.max,a/100)-f.min),typeof a!="number")return;let h=qt(d.min,d.max,l);n===d&&(h-=a),n.min=av(n.min,a,s,h,u),n.max=av(n.max,a,s,h,u)}function iv(n,a,[s,l,u],d,f){Y2(n,a[s],a[l],a[u],a.scale,d,f)}const q2=["x","scaleX","originX"],X2=["y","scaleY","originY"];function sv(n,a,s,l){iv(n.x,a,q2,s?s.x:void 0,l?l.x:void 0),iv(n.y,a,X2,s?s.y:void 0,l?l.y:void 0)}function rv(n){return n.translate===0&&n.scale===1}function Cb(n){return rv(n.x)&&rv(n.y)}function lv(n,a){return n.min===a.min&&n.max===a.max}function K2(n,a){return lv(n.x,a.x)&&lv(n.y,a.y)}function ov(n,a){return Math.round(n.min)===Math.round(a.min)&&Math.round(n.max)===Math.round(a.max)}function Rb(n,a){return ov(n.x,a.x)&&ov(n.y,a.y)}function uv(n){return Se(n.x)/Se(n.y)}function cv(n,a){return n.translate===a.translate&&n.scale===a.scale&&n.originPoint===a.originPoint}class Z2{constructor(){this.members=[]}add(a){bd(this.members,a),a.scheduleRender()}remove(a){if(xd(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(u=>a===u);if(s===0)return!1;let l;for(let u=s;u>=0;u--){const d=this.members[u];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(a,s){const l=this.lead;if(a!==l&&(this.prevLead=l,this.lead=a,a.show(),l)){l.instance&&l.scheduleRender(),a.scheduleRender(),a.resumeFrom=l,s&&(a.resumeFrom.preserveOpacity=!0),l.snapshot&&(a.snapshot=l.snapshot,a.snapshot.latestValues=l.animationValues||l.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:u}=a.options;u===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:l}=a;s.onExitComplete&&s.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Q2(n,a,s){let l="";const u=n.x.translate/a.x,d=n.y.translate/a.y,f=(s==null?void 0:s.z)||0;if((u||d||f)&&(l=`translate3d(${u}px, ${d}px, ${f}px) `),(a.x!==1||a.y!==1)&&(l+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:m,rotate:g,rotateX:v,rotateY:x,skewX:A,skewY:O}=s;m&&(l=`perspective(${m}px) ${l}`),g&&(l+=`rotate(${g}deg) `),v&&(l+=`rotateX(${v}deg) `),x&&(l+=`rotateY(${x}deg) `),A&&(l+=`skewX(${A}deg) `),O&&(l+=`skewY(${O}deg) `)}const h=n.x.scale*a.x,p=n.y.scale*a.y;return(h!==1||p!==1)&&(l+=`scale(${h}, ${p})`),l||"none"}const bf=["","X","Y","Z"],F2={visibility:"hidden"},I2=1e3;let W2=0;function xf(n,a,s,l){const{latestValues:u}=a;u[n]&&(s[n]=u[n],a.setStaticValue(n,0),l&&(l[n]=0))}function Db(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:a}=n.options;if(!a)return;const s=fb(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:u,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Xt,!(u||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&Db(l)}function Ob({attachResizeListener:n,defaultParent:a,measureScroll:s,checkIsScrollRoot:l,resetTransform:u}){return class{constructor(f={},h=a==null?void 0:a()){this.id=W2++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(tM),this.nodes.forEach(sM),this.nodes.forEach(rM),this.nodes.forEach(eM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new B2)}addEventListener(f,h){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new wd),this.eventHandlers.get(f).add(h)}notifyListeners(f,...h){const p=this.eventHandlers.get(f);p&&p.notify(...h)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=I0(f)&&!WA(f),this.instance=f;const{layoutId:h,layout:p,visualElement:m}=this.options;if(m&&!m.current&&m.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(p||h)&&(this.isLayoutDirty=!0),n){let g;const v=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=U2(v,250),xo.hasAnimatedSinceResize&&(xo.hasAnimatedSinceResize=!1,this.nodes.forEach(dv))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&m&&(h||p)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:v,hasRelativeLayoutChanged:x,layout:A})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const O=this.options.transition||m.getDefaultTransition()||fM,{onLayoutAnimationStart:w,onLayoutAnimationComplete:M}=m.getProps(),E=!this.targetLayout||!Rb(this.targetLayout,A),_=!v&&x;if(this.options.layoutRoot||this.resumeFrom||_||v&&(E||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const D={...Ld(O,"layout"),onPlay:w,onComplete:M};(m.shouldReduceMotion||this.options.layoutRoot)&&(D.delay=0,D.type=!1),this.startAnimation(D),this.setAnimationOrigin(g,_)}else v||dv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=A})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Sa(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(lM),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Db(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const v=this.path[g];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:p}=this.options;if(h===void 0&&!p)return;const m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(fv);return}this.isUpdating||this.nodes.forEach(aM),this.isUpdating=!1,this.nodes.forEach(iM),this.nodes.forEach($2),this.nodes.forEach(J2),this.clearAllSnapshots();const h=De.now();de.delta=Yn(0,1e3/60,h-de.timestamp),de.timestamp=h,de.isProcessing=!0,ff.update.process(de),ff.preRender.process(de),ff.render.process(de),de.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ud.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nM),this.sharedNodes.forEach(oM)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Xt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Xt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Se(this.snapshot.measuredBox.x)&&!Se(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Wt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(h=!1),h&&this.instance){const p=l(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:p,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!u)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!Cb(this.projectionDelta),p=this.getTransformTemplate(),m=p?p(this.latestValues,""):void 0,g=m!==this.prevTransformTemplateValue;f&&this.instance&&(h||ti(this.latestValues)||g)&&(u(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const h=this.measurePageBox();let p=this.removeElementScroll(h);return f&&(p=this.removeTransform(p)),dM(p),{animationId:this.root.animationId,measuredBox:h,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){var m;const{visualElement:f}=this.options;if(!f)return Wt();const h=f.measureViewportBox();if(!(((m=this.scroll)==null?void 0:m.wasRoot)||this.path.some(hM))){const{scroll:g}=this.root;g&&(es(h.x,g.offset.x),es(h.y,g.offset.y))}return h}removeElementScroll(f){var p;const h=Wt();if(Ie(h,f),(p=this.scroll)!=null&&p.wasRoot)return h;for(let m=0;m<this.path.length;m++){const g=this.path[m],{scroll:v,options:x}=g;g!==this.root&&v&&x.layoutScroll&&(v.wasRoot&&Ie(h,f),es(h.x,v.offset.x),es(h.y,v.offset.y))}return h}applyTransform(f,h=!1){const p=Wt();Ie(p,f);for(let m=0;m<this.path.length;m++){const g=this.path[m];!h&&g.options.layoutScroll&&g.scroll&&g!==g.root&&ns(p,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),ti(g.latestValues)&&ns(p,g.latestValues)}return ti(this.latestValues)&&ns(p,this.latestValues),p}removeTransform(f){const h=Wt();Ie(h,f);for(let p=0;p<this.path.length;p++){const m=this.path[p];if(!m.instance||!ti(m.latestValues))continue;Wf(m.latestValues)&&m.updateSnapshot();const g=Wt(),v=m.measurePageBox();Ie(g,v),sv(h,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,g)}return ti(this.latestValues)&&sv(h,this.latestValues),h}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==de.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var x;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const p=!!this.resumingFrom||this!==h;if(!(f||p&&this.isSharedProjectionDirty||this.isProjectionDirty||(x=this.parent)!=null&&x.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:v}=this.options;if(!(!this.layout||!(g||v))){if(this.resolvedRelativeTargetAt=de.timestamp,!this.targetDelta&&!this.relativeTarget){const A=this.getClosestProjectingParent();A&&A.layout&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Wt(),this.relativeTargetOrigin=Wt(),wr(this.relativeTargetOrigin,this.layout.layoutBox,A.layout.layoutBox),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Wt(),this.targetWithTransforms=Wt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),p2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ie(this.target,this.layout.layoutBox),bb(this.target,this.targetDelta)):Ie(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const A=this.getClosestProjectingParent();A&&!!A.resumingFrom==!!this.resumingFrom&&!A.options.layoutScroll&&A.target&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Wt(),this.relativeTargetOrigin=Wt(),wr(this.relativeTargetOrigin,this.target,A.target),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wf(this.parent.latestValues)||vb(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var O;const f=this.getLead(),h=!!this.resumingFrom||this!==f;let p=!0;if((this.isProjectionDirty||(O=this.parent)!=null&&O.isProjectionDirty)&&(p=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(p=!1),this.resolvedRelativeTargetAt===de.timestamp&&(p=!1),p)return;const{layout:m,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(m||g))return;Ie(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,x=this.treeScale.y;g2(this.layoutCorrected,this.treeScale,this.path,h),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=Wt());const{target:A}=f;if(!A){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(nv(this.prevProjectionDelta.x,this.projectionDelta.x),nv(this.prevProjectionDelta.y,this.projectionDelta.y)),Tr(this.projectionDelta,this.layoutCorrected,A,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==x||!cv(this.projectionDelta.x,this.prevProjectionDelta.x)||!cv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",A))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),f){const p=this.getStack();p&&p.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ts(),this.projectionDelta=ts(),this.projectionDeltaWithTransform=ts()}setAnimationOrigin(f,h=!1){const p=this.snapshot,m=p?p.latestValues:{},g={...this.latestValues},v=ts();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const x=Wt(),A=p?p.source:void 0,O=this.layout?this.layout.source:void 0,w=A!==O,M=this.getStack(),E=!M||M.members.length<=1,_=!!(w&&!E&&this.options.crossfade===!0&&!this.path.some(cM));this.animationProgress=0;let D;this.mixTargetDelta=H=>{const z=H/1e3;hv(v.x,f.x,z),hv(v.y,f.y,z),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(wr(x,this.layout.layoutBox,this.relativeParent.layout.layoutBox),uM(this.relativeTarget,this.relativeTargetOrigin,x,z),D&&K2(this.relativeTarget,D)&&(this.isProjectionDirty=!1),D||(D=Wt()),Ie(D,this.relativeTarget)),w&&(this.animationValues=g,H2(g,m,this.latestValues,z,_,E)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=z},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var h,p,m;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(m=(p=this.resumingFrom)==null?void 0:p.currentAnimation)==null||m.stop(),this.pendingAnimation&&(Sa(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Xt.update(()=>{xo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ss(0)),this.currentAnimation=z2(this.motionValue,[0,1e3],{...f,isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),f.onUpdate&&f.onUpdate(g)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(I2),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:h,target:p,layout:m,latestValues:g}=f;if(!(!h||!p||!m)){if(this!==f&&this.layout&&m&&Nb(this.options.animationType,this.layout.layoutBox,m.layoutBox)){p=this.target||Wt();const v=Se(this.layout.layoutBox.x);p.x.min=f.target.x.min,p.x.max=p.x.min+v;const x=Se(this.layout.layoutBox.y);p.y.min=f.target.y.min,p.y.max=p.y.min+x}Ie(h,p),ns(h,g),Tr(this.projectionDeltaWithTransform,this.layoutCorrected,h,g)}}registerSharedNode(f,h){this.sharedNodes.has(f)||this.sharedNodes.set(f,new Z2),this.sharedNodes.get(f).add(h);const m=h.options.initialPromotionConfig;h.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(h):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var h;const{layoutId:f}=this.options;return f?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:f}=this.options;return f?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:h,preserveFollowOpacity:p}={}){const m=this.getStack();m&&m.promote(this,p),f&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let h=!1;const{latestValues:p}=f;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(h=!0),!h)return;const m={};p.z&&xf("z",f,m,this.animationValues);for(let g=0;g<bf.length;g++)xf(`rotate${bf[g]}`,f,m,this.animationValues),xf(`skew${bf[g]}`,f,m,this.animationValues);f.render();for(const g in m)f.setStaticValue(g,m[g]),this.animationValues&&(this.animationValues[g]=m[g]);f.scheduleRender()}getProjectionStyles(f){if(!this.instance||this.isSVG)return;if(!this.isVisible)return F2;const h={visibility:""},p=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=bo(f==null?void 0:f.pointerEvents)||"",h.transform=p?p(this.latestValues,""):"none",h;const m=this.getLead();if(!this.projectionDelta||!this.layout||!m.target){const A={};return this.options.layoutId&&(A.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,A.pointerEvents=bo(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!ti(this.latestValues)&&(A.transform=p?p({},""):"none",this.hasProjected=!1),A}const g=m.animationValues||m.latestValues;this.applyTransformsToTarget(),h.transform=Q2(this.projectionDeltaWithTransform,this.treeScale,g),p&&(h.transform=p(g,h.transform));const{x:v,y:x}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${x.origin*100}% 0`,m.animationValues?h.opacity=m===this?g.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:h.opacity=m===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const A in Rr){if(g[A]===void 0)continue;const{correct:O,applyTo:w,isCSSVariable:M}=Rr[A],E=h.transform==="none"?g[A]:O(g[A],m);if(w){const _=w.length;for(let D=0;D<_;D++)h[w[D]]=E}else M?this.options.visualElement.renderState.vars[A]=E:h[A]=E}return this.options.layoutId&&(h.pointerEvents=m===this?bo(f==null?void 0:f.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var h;return(h=f.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(fv),this.root.sharedNodes.clear()}}}function $2(n){n.updateLayout()}function J2(n){var s;const a=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&a&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:u}=n.layout,{animationType:d}=n.options,f=a.source!==n.layout.source;d==="size"?We(v=>{const x=f?a.measuredBox[v]:a.layoutBox[v],A=Se(x);x.min=l[v].min,x.max=x.min+A}):Nb(d,a.layoutBox,l)&&We(v=>{const x=f?a.measuredBox[v]:a.layoutBox[v],A=Se(l[v]);x.max=x.min+A,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+A)});const h=ts();Tr(h,l,a.layoutBox);const p=ts();f?Tr(p,n.applyTransform(u,!0),a.measuredBox):Tr(p,l,a.layoutBox);const m=!Cb(h);let g=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:x,layout:A}=v;if(x&&A){const O=Wt();wr(O,a.layoutBox,x.layoutBox);const w=Wt();wr(w,l,A.layoutBox),Rb(O,w)||(g=!0),v.options.layoutRoot&&(n.relativeTarget=w,n.relativeTargetOrigin=O,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:a,delta:p,layoutDelta:h,hasLayoutChanged:m,hasRelativeLayoutChanged:g})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function tM(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function eM(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function nM(n){n.clearSnapshot()}function fv(n){n.clearMeasurements()}function aM(n){n.isLayoutDirty=!1}function iM(n){const{visualElement:a}=n.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),n.resetTransform()}function dv(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function sM(n){n.resolveTargetDelta()}function rM(n){n.calcProjection()}function lM(n){n.resetSkewAndRotation()}function oM(n){n.removeLeadSnapshot()}function hv(n,a,s){n.translate=qt(a.translate,0,s),n.scale=qt(a.scale,1,s),n.origin=a.origin,n.originPoint=a.originPoint}function mv(n,a,s,l){n.min=qt(a.min,s.min,l),n.max=qt(a.max,s.max,l)}function uM(n,a,s,l){mv(n.x,a.x,s.x,l),mv(n.y,a.y,s.y,l)}function cM(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const fM={duration:.45,ease:[.4,0,.1,1]},pv=n=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),gv=pv("applewebkit/")&&!pv("chrome/")?Math.round:$e;function yv(n){n.min=gv(n.min),n.max=gv(n.max)}function dM(n){yv(n.x),yv(n.y)}function Nb(n,a,s){return n==="position"||n==="preserve-aspect"&&!m2(uv(a),uv(s),.2)}function hM(n){var a;return n!==n.root&&((a=n.scroll)==null?void 0:a.wasRoot)}const mM=Ob({attachResizeListener:(n,a)=>Or(n,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Sf={current:void 0},_b=Ob({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Sf.current){const n=new mM({});n.mount(window),n.setOptions({layoutScroll:!0}),Sf.current=n}return Sf.current},resetTransform:(n,a)=>{n.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),pM={pan:{Feature:N2},drag:{Feature:O2,ProjectionNode:_b,MeasureLayout:Ab}};function vv(n,a,s){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",s==="Start");const u="onHover"+s,d=l[u];d&&Xt.postRender(()=>d(a,Br(a)))}class gM extends Ma{mount(){const{current:a}=this.node;a&&(this.unmount=KA(a,(s,l)=>(vv(this.node,l,"Start"),u=>vv(this.node,u,"End"))))}unmount(){}}class yM extends Ma{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch{a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Vr(Or(this.node.current,"focus",()=>this.onFocus()),Or(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function bv(n,a,s){const{props:l}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",s==="Start");const u="onTap"+(s==="End"?"":s),d=l[u];d&&Xt.postRender(()=>d(a,Br(a)))}class vM extends Ma{mount(){const{current:a}=this.node;a&&(this.unmount=IA(a,(s,l)=>(bv(this.node,l,"Start"),(u,{success:d})=>bv(this.node,u,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const td=new WeakMap,Tf=new WeakMap,bM=n=>{const a=td.get(n.target);a&&a(n)},xM=n=>{n.forEach(bM)};function SM({root:n,...a}){const s=n||document;Tf.has(s)||Tf.set(s,{});const l=Tf.get(s),u=JSON.stringify(a);return l[u]||(l[u]=new IntersectionObserver(xM,{root:n,...a})),l[u]}function TM(n,a,s){const l=SM(a);return td.set(n,s),l.observe(n),()=>{td.delete(n),l.unobserve(n)}}const wM={some:0,all:1};class AM extends Ma{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:l,amount:u="some",once:d}=a,f={root:s?s.current:void 0,rootMargin:l,threshold:typeof u=="number"?u:wM[u]},h=p=>{const{isIntersecting:m}=p;if(this.isInView===m||(this.isInView=m,d&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);const{onViewportEnter:g,onViewportLeave:v}=this.node.getProps(),x=m?g:v;x&&x(p)};return TM(this.node.current,f,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(EM(a,s))&&this.startObserver()}unmount(){}}function EM({viewport:n={}},{viewport:a={}}={}){return s=>n[s]!==a[s]}const MM={inView:{Feature:AM},tap:{Feature:vM},focus:{Feature:yM},hover:{Feature:gM}},CM={layout:{ProjectionNode:_b,MeasureLayout:Ab}},ed={current:null},jb={current:!1};function RM(){if(jb.current=!0,!!yd)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),a=()=>ed.current=n.matches;n.addListener(a),a()}else ed.current=!1}const DM=new WeakMap;function OM(n,a,s){for(const l in a){const u=a[l],d=s[l];if(ve(u))n.addValue(l,u);else if(ve(d))n.addValue(l,ss(u,{owner:n}));else if(d!==u)if(n.hasValue(l)){const f=n.getValue(l);f.liveStyle===!0?f.jump(u):f.hasAnimated||f.set(u)}else{const f=n.getStaticValue(l);n.addValue(l,ss(f!==void 0?f:u,{owner:n}))}}for(const l in s)a[l]===void 0&&n.removeValue(l);return a}const xv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class NM{scrapeMotionValuesFromProps(a,s,l){return{}}constructor({parent:a,props:s,presenceContext:l,reducedMotionConfig:u,blockInitialAnimation:d,visualState:f},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=zd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const x=De.now();this.renderScheduledAt<x&&(this.renderScheduledAt=x,Xt.render(this.render,!1,!0))};const{latestValues:p,renderState:m}=f;this.latestValues=p,this.baseTarget={...p},this.initialValues=s.initial?{...p}:{},this.renderState=m,this.parent=a,this.props=s,this.presenceContext=l,this.depth=a?a.depth+1:0,this.reducedMotionConfig=u,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=Bo(s),this.isVariantNode=tb(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const{willChange:g,...v}=this.scrapeMotionValuesFromProps(s,{},this);for(const x in v){const A=v[x];p[x]!==void 0&&ve(A)&&A.set(p[x],!1)}}mount(a){this.current=a,DM.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,l)=>this.bindToMotionValue(l,s)),jb.current||RM(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ed.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Sa(this.notifyUpdate),Sa(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const l=cs.has(a);l&&this.onBindTransform&&this.onBindTransform();const u=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Xt.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{u(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in rs){const s=rs[a];if(!s)continue;const{isEnabled:l,Feature:u}=s;if(!this.features[a]&&u&&l(this.props)&&(this.features[a]=new u(this)),this.features[a]){const d=this.features[a];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Wt()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let l=0;l<xv.length;l++){const u=xv[l];this.propEventSubscriptions[u]&&(this.propEventSubscriptions[u](),delete this.propEventSubscriptions[u]);const d="on"+u,f=a[d];f&&(this.propEventSubscriptions[u]=this.on(u,f))}this.prevMotionValues=OM(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const l=this.values.get(a);s!==l&&(l&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let l=this.values.get(a);return l===void 0&&s!==void 0&&(l=ss(s===null?void 0:s,{owner:this}),this.addValue(a,l)),l}readValue(a,s){let l=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options);return l!=null&&(typeof l=="string"&&(l0(l)||u0(l))?l=parseFloat(l):!JA(l)&&Ta.test(s)&&(l=X0(a,s)),this.setBaseTarget(a,ve(l)?l.get():l)),ve(l)?l.get():l}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var d;const{initial:s}=this.props;let l;if(typeof s=="string"||typeof s=="object"){const f=Kd(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(l=f[a])}if(s&&l!==void 0)return l;const u=this.getBaseTargetFromProps(this.props,a);return u!==void 0&&!ve(u)?u:this.initialValues[a]!==void 0&&l===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new wd),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class Vb extends NM{constructor(){super(...arguments),this.KeyframeResolver=PA}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:l}){delete s[a],delete l[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;ve(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function zb(n,{style:a,vars:s},l,u){Object.assign(n.style,a,u&&u.getProjectionStyles(l));for(const d in s)n.style.setProperty(d,s[d])}function _M(n){return window.getComputedStyle(n)}class jM extends Vb{constructor(){super(...arguments),this.type="html",this.renderInstance=zb}readValueFromInstance(a,s){var l;if(cs.has(s))return(l=this.projection)!=null&&l.isProjecting?Gf(s):rA(a,s);{const u=_M(a),d=(Md(s)?u.getPropertyValue(s):u[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:s}){return xb(a,s)}build(a,s,l){Yd(a,s,l.transformTemplate)}scrapeMotionValuesFromProps(a,s,l){return Zd(a,s,l)}}const Lb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function VM(n,a,s,l){zb(n,a,void 0,l);for(const u in a.attrs)n.setAttribute(Lb.has(u)?u:Gd(u),a.attrs[u])}class zM extends Vb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Wt}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(cs.has(s)){const l=q0(s);return l&&l.default||0}return s=Lb.has(s)?s:Gd(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,l){return cb(a,s,l)}build(a,s,l){rb(a,s,this.isSVGTag,l.transformTemplate,l.style)}renderInstance(a,s,l,u){VM(a,s,l,u)}mount(a){this.isSVGTag=ob(a.tagName),super.mount(a)}}const LM=(n,a)=>Xd(n)?new zM(a):new jM(a,{allowProjection:n!==T.Fragment}),BM=VE({...r2,...MM,...pM,...CM},LM),dn=sE(BM);function Sv(n,a){if(typeof n=="function")return n(a);n!=null&&(n.current=a)}function UM(...n){return a=>{let s=!1;const l=n.map(u=>{const d=Sv(u,a);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let u=0;u<l.length;u++){const d=l[u];typeof d=="function"?d():Sv(n[u],null)}}}}function te(...n){return T.useCallback(UM(...n),n)}function Nr(n){const a=kM(n),s=T.forwardRef((l,u)=>{const{children:d,...f}=l,h=T.Children.toArray(d),p=h.find(PM);if(p){const m=p.props.children,g=h.map(v=>v===p?T.Children.count(m)>1?T.Children.only(null):T.isValidElement(m)?m.props.children:null:v);return S.jsx(a,{...f,ref:u,children:T.isValidElement(m)?T.cloneElement(m,void 0,g):null})}return S.jsx(a,{...f,ref:u,children:d})});return s.displayName=`${n}.Slot`,s}var Bb=Nr("Slot");function kM(n){const a=T.forwardRef((s,l)=>{const{children:u,...d}=s,f=T.isValidElement(u)?YM(u):void 0,h=te(f,l);if(T.isValidElement(u)){const p=GM(d,u.props);return u.type!==T.Fragment&&(p.ref=h),T.cloneElement(u,p)}return T.Children.count(u)>1?T.Children.only(null):null});return a.displayName=`${n}.SlotClone`,a}var HM=Symbol("radix.slottable");function PM(n){return T.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===HM}function GM(n,a){const s={...a};for(const l in a){const u=n[l],d=a[l];/^on[A-Z]/.test(l)?u&&d?s[l]=(...h)=>{const p=d(...h);return u(...h),p}:u&&(s[l]=u):l==="style"?s[l]={...u,...d}:l==="className"&&(s[l]=[u,d].filter(Boolean).join(" "))}return{...n,...s}}function YM(n){var l,u;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Ub(n){var a,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(a=0;a<u;a++)n[a]&&(s=Ub(n[a]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function kb(){for(var n,a,s=0,l="",u=arguments.length;s<u;s++)(n=arguments[s])&&(a=Ub(n))&&(l&&(l+=" "),l+=a);return l}const Tv=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,wv=kb,Hb=(n,a)=>s=>{var l;if((a==null?void 0:a.variants)==null)return wv(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:u,defaultVariants:d}=a,f=Object.keys(u).map(m=>{const g=s==null?void 0:s[m],v=d==null?void 0:d[m];if(g===null)return null;const x=Tv(g)||Tv(v);return u[m][x]}),h=s&&Object.entries(s).reduce((m,g)=>{let[v,x]=g;return x===void 0||(m[v]=x),m},{}),p=a==null||(l=a.compoundVariants)===null||l===void 0?void 0:l.reduce((m,g)=>{let{class:v,className:x,...A}=g;return Object.entries(A).every(O=>{let[w,M]=O;return Array.isArray(M)?M.includes({...d,...h}[w]):{...d,...h}[w]===M})?[...m,v,x]:m},[]);return wv(n,f,p,s==null?void 0:s.class,s==null?void 0:s.className)},Fd="-",qM=n=>{const a=KM(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const h=f.split(Fd);return h[0]===""&&h.length!==1&&h.shift(),Pb(h,a)||XM(f)},getConflictingClassGroupIds:(f,h)=>{const p=s[f]||[];return h&&l[f]?[...p,...l[f]]:p}}},Pb=(n,a)=>{var f;if(n.length===0)return a.classGroupId;const s=n[0],l=a.nextPart.get(s),u=l?Pb(n.slice(1),l):void 0;if(u)return u;if(a.validators.length===0)return;const d=n.join(Fd);return(f=a.validators.find(({validator:h})=>h(d)))==null?void 0:f.classGroupId},Av=/^\[(.+)\]$/,XM=n=>{if(Av.test(n)){const a=Av.exec(n)[1],s=a==null?void 0:a.substring(0,a.indexOf(":"));if(s)return"arbitrary.."+s}},KM=n=>{const{theme:a,classGroups:s}=n,l={nextPart:new Map,validators:[]};for(const u in s)nd(s[u],l,u,a);return l},nd=(n,a,s,l)=>{n.forEach(u=>{if(typeof u=="string"){const d=u===""?a:Ev(a,u);d.classGroupId=s;return}if(typeof u=="function"){if(ZM(u)){nd(u(l),a,s,l);return}a.validators.push({validator:u,classGroupId:s});return}Object.entries(u).forEach(([d,f])=>{nd(f,Ev(a,d),s,l)})})},Ev=(n,a)=>{let s=n;return a.split(Fd).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},ZM=n=>n.isThemeGetter,QM=n=>{if(n<1)return{get:()=>{},set:()=>{}};let a=0,s=new Map,l=new Map;const u=(d,f)=>{s.set(d,f),a++,a>n&&(a=0,l=s,s=new Map)};return{get(d){let f=s.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return u(d,f),f},set(d,f){s.has(d)?s.set(d,f):u(d,f)}}},ad="!",id=":",FM=id.length,IM=n=>{const{prefix:a,experimentalParseClassName:s}=n;let l=u=>{const d=[];let f=0,h=0,p=0,m;for(let O=0;O<u.length;O++){let w=u[O];if(f===0&&h===0){if(w===id){d.push(u.slice(p,O)),p=O+FM;continue}if(w==="/"){m=O;continue}}w==="["?f++:w==="]"?f--:w==="("?h++:w===")"&&h--}const g=d.length===0?u:u.substring(p),v=WM(g),x=v!==g,A=m&&m>p?m-p:void 0;return{modifiers:d,hasImportantModifier:x,baseClassName:v,maybePostfixModifierPosition:A}};if(a){const u=a+id,d=l;l=f=>f.startsWith(u)?d(f.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:f,maybePostfixModifierPosition:void 0}}if(s){const u=l;l=d=>s({className:d,parseClassName:u})}return l},WM=n=>n.endsWith(ad)?n.substring(0,n.length-1):n.startsWith(ad)?n.substring(1):n,$M=n=>{const a=Object.fromEntries(n.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;const u=[];let d=[];return l.forEach(f=>{f[0]==="["||a[f]?(u.push(...d.sort(),f),d=[]):d.push(f)}),u.push(...d.sort()),u}},JM=n=>({cache:QM(n.cacheSize),parseClassName:IM(n),sortModifiers:$M(n),...qM(n)}),tC=/\s+/,eC=(n,a)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:u,sortModifiers:d}=a,f=[],h=n.trim().split(tC);let p="";for(let m=h.length-1;m>=0;m-=1){const g=h[m],{isExternal:v,modifiers:x,hasImportantModifier:A,baseClassName:O,maybePostfixModifierPosition:w}=s(g);if(v){p=g+(p.length>0?" "+p:p);continue}let M=!!w,E=l(M?O.substring(0,w):O);if(!E){if(!M){p=g+(p.length>0?" "+p:p);continue}if(E=l(O),!E){p=g+(p.length>0?" "+p:p);continue}M=!1}const _=d(x).join(":"),D=A?_+ad:_,H=D+E;if(f.includes(H))continue;f.push(H);const z=u(E,M);for(let F=0;F<z.length;++F){const Q=z[F];f.push(D+Q)}p=g+(p.length>0?" "+p:p)}return p};function nC(){let n=0,a,s,l="";for(;n<arguments.length;)(a=arguments[n++])&&(s=Gb(a))&&(l&&(l+=" "),l+=s);return l}const Gb=n=>{if(typeof n=="string")return n;let a,s="";for(let l=0;l<n.length;l++)n[l]&&(a=Gb(n[l]))&&(s&&(s+=" "),s+=a);return s};function aC(n,...a){let s,l,u,d=f;function f(p){const m=a.reduce((g,v)=>v(g),n());return s=JM(m),l=s.cache.get,u=s.cache.set,d=h,h(p)}function h(p){const m=l(p);if(m)return m;const g=eC(p,s);return u(p,g),g}return function(){return d(nC.apply(null,arguments))}}const ae=n=>{const a=s=>s[n]||[];return a.isThemeGetter=!0,a},Yb=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,qb=/^\((?:(\w[\w-]*):)?(.+)\)$/i,iC=/^\d+\/\d+$/,sC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,rC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,lC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,oC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,uC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Zi=n=>iC.test(n),St=n=>!!n&&!Number.isNaN(Number(n)),ya=n=>!!n&&Number.isInteger(Number(n)),wf=n=>n.endsWith("%")&&St(n.slice(0,-1)),Pn=n=>sC.test(n),cC=()=>!0,fC=n=>rC.test(n)&&!lC.test(n),Xb=()=>!1,dC=n=>oC.test(n),hC=n=>uC.test(n),mC=n=>!nt(n)&&!at(n),pC=n=>fs(n,Qb,Xb),nt=n=>Yb.test(n),Wa=n=>fs(n,Fb,fC),Af=n=>fs(n,xC,St),Mv=n=>fs(n,Kb,Xb),gC=n=>fs(n,Zb,hC),ro=n=>fs(n,Ib,dC),at=n=>qb.test(n),gr=n=>ds(n,Fb),yC=n=>ds(n,SC),Cv=n=>ds(n,Kb),vC=n=>ds(n,Qb),bC=n=>ds(n,Zb),lo=n=>ds(n,Ib,!0),fs=(n,a,s)=>{const l=Yb.exec(n);return l?l[1]?a(l[1]):s(l[2]):!1},ds=(n,a,s=!1)=>{const l=qb.exec(n);return l?l[1]?a(l[1]):s:!1},Kb=n=>n==="position"||n==="percentage",Zb=n=>n==="image"||n==="url",Qb=n=>n==="length"||n==="size"||n==="bg-size",Fb=n=>n==="length",xC=n=>n==="number",SC=n=>n==="family-name",Ib=n=>n==="shadow",TC=()=>{const n=ae("color"),a=ae("font"),s=ae("text"),l=ae("font-weight"),u=ae("tracking"),d=ae("leading"),f=ae("breakpoint"),h=ae("container"),p=ae("spacing"),m=ae("radius"),g=ae("shadow"),v=ae("inset-shadow"),x=ae("text-shadow"),A=ae("drop-shadow"),O=ae("blur"),w=ae("perspective"),M=ae("aspect"),E=ae("ease"),_=ae("animate"),D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],H=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],z=()=>[...H(),at,nt],F=()=>["auto","hidden","clip","visible","scroll"],Q=()=>["auto","contain","none"],G=()=>[at,nt,p],tt=()=>[Zi,"full","auto",...G()],rt=()=>[ya,"none","subgrid",at,nt],ht=()=>["auto",{span:["full",ya,at,nt]},ya,at,nt],ct=()=>[ya,"auto",at,nt],yt=()=>["auto","min","max","fr",at,nt],vt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ot=()=>["start","end","center","stretch","center-safe","end-safe"],V=()=>["auto",...G()],Y=()=>[Zi,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],k=()=>[n,at,nt],it=()=>[...H(),Cv,Mv,{position:[at,nt]}],R=()=>["no-repeat",{repeat:["","x","y","space","round"]}],X=()=>["auto","cover","contain",vC,pC,{size:[at,nt]}],W=()=>[wf,gr,Wa],I=()=>["","none","full",m,at,nt],J=()=>["",St,gr,Wa],gt=()=>["solid","dashed","dotted","double"],lt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$=()=>[St,wf,Cv,Mv],ut=()=>["","none",O,at,nt],Nt=()=>["none",St,at,nt],Ct=()=>["none",St,at,nt],wt=()=>[St,at,nt],At=()=>[Zi,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Pn],breakpoint:[Pn],color:[cC],container:[Pn],"drop-shadow":[Pn],ease:["in","out","in-out"],font:[mC],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Pn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Pn],shadow:[Pn],spacing:["px",St],text:[Pn],"text-shadow":[Pn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Zi,nt,at,M]}],container:["container"],columns:[{columns:[St,nt,at,h]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:z()}],overflow:[{overflow:F()}],"overflow-x":[{"overflow-x":F()}],"overflow-y":[{"overflow-y":F()}],overscroll:[{overscroll:Q()}],"overscroll-x":[{"overscroll-x":Q()}],"overscroll-y":[{"overscroll-y":Q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:tt()}],"inset-x":[{"inset-x":tt()}],"inset-y":[{"inset-y":tt()}],start:[{start:tt()}],end:[{end:tt()}],top:[{top:tt()}],right:[{right:tt()}],bottom:[{bottom:tt()}],left:[{left:tt()}],visibility:["visible","invisible","collapse"],z:[{z:[ya,"auto",at,nt]}],basis:[{basis:[Zi,"full","auto",h,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[St,Zi,"auto","initial","none",nt]}],grow:[{grow:["",St,at,nt]}],shrink:[{shrink:["",St,at,nt]}],order:[{order:[ya,"first","last","none",at,nt]}],"grid-cols":[{"grid-cols":rt()}],"col-start-end":[{col:ht()}],"col-start":[{"col-start":ct()}],"col-end":[{"col-end":ct()}],"grid-rows":[{"grid-rows":rt()}],"row-start-end":[{row:ht()}],"row-start":[{"row-start":ct()}],"row-end":[{"row-end":ct()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":yt()}],"auto-rows":[{"auto-rows":yt()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...vt(),"normal"]}],"justify-items":[{"justify-items":[...ot(),"normal"]}],"justify-self":[{"justify-self":["auto",...ot()]}],"align-content":[{content:["normal",...vt()]}],"align-items":[{items:[...ot(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ot(),{baseline:["","last"]}]}],"place-content":[{"place-content":vt()}],"place-items":[{"place-items":[...ot(),"baseline"]}],"place-self":[{"place-self":["auto",...ot()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:V()}],mx:[{mx:V()}],my:[{my:V()}],ms:[{ms:V()}],me:[{me:V()}],mt:[{mt:V()}],mr:[{mr:V()}],mb:[{mb:V()}],ml:[{ml:V()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:Y()}],w:[{w:[h,"screen",...Y()]}],"min-w":[{"min-w":[h,"screen","none",...Y()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[f]},...Y()]}],h:[{h:["screen","lh",...Y()]}],"min-h":[{"min-h":["screen","lh","none",...Y()]}],"max-h":[{"max-h":["screen","lh",...Y()]}],"font-size":[{text:["base",s,gr,Wa]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,at,Af]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",wf,nt]}],"font-family":[{font:[yC,nt,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,at,nt]}],"line-clamp":[{"line-clamp":[St,"none",at,Af]}],leading:[{leading:[d,...G()]}],"list-image":[{"list-image":["none",at,nt]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",at,nt]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:k()}],"text-color":[{text:k()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...gt(),"wavy"]}],"text-decoration-thickness":[{decoration:[St,"from-font","auto",at,Wa]}],"text-decoration-color":[{decoration:k()}],"underline-offset":[{"underline-offset":[St,"auto",at,nt]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",at,nt]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",at,nt]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:it()}],"bg-repeat":[{bg:R()}],"bg-size":[{bg:X()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ya,at,nt],radial:["",at,nt],conic:[ya,at,nt]},bC,gC]}],"bg-color":[{bg:k()}],"gradient-from-pos":[{from:W()}],"gradient-via-pos":[{via:W()}],"gradient-to-pos":[{to:W()}],"gradient-from":[{from:k()}],"gradient-via":[{via:k()}],"gradient-to":[{to:k()}],rounded:[{rounded:I()}],"rounded-s":[{"rounded-s":I()}],"rounded-e":[{"rounded-e":I()}],"rounded-t":[{"rounded-t":I()}],"rounded-r":[{"rounded-r":I()}],"rounded-b":[{"rounded-b":I()}],"rounded-l":[{"rounded-l":I()}],"rounded-ss":[{"rounded-ss":I()}],"rounded-se":[{"rounded-se":I()}],"rounded-ee":[{"rounded-ee":I()}],"rounded-es":[{"rounded-es":I()}],"rounded-tl":[{"rounded-tl":I()}],"rounded-tr":[{"rounded-tr":I()}],"rounded-br":[{"rounded-br":I()}],"rounded-bl":[{"rounded-bl":I()}],"border-w":[{border:J()}],"border-w-x":[{"border-x":J()}],"border-w-y":[{"border-y":J()}],"border-w-s":[{"border-s":J()}],"border-w-e":[{"border-e":J()}],"border-w-t":[{"border-t":J()}],"border-w-r":[{"border-r":J()}],"border-w-b":[{"border-b":J()}],"border-w-l":[{"border-l":J()}],"divide-x":[{"divide-x":J()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":J()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...gt(),"hidden","none"]}],"divide-style":[{divide:[...gt(),"hidden","none"]}],"border-color":[{border:k()}],"border-color-x":[{"border-x":k()}],"border-color-y":[{"border-y":k()}],"border-color-s":[{"border-s":k()}],"border-color-e":[{"border-e":k()}],"border-color-t":[{"border-t":k()}],"border-color-r":[{"border-r":k()}],"border-color-b":[{"border-b":k()}],"border-color-l":[{"border-l":k()}],"divide-color":[{divide:k()}],"outline-style":[{outline:[...gt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[St,at,nt]}],"outline-w":[{outline:["",St,gr,Wa]}],"outline-color":[{outline:k()}],shadow:[{shadow:["","none",g,lo,ro]}],"shadow-color":[{shadow:k()}],"inset-shadow":[{"inset-shadow":["none",v,lo,ro]}],"inset-shadow-color":[{"inset-shadow":k()}],"ring-w":[{ring:J()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:k()}],"ring-offset-w":[{"ring-offset":[St,Wa]}],"ring-offset-color":[{"ring-offset":k()}],"inset-ring-w":[{"inset-ring":J()}],"inset-ring-color":[{"inset-ring":k()}],"text-shadow":[{"text-shadow":["none",x,lo,ro]}],"text-shadow-color":[{"text-shadow":k()}],opacity:[{opacity:[St,at,nt]}],"mix-blend":[{"mix-blend":[...lt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":lt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[St]}],"mask-image-linear-from-pos":[{"mask-linear-from":$()}],"mask-image-linear-to-pos":[{"mask-linear-to":$()}],"mask-image-linear-from-color":[{"mask-linear-from":k()}],"mask-image-linear-to-color":[{"mask-linear-to":k()}],"mask-image-t-from-pos":[{"mask-t-from":$()}],"mask-image-t-to-pos":[{"mask-t-to":$()}],"mask-image-t-from-color":[{"mask-t-from":k()}],"mask-image-t-to-color":[{"mask-t-to":k()}],"mask-image-r-from-pos":[{"mask-r-from":$()}],"mask-image-r-to-pos":[{"mask-r-to":$()}],"mask-image-r-from-color":[{"mask-r-from":k()}],"mask-image-r-to-color":[{"mask-r-to":k()}],"mask-image-b-from-pos":[{"mask-b-from":$()}],"mask-image-b-to-pos":[{"mask-b-to":$()}],"mask-image-b-from-color":[{"mask-b-from":k()}],"mask-image-b-to-color":[{"mask-b-to":k()}],"mask-image-l-from-pos":[{"mask-l-from":$()}],"mask-image-l-to-pos":[{"mask-l-to":$()}],"mask-image-l-from-color":[{"mask-l-from":k()}],"mask-image-l-to-color":[{"mask-l-to":k()}],"mask-image-x-from-pos":[{"mask-x-from":$()}],"mask-image-x-to-pos":[{"mask-x-to":$()}],"mask-image-x-from-color":[{"mask-x-from":k()}],"mask-image-x-to-color":[{"mask-x-to":k()}],"mask-image-y-from-pos":[{"mask-y-from":$()}],"mask-image-y-to-pos":[{"mask-y-to":$()}],"mask-image-y-from-color":[{"mask-y-from":k()}],"mask-image-y-to-color":[{"mask-y-to":k()}],"mask-image-radial":[{"mask-radial":[at,nt]}],"mask-image-radial-from-pos":[{"mask-radial-from":$()}],"mask-image-radial-to-pos":[{"mask-radial-to":$()}],"mask-image-radial-from-color":[{"mask-radial-from":k()}],"mask-image-radial-to-color":[{"mask-radial-to":k()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":H()}],"mask-image-conic-pos":[{"mask-conic":[St]}],"mask-image-conic-from-pos":[{"mask-conic-from":$()}],"mask-image-conic-to-pos":[{"mask-conic-to":$()}],"mask-image-conic-from-color":[{"mask-conic-from":k()}],"mask-image-conic-to-color":[{"mask-conic-to":k()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:it()}],"mask-repeat":[{mask:R()}],"mask-size":[{mask:X()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",at,nt]}],filter:[{filter:["","none",at,nt]}],blur:[{blur:ut()}],brightness:[{brightness:[St,at,nt]}],contrast:[{contrast:[St,at,nt]}],"drop-shadow":[{"drop-shadow":["","none",A,lo,ro]}],"drop-shadow-color":[{"drop-shadow":k()}],grayscale:[{grayscale:["",St,at,nt]}],"hue-rotate":[{"hue-rotate":[St,at,nt]}],invert:[{invert:["",St,at,nt]}],saturate:[{saturate:[St,at,nt]}],sepia:[{sepia:["",St,at,nt]}],"backdrop-filter":[{"backdrop-filter":["","none",at,nt]}],"backdrop-blur":[{"backdrop-blur":ut()}],"backdrop-brightness":[{"backdrop-brightness":[St,at,nt]}],"backdrop-contrast":[{"backdrop-contrast":[St,at,nt]}],"backdrop-grayscale":[{"backdrop-grayscale":["",St,at,nt]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[St,at,nt]}],"backdrop-invert":[{"backdrop-invert":["",St,at,nt]}],"backdrop-opacity":[{"backdrop-opacity":[St,at,nt]}],"backdrop-saturate":[{"backdrop-saturate":[St,at,nt]}],"backdrop-sepia":[{"backdrop-sepia":["",St,at,nt]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",at,nt]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[St,"initial",at,nt]}],ease:[{ease:["linear","initial",E,at,nt]}],delay:[{delay:[St,at,nt]}],animate:[{animate:["none",_,at,nt]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[w,at,nt]}],"perspective-origin":[{"perspective-origin":z()}],rotate:[{rotate:Nt()}],"rotate-x":[{"rotate-x":Nt()}],"rotate-y":[{"rotate-y":Nt()}],"rotate-z":[{"rotate-z":Nt()}],scale:[{scale:Ct()}],"scale-x":[{"scale-x":Ct()}],"scale-y":[{"scale-y":Ct()}],"scale-z":[{"scale-z":Ct()}],"scale-3d":["scale-3d"],skew:[{skew:wt()}],"skew-x":[{"skew-x":wt()}],"skew-y":[{"skew-y":wt()}],transform:[{transform:[at,nt,"","none","gpu","cpu"]}],"transform-origin":[{origin:z()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:At()}],"translate-x":[{"translate-x":At()}],"translate-y":[{"translate-y":At()}],"translate-z":[{"translate-z":At()}],"translate-none":["translate-none"],accent:[{accent:k()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:k()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",at,nt]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",at,nt]}],fill:[{fill:["none",...k()]}],"stroke-w":[{stroke:[St,gr,Wa,Af]}],stroke:[{stroke:["none",...k()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},wC=aC(TC);function Gt(...n){return wC(kb(n))}const AC=Hb("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function $a({className:n,variant:a,size:s,asChild:l=!1,...u}){const d=l?Bb:"button";return S.jsx(d,{"data-slot":"button",className:Gt(AC({variant:a,size:s,className:n})),...u})}function hn({className:n,...a}){return S.jsx("div",{"data-slot":"card",className:Gt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n),...a})}function mn({className:n,...a}){return S.jsx("div",{"data-slot":"card-header",className:Gt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n),...a})}function pn({className:n,...a}){return S.jsx("div",{"data-slot":"card-title",className:Gt("leading-none font-semibold",n),...a})}function gn({className:n,...a}){return S.jsx("div",{"data-slot":"card-content",className:Gt("px-6",n),...a})}function Qi({className:n,type:a,...s}){return S.jsx("input",{type:a,"data-slot":"input",className:Gt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n),...s})}var Ur=s0();const EC=a0(Ur);var MC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ut=MC.reduce((n,a)=>{const s=Nr(`Primitive.${a}`),l=T.forwardRef((u,d)=>{const{asChild:f,...h}=u,p=f?s:a;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(p,{...h,ref:d})});return l.displayName=`Primitive.${a}`,{...n,[a]:l}},{});function CC(n,a){n&&Ur.flushSync(()=>n.dispatchEvent(a))}var RC="Label",Wb=T.forwardRef((n,a)=>S.jsx(Ut.label,{...n,ref:a,onMouseDown:s=>{var u;s.target.closest("button, input, select, textarea")||((u=n.onMouseDown)==null||u.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));Wb.displayName=RC;var DC=Wb;function Ja({className:n,...a}){return S.jsx(DC,{"data-slot":"label",className:Gt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",n),...a})}function Rv(n,[a,s]){return Math.min(s,Math.max(a,n))}function Vt(n,a,{checkForDefaultPrevented:s=!0}={}){return function(u){if(n==null||n(u),s===!1||!u.defaultPrevented)return a==null?void 0:a(u)}}function kr(n,a=[]){let s=[];function l(d,f){const h=T.createContext(f),p=s.length;s=[...s,f];const m=v=>{var E;const{scope:x,children:A,...O}=v,w=((E=x==null?void 0:x[n])==null?void 0:E[p])||h,M=T.useMemo(()=>O,Object.values(O));return S.jsx(w.Provider,{value:M,children:A})};m.displayName=d+"Provider";function g(v,x){var w;const A=((w=x==null?void 0:x[n])==null?void 0:w[p])||h,O=T.useContext(A);if(O)return O;if(f!==void 0)return f;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[m,g]}const u=()=>{const d=s.map(f=>T.createContext(f));return function(h){const p=(h==null?void 0:h[n])||d;return T.useMemo(()=>({[`__scope${n}`]:{...h,[n]:p}}),[h,p])}};return u.scopeName=n,[l,OC(u,...a)]}function OC(...n){const a=n[0];if(n.length===1)return a;const s=()=>{const l=n.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(d){const f=l.reduce((h,{useScope:p,scopeName:m})=>{const v=p(d)[`__scope${m}`];return{...h,...v}},{});return T.useMemo(()=>({[`__scope${a.scopeName}`]:f}),[f])}};return s.scopeName=a.scopeName,s}function $b(n){const a=n+"CollectionProvider",[s,l]=kr(a),[u,d]=s(a,{collectionRef:{current:null},itemMap:new Map}),f=w=>{const{scope:M,children:E}=w,_=va.useRef(null),D=va.useRef(new Map).current;return S.jsx(u,{scope:M,itemMap:D,collectionRef:_,children:E})};f.displayName=a;const h=n+"CollectionSlot",p=Nr(h),m=va.forwardRef((w,M)=>{const{scope:E,children:_}=w,D=d(h,E),H=te(M,D.collectionRef);return S.jsx(p,{ref:H,children:_})});m.displayName=h;const g=n+"CollectionItemSlot",v="data-radix-collection-item",x=Nr(g),A=va.forwardRef((w,M)=>{const{scope:E,children:_,...D}=w,H=va.useRef(null),z=te(M,H),F=d(g,E);return va.useEffect(()=>(F.itemMap.set(H,{ref:H,...D}),()=>void F.itemMap.delete(H))),S.jsx(x,{[v]:"",ref:z,children:_})});A.displayName=g;function O(w){const M=d(n+"CollectionConsumer",w);return va.useCallback(()=>{const _=M.collectionRef.current;if(!_)return[];const D=Array.from(_.querySelectorAll(`[${v}]`));return Array.from(M.itemMap.values()).sort((F,Q)=>D.indexOf(F.ref.current)-D.indexOf(Q.ref.current))},[M.collectionRef,M.itemMap])}return[{Provider:f,Slot:m,ItemSlot:A},O,l]}var NC=T.createContext(void 0);function Id(n){const a=T.useContext(NC);return n||a||"ltr"}function wa(n){const a=T.useRef(n);return T.useEffect(()=>{a.current=n}),T.useMemo(()=>(...s)=>{var l;return(l=a.current)==null?void 0:l.call(a,...s)},[])}function _C(n,a=globalThis==null?void 0:globalThis.document){const s=wa(n);T.useEffect(()=>{const l=u=>{u.key==="Escape"&&s(u)};return a.addEventListener("keydown",l,{capture:!0}),()=>a.removeEventListener("keydown",l,{capture:!0})},[s,a])}var jC="DismissableLayer",sd="dismissableLayer.update",VC="dismissableLayer.pointerDownOutside",zC="dismissableLayer.focusOutside",Dv,Jb=T.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tx=T.forwardRef((n,a)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:h,...p}=n,m=T.useContext(Jb),[g,v]=T.useState(null),x=(g==null?void 0:g.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,A]=T.useState({}),O=te(a,Q=>v(Q)),w=Array.from(m.layers),[M]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),E=w.indexOf(M),_=g?w.indexOf(g):-1,D=m.layersWithOutsidePointerEventsDisabled.size>0,H=_>=E,z=UC(Q=>{const G=Q.target,tt=[...m.branches].some(rt=>rt.contains(G));!H||tt||(u==null||u(Q),f==null||f(Q),Q.defaultPrevented||h==null||h())},x),F=kC(Q=>{const G=Q.target;[...m.branches].some(rt=>rt.contains(G))||(d==null||d(Q),f==null||f(Q),Q.defaultPrevented||h==null||h())},x);return _C(Q=>{_===m.layers.size-1&&(l==null||l(Q),!Q.defaultPrevented&&h&&(Q.preventDefault(),h()))},x),T.useEffect(()=>{if(g)return s&&(m.layersWithOutsidePointerEventsDisabled.size===0&&(Dv=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(g)),m.layers.add(g),Ov(),()=>{s&&m.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=Dv)}},[g,x,s,m]),T.useEffect(()=>()=>{g&&(m.layers.delete(g),m.layersWithOutsidePointerEventsDisabled.delete(g),Ov())},[g,m]),T.useEffect(()=>{const Q=()=>A({});return document.addEventListener(sd,Q),()=>document.removeEventListener(sd,Q)},[]),S.jsx(Ut.div,{...p,ref:O,style:{pointerEvents:D?H?"auto":"none":void 0,...n.style},onFocusCapture:Vt(n.onFocusCapture,F.onFocusCapture),onBlurCapture:Vt(n.onBlurCapture,F.onBlurCapture),onPointerDownCapture:Vt(n.onPointerDownCapture,z.onPointerDownCapture)})});tx.displayName=jC;var LC="DismissableLayerBranch",BC=T.forwardRef((n,a)=>{const s=T.useContext(Jb),l=T.useRef(null),u=te(a,l);return T.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),S.jsx(Ut.div,{...n,ref:u})});BC.displayName=LC;function UC(n,a=globalThis==null?void 0:globalThis.document){const s=wa(n),l=T.useRef(!1),u=T.useRef(()=>{});return T.useEffect(()=>{const d=h=>{if(h.target&&!l.current){let p=function(){ex(VC,s,m,{discrete:!0})};const m={originalEvent:h};h.pointerType==="touch"?(a.removeEventListener("click",u.current),u.current=p,a.addEventListener("click",u.current,{once:!0})):p()}else a.removeEventListener("click",u.current);l.current=!1},f=window.setTimeout(()=>{a.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(f),a.removeEventListener("pointerdown",d),a.removeEventListener("click",u.current)}},[a,s]),{onPointerDownCapture:()=>l.current=!0}}function kC(n,a=globalThis==null?void 0:globalThis.document){const s=wa(n),l=T.useRef(!1);return T.useEffect(()=>{const u=d=>{d.target&&!l.current&&ex(zC,s,{originalEvent:d},{discrete:!1})};return a.addEventListener("focusin",u),()=>a.removeEventListener("focusin",u)},[a,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Ov(){const n=new CustomEvent(sd);document.dispatchEvent(n)}function ex(n,a,s,{discrete:l}){const u=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});a&&u.addEventListener(n,a,{once:!0}),l?CC(u,d):u.dispatchEvent(d)}var Ef=0;function HC(){T.useEffect(()=>{const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",n[0]??Nv()),document.body.insertAdjacentElement("beforeend",n[1]??Nv()),Ef++,()=>{Ef===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),Ef--}},[])}function Nv(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Mf="focusScope.autoFocusOnMount",Cf="focusScope.autoFocusOnUnmount",_v={bubbles:!1,cancelable:!0},PC="FocusScope",nx=T.forwardRef((n,a)=>{const{loop:s=!1,trapped:l=!1,onMountAutoFocus:u,onUnmountAutoFocus:d,...f}=n,[h,p]=T.useState(null),m=wa(u),g=wa(d),v=T.useRef(null),x=te(a,w=>p(w)),A=T.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;T.useEffect(()=>{if(l){let w=function(D){if(A.paused||!h)return;const H=D.target;h.contains(H)?v.current=H:xa(v.current,{select:!0})},M=function(D){if(A.paused||!h)return;const H=D.relatedTarget;H!==null&&(h.contains(H)||xa(v.current,{select:!0}))},E=function(D){if(document.activeElement===document.body)for(const z of D)z.removedNodes.length>0&&xa(h)};document.addEventListener("focusin",w),document.addEventListener("focusout",M);const _=new MutationObserver(E);return h&&_.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",w),document.removeEventListener("focusout",M),_.disconnect()}}},[l,h,A.paused]),T.useEffect(()=>{if(h){Vv.add(A);const w=document.activeElement;if(!h.contains(w)){const E=new CustomEvent(Mf,_v);h.addEventListener(Mf,m),h.dispatchEvent(E),E.defaultPrevented||(GC(ZC(ax(h)),{select:!0}),document.activeElement===w&&xa(h))}return()=>{h.removeEventListener(Mf,m),setTimeout(()=>{const E=new CustomEvent(Cf,_v);h.addEventListener(Cf,g),h.dispatchEvent(E),E.defaultPrevented||xa(w??document.body,{select:!0}),h.removeEventListener(Cf,g),Vv.remove(A)},0)}}},[h,m,g,A]);const O=T.useCallback(w=>{if(!s&&!l||A.paused)return;const M=w.key==="Tab"&&!w.altKey&&!w.ctrlKey&&!w.metaKey,E=document.activeElement;if(M&&E){const _=w.currentTarget,[D,H]=YC(_);D&&H?!w.shiftKey&&E===H?(w.preventDefault(),s&&xa(D,{select:!0})):w.shiftKey&&E===D&&(w.preventDefault(),s&&xa(H,{select:!0})):E===_&&w.preventDefault()}},[s,l,A.paused]);return S.jsx(Ut.div,{tabIndex:-1,...f,ref:x,onKeyDown:O})});nx.displayName=PC;function GC(n,{select:a=!1}={}){const s=document.activeElement;for(const l of n)if(xa(l,{select:a}),document.activeElement!==s)return}function YC(n){const a=ax(n),s=jv(a,n),l=jv(a.reverse(),n);return[s,l]}function ax(n){const a=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const u=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||u?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)a.push(s.currentNode);return a}function jv(n,a){for(const s of n)if(!qC(s,{upTo:a}))return s}function qC(n,{upTo:a}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(a!==void 0&&n===a)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function XC(n){return n instanceof HTMLInputElement&&"select"in n}function xa(n,{select:a=!1}={}){if(n&&n.focus){const s=document.activeElement;n.focus({preventScroll:!0}),n!==s&&XC(n)&&a&&n.select()}}var Vv=KC();function KC(){let n=[];return{add(a){const s=n[0];a!==s&&(s==null||s.pause()),n=zv(n,a),n.unshift(a)},remove(a){var s;n=zv(n,a),(s=n[0])==null||s.resume()}}}function zv(n,a){const s=[...n],l=s.indexOf(a);return l!==-1&&s.splice(l,1),s}function ZC(n){return n.filter(a=>a.tagName!=="A")}var be=globalThis!=null&&globalThis.document?T.useLayoutEffect:()=>{},QC=i0[" useId ".trim().toString()]||(()=>{}),FC=0;function Hr(n){const[a,s]=T.useState(QC());return be(()=>{s(l=>l??String(FC++))},[n]),n||(a?`radix-${a}`:"")}const IC=["top","right","bottom","left"],Aa=Math.min,ke=Math.max,Do=Math.round,oo=Math.floor,wn=n=>({x:n,y:n}),WC={left:"right",right:"left",bottom:"top",top:"bottom"},$C={start:"end",end:"start"};function rd(n,a,s){return ke(n,Aa(a,s))}function Xn(n,a){return typeof n=="function"?n(a):n}function Kn(n){return n.split("-")[0]}function hs(n){return n.split("-")[1]}function Wd(n){return n==="x"?"y":"x"}function $d(n){return n==="y"?"height":"width"}function Gn(n){return["top","bottom"].includes(Kn(n))?"y":"x"}function Jd(n){return Wd(Gn(n))}function JC(n,a,s){s===void 0&&(s=!1);const l=hs(n),u=Jd(n),d=$d(u);let f=u==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return a.reference[d]>a.floating[d]&&(f=Oo(f)),[f,Oo(f)]}function tR(n){const a=Oo(n);return[ld(n),a,ld(a)]}function ld(n){return n.replace(/start|end/g,a=>$C[a])}function eR(n,a,s){const l=["left","right"],u=["right","left"],d=["top","bottom"],f=["bottom","top"];switch(n){case"top":case"bottom":return s?a?u:l:a?l:u;case"left":case"right":return a?d:f;default:return[]}}function nR(n,a,s,l){const u=hs(n);let d=eR(Kn(n),s==="start",l);return u&&(d=d.map(f=>f+"-"+u),a&&(d=d.concat(d.map(ld)))),d}function Oo(n){return n.replace(/left|right|bottom|top/g,a=>WC[a])}function aR(n){return{top:0,right:0,bottom:0,left:0,...n}}function ix(n){return typeof n!="number"?aR(n):{top:n,right:n,bottom:n,left:n}}function No(n){const{x:a,y:s,width:l,height:u}=n;return{width:l,height:u,top:s,left:a,right:a+l,bottom:s+u,x:a,y:s}}function Lv(n,a,s){let{reference:l,floating:u}=n;const d=Gn(a),f=Jd(a),h=$d(f),p=Kn(a),m=d==="y",g=l.x+l.width/2-u.width/2,v=l.y+l.height/2-u.height/2,x=l[h]/2-u[h]/2;let A;switch(p){case"top":A={x:g,y:l.y-u.height};break;case"bottom":A={x:g,y:l.y+l.height};break;case"right":A={x:l.x+l.width,y:v};break;case"left":A={x:l.x-u.width,y:v};break;default:A={x:l.x,y:l.y}}switch(hs(a)){case"start":A[f]-=x*(s&&m?-1:1);break;case"end":A[f]+=x*(s&&m?-1:1);break}return A}const iR=async(n,a,s)=>{const{placement:l="bottom",strategy:u="absolute",middleware:d=[],platform:f}=s,h=d.filter(Boolean),p=await(f.isRTL==null?void 0:f.isRTL(a));let m=await f.getElementRects({reference:n,floating:a,strategy:u}),{x:g,y:v}=Lv(m,l,p),x=l,A={},O=0;for(let w=0;w<h.length;w++){const{name:M,fn:E}=h[w],{x:_,y:D,data:H,reset:z}=await E({x:g,y:v,initialPlacement:l,placement:x,strategy:u,middlewareData:A,rects:m,platform:f,elements:{reference:n,floating:a}});g=_??g,v=D??v,A={...A,[M]:{...A[M],...H}},z&&O<=50&&(O++,typeof z=="object"&&(z.placement&&(x=z.placement),z.rects&&(m=z.rects===!0?await f.getElementRects({reference:n,floating:a,strategy:u}):z.rects),{x:g,y:v}=Lv(m,x,p)),w=-1)}return{x:g,y:v,placement:x,strategy:u,middlewareData:A}};async function _r(n,a){var s;a===void 0&&(a={});const{x:l,y:u,platform:d,rects:f,elements:h,strategy:p}=n,{boundary:m="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:x=!1,padding:A=0}=Xn(a,n),O=ix(A),M=h[x?v==="floating"?"reference":"floating":v],E=No(await d.getClippingRect({element:(s=await(d.isElement==null?void 0:d.isElement(M)))==null||s?M:M.contextElement||await(d.getDocumentElement==null?void 0:d.getDocumentElement(h.floating)),boundary:m,rootBoundary:g,strategy:p})),_=v==="floating"?{x:l,y:u,width:f.floating.width,height:f.floating.height}:f.reference,D=await(d.getOffsetParent==null?void 0:d.getOffsetParent(h.floating)),H=await(d.isElement==null?void 0:d.isElement(D))?await(d.getScale==null?void 0:d.getScale(D))||{x:1,y:1}:{x:1,y:1},z=No(d.convertOffsetParentRelativeRectToViewportRelativeRect?await d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:_,offsetParent:D,strategy:p}):_);return{top:(E.top-z.top+O.top)/H.y,bottom:(z.bottom-E.bottom+O.bottom)/H.y,left:(E.left-z.left+O.left)/H.x,right:(z.right-E.right+O.right)/H.x}}const sR=n=>({name:"arrow",options:n,async fn(a){const{x:s,y:l,placement:u,rects:d,platform:f,elements:h,middlewareData:p}=a,{element:m,padding:g=0}=Xn(n,a)||{};if(m==null)return{};const v=ix(g),x={x:s,y:l},A=Jd(u),O=$d(A),w=await f.getDimensions(m),M=A==="y",E=M?"top":"left",_=M?"bottom":"right",D=M?"clientHeight":"clientWidth",H=d.reference[O]+d.reference[A]-x[A]-d.floating[O],z=x[A]-d.reference[A],F=await(f.getOffsetParent==null?void 0:f.getOffsetParent(m));let Q=F?F[D]:0;(!Q||!await(f.isElement==null?void 0:f.isElement(F)))&&(Q=h.floating[D]||d.floating[O]);const G=H/2-z/2,tt=Q/2-w[O]/2-1,rt=Aa(v[E],tt),ht=Aa(v[_],tt),ct=rt,yt=Q-w[O]-ht,vt=Q/2-w[O]/2+G,ot=rd(ct,vt,yt),V=!p.arrow&&hs(u)!=null&&vt!==ot&&d.reference[O]/2-(vt<ct?rt:ht)-w[O]/2<0,Y=V?vt<ct?vt-ct:vt-yt:0;return{[A]:x[A]+Y,data:{[A]:ot,centerOffset:vt-ot-Y,...V&&{alignmentOffset:Y}},reset:V}}}),rR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(a){var s,l;const{placement:u,middlewareData:d,rects:f,initialPlacement:h,platform:p,elements:m}=a,{mainAxis:g=!0,crossAxis:v=!0,fallbackPlacements:x,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:w=!0,...M}=Xn(n,a);if((s=d.arrow)!=null&&s.alignmentOffset)return{};const E=Kn(u),_=Gn(h),D=Kn(h)===h,H=await(p.isRTL==null?void 0:p.isRTL(m.floating)),z=x||(D||!w?[Oo(h)]:tR(h)),F=O!=="none";!x&&F&&z.push(...nR(h,w,O,H));const Q=[h,...z],G=await _r(a,M),tt=[];let rt=((l=d.flip)==null?void 0:l.overflows)||[];if(g&&tt.push(G[E]),v){const ot=JC(u,f,H);tt.push(G[ot[0]],G[ot[1]])}if(rt=[...rt,{placement:u,overflows:tt}],!tt.every(ot=>ot<=0)){var ht,ct;const ot=(((ht=d.flip)==null?void 0:ht.index)||0)+1,V=Q[ot];if(V){var yt;const k=v==="alignment"?_!==Gn(V):!1,it=((yt=rt[0])==null?void 0:yt.overflows[0])>0;if(!k||it)return{data:{index:ot,overflows:rt},reset:{placement:V}}}let Y=(ct=rt.filter(k=>k.overflows[0]<=0).sort((k,it)=>k.overflows[1]-it.overflows[1])[0])==null?void 0:ct.placement;if(!Y)switch(A){case"bestFit":{var vt;const k=(vt=rt.filter(it=>{if(F){const R=Gn(it.placement);return R===_||R==="y"}return!0}).map(it=>[it.placement,it.overflows.filter(R=>R>0).reduce((R,X)=>R+X,0)]).sort((it,R)=>it[1]-R[1])[0])==null?void 0:vt[0];k&&(Y=k);break}case"initialPlacement":Y=h;break}if(u!==Y)return{reset:{placement:Y}}}return{}}}};function Bv(n,a){return{top:n.top-a.height,right:n.right-a.width,bottom:n.bottom-a.height,left:n.left-a.width}}function Uv(n){return IC.some(a=>n[a]>=0)}const lR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(a){const{rects:s}=a,{strategy:l="referenceHidden",...u}=Xn(n,a);switch(l){case"referenceHidden":{const d=await _r(a,{...u,elementContext:"reference"}),f=Bv(d,s.reference);return{data:{referenceHiddenOffsets:f,referenceHidden:Uv(f)}}}case"escaped":{const d=await _r(a,{...u,altBoundary:!0}),f=Bv(d,s.floating);return{data:{escapedOffsets:f,escaped:Uv(f)}}}default:return{}}}}};async function oR(n,a){const{placement:s,platform:l,elements:u}=n,d=await(l.isRTL==null?void 0:l.isRTL(u.floating)),f=Kn(s),h=hs(s),p=Gn(s)==="y",m=["left","top"].includes(f)?-1:1,g=d&&p?-1:1,v=Xn(a,n);let{mainAxis:x,crossAxis:A,alignmentAxis:O}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return h&&typeof O=="number"&&(A=h==="end"?O*-1:O),p?{x:A*g,y:x*m}:{x:x*m,y:A*g}}const uR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(a){var s,l;const{x:u,y:d,placement:f,middlewareData:h}=a,p=await oR(a,n);return f===((s=h.offset)==null?void 0:s.placement)&&(l=h.arrow)!=null&&l.alignmentOffset?{}:{x:u+p.x,y:d+p.y,data:{...p,placement:f}}}}},cR=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(a){const{x:s,y:l,placement:u}=a,{mainAxis:d=!0,crossAxis:f=!1,limiter:h={fn:M=>{let{x:E,y:_}=M;return{x:E,y:_}}},...p}=Xn(n,a),m={x:s,y:l},g=await _r(a,p),v=Gn(Kn(u)),x=Wd(v);let A=m[x],O=m[v];if(d){const M=x==="y"?"top":"left",E=x==="y"?"bottom":"right",_=A+g[M],D=A-g[E];A=rd(_,A,D)}if(f){const M=v==="y"?"top":"left",E=v==="y"?"bottom":"right",_=O+g[M],D=O-g[E];O=rd(_,O,D)}const w=h.fn({...a,[x]:A,[v]:O});return{...w,data:{x:w.x-s,y:w.y-l,enabled:{[x]:d,[v]:f}}}}}},fR=function(n){return n===void 0&&(n={}),{options:n,fn(a){const{x:s,y:l,placement:u,rects:d,middlewareData:f}=a,{offset:h=0,mainAxis:p=!0,crossAxis:m=!0}=Xn(n,a),g={x:s,y:l},v=Gn(u),x=Wd(v);let A=g[x],O=g[v];const w=Xn(h,a),M=typeof w=="number"?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(p){const D=x==="y"?"height":"width",H=d.reference[x]-d.floating[D]+M.mainAxis,z=d.reference[x]+d.reference[D]-M.mainAxis;A<H?A=H:A>z&&(A=z)}if(m){var E,_;const D=x==="y"?"width":"height",H=["top","left"].includes(Kn(u)),z=d.reference[v]-d.floating[D]+(H&&((E=f.offset)==null?void 0:E[v])||0)+(H?0:M.crossAxis),F=d.reference[v]+d.reference[D]+(H?0:((_=f.offset)==null?void 0:_[v])||0)-(H?M.crossAxis:0);O<z?O=z:O>F&&(O=F)}return{[x]:A,[v]:O}}}},dR=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(a){var s,l;const{placement:u,rects:d,platform:f,elements:h}=a,{apply:p=()=>{},...m}=Xn(n,a),g=await _r(a,m),v=Kn(u),x=hs(u),A=Gn(u)==="y",{width:O,height:w}=d.floating;let M,E;v==="top"||v==="bottom"?(M=v,E=x===(await(f.isRTL==null?void 0:f.isRTL(h.floating))?"start":"end")?"left":"right"):(E=v,M=x==="end"?"top":"bottom");const _=w-g.top-g.bottom,D=O-g.left-g.right,H=Aa(w-g[M],_),z=Aa(O-g[E],D),F=!a.middlewareData.shift;let Q=H,G=z;if((s=a.middlewareData.shift)!=null&&s.enabled.x&&(G=D),(l=a.middlewareData.shift)!=null&&l.enabled.y&&(Q=_),F&&!x){const rt=ke(g.left,0),ht=ke(g.right,0),ct=ke(g.top,0),yt=ke(g.bottom,0);A?G=O-2*(rt!==0||ht!==0?rt+ht:ke(g.left,g.right)):Q=w-2*(ct!==0||yt!==0?ct+yt:ke(g.top,g.bottom))}await p({...a,availableWidth:G,availableHeight:Q});const tt=await f.getDimensions(h.floating);return O!==tt.width||w!==tt.height?{reset:{rects:!0}}:{}}}};function Uo(){return typeof window<"u"}function ms(n){return sx(n)?(n.nodeName||"").toLowerCase():"#document"}function He(n){var a;return(n==null||(a=n.ownerDocument)==null?void 0:a.defaultView)||window}function En(n){var a;return(a=(sx(n)?n.ownerDocument:n.document)||window.document)==null?void 0:a.documentElement}function sx(n){return Uo()?n instanceof Node||n instanceof He(n).Node:!1}function sn(n){return Uo()?n instanceof Element||n instanceof He(n).Element:!1}function An(n){return Uo()?n instanceof HTMLElement||n instanceof He(n).HTMLElement:!1}function kv(n){return!Uo()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof He(n).ShadowRoot}function Pr(n){const{overflow:a,overflowX:s,overflowY:l,display:u}=rn(n);return/auto|scroll|overlay|hidden|clip/.test(a+l+s)&&!["inline","contents"].includes(u)}function hR(n){return["table","td","th"].includes(ms(n))}function ko(n){return[":popover-open",":modal"].some(a=>{try{return n.matches(a)}catch{return!1}})}function th(n){const a=eh(),s=sn(n)?rn(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function mR(n){let a=Ea(n);for(;An(a)&&!ls(a);){if(th(a))return a;if(ko(a))return null;a=Ea(a)}return null}function eh(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ls(n){return["html","body","#document"].includes(ms(n))}function rn(n){return He(n).getComputedStyle(n)}function Ho(n){return sn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Ea(n){if(ms(n)==="html")return n;const a=n.assignedSlot||n.parentNode||kv(n)&&n.host||En(n);return kv(a)?a.host:a}function rx(n){const a=Ea(n);return ls(a)?n.ownerDocument?n.ownerDocument.body:n.body:An(a)&&Pr(a)?a:rx(a)}function jr(n,a,s){var l;a===void 0&&(a=[]),s===void 0&&(s=!0);const u=rx(n),d=u===((l=n.ownerDocument)==null?void 0:l.body),f=He(u);if(d){const h=od(f);return a.concat(f,f.visualViewport||[],Pr(u)?u:[],h&&s?jr(h):[])}return a.concat(u,jr(u,[],s))}function od(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function lx(n){const a=rn(n);let s=parseFloat(a.width)||0,l=parseFloat(a.height)||0;const u=An(n),d=u?n.offsetWidth:s,f=u?n.offsetHeight:l,h=Do(s)!==d||Do(l)!==f;return h&&(s=d,l=f),{width:s,height:l,$:h}}function nh(n){return sn(n)?n:n.contextElement}function as(n){const a=nh(n);if(!An(a))return wn(1);const s=a.getBoundingClientRect(),{width:l,height:u,$:d}=lx(a);let f=(d?Do(s.width):s.width)/l,h=(d?Do(s.height):s.height)/u;return(!f||!Number.isFinite(f))&&(f=1),(!h||!Number.isFinite(h))&&(h=1),{x:f,y:h}}const pR=wn(0);function ox(n){const a=He(n);return!eh()||!a.visualViewport?pR:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function gR(n,a,s){return a===void 0&&(a=!1),!s||a&&s!==He(n)?!1:a}function si(n,a,s,l){a===void 0&&(a=!1),s===void 0&&(s=!1);const u=n.getBoundingClientRect(),d=nh(n);let f=wn(1);a&&(l?sn(l)&&(f=as(l)):f=as(n));const h=gR(d,s,l)?ox(d):wn(0);let p=(u.left+h.x)/f.x,m=(u.top+h.y)/f.y,g=u.width/f.x,v=u.height/f.y;if(d){const x=He(d),A=l&&sn(l)?He(l):l;let O=x,w=od(O);for(;w&&l&&A!==O;){const M=as(w),E=w.getBoundingClientRect(),_=rn(w),D=E.left+(w.clientLeft+parseFloat(_.paddingLeft))*M.x,H=E.top+(w.clientTop+parseFloat(_.paddingTop))*M.y;p*=M.x,m*=M.y,g*=M.x,v*=M.y,p+=D,m+=H,O=He(w),w=od(O)}}return No({width:g,height:v,x:p,y:m})}function ah(n,a){const s=Ho(n).scrollLeft;return a?a.left+s:si(En(n)).left+s}function ux(n,a,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),u=l.left+a.scrollLeft-(s?0:ah(n,l)),d=l.top+a.scrollTop;return{x:u,y:d}}function yR(n){let{elements:a,rect:s,offsetParent:l,strategy:u}=n;const d=u==="fixed",f=En(l),h=a?ko(a.floating):!1;if(l===f||h&&d)return s;let p={scrollLeft:0,scrollTop:0},m=wn(1);const g=wn(0),v=An(l);if((v||!v&&!d)&&((ms(l)!=="body"||Pr(f))&&(p=Ho(l)),An(l))){const A=si(l);m=as(l),g.x=A.x+l.clientLeft,g.y=A.y+l.clientTop}const x=f&&!v&&!d?ux(f,p,!0):wn(0);return{width:s.width*m.x,height:s.height*m.y,x:s.x*m.x-p.scrollLeft*m.x+g.x+x.x,y:s.y*m.y-p.scrollTop*m.y+g.y+x.y}}function vR(n){return Array.from(n.getClientRects())}function bR(n){const a=En(n),s=Ho(n),l=n.ownerDocument.body,u=ke(a.scrollWidth,a.clientWidth,l.scrollWidth,l.clientWidth),d=ke(a.scrollHeight,a.clientHeight,l.scrollHeight,l.clientHeight);let f=-s.scrollLeft+ah(n);const h=-s.scrollTop;return rn(l).direction==="rtl"&&(f+=ke(a.clientWidth,l.clientWidth)-u),{width:u,height:d,x:f,y:h}}function xR(n,a){const s=He(n),l=En(n),u=s.visualViewport;let d=l.clientWidth,f=l.clientHeight,h=0,p=0;if(u){d=u.width,f=u.height;const m=eh();(!m||m&&a==="fixed")&&(h=u.offsetLeft,p=u.offsetTop)}return{width:d,height:f,x:h,y:p}}function SR(n,a){const s=si(n,!0,a==="fixed"),l=s.top+n.clientTop,u=s.left+n.clientLeft,d=An(n)?as(n):wn(1),f=n.clientWidth*d.x,h=n.clientHeight*d.y,p=u*d.x,m=l*d.y;return{width:f,height:h,x:p,y:m}}function Hv(n,a,s){let l;if(a==="viewport")l=xR(n,s);else if(a==="document")l=bR(En(n));else if(sn(a))l=SR(a,s);else{const u=ox(n);l={x:a.x-u.x,y:a.y-u.y,width:a.width,height:a.height}}return No(l)}function cx(n,a){const s=Ea(n);return s===a||!sn(s)||ls(s)?!1:rn(s).position==="fixed"||cx(s,a)}function TR(n,a){const s=a.get(n);if(s)return s;let l=jr(n,[],!1).filter(h=>sn(h)&&ms(h)!=="body"),u=null;const d=rn(n).position==="fixed";let f=d?Ea(n):n;for(;sn(f)&&!ls(f);){const h=rn(f),p=th(f);!p&&h.position==="fixed"&&(u=null),(d?!p&&!u:!p&&h.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||Pr(f)&&!p&&cx(n,f))?l=l.filter(g=>g!==f):u=h,f=Ea(f)}return a.set(n,l),l}function wR(n){let{element:a,boundary:s,rootBoundary:l,strategy:u}=n;const f=[...s==="clippingAncestors"?ko(a)?[]:TR(a,this._c):[].concat(s),l],h=f[0],p=f.reduce((m,g)=>{const v=Hv(a,g,u);return m.top=ke(v.top,m.top),m.right=Aa(v.right,m.right),m.bottom=Aa(v.bottom,m.bottom),m.left=ke(v.left,m.left),m},Hv(a,h,u));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function AR(n){const{width:a,height:s}=lx(n);return{width:a,height:s}}function ER(n,a,s){const l=An(a),u=En(a),d=s==="fixed",f=si(n,!0,d,a);let h={scrollLeft:0,scrollTop:0};const p=wn(0);function m(){p.x=ah(u)}if(l||!l&&!d)if((ms(a)!=="body"||Pr(u))&&(h=Ho(a)),l){const A=si(a,!0,d,a);p.x=A.x+a.clientLeft,p.y=A.y+a.clientTop}else u&&m();d&&!l&&u&&m();const g=u&&!l&&!d?ux(u,h):wn(0),v=f.left+h.scrollLeft-p.x-g.x,x=f.top+h.scrollTop-p.y-g.y;return{x:v,y:x,width:f.width,height:f.height}}function Rf(n){return rn(n).position==="static"}function Pv(n,a){if(!An(n)||rn(n).position==="fixed")return null;if(a)return a(n);let s=n.offsetParent;return En(n)===s&&(s=s.ownerDocument.body),s}function fx(n,a){const s=He(n);if(ko(n))return s;if(!An(n)){let u=Ea(n);for(;u&&!ls(u);){if(sn(u)&&!Rf(u))return u;u=Ea(u)}return s}let l=Pv(n,a);for(;l&&hR(l)&&Rf(l);)l=Pv(l,a);return l&&ls(l)&&Rf(l)&&!th(l)?s:l||mR(n)||s}const MR=async function(n){const a=this.getOffsetParent||fx,s=this.getDimensions,l=await s(n.floating);return{reference:ER(n.reference,await a(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}};function CR(n){return rn(n).direction==="rtl"}const RR={convertOffsetParentRelativeRectToViewportRelativeRect:yR,getDocumentElement:En,getClippingRect:wR,getOffsetParent:fx,getElementRects:MR,getClientRects:vR,getDimensions:AR,getScale:as,isElement:sn,isRTL:CR};function dx(n,a){return n.x===a.x&&n.y===a.y&&n.width===a.width&&n.height===a.height}function DR(n,a){let s=null,l;const u=En(n);function d(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function f(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),d();const m=n.getBoundingClientRect(),{left:g,top:v,width:x,height:A}=m;if(h||a(),!x||!A)return;const O=oo(v),w=oo(u.clientWidth-(g+x)),M=oo(u.clientHeight-(v+A)),E=oo(g),D={rootMargin:-O+"px "+-w+"px "+-M+"px "+-E+"px",threshold:ke(0,Aa(1,p))||1};let H=!0;function z(F){const Q=F[0].intersectionRatio;if(Q!==p){if(!H)return f();Q?f(!1,Q):l=setTimeout(()=>{f(!1,1e-7)},1e3)}Q===1&&!dx(m,n.getBoundingClientRect())&&f(),H=!1}try{s=new IntersectionObserver(z,{...D,root:u.ownerDocument})}catch{s=new IntersectionObserver(z,D)}s.observe(n)}return f(!0),d}function OR(n,a,s,l){l===void 0&&(l={});const{ancestorScroll:u=!0,ancestorResize:d=!0,elementResize:f=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=l,m=nh(n),g=u||d?[...m?jr(m):[],...jr(a)]:[];g.forEach(E=>{u&&E.addEventListener("scroll",s,{passive:!0}),d&&E.addEventListener("resize",s)});const v=m&&h?DR(m,s):null;let x=-1,A=null;f&&(A=new ResizeObserver(E=>{let[_]=E;_&&_.target===m&&A&&(A.unobserve(a),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var D;(D=A)==null||D.observe(a)})),s()}),m&&!p&&A.observe(m),A.observe(a));let O,w=p?si(n):null;p&&M();function M(){const E=si(n);w&&!dx(w,E)&&s(),w=E,O=requestAnimationFrame(M)}return s(),()=>{var E;g.forEach(_=>{u&&_.removeEventListener("scroll",s),d&&_.removeEventListener("resize",s)}),v==null||v(),(E=A)==null||E.disconnect(),A=null,p&&cancelAnimationFrame(O)}}const NR=uR,_R=cR,jR=rR,VR=dR,zR=lR,Gv=sR,LR=fR,BR=(n,a,s)=>{const l=new Map,u={platform:RR,...s},d={...u.platform,_c:l};return iR(n,a,{...u,platform:d})};var So=typeof document<"u"?T.useLayoutEffect:T.useEffect;function _o(n,a){if(n===a)return!0;if(typeof n!=typeof a)return!1;if(typeof n=="function"&&n.toString()===a.toString())return!0;let s,l,u;if(n&&a&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==a.length)return!1;for(l=s;l--!==0;)if(!_o(n[l],a[l]))return!1;return!0}if(u=Object.keys(n),s=u.length,s!==Object.keys(a).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(a,u[l]))return!1;for(l=s;l--!==0;){const d=u[l];if(!(d==="_owner"&&n.$$typeof)&&!_o(n[d],a[d]))return!1}return!0}return n!==n&&a!==a}function hx(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Yv(n,a){const s=hx(n);return Math.round(a*s)/s}function Df(n){const a=T.useRef(n);return So(()=>{a.current=n}),a}function UR(n){n===void 0&&(n={});const{placement:a="bottom",strategy:s="absolute",middleware:l=[],platform:u,elements:{reference:d,floating:f}={},transform:h=!0,whileElementsMounted:p,open:m}=n,[g,v]=T.useState({x:0,y:0,strategy:s,placement:a,middlewareData:{},isPositioned:!1}),[x,A]=T.useState(l);_o(x,l)||A(l);const[O,w]=T.useState(null),[M,E]=T.useState(null),_=T.useCallback(k=>{k!==F.current&&(F.current=k,w(k))},[]),D=T.useCallback(k=>{k!==Q.current&&(Q.current=k,E(k))},[]),H=d||O,z=f||M,F=T.useRef(null),Q=T.useRef(null),G=T.useRef(g),tt=p!=null,rt=Df(p),ht=Df(u),ct=Df(m),yt=T.useCallback(()=>{if(!F.current||!Q.current)return;const k={placement:a,strategy:s,middleware:x};ht.current&&(k.platform=ht.current),BR(F.current,Q.current,k).then(it=>{const R={...it,isPositioned:ct.current!==!1};vt.current&&!_o(G.current,R)&&(G.current=R,Ur.flushSync(()=>{v(R)}))})},[x,a,s,ht,ct]);So(()=>{m===!1&&G.current.isPositioned&&(G.current.isPositioned=!1,v(k=>({...k,isPositioned:!1})))},[m]);const vt=T.useRef(!1);So(()=>(vt.current=!0,()=>{vt.current=!1}),[]),So(()=>{if(H&&(F.current=H),z&&(Q.current=z),H&&z){if(rt.current)return rt.current(H,z,yt);yt()}},[H,z,yt,rt,tt]);const ot=T.useMemo(()=>({reference:F,floating:Q,setReference:_,setFloating:D}),[_,D]),V=T.useMemo(()=>({reference:H,floating:z}),[H,z]),Y=T.useMemo(()=>{const k={position:s,left:0,top:0};if(!V.floating)return k;const it=Yv(V.floating,g.x),R=Yv(V.floating,g.y);return h?{...k,transform:"translate("+it+"px, "+R+"px)",...hx(V.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:it,top:R}},[s,h,V.floating,g.x,g.y]);return T.useMemo(()=>({...g,update:yt,refs:ot,elements:V,floatingStyles:Y}),[g,yt,ot,V,Y])}const kR=n=>{function a(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:u}=typeof n=="function"?n(s):n;return l&&a(l)?l.current!=null?Gv({element:l.current,padding:u}).fn(s):{}:l?Gv({element:l,padding:u}).fn(s):{}}}},HR=(n,a)=>({...NR(n),options:[n,a]}),PR=(n,a)=>({..._R(n),options:[n,a]}),GR=(n,a)=>({...LR(n),options:[n,a]}),YR=(n,a)=>({...jR(n),options:[n,a]}),qR=(n,a)=>({...VR(n),options:[n,a]}),XR=(n,a)=>({...zR(n),options:[n,a]}),KR=(n,a)=>({...kR(n),options:[n,a]});var ZR="Arrow",mx=T.forwardRef((n,a)=>{const{children:s,width:l=10,height:u=5,...d}=n;return S.jsx(Ut.svg,{...d,ref:a,width:l,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:S.jsx("polygon",{points:"0,0 30,0 15,10"})})});mx.displayName=ZR;var QR=mx;function FR(n){const[a,s]=T.useState(void 0);return be(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const d=u[0];let f,h;if("borderBoxSize"in d){const p=d.borderBoxSize,m=Array.isArray(p)?p[0]:p;f=m.inlineSize,h=m.blockSize}else f=n.offsetWidth,h=n.offsetHeight;s({width:f,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),a}var ih="Popper",[px,gx]=kr(ih),[IR,yx]=px(ih),vx=n=>{const{__scopePopper:a,children:s}=n,[l,u]=T.useState(null);return S.jsx(IR,{scope:a,anchor:l,onAnchorChange:u,children:s})};vx.displayName=ih;var bx="PopperAnchor",xx=T.forwardRef((n,a)=>{const{__scopePopper:s,virtualRef:l,...u}=n,d=yx(bx,s),f=T.useRef(null),h=te(a,f);return T.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||f.current)}),l?null:S.jsx(Ut.div,{...u,ref:h})});xx.displayName=bx;var sh="PopperContent",[WR,$R]=px(sh),Sx=T.forwardRef((n,a)=>{var $,ut,Nt,Ct,wt,At;const{__scopePopper:s,side:l="bottom",sideOffset:u=0,align:d="center",alignOffset:f=0,arrowPadding:h=0,avoidCollisions:p=!0,collisionBoundary:m=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:x=!1,updatePositionStrategy:A="optimized",onPlaced:O,...w}=n,M=yx(sh,s),[E,_]=T.useState(null),D=te(a,oe=>_(oe)),[H,z]=T.useState(null),F=FR(H),Q=(F==null?void 0:F.width)??0,G=(F==null?void 0:F.height)??0,tt=l+(d!=="center"?"-"+d:""),rt=typeof g=="number"?g:{top:0,right:0,bottom:0,left:0,...g},ht=Array.isArray(m)?m:[m],ct=ht.length>0,yt={padding:rt,boundary:ht.filter(tD),altBoundary:ct},{refs:vt,floatingStyles:ot,placement:V,isPositioned:Y,middlewareData:k}=UR({strategy:"fixed",placement:tt,whileElementsMounted:(...oe)=>OR(...oe,{animationFrame:A==="always"}),elements:{reference:M.anchor},middleware:[HR({mainAxis:u+G,alignmentAxis:f}),p&&PR({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?GR():void 0,...yt}),p&&YR({...yt}),qR({...yt,apply:({elements:oe,rects:we,availableWidth:Da,availableHeight:Oa})=>{const{width:he,height:Ko}=we.reference,Na=oe.floating.style;Na.setProperty("--radix-popper-available-width",`${Da}px`),Na.setProperty("--radix-popper-available-height",`${Oa}px`),Na.setProperty("--radix-popper-anchor-width",`${he}px`),Na.setProperty("--radix-popper-anchor-height",`${Ko}px`)}}),H&&KR({element:H,padding:h}),eD({arrowWidth:Q,arrowHeight:G}),x&&XR({strategy:"referenceHidden",...yt})]}),[it,R]=Ax(V),X=wa(O);be(()=>{Y&&(X==null||X())},[Y,X]);const W=($=k.arrow)==null?void 0:$.x,I=(ut=k.arrow)==null?void 0:ut.y,J=((Nt=k.arrow)==null?void 0:Nt.centerOffset)!==0,[gt,lt]=T.useState();return be(()=>{E&&lt(window.getComputedStyle(E).zIndex)},[E]),S.jsx("div",{ref:vt.setFloating,"data-radix-popper-content-wrapper":"",style:{...ot,transform:Y?ot.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:gt,"--radix-popper-transform-origin":[(Ct=k.transformOrigin)==null?void 0:Ct.x,(wt=k.transformOrigin)==null?void 0:wt.y].join(" "),...((At=k.hide)==null?void 0:At.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:S.jsx(WR,{scope:s,placedSide:it,onArrowChange:z,arrowX:W,arrowY:I,shouldHideArrow:J,children:S.jsx(Ut.div,{"data-side":it,"data-align":R,...w,ref:D,style:{...w.style,animation:Y?void 0:"none"}})})})});Sx.displayName=sh;var Tx="PopperArrow",JR={top:"bottom",right:"left",bottom:"top",left:"right"},wx=T.forwardRef(function(a,s){const{__scopePopper:l,...u}=a,d=$R(Tx,l),f=JR[d.placedSide];return S.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:S.jsx(QR,{...u,ref:s,style:{...u.style,display:"block"}})})});wx.displayName=Tx;function tD(n){return n!==null}var eD=n=>({name:"transformOrigin",options:n,fn(a){var M,E,_;const{placement:s,rects:l,middlewareData:u}=a,f=((M=u.arrow)==null?void 0:M.centerOffset)!==0,h=f?0:n.arrowWidth,p=f?0:n.arrowHeight,[m,g]=Ax(s),v={start:"0%",center:"50%",end:"100%"}[g],x=(((E=u.arrow)==null?void 0:E.x)??0)+h/2,A=(((_=u.arrow)==null?void 0:_.y)??0)+p/2;let O="",w="";return m==="bottom"?(O=f?v:`${x}px`,w=`${-p}px`):m==="top"?(O=f?v:`${x}px`,w=`${l.floating.height+p}px`):m==="right"?(O=`${-p}px`,w=f?v:`${A}px`):m==="left"&&(O=`${l.floating.width+p}px`,w=f?v:`${A}px`),{data:{x:O,y:w}}}});function Ax(n){const[a,s="center"]=n.split("-");return[a,s]}var nD=vx,aD=xx,iD=Sx,sD=wx,rD="Portal",Ex=T.forwardRef((n,a)=>{var h;const{container:s,...l}=n,[u,d]=T.useState(!1);be(()=>d(!0),[]);const f=s||u&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return f?EC.createPortal(S.jsx(Ut.div,{...l,ref:a}),f):null});Ex.displayName=rD;var lD=i0[" useInsertionEffect ".trim().toString()]||be;function jo({prop:n,defaultProp:a,onChange:s=()=>{},caller:l}){const[u,d,f]=oD({defaultProp:a,onChange:s}),h=n!==void 0,p=h?n:u;{const g=T.useRef(n!==void 0);T.useEffect(()=>{const v=g.current;v!==h&&console.warn(`${l} is changing from ${v?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=h},[h,l])}const m=T.useCallback(g=>{var v;if(h){const x=uD(g)?g(n):g;x!==n&&((v=f.current)==null||v.call(f,x))}else d(g)},[h,n,d,f]);return[p,m]}function oD({defaultProp:n,onChange:a}){const[s,l]=T.useState(n),u=T.useRef(s),d=T.useRef(a);return lD(()=>{d.current=a},[a]),T.useEffect(()=>{var f;u.current!==s&&((f=d.current)==null||f.call(d,s),u.current=s)},[s,u]),[s,l,d]}function uD(n){return typeof n=="function"}function cD(n){const a=T.useRef({value:n,previous:n});return T.useMemo(()=>(a.current.value!==n&&(a.current.previous=a.current.value,a.current.value=n),a.current.previous),[n])}var Mx=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),fD="VisuallyHidden",dD=T.forwardRef((n,a)=>S.jsx(Ut.span,{...n,ref:a,style:{...Mx,...n.style}}));dD.displayName=fD;var hD=function(n){if(typeof document>"u")return null;var a=Array.isArray(n)?n[0]:n;return a.ownerDocument.body},Fi=new WeakMap,uo=new WeakMap,co={},Of=0,Cx=function(n){return n&&(n.host||Cx(n.parentNode))},mD=function(n,a){return a.map(function(s){if(n.contains(s))return s;var l=Cx(s);return l&&n.contains(l)?l:(console.error("aria-hidden",s,"in not contained inside",n,". Doing nothing"),null)}).filter(function(s){return!!s})},pD=function(n,a,s,l){var u=mD(a,Array.isArray(n)?n:[n]);co[s]||(co[s]=new WeakMap);var d=co[s],f=[],h=new Set,p=new Set(u),m=function(v){!v||h.has(v)||(h.add(v),m(v.parentNode))};u.forEach(m);var g=function(v){!v||p.has(v)||Array.prototype.forEach.call(v.children,function(x){if(h.has(x))g(x);else try{var A=x.getAttribute(l),O=A!==null&&A!=="false",w=(Fi.get(x)||0)+1,M=(d.get(x)||0)+1;Fi.set(x,w),d.set(x,M),f.push(x),w===1&&O&&uo.set(x,!0),M===1&&x.setAttribute(s,"true"),O||x.setAttribute(l,"true")}catch(E){console.error("aria-hidden: cannot operate on ",x,E)}})};return g(a),h.clear(),Of++,function(){f.forEach(function(v){var x=Fi.get(v)-1,A=d.get(v)-1;Fi.set(v,x),d.set(v,A),x||(uo.has(v)||v.removeAttribute(l),uo.delete(v)),A||v.removeAttribute(s)}),Of--,Of||(Fi=new WeakMap,Fi=new WeakMap,uo=new WeakMap,co={})}},gD=function(n,a,s){s===void 0&&(s="data-aria-hidden");var l=Array.from(Array.isArray(n)?n:[n]),u=hD(n);return u?(l.push.apply(l,Array.from(u.querySelectorAll("[aria-live]"))),pD(l,u,s,"aria-hidden")):function(){return null}},bn=function(){return bn=Object.assign||function(a){for(var s,l=1,u=arguments.length;l<u;l++){s=arguments[l];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(a[d]=s[d])}return a},bn.apply(this,arguments)};function Rx(n,a){var s={};for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,l=Object.getOwnPropertySymbols(n);u<l.length;u++)a.indexOf(l[u])<0&&Object.prototype.propertyIsEnumerable.call(n,l[u])&&(s[l[u]]=n[l[u]]);return s}function yD(n,a,s){if(s||arguments.length===2)for(var l=0,u=a.length,d;l<u;l++)(d||!(l in a))&&(d||(d=Array.prototype.slice.call(a,0,l)),d[l]=a[l]);return n.concat(d||Array.prototype.slice.call(a))}var To="right-scroll-bar-position",wo="width-before-scroll-bar",vD="with-scroll-bars-hidden",bD="--removed-body-scroll-bar-size";function Nf(n,a){return typeof n=="function"?n(a):n&&(n.current=a),n}function xD(n,a){var s=T.useState(function(){return{value:n,callback:a,facade:{get current(){return s.value},set current(l){var u=s.value;u!==l&&(s.value=l,s.callback(l,u))}}}})[0];return s.callback=a,s.facade}var SD=typeof window<"u"?T.useLayoutEffect:T.useEffect,qv=new WeakMap;function TD(n,a){var s=xD(null,function(l){return n.forEach(function(u){return Nf(u,l)})});return SD(function(){var l=qv.get(s);if(l){var u=new Set(l),d=new Set(n),f=s.current;u.forEach(function(h){d.has(h)||Nf(h,null)}),d.forEach(function(h){u.has(h)||Nf(h,f)})}qv.set(s,n)},[n]),s}function wD(n){return n}function AD(n,a){a===void 0&&(a=wD);var s=[],l=!1,u={read:function(){if(l)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:n},useMedium:function(d){var f=a(d,l);return s.push(f),function(){s=s.filter(function(h){return h!==f})}},assignSyncMedium:function(d){for(l=!0;s.length;){var f=s;s=[],f.forEach(d)}s={push:function(h){return d(h)},filter:function(){return s}}},assignMedium:function(d){l=!0;var f=[];if(s.length){var h=s;s=[],h.forEach(d),f=s}var p=function(){var g=f;f=[],g.forEach(d)},m=function(){return Promise.resolve().then(p)};m(),s={push:function(g){f.push(g),m()},filter:function(g){return f=f.filter(g),s}}}};return u}function ED(n){n===void 0&&(n={});var a=AD(null);return a.options=bn({async:!0,ssr:!1},n),a}var Dx=function(n){var a=n.sideCar,s=Rx(n,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var l=a.read();if(!l)throw new Error("Sidecar medium not found");return T.createElement(l,bn({},s))};Dx.isSideCarExport=!0;function MD(n,a){return n.useMedium(a),Dx}var Ox=ED(),_f=function(){},Po=T.forwardRef(function(n,a){var s=T.useRef(null),l=T.useState({onScrollCapture:_f,onWheelCapture:_f,onTouchMoveCapture:_f}),u=l[0],d=l[1],f=n.forwardProps,h=n.children,p=n.className,m=n.removeScrollBar,g=n.enabled,v=n.shards,x=n.sideCar,A=n.noIsolation,O=n.inert,w=n.allowPinchZoom,M=n.as,E=M===void 0?"div":M,_=n.gapMode,D=Rx(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),H=x,z=TD([s,a]),F=bn(bn({},D),u);return T.createElement(T.Fragment,null,g&&T.createElement(H,{sideCar:Ox,removeScrollBar:m,shards:v,noIsolation:A,inert:O,setCallbacks:d,allowPinchZoom:!!w,lockRef:s,gapMode:_}),f?T.cloneElement(T.Children.only(h),bn(bn({},F),{ref:z})):T.createElement(E,bn({},F,{className:p,ref:z}),h))});Po.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Po.classNames={fullWidth:wo,zeroRight:To};var CD=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function RD(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var a=CD();return a&&n.setAttribute("nonce",a),n}function DD(n,a){n.styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a))}function OD(n){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(n)}var ND=function(){var n=0,a=null;return{add:function(s){n==0&&(a=RD())&&(DD(a,s),OD(a)),n++},remove:function(){n--,!n&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},_D=function(){var n=ND();return function(a,s){T.useEffect(function(){return n.add(a),function(){n.remove()}},[a&&s])}},Nx=function(){var n=_D(),a=function(s){var l=s.styles,u=s.dynamic;return n(l,u),null};return a},jD={left:0,top:0,right:0,gap:0},jf=function(n){return parseInt(n||"",10)||0},VD=function(n){var a=window.getComputedStyle(document.body),s=a[n==="padding"?"paddingLeft":"marginLeft"],l=a[n==="padding"?"paddingTop":"marginTop"],u=a[n==="padding"?"paddingRight":"marginRight"];return[jf(s),jf(l),jf(u)]},zD=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return jD;var a=VD(n),s=document.documentElement.clientWidth,l=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,l-s+a[2]-a[0])}},LD=Nx(),is="data-scroll-locked",BD=function(n,a,s,l){var u=n.left,d=n.top,f=n.right,h=n.gap;return s===void 0&&(s="margin"),`
  .`.concat(vD,` {
   overflow: hidden `).concat(l,`;
   padding-right: `).concat(h,"px ").concat(l,`;
  }
  body[`).concat(is,`] {
    overflow: hidden `).concat(l,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(l,";"),s==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(f,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(l,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(l,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(To,` {
    right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(wo,` {
    margin-right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(To," .").concat(To,` {
    right: 0 `).concat(l,`;
  }
  
  .`).concat(wo," .").concat(wo,` {
    margin-right: 0 `).concat(l,`;
  }
  
  body[`).concat(is,`] {
    `).concat(bD,": ").concat(h,`px;
  }
`)},Xv=function(){var n=parseInt(document.body.getAttribute(is)||"0",10);return isFinite(n)?n:0},UD=function(){T.useEffect(function(){return document.body.setAttribute(is,(Xv()+1).toString()),function(){var n=Xv()-1;n<=0?document.body.removeAttribute(is):document.body.setAttribute(is,n.toString())}},[])},kD=function(n){var a=n.noRelative,s=n.noImportant,l=n.gapMode,u=l===void 0?"margin":l;UD();var d=T.useMemo(function(){return zD(u)},[u]);return T.createElement(LD,{styles:BD(d,!a,u,s?"":"!important")})},ud=!1;if(typeof window<"u")try{var fo=Object.defineProperty({},"passive",{get:function(){return ud=!0,!0}});window.addEventListener("test",fo,fo),window.removeEventListener("test",fo,fo)}catch{ud=!1}var Ii=ud?{passive:!1}:!1,HD=function(n){return n.tagName==="TEXTAREA"},_x=function(n,a){if(!(n instanceof Element))return!1;var s=window.getComputedStyle(n);return s[a]!=="hidden"&&!(s.overflowY===s.overflowX&&!HD(n)&&s[a]==="visible")},PD=function(n){return _x(n,"overflowY")},GD=function(n){return _x(n,"overflowX")},Kv=function(n,a){var s=a.ownerDocument,l=a;do{typeof ShadowRoot<"u"&&l instanceof ShadowRoot&&(l=l.host);var u=jx(n,l);if(u){var d=Vx(n,l),f=d[1],h=d[2];if(f>h)return!0}l=l.parentNode}while(l&&l!==s.body);return!1},YD=function(n){var a=n.scrollTop,s=n.scrollHeight,l=n.clientHeight;return[a,s,l]},qD=function(n){var a=n.scrollLeft,s=n.scrollWidth,l=n.clientWidth;return[a,s,l]},jx=function(n,a){return n==="v"?PD(a):GD(a)},Vx=function(n,a){return n==="v"?YD(a):qD(a)},XD=function(n,a){return n==="h"&&a==="rtl"?-1:1},KD=function(n,a,s,l,u){var d=XD(n,window.getComputedStyle(a).direction),f=d*l,h=s.target,p=a.contains(h),m=!1,g=f>0,v=0,x=0;do{var A=Vx(n,h),O=A[0],w=A[1],M=A[2],E=w-M-d*O;(O||E)&&jx(n,h)&&(v+=E,x+=O),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!p&&h!==document.body||p&&(a.contains(h)||a===h));return(g&&Math.abs(v)<1||!g&&Math.abs(x)<1)&&(m=!0),m},ho=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},Zv=function(n){return[n.deltaX,n.deltaY]},Qv=function(n){return n&&"current"in n?n.current:n},ZD=function(n,a){return n[0]===a[0]&&n[1]===a[1]},QD=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},FD=0,Wi=[];function ID(n){var a=T.useRef([]),s=T.useRef([0,0]),l=T.useRef(),u=T.useState(FD++)[0],d=T.useState(Nx)[0],f=T.useRef(n);T.useEffect(function(){f.current=n},[n]),T.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(u));var w=yD([n.lockRef.current],(n.shards||[]).map(Qv),!0).filter(Boolean);return w.forEach(function(M){return M.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),w.forEach(function(M){return M.classList.remove("allow-interactivity-".concat(u))})}}},[n.inert,n.lockRef.current,n.shards]);var h=T.useCallback(function(w,M){if("touches"in w&&w.touches.length===2||w.type==="wheel"&&w.ctrlKey)return!f.current.allowPinchZoom;var E=ho(w),_=s.current,D="deltaX"in w?w.deltaX:_[0]-E[0],H="deltaY"in w?w.deltaY:_[1]-E[1],z,F=w.target,Q=Math.abs(D)>Math.abs(H)?"h":"v";if("touches"in w&&Q==="h"&&F.type==="range")return!1;var G=Kv(Q,F);if(!G)return!0;if(G?z=Q:(z=Q==="v"?"h":"v",G=Kv(Q,F)),!G)return!1;if(!l.current&&"changedTouches"in w&&(D||H)&&(l.current=z),!z)return!0;var tt=l.current||z;return KD(tt,M,w,tt==="h"?D:H)},[]),p=T.useCallback(function(w){var M=w;if(!(!Wi.length||Wi[Wi.length-1]!==d)){var E="deltaY"in M?Zv(M):ho(M),_=a.current.filter(function(z){return z.name===M.type&&(z.target===M.target||M.target===z.shadowParent)&&ZD(z.delta,E)})[0];if(_&&_.should){M.cancelable&&M.preventDefault();return}if(!_){var D=(f.current.shards||[]).map(Qv).filter(Boolean).filter(function(z){return z.contains(M.target)}),H=D.length>0?h(M,D[0]):!f.current.noIsolation;H&&M.cancelable&&M.preventDefault()}}},[]),m=T.useCallback(function(w,M,E,_){var D={name:w,delta:M,target:E,should:_,shadowParent:WD(E)};a.current.push(D),setTimeout(function(){a.current=a.current.filter(function(H){return H!==D})},1)},[]),g=T.useCallback(function(w){s.current=ho(w),l.current=void 0},[]),v=T.useCallback(function(w){m(w.type,Zv(w),w.target,h(w,n.lockRef.current))},[]),x=T.useCallback(function(w){m(w.type,ho(w),w.target,h(w,n.lockRef.current))},[]);T.useEffect(function(){return Wi.push(d),n.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:x}),document.addEventListener("wheel",p,Ii),document.addEventListener("touchmove",p,Ii),document.addEventListener("touchstart",g,Ii),function(){Wi=Wi.filter(function(w){return w!==d}),document.removeEventListener("wheel",p,Ii),document.removeEventListener("touchmove",p,Ii),document.removeEventListener("touchstart",g,Ii)}},[]);var A=n.removeScrollBar,O=n.inert;return T.createElement(T.Fragment,null,O?T.createElement(d,{styles:QD(u)}):null,A?T.createElement(kD,{gapMode:n.gapMode}):null)}function WD(n){for(var a=null;n!==null;)n instanceof ShadowRoot&&(a=n.host,n=n.host),n=n.parentNode;return a}const $D=MD(Ox,ID);var zx=T.forwardRef(function(n,a){return T.createElement(Po,bn({},n,{ref:a,sideCar:$D}))});zx.classNames=Po.classNames;var JD=[" ","Enter","ArrowUp","ArrowDown"],tO=[" ","Enter"],ri="Select",[Go,Yo,eO]=$b(ri),[ps,XN]=kr(ri,[eO,gx]),qo=gx(),[nO,Ca]=ps(ri),[aO,iO]=ps(ri),Lx=n=>{const{__scopeSelect:a,children:s,open:l,defaultOpen:u,onOpenChange:d,value:f,defaultValue:h,onValueChange:p,dir:m,name:g,autoComplete:v,disabled:x,required:A,form:O}=n,w=qo(a),[M,E]=T.useState(null),[_,D]=T.useState(null),[H,z]=T.useState(!1),F=Id(m),[Q,G]=jo({prop:l,defaultProp:u??!1,onChange:d,caller:ri}),[tt,rt]=jo({prop:f,defaultProp:h,onChange:p,caller:ri}),ht=T.useRef(null),ct=M?O||!!M.closest("form"):!0,[yt,vt]=T.useState(new Set),ot=Array.from(yt).map(V=>V.props.value).join(";");return S.jsx(nD,{...w,children:S.jsxs(nO,{required:A,scope:a,trigger:M,onTriggerChange:E,valueNode:_,onValueNodeChange:D,valueNodeHasChildren:H,onValueNodeHasChildrenChange:z,contentId:Hr(),value:tt,onValueChange:rt,open:Q,onOpenChange:G,dir:F,triggerPointerDownPosRef:ht,disabled:x,children:[S.jsx(Go.Provider,{scope:a,children:S.jsx(aO,{scope:n.__scopeSelect,onNativeOptionAdd:T.useCallback(V=>{vt(Y=>new Set(Y).add(V))},[]),onNativeOptionRemove:T.useCallback(V=>{vt(Y=>{const k=new Set(Y);return k.delete(V),k})},[]),children:s})}),ct?S.jsxs(iS,{"aria-hidden":!0,required:A,tabIndex:-1,name:g,autoComplete:v,value:tt,onChange:V=>rt(V.target.value),disabled:x,form:O,children:[tt===void 0?S.jsx("option",{value:""}):null,Array.from(yt)]},ot):null]})})};Lx.displayName=ri;var Bx="SelectTrigger",Ux=T.forwardRef((n,a)=>{const{__scopeSelect:s,disabled:l=!1,...u}=n,d=qo(s),f=Ca(Bx,s),h=f.disabled||l,p=te(a,f.onTriggerChange),m=Yo(s),g=T.useRef("touch"),[v,x,A]=rS(w=>{const M=m().filter(D=>!D.disabled),E=M.find(D=>D.value===f.value),_=lS(M,w,E);_!==void 0&&f.onValueChange(_.value)}),O=w=>{h||(f.onOpenChange(!0),A()),w&&(f.triggerPointerDownPosRef.current={x:Math.round(w.pageX),y:Math.round(w.pageY)})};return S.jsx(aD,{asChild:!0,...d,children:S.jsx(Ut.button,{type:"button",role:"combobox","aria-controls":f.contentId,"aria-expanded":f.open,"aria-required":f.required,"aria-autocomplete":"none",dir:f.dir,"data-state":f.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":sS(f.value)?"":void 0,...u,ref:p,onClick:Vt(u.onClick,w=>{w.currentTarget.focus(),g.current!=="mouse"&&O(w)}),onPointerDown:Vt(u.onPointerDown,w=>{g.current=w.pointerType;const M=w.target;M.hasPointerCapture(w.pointerId)&&M.releasePointerCapture(w.pointerId),w.button===0&&w.ctrlKey===!1&&w.pointerType==="mouse"&&(O(w),w.preventDefault())}),onKeyDown:Vt(u.onKeyDown,w=>{const M=v.current!=="";!(w.ctrlKey||w.altKey||w.metaKey)&&w.key.length===1&&x(w.key),!(M&&w.key===" ")&&JD.includes(w.key)&&(O(),w.preventDefault())})})})});Ux.displayName=Bx;var kx="SelectValue",Hx=T.forwardRef((n,a)=>{const{__scopeSelect:s,className:l,style:u,children:d,placeholder:f="",...h}=n,p=Ca(kx,s),{onValueNodeHasChildrenChange:m}=p,g=d!==void 0,v=te(a,p.onValueNodeChange);return be(()=>{m(g)},[m,g]),S.jsx(Ut.span,{...h,ref:v,style:{pointerEvents:"none"},children:sS(p.value)?S.jsx(S.Fragment,{children:f}):d})});Hx.displayName=kx;var sO="SelectIcon",Px=T.forwardRef((n,a)=>{const{__scopeSelect:s,children:l,...u}=n;return S.jsx(Ut.span,{"aria-hidden":!0,...u,ref:a,children:l||"▼"})});Px.displayName=sO;var rO="SelectPortal",Gx=n=>S.jsx(Ex,{asChild:!0,...n});Gx.displayName=rO;var li="SelectContent",Yx=T.forwardRef((n,a)=>{const s=Ca(li,n.__scopeSelect),[l,u]=T.useState();if(be(()=>{u(new DocumentFragment)},[]),!s.open){const d=l;return d?Ur.createPortal(S.jsx(qx,{scope:n.__scopeSelect,children:S.jsx(Go.Slot,{scope:n.__scopeSelect,children:S.jsx("div",{children:n.children})})}),d):null}return S.jsx(Xx,{...n,ref:a})});Yx.displayName=li;var an=10,[qx,Ra]=ps(li),lO="SelectContentImpl",oO=Nr("SelectContent.RemoveScroll"),Xx=T.forwardRef((n,a)=>{const{__scopeSelect:s,position:l="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:d,onPointerDownOutside:f,side:h,sideOffset:p,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:x,collisionPadding:A,sticky:O,hideWhenDetached:w,avoidCollisions:M,...E}=n,_=Ca(li,s),[D,H]=T.useState(null),[z,F]=T.useState(null),Q=te(a,$=>H($)),[G,tt]=T.useState(null),[rt,ht]=T.useState(null),ct=Yo(s),[yt,vt]=T.useState(!1),ot=T.useRef(!1);T.useEffect(()=>{if(D)return gD(D)},[D]),HC();const V=T.useCallback($=>{const[ut,...Nt]=ct().map(At=>At.ref.current),[Ct]=Nt.slice(-1),wt=document.activeElement;for(const At of $)if(At===wt||(At==null||At.scrollIntoView({block:"nearest"}),At===ut&&z&&(z.scrollTop=0),At===Ct&&z&&(z.scrollTop=z.scrollHeight),At==null||At.focus(),document.activeElement!==wt))return},[ct,z]),Y=T.useCallback(()=>V([G,D]),[V,G,D]);T.useEffect(()=>{yt&&Y()},[yt,Y]);const{onOpenChange:k,triggerPointerDownPosRef:it}=_;T.useEffect(()=>{if(D){let $={x:0,y:0};const ut=Ct=>{var wt,At;$={x:Math.abs(Math.round(Ct.pageX)-(((wt=it.current)==null?void 0:wt.x)??0)),y:Math.abs(Math.round(Ct.pageY)-(((At=it.current)==null?void 0:At.y)??0))}},Nt=Ct=>{$.x<=10&&$.y<=10?Ct.preventDefault():D.contains(Ct.target)||k(!1),document.removeEventListener("pointermove",ut),it.current=null};return it.current!==null&&(document.addEventListener("pointermove",ut),document.addEventListener("pointerup",Nt,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ut),document.removeEventListener("pointerup",Nt,{capture:!0})}}},[D,k,it]),T.useEffect(()=>{const $=()=>k(!1);return window.addEventListener("blur",$),window.addEventListener("resize",$),()=>{window.removeEventListener("blur",$),window.removeEventListener("resize",$)}},[k]);const[R,X]=rS($=>{const ut=ct().filter(wt=>!wt.disabled),Nt=ut.find(wt=>wt.ref.current===document.activeElement),Ct=lS(ut,$,Nt);Ct&&setTimeout(()=>Ct.ref.current.focus())}),W=T.useCallback(($,ut,Nt)=>{const Ct=!ot.current&&!Nt;(_.value!==void 0&&_.value===ut||Ct)&&(tt($),Ct&&(ot.current=!0))},[_.value]),I=T.useCallback(()=>D==null?void 0:D.focus(),[D]),J=T.useCallback(($,ut,Nt)=>{const Ct=!ot.current&&!Nt;(_.value!==void 0&&_.value===ut||Ct)&&ht($)},[_.value]),gt=l==="popper"?cd:Kx,lt=gt===cd?{side:h,sideOffset:p,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:x,collisionPadding:A,sticky:O,hideWhenDetached:w,avoidCollisions:M}:{};return S.jsx(qx,{scope:s,content:D,viewport:z,onViewportChange:F,itemRefCallback:W,selectedItem:G,onItemLeave:I,itemTextRefCallback:J,focusSelectedItem:Y,selectedItemText:rt,position:l,isPositioned:yt,searchRef:R,children:S.jsx(zx,{as:oO,allowPinchZoom:!0,children:S.jsx(nx,{asChild:!0,trapped:_.open,onMountAutoFocus:$=>{$.preventDefault()},onUnmountAutoFocus:Vt(u,$=>{var ut;(ut=_.trigger)==null||ut.focus({preventScroll:!0}),$.preventDefault()}),children:S.jsx(tx,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:$=>$.preventDefault(),onDismiss:()=>_.onOpenChange(!1),children:S.jsx(gt,{role:"listbox",id:_.contentId,"data-state":_.open?"open":"closed",dir:_.dir,onContextMenu:$=>$.preventDefault(),...E,...lt,onPlaced:()=>vt(!0),ref:Q,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:Vt(E.onKeyDown,$=>{const ut=$.ctrlKey||$.altKey||$.metaKey;if($.key==="Tab"&&$.preventDefault(),!ut&&$.key.length===1&&X($.key),["ArrowUp","ArrowDown","Home","End"].includes($.key)){let Ct=ct().filter(wt=>!wt.disabled).map(wt=>wt.ref.current);if(["ArrowUp","End"].includes($.key)&&(Ct=Ct.slice().reverse()),["ArrowUp","ArrowDown"].includes($.key)){const wt=$.target,At=Ct.indexOf(wt);Ct=Ct.slice(At+1)}setTimeout(()=>V(Ct)),$.preventDefault()}})})})})})})});Xx.displayName=lO;var uO="SelectItemAlignedPosition",Kx=T.forwardRef((n,a)=>{const{__scopeSelect:s,onPlaced:l,...u}=n,d=Ca(li,s),f=Ra(li,s),[h,p]=T.useState(null),[m,g]=T.useState(null),v=te(a,Q=>g(Q)),x=Yo(s),A=T.useRef(!1),O=T.useRef(!0),{viewport:w,selectedItem:M,selectedItemText:E,focusSelectedItem:_}=f,D=T.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&m&&w&&M&&E){const Q=d.trigger.getBoundingClientRect(),G=m.getBoundingClientRect(),tt=d.valueNode.getBoundingClientRect(),rt=E.getBoundingClientRect();if(d.dir!=="rtl"){const wt=rt.left-G.left,At=tt.left-wt,oe=Q.left-At,we=Q.width+oe,Da=Math.max(we,G.width),Oa=window.innerWidth-an,he=Rv(At,[an,Math.max(an,Oa-Da)]);h.style.minWidth=we+"px",h.style.left=he+"px"}else{const wt=G.right-rt.right,At=window.innerWidth-tt.right-wt,oe=window.innerWidth-Q.right-At,we=Q.width+oe,Da=Math.max(we,G.width),Oa=window.innerWidth-an,he=Rv(At,[an,Math.max(an,Oa-Da)]);h.style.minWidth=we+"px",h.style.right=he+"px"}const ht=x(),ct=window.innerHeight-an*2,yt=w.scrollHeight,vt=window.getComputedStyle(m),ot=parseInt(vt.borderTopWidth,10),V=parseInt(vt.paddingTop,10),Y=parseInt(vt.borderBottomWidth,10),k=parseInt(vt.paddingBottom,10),it=ot+V+yt+k+Y,R=Math.min(M.offsetHeight*5,it),X=window.getComputedStyle(w),W=parseInt(X.paddingTop,10),I=parseInt(X.paddingBottom,10),J=Q.top+Q.height/2-an,gt=ct-J,lt=M.offsetHeight/2,$=M.offsetTop+lt,ut=ot+V+$,Nt=it-ut;if(ut<=J){const wt=ht.length>0&&M===ht[ht.length-1].ref.current;h.style.bottom="0px";const At=m.clientHeight-w.offsetTop-w.offsetHeight,oe=Math.max(gt,lt+(wt?I:0)+At+Y),we=ut+oe;h.style.height=we+"px"}else{const wt=ht.length>0&&M===ht[0].ref.current;h.style.top="0px";const oe=Math.max(J,ot+w.offsetTop+(wt?W:0)+lt)+Nt;h.style.height=oe+"px",w.scrollTop=ut-J+w.offsetTop}h.style.margin=`${an}px 0`,h.style.minHeight=R+"px",h.style.maxHeight=ct+"px",l==null||l(),requestAnimationFrame(()=>A.current=!0)}},[x,d.trigger,d.valueNode,h,m,w,M,E,d.dir,l]);be(()=>D(),[D]);const[H,z]=T.useState();be(()=>{m&&z(window.getComputedStyle(m).zIndex)},[m]);const F=T.useCallback(Q=>{Q&&O.current===!0&&(D(),_==null||_(),O.current=!1)},[D,_]);return S.jsx(fO,{scope:s,contentWrapper:h,shouldExpandOnScrollRef:A,onScrollButtonChange:F,children:S.jsx("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:H},children:S.jsx(Ut.div,{...u,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...u.style}})})})});Kx.displayName=uO;var cO="SelectPopperPosition",cd=T.forwardRef((n,a)=>{const{__scopeSelect:s,align:l="start",collisionPadding:u=an,...d}=n,f=qo(s);return S.jsx(iD,{...f,...d,ref:a,align:l,collisionPadding:u,style:{boxSizing:"border-box",...d.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});cd.displayName=cO;var[fO,rh]=ps(li,{}),fd="SelectViewport",Zx=T.forwardRef((n,a)=>{const{__scopeSelect:s,nonce:l,...u}=n,d=Ra(fd,s),f=rh(fd,s),h=te(a,d.onViewportChange),p=T.useRef(0);return S.jsxs(S.Fragment,{children:[S.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),S.jsx(Go.Slot,{scope:s,children:S.jsx(Ut.div,{"data-radix-select-viewport":"",role:"presentation",...u,ref:h,style:{position:"relative",flex:1,overflow:"hidden auto",...u.style},onScroll:Vt(u.onScroll,m=>{const g=m.currentTarget,{contentWrapper:v,shouldExpandOnScrollRef:x}=f;if(x!=null&&x.current&&v){const A=Math.abs(p.current-g.scrollTop);if(A>0){const O=window.innerHeight-an*2,w=parseFloat(v.style.minHeight),M=parseFloat(v.style.height),E=Math.max(w,M);if(E<O){const _=E+A,D=Math.min(O,_),H=_-D;v.style.height=D+"px",v.style.bottom==="0px"&&(g.scrollTop=H>0?H:0,v.style.justifyContent="flex-end")}}}p.current=g.scrollTop})})})]})});Zx.displayName=fd;var Qx="SelectGroup",[dO,hO]=ps(Qx),mO=T.forwardRef((n,a)=>{const{__scopeSelect:s,...l}=n,u=Hr();return S.jsx(dO,{scope:s,id:u,children:S.jsx(Ut.div,{role:"group","aria-labelledby":u,...l,ref:a})})});mO.displayName=Qx;var Fx="SelectLabel",pO=T.forwardRef((n,a)=>{const{__scopeSelect:s,...l}=n,u=hO(Fx,s);return S.jsx(Ut.div,{id:u.id,...l,ref:a})});pO.displayName=Fx;var Vo="SelectItem",[gO,Ix]=ps(Vo),Wx=T.forwardRef((n,a)=>{const{__scopeSelect:s,value:l,disabled:u=!1,textValue:d,...f}=n,h=Ca(Vo,s),p=Ra(Vo,s),m=h.value===l,[g,v]=T.useState(d??""),[x,A]=T.useState(!1),O=te(a,_=>{var D;return(D=p.itemRefCallback)==null?void 0:D.call(p,_,l,u)}),w=Hr(),M=T.useRef("touch"),E=()=>{u||(h.onValueChange(l),h.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return S.jsx(gO,{scope:s,value:l,disabled:u,textId:w,isSelected:m,onItemTextChange:T.useCallback(_=>{v(D=>D||((_==null?void 0:_.textContent)??"").trim())},[]),children:S.jsx(Go.ItemSlot,{scope:s,value:l,disabled:u,textValue:g,children:S.jsx(Ut.div,{role:"option","aria-labelledby":w,"data-highlighted":x?"":void 0,"aria-selected":m&&x,"data-state":m?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1,...f,ref:O,onFocus:Vt(f.onFocus,()=>A(!0)),onBlur:Vt(f.onBlur,()=>A(!1)),onClick:Vt(f.onClick,()=>{M.current!=="mouse"&&E()}),onPointerUp:Vt(f.onPointerUp,()=>{M.current==="mouse"&&E()}),onPointerDown:Vt(f.onPointerDown,_=>{M.current=_.pointerType}),onPointerMove:Vt(f.onPointerMove,_=>{var D;M.current=_.pointerType,u?(D=p.onItemLeave)==null||D.call(p):M.current==="mouse"&&_.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Vt(f.onPointerLeave,_=>{var D;_.currentTarget===document.activeElement&&((D=p.onItemLeave)==null||D.call(p))}),onKeyDown:Vt(f.onKeyDown,_=>{var H;((H=p.searchRef)==null?void 0:H.current)!==""&&_.key===" "||(tO.includes(_.key)&&E(),_.key===" "&&_.preventDefault())})})})})});Wx.displayName=Vo;var vr="SelectItemText",$x=T.forwardRef((n,a)=>{const{__scopeSelect:s,className:l,style:u,...d}=n,f=Ca(vr,s),h=Ra(vr,s),p=Ix(vr,s),m=iO(vr,s),[g,v]=T.useState(null),x=te(a,E=>v(E),p.onItemTextChange,E=>{var _;return(_=h.itemTextRefCallback)==null?void 0:_.call(h,E,p.value,p.disabled)}),A=g==null?void 0:g.textContent,O=T.useMemo(()=>S.jsx("option",{value:p.value,disabled:p.disabled,children:A},p.value),[p.disabled,p.value,A]),{onNativeOptionAdd:w,onNativeOptionRemove:M}=m;return be(()=>(w(O),()=>M(O)),[w,M,O]),S.jsxs(S.Fragment,{children:[S.jsx(Ut.span,{id:p.textId,...d,ref:x}),p.isSelected&&f.valueNode&&!f.valueNodeHasChildren?Ur.createPortal(d.children,f.valueNode):null]})});$x.displayName=vr;var Jx="SelectItemIndicator",tS=T.forwardRef((n,a)=>{const{__scopeSelect:s,...l}=n;return Ix(Jx,s).isSelected?S.jsx(Ut.span,{"aria-hidden":!0,...l,ref:a}):null});tS.displayName=Jx;var dd="SelectScrollUpButton",eS=T.forwardRef((n,a)=>{const s=Ra(dd,n.__scopeSelect),l=rh(dd,n.__scopeSelect),[u,d]=T.useState(!1),f=te(a,l.onScrollButtonChange);return be(()=>{if(s.viewport&&s.isPositioned){let h=function(){const m=p.scrollTop>0;d(m)};const p=s.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(aS,{...n,ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=s;h&&p&&(h.scrollTop=h.scrollTop-p.offsetHeight)}}):null});eS.displayName=dd;var hd="SelectScrollDownButton",nS=T.forwardRef((n,a)=>{const s=Ra(hd,n.__scopeSelect),l=rh(hd,n.__scopeSelect),[u,d]=T.useState(!1),f=te(a,l.onScrollButtonChange);return be(()=>{if(s.viewport&&s.isPositioned){let h=function(){const m=p.scrollHeight-p.clientHeight,g=Math.ceil(p.scrollTop)<m;d(g)};const p=s.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),u?S.jsx(aS,{...n,ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=s;h&&p&&(h.scrollTop=h.scrollTop+p.offsetHeight)}}):null});nS.displayName=hd;var aS=T.forwardRef((n,a)=>{const{__scopeSelect:s,onAutoScroll:l,...u}=n,d=Ra("SelectScrollButton",s),f=T.useRef(null),h=Yo(s),p=T.useCallback(()=>{f.current!==null&&(window.clearInterval(f.current),f.current=null)},[]);return T.useEffect(()=>()=>p(),[p]),be(()=>{var g;const m=h().find(v=>v.ref.current===document.activeElement);(g=m==null?void 0:m.ref.current)==null||g.scrollIntoView({block:"nearest"})},[h]),S.jsx(Ut.div,{"aria-hidden":!0,...u,ref:a,style:{flexShrink:0,...u.style},onPointerDown:Vt(u.onPointerDown,()=>{f.current===null&&(f.current=window.setInterval(l,50))}),onPointerMove:Vt(u.onPointerMove,()=>{var m;(m=d.onItemLeave)==null||m.call(d),f.current===null&&(f.current=window.setInterval(l,50))}),onPointerLeave:Vt(u.onPointerLeave,()=>{p()})})}),yO="SelectSeparator",vO=T.forwardRef((n,a)=>{const{__scopeSelect:s,...l}=n;return S.jsx(Ut.div,{"aria-hidden":!0,...l,ref:a})});vO.displayName=yO;var md="SelectArrow",bO=T.forwardRef((n,a)=>{const{__scopeSelect:s,...l}=n,u=qo(s),d=Ca(md,s),f=Ra(md,s);return d.open&&f.position==="popper"?S.jsx(sD,{...u,...l,ref:a}):null});bO.displayName=md;var xO="SelectBubbleInput",iS=T.forwardRef(({__scopeSelect:n,value:a,...s},l)=>{const u=T.useRef(null),d=te(l,u),f=cD(a);return T.useEffect(()=>{const h=u.current;if(!h)return;const p=window.HTMLSelectElement.prototype,g=Object.getOwnPropertyDescriptor(p,"value").set;if(f!==a&&g){const v=new Event("change",{bubbles:!0});g.call(h,a),h.dispatchEvent(v)}},[f,a]),S.jsx(Ut.select,{...s,style:{...Mx,...s.style},ref:d,defaultValue:a})});iS.displayName=xO;function sS(n){return n===""||n===void 0}function rS(n){const a=wa(n),s=T.useRef(""),l=T.useRef(0),u=T.useCallback(f=>{const h=s.current+f;a(h),function p(m){s.current=m,window.clearTimeout(l.current),m!==""&&(l.current=window.setTimeout(()=>p(""),1e3))}(h)},[a]),d=T.useCallback(()=>{s.current="",window.clearTimeout(l.current)},[]);return T.useEffect(()=>()=>window.clearTimeout(l.current),[]),[s,u,d]}function lS(n,a,s){const u=a.length>1&&Array.from(a).every(m=>m===a[0])?a[0]:a,d=s?n.indexOf(s):-1;let f=SO(n,Math.max(d,0));u.length===1&&(f=f.filter(m=>m!==s));const p=f.find(m=>m.textValue.toLowerCase().startsWith(u.toLowerCase()));return p!==s?p:void 0}function SO(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var TO=Lx,wO=Ux,AO=Hx,EO=Px,MO=Gx,CO=Yx,RO=Zx,DO=Wx,OO=$x,NO=tS,_O=eS,jO=nS;/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VO=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),zO=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,s,l)=>l?l.toUpperCase():s.toLowerCase()),Fv=n=>{const a=zO(n);return a.charAt(0).toUpperCase()+a.slice(1)},oS=(...n)=>n.filter((a,s,l)=>!!a&&a.trim()!==""&&l.indexOf(a)===s).join(" ").trim(),LO=n=>{for(const a in n)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var BO={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UO=T.forwardRef(({color:n="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:f,...h},p)=>T.createElement("svg",{ref:p,...BO,width:a,height:a,stroke:n,strokeWidth:l?Number(s)*24/Number(a):s,className:oS("lucide",u),...!d&&!LO(h)&&{"aria-hidden":"true"},...h},[...f.map(([m,g])=>T.createElement(m,g)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=(n,a)=>{const s=T.forwardRef(({className:l,...u},d)=>T.createElement(UO,{ref:d,iconNode:a,className:oS(`lucide-${VO(Fv(n))}`,`lucide-${n}`,l),...u}));return s.displayName=Fv(n),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kO=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],HO=Te("building",kO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PO=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],GO=Te("calendar",PO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YO=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],qO=Te("check",YO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XO=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],uS=Te("chevron-down",XO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KO=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],ZO=Te("chevron-up",KO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QO=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Iv=Te("circle-alert",QO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FO=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Wv=Te("circle-check-big",FO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IO=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],WO=Te("log-out",IO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $O=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],JO=Te("refresh-cw",$O);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],eN=Te("search",tN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],aN=Te("settings",nN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],sN=Te("sparkles",iN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],lN=Te("user-plus",rN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],uN=Te("user",oN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],fN=Te("users",cN);function dN({...n}){return S.jsx(TO,{"data-slot":"select",...n})}function hN({...n}){return S.jsx(AO,{"data-slot":"select-value",...n})}function mN({className:n,size:a="default",children:s,...l}){return S.jsxs(wO,{"data-slot":"select-trigger","data-size":a,className:Gt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...l,children:[s,S.jsx(EO,{asChild:!0,children:S.jsx(uS,{className:"size-4 opacity-50"})})]})}function pN({className:n,children:a,position:s="popper",...l}){return S.jsx(MO,{children:S.jsxs(CO,{"data-slot":"select-content",className:Gt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:s,...l,children:[S.jsx(yN,{}),S.jsx(RO,{className:Gt("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),S.jsx(vN,{})]})})}function gN({className:n,children:a,...s}){return S.jsxs(DO,{"data-slot":"select-item",className:Gt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n),...s,children:[S.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:S.jsx(NO,{children:S.jsx(qO,{className:"size-4"})})}),S.jsx(OO,{children:a})]})}function yN({className:n,...a}){return S.jsx(_O,{"data-slot":"select-scroll-up-button",className:Gt("flex cursor-default items-center justify-center py-1",n),...a,children:S.jsx(ZO,{className:"size-4"})})}function vN({className:n,...a}){return S.jsx(jO,{"data-slot":"select-scroll-down-button",className:Gt("flex cursor-default items-center justify-center py-1",n),...a,children:S.jsx(uS,{className:"size-4"})})}function $v({className:n,...a}){return S.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:S.jsx("table",{"data-slot":"table",className:Gt("w-full caption-bottom text-sm",n),...a})})}function Jv({className:n,...a}){return S.jsx("thead",{"data-slot":"table-header",className:Gt("[&_tr]:border-b",n),...a})}function t0({className:n,...a}){return S.jsx("tbody",{"data-slot":"table-body",className:Gt("[&_tr:last-child]:border-0",n),...a})}function Vf({className:n,...a}){return S.jsx("tr",{"data-slot":"table-row",className:Gt("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",n),...a})}function yn({className:n,...a}){return S.jsx("th",{"data-slot":"table-head",className:Gt("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n),...a})}function vn({className:n,...a}){return S.jsx("td",{"data-slot":"table-cell",className:Gt("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n),...a})}const bN=Hb("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function e0({className:n,variant:a,asChild:s=!1,...l}){const u=s?Bb:"span";return S.jsx(u,{"data-slot":"badge",className:Gt(bN({variant:a}),n),...l})}var zf="rovingFocusGroup.onEntryFocus",xN={bubbles:!1,cancelable:!0},Gr="RovingFocusGroup",[pd,cS,SN]=$b(Gr),[TN,fS]=kr(Gr,[SN]),[wN,AN]=TN(Gr),dS=T.forwardRef((n,a)=>S.jsx(pd.Provider,{scope:n.__scopeRovingFocusGroup,children:S.jsx(pd.Slot,{scope:n.__scopeRovingFocusGroup,children:S.jsx(EN,{...n,ref:a})})}));dS.displayName=Gr;var EN=T.forwardRef((n,a)=>{const{__scopeRovingFocusGroup:s,orientation:l,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:p,onEntryFocus:m,preventScrollOnEntryFocus:g=!1,...v}=n,x=T.useRef(null),A=te(a,x),O=Id(d),[w,M]=jo({prop:f,defaultProp:h??null,onChange:p,caller:Gr}),[E,_]=T.useState(!1),D=wa(m),H=cS(s),z=T.useRef(!1),[F,Q]=T.useState(0);return T.useEffect(()=>{const G=x.current;if(G)return G.addEventListener(zf,D),()=>G.removeEventListener(zf,D)},[D]),S.jsx(wN,{scope:s,orientation:l,dir:O,loop:u,currentTabStopId:w,onItemFocus:T.useCallback(G=>M(G),[M]),onItemShiftTab:T.useCallback(()=>_(!0),[]),onFocusableItemAdd:T.useCallback(()=>Q(G=>G+1),[]),onFocusableItemRemove:T.useCallback(()=>Q(G=>G-1),[]),children:S.jsx(Ut.div,{tabIndex:E||F===0?-1:0,"data-orientation":l,...v,ref:A,style:{outline:"none",...n.style},onMouseDown:Vt(n.onMouseDown,()=>{z.current=!0}),onFocus:Vt(n.onFocus,G=>{const tt=!z.current;if(G.target===G.currentTarget&&tt&&!E){const rt=new CustomEvent(zf,xN);if(G.currentTarget.dispatchEvent(rt),!rt.defaultPrevented){const ht=H().filter(V=>V.focusable),ct=ht.find(V=>V.active),yt=ht.find(V=>V.id===w),ot=[ct,yt,...ht].filter(Boolean).map(V=>V.ref.current);pS(ot,g)}}z.current=!1}),onBlur:Vt(n.onBlur,()=>_(!1))})})}),hS="RovingFocusGroupItem",mS=T.forwardRef((n,a)=>{const{__scopeRovingFocusGroup:s,focusable:l=!0,active:u=!1,tabStopId:d,children:f,...h}=n,p=Hr(),m=d||p,g=AN(hS,s),v=g.currentTabStopId===m,x=cS(s),{onFocusableItemAdd:A,onFocusableItemRemove:O,currentTabStopId:w}=g;return T.useEffect(()=>{if(l)return A(),()=>O()},[l,A,O]),S.jsx(pd.ItemSlot,{scope:s,id:m,focusable:l,active:u,children:S.jsx(Ut.span,{tabIndex:v?0:-1,"data-orientation":g.orientation,...h,ref:a,onMouseDown:Vt(n.onMouseDown,M=>{l?g.onItemFocus(m):M.preventDefault()}),onFocus:Vt(n.onFocus,()=>g.onItemFocus(m)),onKeyDown:Vt(n.onKeyDown,M=>{if(M.key==="Tab"&&M.shiftKey){g.onItemShiftTab();return}if(M.target!==M.currentTarget)return;const E=RN(M,g.orientation,g.dir);if(E!==void 0){if(M.metaKey||M.ctrlKey||M.altKey||M.shiftKey)return;M.preventDefault();let D=x().filter(H=>H.focusable).map(H=>H.ref.current);if(E==="last")D.reverse();else if(E==="prev"||E==="next"){E==="prev"&&D.reverse();const H=D.indexOf(M.currentTarget);D=g.loop?DN(D,H+1):D.slice(H+1)}setTimeout(()=>pS(D))}}),children:typeof f=="function"?f({isCurrentTabStop:v,hasTabStop:w!=null}):f})})});mS.displayName=hS;var MN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function CN(n,a){return a!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function RN(n,a,s){const l=CN(n.key,s);if(!(a==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(a==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return MN[l]}function pS(n,a=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:a}),document.activeElement!==s))return}function DN(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var ON=dS,NN=mS;function _N(n,a){return T.useReducer((s,l)=>a[s][l]??s,n)}var gS=n=>{const{present:a,children:s}=n,l=jN(a),u=typeof s=="function"?s({present:l.isPresent}):T.Children.only(s),d=te(l.ref,VN(u));return typeof s=="function"||l.isPresent?T.cloneElement(u,{ref:d}):null};gS.displayName="Presence";function jN(n){const[a,s]=T.useState(),l=T.useRef(null),u=T.useRef(n),d=T.useRef("none"),f=n?"mounted":"unmounted",[h,p]=_N(f,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return T.useEffect(()=>{const m=mo(l.current);d.current=h==="mounted"?m:"none"},[h]),be(()=>{const m=l.current,g=u.current;if(g!==n){const x=d.current,A=mo(m);n?p("MOUNT"):A==="none"||(m==null?void 0:m.display)==="none"?p("UNMOUNT"):p(g&&x!==A?"ANIMATION_OUT":"UNMOUNT"),u.current=n}},[n,p]),be(()=>{if(a){let m;const g=a.ownerDocument.defaultView??window,v=A=>{const w=mo(l.current).includes(A.animationName);if(A.target===a&&w&&(p("ANIMATION_END"),!u.current)){const M=a.style.animationFillMode;a.style.animationFillMode="forwards",m=g.setTimeout(()=>{a.style.animationFillMode==="forwards"&&(a.style.animationFillMode=M)})}},x=A=>{A.target===a&&(d.current=mo(l.current))};return a.addEventListener("animationstart",x),a.addEventListener("animationcancel",v),a.addEventListener("animationend",v),()=>{g.clearTimeout(m),a.removeEventListener("animationstart",x),a.removeEventListener("animationcancel",v),a.removeEventListener("animationend",v)}}else p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:T.useCallback(m=>{l.current=m?getComputedStyle(m):null,s(m)},[])}}function mo(n){return(n==null?void 0:n.animationName)||"none"}function VN(n){var l,u;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var Xo="Tabs",[zN,KN]=kr(Xo,[fS]),yS=fS(),[LN,lh]=zN(Xo),vS=T.forwardRef((n,a)=>{const{__scopeTabs:s,value:l,onValueChange:u,defaultValue:d,orientation:f="horizontal",dir:h,activationMode:p="automatic",...m}=n,g=Id(h),[v,x]=jo({prop:l,onChange:u,defaultProp:d??"",caller:Xo});return S.jsx(LN,{scope:s,baseId:Hr(),value:v,onValueChange:x,orientation:f,dir:g,activationMode:p,children:S.jsx(Ut.div,{dir:g,"data-orientation":f,...m,ref:a})})});vS.displayName=Xo;var bS="TabsList",xS=T.forwardRef((n,a)=>{const{__scopeTabs:s,loop:l=!0,...u}=n,d=lh(bS,s),f=yS(s);return S.jsx(ON,{asChild:!0,...f,orientation:d.orientation,dir:d.dir,loop:l,children:S.jsx(Ut.div,{role:"tablist","aria-orientation":d.orientation,...u,ref:a})})});xS.displayName=bS;var SS="TabsTrigger",TS=T.forwardRef((n,a)=>{const{__scopeTabs:s,value:l,disabled:u=!1,...d}=n,f=lh(SS,s),h=yS(s),p=ES(f.baseId,l),m=MS(f.baseId,l),g=l===f.value;return S.jsx(NN,{asChild:!0,...h,focusable:!u,active:g,children:S.jsx(Ut.button,{type:"button",role:"tab","aria-selected":g,"aria-controls":m,"data-state":g?"active":"inactive","data-disabled":u?"":void 0,disabled:u,id:p,...d,ref:a,onMouseDown:Vt(n.onMouseDown,v=>{!u&&v.button===0&&v.ctrlKey===!1?f.onValueChange(l):v.preventDefault()}),onKeyDown:Vt(n.onKeyDown,v=>{[" ","Enter"].includes(v.key)&&f.onValueChange(l)}),onFocus:Vt(n.onFocus,()=>{const v=f.activationMode!=="manual";!g&&!u&&v&&f.onValueChange(l)})})})});TS.displayName=SS;var wS="TabsContent",AS=T.forwardRef((n,a)=>{const{__scopeTabs:s,value:l,forceMount:u,children:d,...f}=n,h=lh(wS,s),p=ES(h.baseId,l),m=MS(h.baseId,l),g=l===h.value,v=T.useRef(g);return T.useEffect(()=>{const x=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(x)},[]),S.jsx(gS,{present:u||g,children:({present:x})=>S.jsx(Ut.div,{"data-state":g?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":p,hidden:!x,id:m,tabIndex:0,...f,ref:a,style:{...n.style,animationDuration:v.current?"0s":void 0},children:x&&d})})});AS.displayName=wS;function ES(n,a){return`${n}-trigger-${a}`}function MS(n,a){return`${n}-content-${a}`}var BN=vS,UN=xS,kN=TS,HN=AS;function PN({className:n,...a}){return S.jsx(BN,{"data-slot":"tabs",className:Gt("flex flex-col gap-2",n),...a})}function GN({className:n,...a}){return S.jsx(UN,{"data-slot":"tabs-list",className:Gt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n),...a})}function po({className:n,...a}){return S.jsx(kN,{"data-slot":"tabs-trigger",className:Gt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...a})}function go({className:n,...a}){return S.jsx(HN,{"data-slot":"tabs-content",className:Gt("flex-1 outline-none",n),...a})}function n0(n,a){const[s,l]=T.useState(()=>{try{const d=window.localStorage.getItem(n);return d?JSON.parse(d):a}catch(d){return console.error(`خطأ في قراءة ${n} من localStorage:`,d),a}});return[s,d=>{try{const f=d instanceof Function?d(s):d;l(f),window.localStorage.setItem(n,JSON.stringify(f))}catch(f){console.error(`خطأ في حفظ ${n} في localStorage:`,f)}}]}function YN(){const[n,a]=T.useState(!0),[s,l]=n0("work-attendance-employees",[]),[u,d]=T.useState({name:"",department:"",startMonth:""}),[f,h]=n0("work-attendance-search-filters",{name:"",department:"",startMonth:"",status:""}),p=["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"];T.useEffect(()=>{const E=setTimeout(()=>{a(!1)},4e3);return()=>clearTimeout(E)},[]);const m=E=>{const _=new Date().getMonth(),D=p.indexOf(E);return(_-D+12)%12>=3},g=E=>{if(E.preventDefault(),u.name&&u.department&&u.startMonth){const _={id:Date.now(),...u,status:m(u.startMonth)};l([...s,_]),d({name:"",department:"",startMonth:""}),O("✅ تم حفظ البيانات بنجاح!")}},v=()=>{l(s.map(E=>({...E,status:m(E.startMonth)})))},x=()=>{const E={employees:s,exportDate:new Date().toISOString(),version:"1.0"},_=JSON.stringify(E,null,2),D=new Blob([_],{type:"application/json"}),H=document.createElement("a");H.href=URL.createObjectURL(D),H.download=`work-attendance-backup-${new Date().toISOString().split("T")[0]}.json`,H.click(),O("📁 تم تصدير البيانات بنجاح!","success")},A=E=>{const _=E.target.files[0];if(!_)return;const D=new FileReader;D.onload=H=>{try{const z=JSON.parse(H.target.result);z.employees&&Array.isArray(z.employees)?(l(z.employees),O("📥 تم استيراد البيانات بنجاح!","success")):O("❌ ملف غير صالح!","error")}catch(z){console.error("خطأ في استيراد البيانات:",z),O("❌ خطأ في قراءة الملف!","error")}},D.readAsText(_),E.target.value=""},O=(E,_="success")=>{const D=document.createElement("div"),H=_==="success"?"bg-green-500":"bg-red-500";D.className=`fixed top-4 right-4 ${H} text-white px-6 py-3 rounded-lg shadow-lg z-50`,D.textContent=E,document.body.appendChild(D),setTimeout(()=>{document.body.contains(D)&&document.body.removeChild(D)},3e3)},w=()=>{window.confirm("هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!")&&(l([]),h({name:"",department:"",startMonth:"",status:""}),O("🗑️ تم مسح جميع البيانات!","success"))},M=s.filter(E=>(!f.name||E.name.includes(f.name))&&(!f.department||E.department.includes(f.department))&&(!f.startMonth||E.startMonth.includes(f.startMonth))&&(!f.status||f.status.includes("مطلوب")&&E.status||f.status.includes("تم")&&!E.status));return n?S.jsx("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:S.jsxs(dn.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1},className:"text-center",children:[S.jsx(dn.h1,{className:"text-6xl font-bold text-white mb-8 typewriter",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"🌟 برنامج استحقاق كشف عمل 🌟"}),S.jsx(dn.p,{className:"text-2xl text-white mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:"👨‍💻 المبرمج: علي عاجل خشان المحنّة"}),S.jsx(dn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:S.jsx($a,{onClick:()=>a(!1),className:"pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4",children:"ابدأ الآن"})})]})}):S.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900",children:S.jsxs("div",{className:"flex",children:[S.jsxs(dn.div,{initial:{x:-300},animate:{x:0},className:"w-80 min-h-screen glassmorphism p-6",children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-2xl font-bold gradient-text mb-2",children:"برنامج استحقاق كشف عمل"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة"})]}),S.jsxs($a,{variant:"destructive",className:"w-full mb-6 hover-lift",onClick:()=>window.location.reload(),children:[S.jsx(WO,{className:"mr-2 h-4 w-4"}),"خروج"]})]}),S.jsx("div",{className:"flex-1 p-8",children:S.jsxs(PN,{defaultValue:"input",className:"w-full",children:[S.jsxs(GN,{className:"grid w-full grid-cols-4 mb-8",children:[S.jsxs(po,{value:"input",className:"flex items-center gap-2",children:[S.jsx(lN,{className:"h-4 w-4"}),"إدخال البيانات"]}),S.jsxs(po,{value:"employees",className:"flex items-center gap-2",children:[S.jsx(fN,{className:"h-4 w-4"}),"المستحقين للكشوفات"]}),S.jsxs(po,{value:"search",className:"flex items-center gap-2",children:[S.jsx(eN,{className:"h-4 w-4"}),"البحث المتقدم"]}),S.jsxs(po,{value:"settings",className:"flex items-center gap-2",children:[S.jsx(aN,{className:"h-4 w-4"}),"الإعدادات"]})]}),S.jsx(go,{value:"input",children:S.jsx(dn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(hn,{className:"max-w-2xl mx-auto neomorphism hover-lift",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-center gradient-text text-2xl",children:"📥 إدخال بيانات موظف جديد"})}),S.jsx(gn,{children:S.jsxs("form",{onSubmit:g,className:"space-y-6",children:[S.jsxs("div",{className:"space-y-2",children:[S.jsxs(Ja,{htmlFor:"name",className:"flex items-center gap-2",children:[S.jsx(uN,{className:"h-4 w-4"}),"اسم الموظف"]}),S.jsx(Qi,{id:"name",value:u.name,onChange:E=>d({...u,name:E.target.value}),placeholder:"أدخل اسم الموظف",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(Ja,{htmlFor:"department",className:"flex items-center gap-2",children:[S.jsx(HO,{className:"h-4 w-4"}),"القسم"]}),S.jsx(Qi,{id:"department",value:u.department,onChange:E=>d({...u,department:E.target.value}),placeholder:"أدخل اسم القسم",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(Ja,{htmlFor:"startMonth",className:"flex items-center gap-2",children:[S.jsx(GO,{className:"h-4 w-4"}),"أول شهر للكشف 🌙"]}),S.jsxs(dN,{value:u.startMonth,onValueChange:E=>d({...u,startMonth:E}),required:!0,children:[S.jsx(mN,{children:S.jsx(hN,{placeholder:"اختر الشهر"})}),S.jsx(pN,{children:p.map(E=>S.jsx(gN,{value:E,children:E},E))})]})]}),S.jsx($a,{type:"submit",className:"w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6",children:"➕ حفظ البيانات"})]})})]})})}),S.jsx(go,{value:"employees",children:S.jsx(dn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(hn,{className:"neomorphism",children:[S.jsxs(mn,{className:"flex flex-row items-center justify-between",children:[S.jsx(pn,{className:"gradient-text text-2xl",children:"📄 المستحقين للكشوفات"}),S.jsxs($a,{onClick:v,className:"bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500",children:[S.jsx(JO,{className:"mr-2 h-4 w-4"}),"🔁 تحديث الكشفات"]})]}),S.jsx(gn,{children:s.length===0?S.jsxs("div",{className:"text-center py-12",children:[S.jsx(sN,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),S.jsx("p",{className:"text-gray-500 text-lg",children:"لا توجد بيانات موظفين حتى الآن"})]}):S.jsxs($v,{children:[S.jsx(Jv,{children:S.jsxs(Vf,{children:[S.jsx(yn,{className:"text-right",children:"#"}),S.jsx(yn,{className:"text-right",children:"اسم الموظف"}),S.jsx(yn,{className:"text-right",children:"القسم"}),S.jsx(yn,{className:"text-right",children:"أول شهر"}),S.jsx(yn,{className:"text-right",children:"الحالة"})]})}),S.jsx(t0,{children:s.map((E,_)=>S.jsxs(dn.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:_*.1},className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[S.jsx(vn,{children:_+1}),S.jsx(vn,{className:"font-medium",children:E.name}),S.jsx(vn,{children:E.department}),S.jsx(vn,{children:E.startMonth}),S.jsx(vn,{children:S.jsx(e0,{className:E.status?"status-required":"status-completed",children:E.status?S.jsxs(S.Fragment,{children:[S.jsx(Iv,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(Wv,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})})]},E.id))})]})})]})})}),S.jsx(go,{value:"search",children:S.jsxs(dn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[S.jsxs(hn,{className:"neomorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"gradient-text text-2xl",children:"🔍 البحث المتقدم"})}),S.jsx(gn,{children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[S.jsxs("div",{children:[S.jsx(Ja,{children:"البحث بالاسم"}),S.jsx(Qi,{placeholder:"🔎 اسم الموظف",value:f.name,onChange:E=>h({...f,name:E.target.value}),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(Ja,{children:"القسم"}),S.jsx(Qi,{placeholder:"🏢 اسم القسم",value:f.department,onChange:E=>h({...f,department:E.target.value}),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(Ja,{children:"أول شهر"}),S.jsx(Qi,{placeholder:"🗓️ اسم الشهر",value:f.startMonth,onChange:E=>h({...f,startMonth:E.target.value}),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(Ja,{children:"الحالة"}),S.jsx(Qi,{placeholder:"🔍 الحالة (مطلوب/تم)",value:f.status,onChange:E=>h({...f,status:E.target.value}),className:"text-right"})]})]})})]}),S.jsxs(hn,{className:"neomorphism",children:[S.jsx(mn,{children:S.jsx(pn,{children:"نتائج البحث"})}),S.jsx(gn,{children:M.length===0?S.jsx("div",{className:"text-center py-8",children:S.jsx("p",{className:"text-gray-500",children:"🚫 لا توجد بيانات مطابقة!"})}):S.jsxs($v,{children:[S.jsx(Jv,{children:S.jsxs(Vf,{children:[S.jsx(yn,{className:"text-right",children:"#"}),S.jsx(yn,{className:"text-right",children:"اسم الموظف"}),S.jsx(yn,{className:"text-right",children:"القسم"}),S.jsx(yn,{className:"text-right",children:"أول شهر"}),S.jsx(yn,{className:"text-right",children:"الحالة"})]})}),S.jsx(t0,{children:M.map((E,_)=>S.jsxs(Vf,{children:[S.jsx(vn,{children:_+1}),S.jsx(vn,{className:"font-medium",children:E.name}),S.jsx(vn,{children:E.department}),S.jsx(vn,{children:E.startMonth}),S.jsx(vn,{children:S.jsx(e0,{className:E.status?"status-required":"status-completed",children:E.status?S.jsxs(S.Fragment,{children:[S.jsx(Iv,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(Wv,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})})]},E.id))})]})})]})]})}),S.jsx(go,{value:"settings",children:S.jsx(dn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto space-y-6",children:S.jsxs(hn,{className:"neomorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"gradient-text text-2xl",children:"⚙️ الإعدادات"})}),S.jsx(gn,{className:"space-y-6",children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[S.jsxs(hn,{className:"glassmorphism md:col-span-2",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-lg",children:"💾 إدارة البيانات"})}),S.jsxs(gn,{children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[S.jsx($a,{onClick:x,className:"bg-blue-500 hover:bg-blue-600 text-white",children:"📁 تصدير البيانات"}),S.jsxs("div",{children:[S.jsx("input",{type:"file",accept:".json",onChange:A,style:{display:"none"},id:"import-file"}),S.jsx($a,{onClick:()=>document.getElementById("import-file").click(),className:"bg-green-500 hover:bg-green-600 text-white w-full",children:"📥 استيراد البيانات"})]}),S.jsx($a,{onClick:w,className:"bg-red-500 hover:bg-red-600 text-white",children:"🗑️ مسح جميع البيانات"})]}),S.jsxs("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[S.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 ",S.jsx("strong",{children:"نصائح:"})]}),S.jsxs("ul",{className:"text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1",children:[S.jsx("li",{children:"• يتم حفظ البيانات تلقائياً في متصفحك"}),S.jsx("li",{children:"• استخدم التصدير لإنشاء نسخة احتياطية"}),S.jsx("li",{children:"• يمكنك استيراد البيانات من ملف JSON"}),S.jsx("li",{children:"• البيانات محفوظة حتى لو أغلقت المتصفح"})]})]})]})]}),S.jsxs(hn,{className:"glassmorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-lg",children:"📝 نبذة عن البرنامج"})}),S.jsx(gn,{children:S.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات."})})]}),S.jsxs(hn,{className:"glassmorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-lg",children:"👨‍💻 عن المبرمج"})}),S.jsx(gn,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"علي عاجل خشان المحنّة"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"مبرمج محترف بخبرة واسعة في:"}),S.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[S.jsx("li",{children:"• VB.NET / C# / Java"}),S.jsx("li",{children:"• JavaScript / React / Node.js"}),S.jsx("li",{children:"• Microsoft Access و Excel VBA"}),S.jsx("li",{children:"• أنظمة الأرشفة الذكية والتحكم الآلي"})]})]})})]}),S.jsxs(hn,{className:"glassmorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-lg",children:"🆔 إصدار البرنامج"})}),S.jsx(gn,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"v2.0.0"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات."})]})})]}),S.jsxs(hn,{className:"glassmorphism",children:[S.jsx(mn,{children:S.jsx(pn,{className:"text-lg",children:"🎖️ حقوق التصميم"})}),S.jsx(gn,{children:S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"جميع الحقوق محفوظة © 2025"})})]})]})})]})})})]})})]})})}ew.createRoot(document.getElementById("root")).render(S.jsx(T.StrictMode,{children:S.jsx(YN,{})}));
