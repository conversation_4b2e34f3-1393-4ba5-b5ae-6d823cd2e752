{"version": 3, "file": "spawn-promise.js", "sourceRoot": "", "sources": ["../src/spawn-promise.ts"], "names": [], "mappings": ";;AAAA,+CAA2E;AAE3E,IAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,CAAC;AAE/D,uDAAuD;AACvD,EAAE;AACF,+BAA+B;AAC/B,2CAA2C;AAC3C,4DAA4D;AAC5D,EAAE;AACF,yEAAyE;AACzE,kBAAkB;AAClB,SAAwB,KAAK,CAAC,GAAW,EAAE,MAAgB,EAAE,IAA+B;IAC1F,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC;QAEhB,CAAC,CAAC,mBAAY,GAAG,cAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,IAAA,qBAAO,EAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,GAAG,IAAA,qBAAO,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SACnC;QAED,wDAAwD;QACxD,4BAA4B;QAC5B,4BAA4B;QAC5B,2BAA2B;QAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAM,OAAO,GAAG;YACd,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,IAAM,UAAU,GAAG,UAAC,KAAa;YAC/B,MAAM,IAAI,KAAK,CAAC;QAClB,CAAC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,CAAQ,IAAW,OAAA,MAAM,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,IAAY;YAC5B,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO,EAAE,CAAC;aACX;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAC;gBAChB,MAAM,CAAC,IAAI,KAAK,CAAC,iCAA0B,IAAI,wBAAc,MAAM,CAAE,CAAC,CAAC,CAAC;aACzE;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AA1CD,wBA0CC"}