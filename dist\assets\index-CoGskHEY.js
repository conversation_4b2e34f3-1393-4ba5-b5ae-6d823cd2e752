var aw=Object.defineProperty,iw=Object.defineProperties;var sw=Object.getOwnPropertyDescriptors;var uo=Object.getOwnPropertySymbols;var ly=Object.prototype.hasOwnProperty,oy=Object.prototype.propertyIsEnumerable;var df=Math.pow,ry=(n,a,s)=>a in n?aw(n,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[a]=s,N=(n,a)=>{for(var s in a||(a={}))ly.call(a,s)&&ry(n,s,a[s]);if(uo)for(var s of uo(a))oy.call(a,s)&&ry(n,s,a[s]);return n},Z=(n,a)=>iw(n,sw(a));var et=(n,a)=>{var s={};for(var l in n)ly.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&uo)for(var l of uo(n))a.indexOf(l)<0&&oy.call(n,l)&&(s[l]=n[l]);return s};var rw=(n,a)=>()=>(a||n((a={exports:{}}).exports,a),a.exports);var fn=(n,a,s)=>new Promise((l,c)=>{var d=m=>{try{h(s.next(m))}catch(p){c(p)}},u=m=>{try{h(s.throw(m))}catch(p){c(p)}},h=m=>m.done?l(m.value):Promise.resolve(m.value).then(d,u);h((s=s.apply(n,a)).next())});var i3=rw(US=>{function lw(n,a){for(var s=0;s<a.length;s++){const l=a[s];if(typeof l!="string"&&!Array.isArray(l)){for(const c in l)if(c!=="default"&&!(c in n)){const d=Object.getOwnPropertyDescriptor(l,c);d&&Object.defineProperty(n,c,d.get?d:{enumerable:!0,get:()=>l[c]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))l(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const u of d.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&l(u)}).observe(document,{childList:!0,subtree:!0});function s(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(c){if(c.ep)return;c.ep=!0;const d=s(c);fetch(c.href,d)}})();function m0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var hf={exports:{}},br={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cy;function ow(){if(cy)return br;cy=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(l,c,d){var u=null;if(d!==void 0&&(u=""+d),c.key!==void 0&&(u=""+c.key),"key"in c){d={};for(var h in c)h!=="key"&&(d[h]=c[h])}else d=c;return c=d.ref,{$$typeof:n,type:l,key:u,ref:c!==void 0?c:null,props:d}}return br.Fragment=a,br.jsx=s,br.jsxs=s,br}var uy;function cw(){return uy||(uy=1,hf.exports=ow()),hf.exports}var S=cw(),mf={exports:{}},St={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy;function uw(){if(fy)return St;fy=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),u=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function b(O){return O===null||typeof O!="object"?null:(O=v&&O[v]||O["@@iterator"],typeof O=="function"?O:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},D=Object.assign,A={};function E(O,K,tt){this.props=O,this.context=K,this.refs=A,this.updater=tt||T}E.prototype.isReactComponent={},E.prototype.setState=function(O,K){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,K,"setState")},E.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function _(){}_.prototype=E.prototype;function z(O,K,tt){this.props=O,this.context=K,this.refs=A,this.updater=tt||T}var j=z.prototype=new _;j.constructor=z,D(j,E.prototype),j.isPureReactComponent=!0;var M=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},X=Object.prototype.hasOwnProperty;function W(O,K,tt,J,at,vt){return tt=vt.ref,{$$typeof:n,type:O,key:K,ref:tt!==void 0?tt:null,props:vt}}function H(O,K){return W(O.type,K,void 0,void 0,void 0,O.props)}function $(O){return typeof O=="object"&&O!==null&&O.$$typeof===n}function ct(O){var K={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(tt){return K[tt]})}var ht=/\/+/g;function lt(O,K){return typeof O=="object"&&O!==null&&O.key!=null?ct(""+O.key):K.toString(36)}function bt(){}function Et(O){switch(O.status){case"fulfilled":return O.value;case"rejected":throw O.reason;default:switch(typeof O.status=="string"?O.then(bt,bt):(O.status="pending",O.then(function(K){O.status==="pending"&&(O.status="fulfilled",O.value=K)},function(K){O.status==="pending"&&(O.status="rejected",O.reason=K)})),O.status){case"fulfilled":return O.value;case"rejected":throw O.reason}}throw O}function ut(O,K,tt,J,at){var vt=typeof O;(vt==="undefined"||vt==="boolean")&&(O=null);var dt=!1;if(O===null)dt=!0;else switch(vt){case"bigint":case"string":case"number":dt=!0;break;case"object":switch(O.$$typeof){case n:case a:dt=!0;break;case g:return dt=O._init,ut(dt(O._payload),K,tt,J,at)}}if(dt)return at=at(O),dt=J===""?"."+lt(O,0):J,M(at)?(tt="",dt!=null&&(tt=dt.replace(ht,"$&/")+"/"),ut(at,K,tt,"",function(Tt){return Tt})):at!=null&&($(at)&&(at=H(at,tt+(at.key==null||O&&O.key===at.key?"":(""+at.key).replace(ht,"$&/")+"/")+dt)),K.push(at)),1;dt=0;var xt=J===""?".":J+":";if(M(O))for(var nt=0;nt<O.length;nt++)J=O[nt],vt=xt+lt(J,nt),dt+=ut(J,K,tt,vt,at);else if(nt=b(O),typeof nt=="function")for(O=nt.call(O),nt=0;!(J=O.next()).done;)J=J.value,vt=xt+lt(J,nt++),dt+=ut(J,K,tt,vt,at);else if(vt==="object"){if(typeof O.then=="function")return ut(Et(O),K,tt,J,at);throw K=String(O),Error("Objects are not valid as a React child (found: "+(K==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":K)+"). If you meant to render a collection of children, use an array instead.")}return dt}function B(O,K,tt){if(O==null)return O;var J=[],at=0;return ut(O,J,"","",function(vt){return K.call(tt,vt,at++)}),J}function q(O){if(O._status===-1){var K=O._result;K=K(),K.then(function(tt){(O._status===0||O._status===-1)&&(O._status=1,O._result=tt)},function(tt){(O._status===0||O._status===-1)&&(O._status=2,O._result=tt)}),O._status===-1&&(O._status=0,O._result=K)}if(O._status===1)return O._result.default;throw O._result}var G=typeof reportError=="function"?reportError:function(O){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var K=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof O=="object"&&O!==null&&typeof O.message=="string"?String(O.message):String(O),error:O});if(!window.dispatchEvent(K))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",O);return}console.error(O)};function ft(){}return St.Children={map:B,forEach:function(O,K,tt){B(O,function(){K.apply(this,arguments)},tt)},count:function(O){var K=0;return B(O,function(){K++}),K},toArray:function(O){return B(O,function(K){return K})||[]},only:function(O){if(!$(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},St.Component=E,St.Fragment=s,St.Profiler=c,St.PureComponent=z,St.StrictMode=l,St.Suspense=m,St.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,St.__COMPILER_RUNTIME={__proto__:null,c:function(O){return C.H.useMemoCache(O)}},St.cache=function(O){return function(){return O.apply(null,arguments)}},St.cloneElement=function(O,K,tt){if(O==null)throw Error("The argument must be a React element, but you passed "+O+".");var J=D({},O.props),at=O.key,vt=void 0;if(K!=null)for(dt in K.ref!==void 0&&(vt=void 0),K.key!==void 0&&(at=""+K.key),K)!X.call(K,dt)||dt==="key"||dt==="__self"||dt==="__source"||dt==="ref"&&K.ref===void 0||(J[dt]=K[dt]);var dt=arguments.length-2;if(dt===1)J.children=tt;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];J.children=xt}return W(O.type,at,void 0,void 0,vt,J)},St.createContext=function(O){return O={$$typeof:u,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null},O.Provider=O,O.Consumer={$$typeof:d,_context:O},O},St.createElement=function(O,K,tt){var J,at={},vt=null;if(K!=null)for(J in K.key!==void 0&&(vt=""+K.key),K)X.call(K,J)&&J!=="key"&&J!=="__self"&&J!=="__source"&&(at[J]=K[J]);var dt=arguments.length-2;if(dt===1)at.children=tt;else if(1<dt){for(var xt=Array(dt),nt=0;nt<dt;nt++)xt[nt]=arguments[nt+2];at.children=xt}if(O&&O.defaultProps)for(J in dt=O.defaultProps,dt)at[J]===void 0&&(at[J]=dt[J]);return W(O,vt,void 0,void 0,null,at)},St.createRef=function(){return{current:null}},St.forwardRef=function(O){return{$$typeof:h,render:O}},St.isValidElement=$,St.lazy=function(O){return{$$typeof:g,_payload:{_status:-1,_result:O},_init:q}},St.memo=function(O,K){return{$$typeof:p,type:O,compare:K===void 0?null:K}},St.startTransition=function(O){var K=C.T,tt={};C.T=tt;try{var J=O(),at=C.S;at!==null&&at(tt,J),typeof J=="object"&&J!==null&&typeof J.then=="function"&&J.then(ft,G)}catch(vt){G(vt)}finally{C.T=K}},St.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},St.use=function(O){return C.H.use(O)},St.useActionState=function(O,K,tt){return C.H.useActionState(O,K,tt)},St.useCallback=function(O,K){return C.H.useCallback(O,K)},St.useContext=function(O){return C.H.useContext(O)},St.useDebugValue=function(){},St.useDeferredValue=function(O,K){return C.H.useDeferredValue(O,K)},St.useEffect=function(O,K,tt){var J=C.H;if(typeof tt=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return J.useEffect(O,K)},St.useId=function(){return C.H.useId()},St.useImperativeHandle=function(O,K,tt){return C.H.useImperativeHandle(O,K,tt)},St.useInsertionEffect=function(O,K){return C.H.useInsertionEffect(O,K)},St.useLayoutEffect=function(O,K){return C.H.useLayoutEffect(O,K)},St.useMemo=function(O,K){return C.H.useMemo(O,K)},St.useOptimistic=function(O,K){return C.H.useOptimistic(O,K)},St.useReducer=function(O,K,tt){return C.H.useReducer(O,K,tt)},St.useRef=function(O){return C.H.useRef(O)},St.useState=function(O){return C.H.useState(O)},St.useSyncExternalStore=function(O,K,tt){return C.H.useSyncExternalStore(O,K,tt)},St.useTransition=function(){return C.H.useTransition()},St.version="19.1.0",St}var dy;function Ad(){return dy||(dy=1,mf.exports=uw()),mf.exports}var w=Ad();const Ea=m0(w),p0=lw({__proto__:null,default:Ea},[w]);var pf={exports:{}},xr={},gf={exports:{}},yf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hy;function fw(){return hy||(hy=1,function(n){function a(B,q){var G=B.length;B.push(q);t:for(;0<G;){var ft=G-1>>>1,O=B[ft];if(0<c(O,q))B[ft]=q,B[G]=O,G=ft;else break t}}function s(B){return B.length===0?null:B[0]}function l(B){if(B.length===0)return null;var q=B[0],G=B.pop();if(G!==q){B[0]=G;t:for(var ft=0,O=B.length,K=O>>>1;ft<K;){var tt=2*(ft+1)-1,J=B[tt],at=tt+1,vt=B[at];if(0>c(J,G))at<O&&0>c(vt,J)?(B[ft]=vt,B[at]=G,ft=at):(B[ft]=J,B[tt]=G,ft=tt);else if(at<O&&0>c(vt,G))B[ft]=vt,B[at]=G,ft=at;else break t}}return q}function c(B,q){var G=B.sortIndex-q.sortIndex;return G!==0?G:B.id-q.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var u=Date,h=u.now();n.unstable_now=function(){return u.now()-h}}var m=[],p=[],g=1,v=null,b=3,T=!1,D=!1,A=!1,E=!1,_=typeof setTimeout=="function"?setTimeout:null,z=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate!="undefined"?setImmediate:null;function M(B){for(var q=s(p);q!==null;){if(q.callback===null)l(p);else if(q.startTime<=B)l(p),q.sortIndex=q.expirationTime,a(m,q);else break;q=s(p)}}function C(B){if(A=!1,M(B),!D)if(s(m)!==null)D=!0,X||(X=!0,lt());else{var q=s(p);q!==null&&ut(C,q.startTime-B)}}var X=!1,W=-1,H=5,$=-1;function ct(){return E?!0:!(n.unstable_now()-$<H)}function ht(){if(E=!1,X){var B=n.unstable_now();$=B;var q=!0;try{t:{D=!1,A&&(A=!1,z(W),W=-1),T=!0;var G=b;try{e:{for(M(B),v=s(m);v!==null&&!(v.expirationTime>B&&ct());){var ft=v.callback;if(typeof ft=="function"){v.callback=null,b=v.priorityLevel;var O=ft(v.expirationTime<=B);if(B=n.unstable_now(),typeof O=="function"){v.callback=O,M(B),q=!0;break e}v===s(m)&&l(m),M(B)}else l(m);v=s(m)}if(v!==null)q=!0;else{var K=s(p);K!==null&&ut(C,K.startTime-B),q=!1}}break t}finally{v=null,b=G,T=!1}q=void 0}}finally{q?lt():X=!1}}}var lt;if(typeof j=="function")lt=function(){j(ht)};else if(typeof MessageChannel!="undefined"){var bt=new MessageChannel,Et=bt.port2;bt.port1.onmessage=ht,lt=function(){Et.postMessage(null)}}else lt=function(){_(ht,0)};function ut(B,q){W=_(function(){B(n.unstable_now())},q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):H=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(B){switch(b){case 1:case 2:case 3:var q=3;break;default:q=b}var G=b;b=q;try{return B()}finally{b=G}},n.unstable_requestPaint=function(){E=!0},n.unstable_runWithPriority=function(B,q){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var G=b;b=B;try{return q()}finally{b=G}},n.unstable_scheduleCallback=function(B,q,G){var ft=n.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?ft+G:ft):G=ft,B){case 1:var O=-1;break;case 2:O=250;break;case 5:O=1073741823;break;case 4:O=1e4;break;default:O=5e3}return O=G+O,B={id:g++,callback:q,priorityLevel:B,startTime:G,expirationTime:O,sortIndex:-1},G>ft?(B.sortIndex=G,a(p,B),s(m)===null&&B===s(p)&&(A?(z(W),W=-1):A=!0,ut(C,G-ft))):(B.sortIndex=O,a(m,B),D||T||(D=!0,X||(X=!0,lt()))),B},n.unstable_shouldYield=ct,n.unstable_wrapCallback=function(B){var q=b;return function(){var G=b;b=q;try{return B.apply(this,arguments)}finally{b=G}}}}(yf)),yf}var my;function dw(){return my||(my=1,gf.exports=fw()),gf.exports}var vf={exports:{}},xe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function hw(){if(py)return xe;py=1;var n=Ad();function a(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)p+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(m,p,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:v==null?null:""+v,children:m,containerInfo:p,implementation:g}}var u=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return xe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,xe.createPortal=function(m,p){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(a(299));return d(m,p,null,g)},xe.flushSync=function(m){var p=u.T,g=l.p;try{if(u.T=null,l.p=2,m)return m()}finally{u.T=p,l.p=g,l.d.f()}},xe.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,l.d.C(m,p))},xe.prefetchDNS=function(m){typeof m=="string"&&l.d.D(m)},xe.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,T=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;g==="style"?l.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:b,fetchPriority:T}):g==="script"&&l.d.X(m,{crossOrigin:v,integrity:b,fetchPriority:T,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},xe.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var g=h(p.as,p.crossOrigin);l.d.M(m,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&l.d.M(m)},xe.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin);l.d.L(m,g,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},xe.preloadModule=function(m,p){if(typeof m=="string")if(p){var g=h(p.as,p.crossOrigin);l.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else l.d.m(m)},xe.requestFormReset=function(m){l.d.r(m)},xe.unstable_batchedUpdates=function(m,p){return m(p)},xe.useFormState=function(m,p,g){return u.H.useFormState(m,p,g)},xe.useFormStatus=function(){return u.H.useHostTransitionStatus()},xe.version="19.1.0",xe}var gy;function g0(){if(gy)return vf.exports;gy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),vf.exports=hw(),vf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yy;function mw(){if(yy)return xr;yy=1;var n=dw(),a=Ad(),s=g0();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function u(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(d(t)!==t)throw Error(l(188))}function m(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(l(188));return e!==t?null:t}for(var i=t,r=e;;){var o=i.return;if(o===null)break;var f=o.alternate;if(f===null){if(r=o.return,r!==null){i=r;continue}break}if(o.child===f.child){for(f=o.child;f;){if(f===i)return h(o),t;if(f===r)return h(o),e;f=f.sibling}throw Error(l(188))}if(i.return!==r.return)i=o,r=f;else{for(var y=!1,x=o.child;x;){if(x===i){y=!0,i=o,r=f;break}if(x===r){y=!0,r=o,i=f;break}x=x.sibling}if(!y){for(x=f.child;x;){if(x===i){y=!0,i=f,r=o;break}if(x===r){y=!0,r=f,i=o;break}x=x.sibling}if(!y)throw Error(l(189))}}if(i.alternate!==r)throw Error(l(190))}if(i.tag!==3)throw Error(l(188));return i.stateNode.current===i?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),z=Symbol.for("react.consumer"),j=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),X=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),H=Symbol.for("react.lazy"),$=Symbol.for("react.activity"),ct=Symbol.for("react.memo_cache_sentinel"),ht=Symbol.iterator;function lt(t){return t===null||typeof t!="object"?null:(t=ht&&t[ht]||t["@@iterator"],typeof t=="function"?t:null)}var bt=Symbol.for("react.client.reference");function Et(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===bt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case D:return"Fragment";case E:return"Profiler";case A:return"StrictMode";case C:return"Suspense";case X:return"SuspenseList";case $:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case T:return"Portal";case j:return(t.displayName||"Context")+".Provider";case z:return(t._context.displayName||"Context")+".Consumer";case M:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case W:return e=t.displayName||null,e!==null?e:Et(t.type)||"Memo";case H:e=t._payload,t=t._init;try{return Et(t(e))}catch(i){}}return null}var ut=Array.isArray,B=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G={pending:!1,data:null,method:null,action:null},ft=[],O=-1;function K(t){return{current:t}}function tt(t){0>O||(t.current=ft[O],ft[O]=null,O--)}function J(t,e){O++,ft[O]=t.current,t.current=e}var at=K(null),vt=K(null),dt=K(null),xt=K(null);function nt(t,e){switch(J(dt,e),J(vt,t),J(at,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Vg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Vg(e),t=zg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}tt(at),J(at,t)}function Tt(){tt(at),tt(vt),tt(dt)}function qt(t){t.memoizedState!==null&&J(xt,t);var e=at.current,i=zg(e,t.type);e!==i&&(J(vt,t),J(at,i))}function Ot(t){vt.current===t&&(tt(at),tt(vt)),xt.current===t&&(tt(xt),mr._currentValue=G)}var Ct=Object.prototype.hasOwnProperty,jt=n.unstable_scheduleCallback,de=n.unstable_cancelCallback,Ae=n.unstable_shouldYield,ln=n.unstable_requestPaint,he=n.unstable_now,La=n.unstable_getCurrentPriorityLevel,Fr=n.unstable_ImmediatePriority,Ir=n.unstable_UserBlockingPriority,di=n.unstable_NormalPriority,hi=n.unstable_LowPriority,gh=n.unstable_IdlePriority,kS=n.log,HS=n.unstable_setDisableYieldValue,Ts=null,Ve=null;function ta(t){if(typeof kS=="function"&&HS(t),Ve&&typeof Ve.setStrictMode=="function")try{Ve.setStrictMode(Ts,t)}catch(e){}}var ze=Math.clz32?Math.clz32:YS,PS=Math.log,GS=Math.LN2;function YS(t){return t>>>=0,t===0?32:31-(PS(t)/GS|0)|0}var $r=256,Wr=4194304;function Ba(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Jr(t,e,i){var r=t.pendingLanes;if(r===0)return 0;var o=0,f=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var x=r&134217727;return x!==0?(r=x&~f,r!==0?o=Ba(r):(y&=x,y!==0?o=Ba(y):i||(i=x&~t,i!==0&&(o=Ba(i))))):(x=r&~f,x!==0?o=Ba(x):y!==0?o=Ba(y):i||(i=r&~t,i!==0&&(o=Ba(i)))),o===0?0:e!==0&&e!==o&&(e&f)===0&&(f=o&-o,i=e&-e,f>=i||f===32&&(i&4194048)!==0)?e:o}function ws(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function qS(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function yh(){var t=$r;return $r<<=1,($r&4194048)===0&&($r=256),t}function vh(){var t=Wr;return Wr<<=1,(Wr&62914560)===0&&(Wr=4194304),t}function tc(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function As(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function XS(t,e,i,r,o,f){var y=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var x=t.entanglements,R=t.expirationTimes,k=t.hiddenUpdates;for(i=y&~i;0<i;){var Q=31-ze(i),I=1<<Q;x[Q]=0,R[Q]=-1;var P=k[Q];if(P!==null)for(k[Q]=null,Q=0;Q<P.length;Q++){var Y=P[Q];Y!==null&&(Y.lane&=-536870913)}i&=~I}r!==0&&bh(t,r,0),f!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=f&~(y&~e))}function bh(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-ze(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|i&4194090}function xh(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var r=31-ze(i),o=1<<r;o&e|t[r]&e&&(t[r]|=e),i&=~o}}function ec(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function nc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Sh(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:ty(t.type))}function KS(t,e){var i=q.p;try{return q.p=t,e()}finally{q.p=i}}var ea=Math.random().toString(36).slice(2),ve="__reactFiber$"+ea,Re="__reactProps$"+ea,mi="__reactContainer$"+ea,ac="__reactEvents$"+ea,ZS="__reactListeners$"+ea,QS="__reactHandles$"+ea,Th="__reactResources$"+ea,Es="__reactMarker$"+ea;function ic(t){delete t[ve],delete t[Re],delete t[ac],delete t[ZS],delete t[QS]}function pi(t){var e=t[ve];if(e)return e;for(var i=t.parentNode;i;){if(e=i[mi]||i[ve]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=kg(t);t!==null;){if(i=t[ve])return i;t=kg(t)}return e}t=i,i=t.parentNode}return null}function gi(t){if(t=t[ve]||t[mi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ms(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function yi(t){var e=t[Th];return e||(e=t[Th]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function oe(t){t[Es]=!0}var wh=new Set,Ah={};function Ua(t,e){vi(t,e),vi(t+"Capture",e)}function vi(t,e){for(Ah[t]=e,t=0;t<e.length;t++)wh.add(e[t])}var FS=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Eh={},Mh={};function IS(t){return Ct.call(Mh,t)?!0:Ct.call(Eh,t)?!1:FS.test(t)?Mh[t]=!0:(Eh[t]=!0,!1)}function tl(t,e,i){if(IS(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function el(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function _n(t,e,i,r){if(r===null)t.removeAttribute(i);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+r)}}var sc,Ch;function bi(t){if(sc===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);sc=e&&e[1]||"",Ch=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+sc+t+Ch}var rc=!1;function lc(t,e){if(!t||rc)return"";rc=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var I=function(){throw Error()};if(Object.defineProperty(I.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(I,[])}catch(Y){var P=Y}Reflect.construct(t,[],I)}else{try{I.call()}catch(Y){P=Y}t.call(I.prototype)}}else{try{throw Error()}catch(Y){P=Y}(I=t())&&typeof I.catch=="function"&&I.catch(function(){})}}catch(Y){if(Y&&P&&typeof Y.stack=="string")return[Y.stack,P.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=r.DetermineComponentFrameRoot(),y=f[0],x=f[1];if(y&&x){var R=y.split(`
`),k=x.split(`
`);for(o=r=0;r<R.length&&!R[r].includes("DetermineComponentFrameRoot");)r++;for(;o<k.length&&!k[o].includes("DetermineComponentFrameRoot");)o++;if(r===R.length||o===k.length)for(r=R.length-1,o=k.length-1;1<=r&&0<=o&&R[r]!==k[o];)o--;for(;1<=r&&0<=o;r--,o--)if(R[r]!==k[o]){if(r!==1||o!==1)do if(r--,o--,0>o||R[r]!==k[o]){var Q=`
`+R[r].replace(" at new "," at ");return t.displayName&&Q.includes("<anonymous>")&&(Q=Q.replace("<anonymous>",t.displayName)),Q}while(1<=r&&0<=o);break}}}finally{rc=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?bi(i):""}function $S(t){switch(t.tag){case 26:case 27:case 5:return bi(t.type);case 16:return bi("Lazy");case 13:return bi("Suspense");case 19:return bi("SuspenseList");case 0:case 15:return lc(t.type,!1);case 11:return lc(t.type.render,!1);case 1:return lc(t.type,!0);case 31:return bi("Activity");default:return""}}function Rh(t){try{var e="";do e+=$S(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Xe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Dh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function WS(t){var e=Dh(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof i!="undefined"&&typeof i.get=="function"&&typeof i.set=="function"){var o=i.get,f=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){r=""+y,f.call(this,y)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function nl(t){t._valueTracker||(t._valueTracker=WS(t))}function Oh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),r="";return t&&(r=Dh(t)?t.checked?"true":"false":t.value),t=r,t!==i?(e.setValue(t),!0):!1}function al(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var JS=/[\n"\\]/g;function Ke(t){return t.replace(JS,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function oc(t,e,i,r,o,f,y,x){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Xe(e)):t.value!==""+Xe(e)&&(t.value=""+Xe(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?cc(t,y,Xe(e)):i!=null?cc(t,y,Xe(i)):r!=null&&t.removeAttribute("value"),o==null&&f!=null&&(t.defaultChecked=!!f),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?t.name=""+Xe(x):t.removeAttribute("name")}function Nh(t,e,i,r,o,f,y,x){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||i!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;i=i!=null?""+Xe(i):"",e=e!=null?""+Xe(e):i,x||e===t.value||(t.value=e),t.defaultValue=e}r=r!=null?r:o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=x?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function cc(t,e,i){e==="number"&&al(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function xi(t,e,i,r){if(t=t.options,e){e={};for(var o=0;o<i.length;o++)e["$"+i[o]]=!0;for(i=0;i<t.length;i++)o=e.hasOwnProperty("$"+t[i].value),t[i].selected!==o&&(t[i].selected=o),o&&r&&(t[i].defaultSelected=!0)}else{for(i=""+Xe(i),e=null,o=0;o<t.length;o++){if(t[o].value===i){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function jh(t,e,i){if(e!=null&&(e=""+Xe(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Xe(i):""}function _h(t,e,i,r){if(e==null){if(r!=null){if(i!=null)throw Error(l(92));if(ut(r)){if(1<r.length)throw Error(l(93));r=r[0]}i=r}i==null&&(i=""),e=i}i=Xe(e),t.defaultValue=i,r=t.textContent,r===i&&r!==""&&r!==null&&(t.value=r)}function Si(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var t1=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Vh(t,e,i){var r=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,i):typeof i!="number"||i===0||t1.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function zh(t,e,i){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,i!=null){for(var r in i)!i.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var o in e)r=e[o],e.hasOwnProperty(o)&&i[o]!==r&&Vh(t,o,r)}else for(var f in e)e.hasOwnProperty(f)&&Vh(t,f,e[f])}function uc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var e1=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),n1=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function il(t){return n1.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var fc=null;function dc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ti=null,wi=null;function Lh(t){var e=gi(t);if(e&&(t=e.stateNode)){var i=t[Re]||null;t:switch(t=e.stateNode,e.type){case"input":if(oc(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Ke(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var r=i[e];if(r!==t&&r.form===t.form){var o=r[Re]||null;if(!o)throw Error(l(90));oc(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<i.length;e++)r=i[e],r.form===t.form&&Oh(r)}break t;case"textarea":jh(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&xi(t,!!i.multiple,e,!1)}}}var hc=!1;function Bh(t,e,i){if(hc)return t(e,i);hc=!0;try{var r=t(e);return r}finally{if(hc=!1,(Ti!==null||wi!==null)&&(Yl(),Ti&&(e=Ti,t=wi,wi=Ti=null,Lh(e),t)))for(e=0;e<t.length;e++)Lh(t[e])}}function Cs(t,e){var i=t.stateNode;if(i===null)return null;var r=i[Re]||null;if(r===null)return null;i=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(l(231,e,typeof i));return i}var Vn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),mc=!1;if(Vn)try{var Rs={};Object.defineProperty(Rs,"passive",{get:function(){mc=!0}}),window.addEventListener("test",Rs,Rs),window.removeEventListener("test",Rs,Rs)}catch(t){mc=!1}var na=null,pc=null,sl=null;function Uh(){if(sl)return sl;var t,e=pc,i=e.length,r,o="value"in na?na.value:na.textContent,f=o.length;for(t=0;t<i&&e[t]===o[t];t++);var y=i-t;for(r=1;r<=y&&e[i-r]===o[f-r];r++);return sl=o.slice(t,1<r?1-r:void 0)}function rl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ll(){return!0}function kh(){return!1}function De(t){function e(i,r,o,f,y){this._reactName=i,this._targetInst=o,this.type=r,this.nativeEvent=f,this.target=y,this.currentTarget=null;for(var x in t)t.hasOwnProperty(x)&&(i=t[x],this[x]=i?i(f):f[x]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?ll:kh,this.isPropagationStopped=kh,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ll)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ll)},persist:function(){},isPersistent:ll}),e}var ka={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ol=De(ka),Ds=g({},ka,{view:0,detail:0}),a1=De(Ds),gc,yc,Os,cl=g({},Ds,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Os&&(Os&&t.type==="mousemove"?(gc=t.screenX-Os.screenX,yc=t.screenY-Os.screenY):yc=gc=0,Os=t),gc)},movementY:function(t){return"movementY"in t?t.movementY:yc}}),Hh=De(cl),i1=g({},cl,{dataTransfer:0}),s1=De(i1),r1=g({},Ds,{relatedTarget:0}),vc=De(r1),l1=g({},ka,{animationName:0,elapsedTime:0,pseudoElement:0}),o1=De(l1),c1=g({},ka,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),u1=De(c1),f1=g({},ka,{data:0}),Ph=De(f1),d1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},h1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},m1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function p1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=m1[t])?!!e[t]:!1}function bc(){return p1}var g1=g({},Ds,{key:function(t){if(t.key){var e=d1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=rl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?h1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bc,charCode:function(t){return t.type==="keypress"?rl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?rl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),y1=De(g1),v1=g({},cl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gh=De(v1),b1=g({},Ds,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bc}),x1=De(b1),S1=g({},ka,{propertyName:0,elapsedTime:0,pseudoElement:0}),T1=De(S1),w1=g({},cl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),A1=De(w1),E1=g({},ka,{newState:0,oldState:0}),M1=De(E1),C1=[9,13,27,32],xc=Vn&&"CompositionEvent"in window,Ns=null;Vn&&"documentMode"in document&&(Ns=document.documentMode);var R1=Vn&&"TextEvent"in window&&!Ns,Yh=Vn&&(!xc||Ns&&8<Ns&&11>=Ns),qh=" ",Xh=!1;function Kh(t,e){switch(t){case"keyup":return C1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ai=!1;function D1(t,e){switch(t){case"compositionend":return Zh(e);case"keypress":return e.which!==32?null:(Xh=!0,qh);case"textInput":return t=e.data,t===qh&&Xh?null:t;default:return null}}function O1(t,e){if(Ai)return t==="compositionend"||!xc&&Kh(t,e)?(t=Uh(),sl=pc=na=null,Ai=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Yh&&e.locale!=="ko"?null:e.data;default:return null}}var N1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!N1[t.type]:e==="textarea"}function Fh(t,e,i,r){Ti?wi?wi.push(r):wi=[r]:Ti=r,e=Fl(e,"onChange"),0<e.length&&(i=new ol("onChange","change",null,i,r),t.push({event:i,listeners:e}))}var js=null,_s=null;function j1(t){Dg(t,0)}function ul(t){var e=Ms(t);if(Oh(e))return t}function Ih(t,e){if(t==="change")return e}var $h=!1;if(Vn){var Sc;if(Vn){var Tc="oninput"in document;if(!Tc){var Wh=document.createElement("div");Wh.setAttribute("oninput","return;"),Tc=typeof Wh.oninput=="function"}Sc=Tc}else Sc=!1;$h=Sc&&(!document.documentMode||9<document.documentMode)}function Jh(){js&&(js.detachEvent("onpropertychange",tm),_s=js=null)}function tm(t){if(t.propertyName==="value"&&ul(_s)){var e=[];Fh(e,_s,t,dc(t)),Bh(j1,e)}}function _1(t,e,i){t==="focusin"?(Jh(),js=e,_s=i,js.attachEvent("onpropertychange",tm)):t==="focusout"&&Jh()}function V1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ul(_s)}function z1(t,e){if(t==="click")return ul(e)}function L1(t,e){if(t==="input"||t==="change")return ul(e)}function B1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Le=typeof Object.is=="function"?Object.is:B1;function Vs(t,e){if(Le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),r=Object.keys(e);if(i.length!==r.length)return!1;for(r=0;r<i.length;r++){var o=i[r];if(!Ct.call(e,o)||!Le(t[o],e[o]))return!1}return!0}function em(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function nm(t,e){var i=em(t);t=0;for(var r;i;){if(i.nodeType===3){if(r=t+i.textContent.length,t<=e&&r>=e)return{node:i,offset:e-t};t=r}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=em(i)}}function am(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?am(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function im(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=al(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch(r){i=!1}if(i)t=e.contentWindow;else break;e=al(t.document)}return e}function wc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var U1=Vn&&"documentMode"in document&&11>=document.documentMode,Ei=null,Ac=null,zs=null,Ec=!1;function sm(t,e,i){var r=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Ec||Ei==null||Ei!==al(r)||(r=Ei,"selectionStart"in r&&wc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zs&&Vs(zs,r)||(zs=r,r=Fl(Ac,"onSelect"),0<r.length&&(e=new ol("onSelect","select",null,e,i),t.push({event:e,listeners:r}),e.target=Ei)))}function Ha(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var Mi={animationend:Ha("Animation","AnimationEnd"),animationiteration:Ha("Animation","AnimationIteration"),animationstart:Ha("Animation","AnimationStart"),transitionrun:Ha("Transition","TransitionRun"),transitionstart:Ha("Transition","TransitionStart"),transitioncancel:Ha("Transition","TransitionCancel"),transitionend:Ha("Transition","TransitionEnd")},Mc={},rm={};Vn&&(rm=document.createElement("div").style,"AnimationEvent"in window||(delete Mi.animationend.animation,delete Mi.animationiteration.animation,delete Mi.animationstart.animation),"TransitionEvent"in window||delete Mi.transitionend.transition);function Pa(t){if(Mc[t])return Mc[t];if(!Mi[t])return t;var e=Mi[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in rm)return Mc[t]=e[i];return t}var lm=Pa("animationend"),om=Pa("animationiteration"),cm=Pa("animationstart"),k1=Pa("transitionrun"),H1=Pa("transitionstart"),P1=Pa("transitioncancel"),um=Pa("transitionend"),fm=new Map,Cc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cc.push("scrollEnd");function on(t,e){fm.set(t,e),Ua(e,[t])}var dm=new WeakMap;function Ze(t,e){if(typeof t=="object"&&t!==null){var i=dm.get(t);return i!==void 0?i:(e={value:t,source:e,stack:Rh(e)},dm.set(t,e),e)}return{value:t,source:e,stack:Rh(e)}}var Qe=[],Ci=0,Rc=0;function fl(){for(var t=Ci,e=Rc=Ci=0;e<t;){var i=Qe[e];Qe[e++]=null;var r=Qe[e];Qe[e++]=null;var o=Qe[e];Qe[e++]=null;var f=Qe[e];if(Qe[e++]=null,r!==null&&o!==null){var y=r.pending;y===null?o.next=o:(o.next=y.next,y.next=o),r.pending=o}f!==0&&hm(i,o,f)}}function dl(t,e,i,r){Qe[Ci++]=t,Qe[Ci++]=e,Qe[Ci++]=i,Qe[Ci++]=r,Rc|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function Dc(t,e,i,r){return dl(t,e,i,r),hl(t)}function Ri(t,e){return dl(t,null,null,e),hl(t)}function hm(t,e,i){t.lanes|=i;var r=t.alternate;r!==null&&(r.lanes|=i);for(var o=!1,f=t.return;f!==null;)f.childLanes|=i,r=f.alternate,r!==null&&(r.childLanes|=i),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(o=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,o&&e!==null&&(o=31-ze(i),t=f.hiddenUpdates,r=t[o],r===null?t[o]=[e]:r.push(e),e.lane=i|536870912),f):null}function hl(t){if(50<rr)throw rr=0,zu=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Di={};function G1(t,e,i,r){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Be(t,e,i,r){return new G1(t,e,i,r)}function Oc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function zn(t,e){var i=t.alternate;return i===null?(i=Be(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function mm(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ml(t,e,i,r,o,f){var y=0;if(r=t,typeof t=="function")Oc(t)&&(y=1);else if(typeof t=="string")y=qT(t,i,at.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case $:return t=Be(31,i,e,o),t.elementType=$,t.lanes=f,t;case D:return Ga(i.children,o,f,e);case A:y=8,o|=24;break;case E:return t=Be(12,i,e,o|2),t.elementType=E,t.lanes=f,t;case C:return t=Be(13,i,e,o),t.elementType=C,t.lanes=f,t;case X:return t=Be(19,i,e,o),t.elementType=X,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case _:case j:y=10;break t;case z:y=9;break t;case M:y=11;break t;case W:y=14;break t;case H:y=16,r=null;break t}y=29,i=Error(l(130,t===null?"null":typeof t,"")),r=null}return e=Be(y,i,e,o),e.elementType=t,e.type=r,e.lanes=f,e}function Ga(t,e,i,r){return t=Be(7,t,r,e),t.lanes=i,t}function Nc(t,e,i){return t=Be(6,t,null,e),t.lanes=i,t}function jc(t,e,i){return e=Be(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oi=[],Ni=0,pl=null,gl=0,Fe=[],Ie=0,Ya=null,Ln=1,Bn="";function qa(t,e){Oi[Ni++]=gl,Oi[Ni++]=pl,pl=t,gl=e}function pm(t,e,i){Fe[Ie++]=Ln,Fe[Ie++]=Bn,Fe[Ie++]=Ya,Ya=t;var r=Ln;t=Bn;var o=32-ze(r)-1;r&=~(1<<o),i+=1;var f=32-ze(e)+o;if(30<f){var y=o-o%5;f=(r&(1<<y)-1).toString(32),r>>=y,o-=y,Ln=1<<32-ze(e)+o|i<<o|r,Bn=f+t}else Ln=1<<f|i<<o|r,Bn=t}function _c(t){t.return!==null&&(qa(t,1),pm(t,1,0))}function Vc(t){for(;t===pl;)pl=Oi[--Ni],Oi[Ni]=null,gl=Oi[--Ni],Oi[Ni]=null;for(;t===Ya;)Ya=Fe[--Ie],Fe[Ie]=null,Bn=Fe[--Ie],Fe[Ie]=null,Ln=Fe[--Ie],Fe[Ie]=null}var Ee=null,It=null,Vt=!1,Xa=null,gn=!1,zc=Error(l(519));function Ka(t){var e=Error(l(418,""));throw Us(Ze(e,t)),zc}function gm(t){var e=t.stateNode,i=t.type,r=t.memoizedProps;switch(e[ve]=t,e[Re]=r,i){case"dialog":Dt("cancel",e),Dt("close",e);break;case"iframe":case"object":case"embed":Dt("load",e);break;case"video":case"audio":for(i=0;i<or.length;i++)Dt(or[i],e);break;case"source":Dt("error",e);break;case"img":case"image":case"link":Dt("error",e),Dt("load",e);break;case"details":Dt("toggle",e);break;case"input":Dt("invalid",e),Nh(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),nl(e);break;case"select":Dt("invalid",e);break;case"textarea":Dt("invalid",e),_h(e,r.value,r.defaultValue,r.children),nl(e)}i=r.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||r.suppressHydrationWarning===!0||_g(e.textContent,i)?(r.popover!=null&&(Dt("beforetoggle",e),Dt("toggle",e)),r.onScroll!=null&&Dt("scroll",e),r.onScrollEnd!=null&&Dt("scrollend",e),r.onClick!=null&&(e.onclick=Il),e=!0):e=!1,e||Ka(t)}function ym(t){for(Ee=t.return;Ee;)switch(Ee.tag){case 5:case 13:gn=!1;return;case 27:case 3:gn=!0;return;default:Ee=Ee.return}}function Ls(t){if(t!==Ee)return!1;if(!Vt)return ym(t),Vt=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||$u(t.type,t.memoizedProps)),i=!i),i&&It&&Ka(t),ym(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){It=un(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}It=null}}else e===27?(e=It,va(t.type)?(t=ef,ef=null,It=t):It=e):It=Ee?un(t.stateNode.nextSibling):null;return!0}function Bs(){It=Ee=null,Vt=!1}function vm(){var t=Xa;return t!==null&&(je===null?je=t:je.push.apply(je,t),Xa=null),t}function Us(t){Xa===null?Xa=[t]:Xa.push(t)}var Lc=K(null),Za=null,Un=null;function aa(t,e,i){J(Lc,e._currentValue),e._currentValue=i}function kn(t){t._currentValue=Lc.current,tt(Lc)}function Bc(t,e,i){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===i)break;t=t.return}}function Uc(t,e,i,r){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var f=o.dependencies;if(f!==null){var y=o.child;f=f.firstContext;t:for(;f!==null;){var x=f;f=o;for(var R=0;R<e.length;R++)if(x.context===e[R]){f.lanes|=i,x=f.alternate,x!==null&&(x.lanes|=i),Bc(f.return,i,t),r||(y=null);break t}f=x.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(l(341));y.lanes|=i,f=y.alternate,f!==null&&(f.lanes|=i),Bc(y,i,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function ks(t,e,i,r){t=null;for(var o=e,f=!1;o!==null;){if(!f){if((o.flags&524288)!==0)f=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var x=o.type;Le(o.pendingProps.value,y.value)||(t!==null?t.push(x):t=[x])}}else if(o===xt.current){if(y=o.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(mr):t=[mr])}o=o.return}t!==null&&Uc(e,t,i,r),e.flags|=262144}function yl(t){for(t=t.firstContext;t!==null;){if(!Le(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Qa(t){Za=t,Un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function be(t){return bm(Za,t)}function vl(t,e){return Za===null&&Qa(t),bm(t,e)}function bm(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},Un===null){if(t===null)throw Error(l(308));Un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Un=Un.next=e;return i}var Y1=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},q1=n.unstable_scheduleCallback,X1=n.unstable_NormalPriority,se={$$typeof:j,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function kc(){return{controller:new Y1,data:new Map,refCount:0}}function Hs(t){t.refCount--,t.refCount===0&&q1(X1,function(){t.controller.abort()})}var Ps=null,Hc=0,ji=0,_i=null;function K1(t,e){if(Ps===null){var i=Ps=[];Hc=0,ji=Gu(),_i={status:"pending",value:void 0,then:function(r){i.push(r)}}}return Hc++,e.then(xm,xm),e}function xm(){if(--Hc===0&&Ps!==null){_i!==null&&(_i.status="fulfilled");var t=Ps;Ps=null,ji=0,_i=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Z1(t,e){var i=[],r={status:"pending",value:null,reason:null,then:function(o){i.push(o)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var o=0;o<i.length;o++)(0,i[o])(e)},function(o){for(r.status="rejected",r.reason=o,o=0;o<i.length;o++)(0,i[o])(void 0)}),r}var Sm=B.S;B.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&K1(t,e),Sm!==null&&Sm(t,e)};var Fa=K(null);function Pc(){var t=Fa.current;return t!==null?t:Yt.pooledCache}function bl(t,e){e===null?J(Fa,Fa.current):J(Fa,e.pool)}function Tm(){var t=Pc();return t===null?null:{parent:se._currentValue,pool:t}}var Gs=Error(l(460)),wm=Error(l(474)),xl=Error(l(542)),Gc={then:function(){}};function Am(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Sl(){}function Em(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(Sl,Sl),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t;default:if(typeof e.status=="string")e.then(Sl,Sl);else{if(t=Yt,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=r}},function(r){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Cm(t),t}throw Ys=e,Gs}}var Ys=null;function Mm(){if(Ys===null)throw Error(l(459));var t=Ys;return Ys=null,t}function Cm(t){if(t===Gs||t===xl)throw Error(l(483))}var ia=!1;function Yc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function sa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ra(t,e,i){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(zt&2)!==0){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,e=hl(t),hm(t,null,i),e}return dl(t,r,e,i),hl(t)}function qs(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,xh(t,i)}}function Xc(t,e){var i=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,i===r)){var o=null,f=null;if(i=i.firstBaseUpdate,i!==null){do{var y={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};f===null?o=f=y:f=f.next=y,i=i.next}while(i!==null);f===null?o=f=e:f=f.next=e}else o=f=e;i={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:f,shared:r.shared,callbacks:r.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var Kc=!1;function Xs(){if(Kc){var t=_i;if(t!==null)throw t}}function Ks(t,e,i,r){Kc=!1;var o=t.updateQueue;ia=!1;var f=o.firstBaseUpdate,y=o.lastBaseUpdate,x=o.shared.pending;if(x!==null){o.shared.pending=null;var R=x,k=R.next;R.next=null,y===null?f=k:y.next=k,y=R;var Q=t.alternate;Q!==null&&(Q=Q.updateQueue,x=Q.lastBaseUpdate,x!==y&&(x===null?Q.firstBaseUpdate=k:x.next=k,Q.lastBaseUpdate=R))}if(f!==null){var I=o.baseState;y=0,Q=k=R=null,x=f;do{var P=x.lane&-536870913,Y=P!==x.lane;if(Y?(Nt&P)===P:(r&P)===P){P!==0&&P===ji&&(Kc=!0),Q!==null&&(Q=Q.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});t:{var gt=t,mt=x;P=e;var Ht=i;switch(mt.tag){case 1:if(gt=mt.payload,typeof gt=="function"){I=gt.call(Ht,I,P);break t}I=gt;break t;case 3:gt.flags=gt.flags&-65537|128;case 0:if(gt=mt.payload,P=typeof gt=="function"?gt.call(Ht,I,P):gt,P==null)break t;I=g({},I,P);break t;case 2:ia=!0}}P=x.callback,P!==null&&(t.flags|=64,Y&&(t.flags|=8192),Y=o.callbacks,Y===null?o.callbacks=[P]:Y.push(P))}else Y={lane:P,tag:x.tag,payload:x.payload,callback:x.callback,next:null},Q===null?(k=Q=Y,R=I):Q=Q.next=Y,y|=P;if(x=x.next,x===null){if(x=o.shared.pending,x===null)break;Y=x,x=Y.next,Y.next=null,o.lastBaseUpdate=Y,o.shared.pending=null}}while(!0);Q===null&&(R=I),o.baseState=R,o.firstBaseUpdate=k,o.lastBaseUpdate=Q,f===null&&(o.shared.lanes=0),ma|=y,t.lanes=y,t.memoizedState=I}}function Rm(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Dm(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)Rm(i[t],e)}var Vi=K(null),Tl=K(0);function Om(t,e){t=Kn,J(Tl,t),J(Vi,e),Kn=t|e.baseLanes}function Zc(){J(Tl,Kn),J(Vi,Vi.current)}function Qc(){Kn=Tl.current,tt(Vi),tt(Tl)}var la=0,wt=null,Ut=null,ne=null,wl=!1,zi=!1,Ia=!1,Al=0,Zs=0,Li=null,Q1=0;function Jt(){throw Error(l(321))}function Fc(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!Le(t[i],e[i]))return!1;return!0}function Ic(t,e,i,r,o,f){return la=f,wt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,B.H=t===null||t.memoizedState===null?hp:mp,Ia=!1,f=i(r,o),Ia=!1,zi&&(f=jm(e,i,r,o)),Nm(t),f}function Nm(t){B.H=Ol;var e=Ut!==null&&Ut.next!==null;if(la=0,ne=Ut=wt=null,wl=!1,Zs=0,Li=null,e)throw Error(l(300));t===null||ce||(t=t.dependencies,t!==null&&yl(t)&&(ce=!0))}function jm(t,e,i,r){wt=t;var o=0;do{if(zi&&(Li=null),Zs=0,zi=!1,25<=o)throw Error(l(301));if(o+=1,ne=Ut=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}B.H=eT,f=e(i,r)}while(zi);return f}function F1(){var t=B.H,e=t.useState()[0];return e=typeof e.then=="function"?Qs(e):e,t=t.useState()[0],(Ut!==null?Ut.memoizedState:null)!==t&&(wt.flags|=1024),e}function $c(){var t=Al!==0;return Al=0,t}function Wc(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function Jc(t){if(wl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}wl=!1}la=0,ne=Ut=wt=null,zi=!1,Zs=Al=0,Li=null}function Oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?wt.memoizedState=ne=t:ne=ne.next=t,ne}function ae(){if(Ut===null){var t=wt.alternate;t=t!==null?t.memoizedState:null}else t=Ut.next;var e=ne===null?wt.memoizedState:ne.next;if(e!==null)ne=e,Ut=t;else{if(t===null)throw wt.alternate===null?Error(l(467)):Error(l(310));Ut=t,t={memoizedState:Ut.memoizedState,baseState:Ut.baseState,baseQueue:Ut.baseQueue,queue:Ut.queue,next:null},ne===null?wt.memoizedState=ne=t:ne=ne.next=t}return ne}function tu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qs(t){var e=Zs;return Zs+=1,Li===null&&(Li=[]),t=Em(Li,t,e),e=wt,(ne===null?e.memoizedState:ne.next)===null&&(e=e.alternate,B.H=e===null||e.memoizedState===null?hp:mp),t}function El(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qs(t);if(t.$$typeof===j)return be(t)}throw Error(l(438,String(t)))}function eu(t){var e=null,i=wt.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var r=wt.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=tu(),wt.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),r=0;r<t;r++)i[r]=ct;return e.index++,i}function Hn(t,e){return typeof e=="function"?e(t):e}function Ml(t){var e=ae();return nu(e,Ut,t)}function nu(t,e,i){var r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=i;var o=t.baseQueue,f=r.pending;if(f!==null){if(o!==null){var y=o.next;o.next=f.next,f.next=y}e.baseQueue=o=f,r.pending=null}if(f=t.baseState,o===null)t.memoizedState=f;else{e=o.next;var x=y=null,R=null,k=e,Q=!1;do{var I=k.lane&-536870913;if(I!==k.lane?(Nt&I)===I:(la&I)===I){var P=k.revertLane;if(P===0)R!==null&&(R=R.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),I===ji&&(Q=!0);else if((la&P)===P){k=k.next,P===ji&&(Q=!0);continue}else I={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=I,y=f):R=R.next=I,wt.lanes|=P,ma|=P;I=k.action,Ia&&i(f,I),f=k.hasEagerState?k.eagerState:i(f,I)}else P={lane:I,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},R===null?(x=R=P,y=f):R=R.next=P,wt.lanes|=I,ma|=I;k=k.next}while(k!==null&&k!==e);if(R===null?y=f:R.next=x,!Le(f,t.memoizedState)&&(ce=!0,Q&&(i=_i,i!==null)))throw i;t.memoizedState=f,t.baseState=y,t.baseQueue=R,r.lastRenderedState=f}return o===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function au(t){var e=ae(),i=e.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=t;var r=i.dispatch,o=i.pending,f=e.memoizedState;if(o!==null){i.pending=null;var y=o=o.next;do f=t(f,y.action),y=y.next;while(y!==o);Le(f,e.memoizedState)||(ce=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),i.lastRenderedState=f}return[f,r]}function _m(t,e,i){var r=wt,o=ae(),f=Vt;if(f){if(i===void 0)throw Error(l(407));i=i()}else i=e();var y=!Le((Ut||o).memoizedState,i);y&&(o.memoizedState=i,ce=!0),o=o.queue;var x=Lm.bind(null,r,o,t);if(Fs(2048,8,x,[t]),o.getSnapshot!==e||y||ne!==null&&ne.memoizedState.tag&1){if(r.flags|=2048,Bi(9,Cl(),zm.bind(null,r,o,i,e),null),Yt===null)throw Error(l(349));f||(la&124)!==0||Vm(r,e,i)}return i}function Vm(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=wt.updateQueue,e===null?(e=tu(),wt.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function zm(t,e,i,r){e.value=i,e.getSnapshot=r,Bm(e)&&Um(t)}function Lm(t,e,i){return i(function(){Bm(e)&&Um(t)})}function Bm(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!Le(t,i)}catch(r){return!0}}function Um(t){var e=Ri(t,2);e!==null&&Ge(e,t,2)}function iu(t){var e=Oe();if(typeof t=="function"){var i=t;if(t=i(),Ia){ta(!0);try{i()}finally{ta(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:t},e}function km(t,e,i,r){return t.baseState=i,nu(t,Ut,typeof r=="function"?r:Hn)}function I1(t,e,i,r,o){if(Dl(t))throw Error(l(485));if(t=e.action,t!==null){var f={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){f.listeners.push(y)}};B.T!==null?i(!0):f.isTransition=!1,r(f),i=e.pending,i===null?(f.next=e.pending=f,Hm(e,f)):(f.next=i.next,e.pending=i.next=f)}}function Hm(t,e){var i=e.action,r=e.payload,o=t.state;if(e.isTransition){var f=B.T,y={};B.T=y;try{var x=i(o,r),R=B.S;R!==null&&R(y,x),Pm(t,e,x)}catch(k){su(t,e,k)}finally{B.T=f}}else try{f=i(o,r),Pm(t,e,f)}catch(k){su(t,e,k)}}function Pm(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(r){Gm(t,e,r)},function(r){return su(t,e,r)}):Gm(t,e,i)}function Gm(t,e,i){e.status="fulfilled",e.value=i,Ym(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,Hm(t,i)))}function su(t,e,i){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=i,Ym(e),e=e.next;while(e!==r)}t.action=null}function Ym(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function qm(t,e){return e}function Xm(t,e){if(Vt){var i=Yt.formState;if(i!==null){t:{var r=wt;if(Vt){if(It){e:{for(var o=It,f=gn;o.nodeType!==8;){if(!f){o=null;break e}if(o=un(o.nextSibling),o===null){o=null;break e}}f=o.data,o=f==="F!"||f==="F"?o:null}if(o){It=un(o.nextSibling),r=o.data==="F!";break t}}Ka(r)}r=!1}r&&(e=i[0])}}return i=Oe(),i.memoizedState=i.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qm,lastRenderedState:e},i.queue=r,i=up.bind(null,wt,r),r.dispatch=i,r=iu(!1),f=uu.bind(null,wt,!1,r.queue),r=Oe(),o={state:e,dispatch:null,action:t,pending:null},r.queue=o,i=I1.bind(null,wt,o,f,i),o.dispatch=i,r.memoizedState=t,[e,i,!1]}function Km(t){var e=ae();return Zm(e,Ut,t)}function Zm(t,e,i){if(e=nu(t,e,qm)[0],t=Ml(Hn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Qs(e)}catch(y){throw y===Gs?xl:y}else r=e;e=ae();var o=e.queue,f=o.dispatch;return i!==e.memoizedState&&(wt.flags|=2048,Bi(9,Cl(),$1.bind(null,o,i),null)),[r,f,t]}function $1(t,e){t.action=e}function Qm(t){var e=ae(),i=Ut;if(i!==null)return Zm(e,i,t);ae(),e=e.memoizedState,i=ae();var r=i.queue.dispatch;return i.memoizedState=t,[e,r,!1]}function Bi(t,e,i,r){return t={tag:t,create:i,deps:r,inst:e,next:null},e=wt.updateQueue,e===null&&(e=tu(),wt.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(r=i.next,i.next=t,t.next=r,e.lastEffect=t),t}function Cl(){return{destroy:void 0,resource:void 0}}function Fm(){return ae().memoizedState}function Rl(t,e,i,r){var o=Oe();r=r===void 0?null:r,wt.flags|=t,o.memoizedState=Bi(1|e,Cl(),i,r)}function Fs(t,e,i,r){var o=ae();r=r===void 0?null:r;var f=o.memoizedState.inst;Ut!==null&&r!==null&&Fc(r,Ut.memoizedState.deps)?o.memoizedState=Bi(e,f,i,r):(wt.flags|=t,o.memoizedState=Bi(1|e,f,i,r))}function Im(t,e){Rl(8390656,8,t,e)}function $m(t,e){Fs(2048,8,t,e)}function Wm(t,e){return Fs(4,2,t,e)}function Jm(t,e){return Fs(4,4,t,e)}function tp(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ep(t,e,i){i=i!=null?i.concat([t]):null,Fs(4,4,tp.bind(null,e,t),i)}function ru(){}function np(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;return e!==null&&Fc(e,r[1])?r[0]:(i.memoizedState=[t,e],t)}function ap(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;if(e!==null&&Fc(e,r[1]))return r[0];if(r=t(),Ia){ta(!0);try{t()}finally{ta(!1)}}return i.memoizedState=[r,e],r}function lu(t,e,i){return i===void 0||(la&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=rg(),wt.lanes|=t,ma|=t,i)}function ip(t,e,i,r){return Le(i,e)?i:Vi.current!==null?(t=lu(t,i,r),Le(t,e)||(ce=!0),t):(la&42)===0?(ce=!0,t.memoizedState=i):(t=rg(),wt.lanes|=t,ma|=t,e)}function sp(t,e,i,r,o){var f=q.p;q.p=f!==0&&8>f?f:8;var y=B.T,x={};B.T=x,uu(t,!1,e,i);try{var R=o(),k=B.S;if(k!==null&&k(x,R),R!==null&&typeof R=="object"&&typeof R.then=="function"){var Q=Z1(R,r);Is(t,e,Q,Pe(t))}else Is(t,e,r,Pe(t))}catch(I){Is(t,e,{then:function(){},status:"rejected",reason:I},Pe())}finally{q.p=f,B.T=y}}function W1(){}function ou(t,e,i,r){if(t.tag!==5)throw Error(l(476));var o=rp(t).queue;sp(t,o,e,G,i===null?W1:function(){return lp(t),i(r)})}function rp(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:G,baseState:G,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:G},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function lp(t){var e=rp(t).next.queue;Is(t,e,{},Pe())}function cu(){return be(mr)}function op(){return ae().memoizedState}function cp(){return ae().memoizedState}function J1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=Pe();t=sa(i);var r=ra(e,t,i);r!==null&&(Ge(r,e,i),qs(r,e,i)),e={cache:kc()},t.payload=e;return}e=e.return}}function tT(t,e,i){var r=Pe();i={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Dl(t)?fp(e,i):(i=Dc(t,e,i,r),i!==null&&(Ge(i,t,r),dp(i,e,r)))}function up(t,e,i){var r=Pe();Is(t,e,i,r)}function Is(t,e,i,r){var o={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Dl(t))fp(e,o);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var y=e.lastRenderedState,x=f(y,i);if(o.hasEagerState=!0,o.eagerState=x,Le(x,y))return dl(t,e,o,0),Yt===null&&fl(),!1}catch(R){}finally{}if(i=Dc(t,e,o,r),i!==null)return Ge(i,t,r),dp(i,e,r),!0}return!1}function uu(t,e,i,r){if(r={lane:2,revertLane:Gu(),action:r,hasEagerState:!1,eagerState:null,next:null},Dl(t)){if(e)throw Error(l(479))}else e=Dc(t,i,r,2),e!==null&&Ge(e,t,2)}function Dl(t){var e=t.alternate;return t===wt||e!==null&&e===wt}function fp(t,e){zi=wl=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function dp(t,e,i){if((i&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,xh(t,i)}}var Ol={readContext:be,use:El,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},hp={readContext:be,use:El,useCallback:function(t,e){return Oe().memoizedState=[t,e===void 0?null:e],t},useContext:be,useEffect:Im,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Rl(4194308,4,tp.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Rl(4194308,4,t,e)},useInsertionEffect:function(t,e){Rl(4,2,t,e)},useMemo:function(t,e){var i=Oe();e=e===void 0?null:e;var r=t();if(Ia){ta(!0);try{t()}finally{ta(!1)}}return i.memoizedState=[r,e],r},useReducer:function(t,e,i){var r=Oe();if(i!==void 0){var o=i(e);if(Ia){ta(!0);try{i(e)}finally{ta(!1)}}}else o=e;return r.memoizedState=r.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},r.queue=t,t=t.dispatch=tT.bind(null,wt,t),[r.memoizedState,t]},useRef:function(t){var e=Oe();return t={current:t},e.memoizedState=t},useState:function(t){t=iu(t);var e=t.queue,i=up.bind(null,wt,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:ru,useDeferredValue:function(t,e){var i=Oe();return lu(i,t,e)},useTransition:function(){var t=iu(!1);return t=sp.bind(null,wt,t.queue,!0,!1),Oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var r=wt,o=Oe();if(Vt){if(i===void 0)throw Error(l(407));i=i()}else{if(i=e(),Yt===null)throw Error(l(349));(Nt&124)!==0||Vm(r,e,i)}o.memoizedState=i;var f={value:i,getSnapshot:e};return o.queue=f,Im(Lm.bind(null,r,f,t),[t]),r.flags|=2048,Bi(9,Cl(),zm.bind(null,r,f,i,e),null),i},useId:function(){var t=Oe(),e=Yt.identifierPrefix;if(Vt){var i=Bn,r=Ln;i=(r&~(1<<32-ze(r)-1)).toString(32)+i,e="«"+e+"R"+i,i=Al++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=Q1++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:cu,useFormState:Xm,useActionState:Xm,useOptimistic:function(t){var e=Oe();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=uu.bind(null,wt,!0,i),i.dispatch=e,[t,e]},useMemoCache:eu,useCacheRefresh:function(){return Oe().memoizedState=J1.bind(null,wt)}},mp={readContext:be,use:El,useCallback:np,useContext:be,useEffect:$m,useImperativeHandle:ep,useInsertionEffect:Wm,useLayoutEffect:Jm,useMemo:ap,useReducer:Ml,useRef:Fm,useState:function(){return Ml(Hn)},useDebugValue:ru,useDeferredValue:function(t,e){var i=ae();return ip(i,Ut.memoizedState,t,e)},useTransition:function(){var t=Ml(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:_m,useId:op,useHostTransitionStatus:cu,useFormState:Km,useActionState:Km,useOptimistic:function(t,e){var i=ae();return km(i,Ut,t,e)},useMemoCache:eu,useCacheRefresh:cp},eT={readContext:be,use:El,useCallback:np,useContext:be,useEffect:$m,useImperativeHandle:ep,useInsertionEffect:Wm,useLayoutEffect:Jm,useMemo:ap,useReducer:au,useRef:Fm,useState:function(){return au(Hn)},useDebugValue:ru,useDeferredValue:function(t,e){var i=ae();return Ut===null?lu(i,t,e):ip(i,Ut.memoizedState,t,e)},useTransition:function(){var t=au(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:_m,useId:op,useHostTransitionStatus:cu,useFormState:Qm,useActionState:Qm,useOptimistic:function(t,e){var i=ae();return Ut!==null?km(i,Ut,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:eu,useCacheRefresh:cp},Ui=null,$s=0;function Nl(t){var e=$s;return $s+=1,Ui===null&&(Ui=[]),Em(Ui,t,e)}function Ws(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function jl(t,e){throw e.$$typeof===v?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function pp(t){var e=t._init;return e(t._payload)}function gp(t){function e(L,V){if(t){var U=L.deletions;U===null?(L.deletions=[V],L.flags|=16):U.push(V)}}function i(L,V){if(!t)return null;for(;V!==null;)e(L,V),V=V.sibling;return null}function r(L){for(var V=new Map;L!==null;)L.key!==null?V.set(L.key,L):V.set(L.index,L),L=L.sibling;return V}function o(L,V){return L=zn(L,V),L.index=0,L.sibling=null,L}function f(L,V,U){return L.index=U,t?(U=L.alternate,U!==null?(U=U.index,U<V?(L.flags|=67108866,V):U):(L.flags|=67108866,V)):(L.flags|=1048576,V)}function y(L){return t&&L.alternate===null&&(L.flags|=67108866),L}function x(L,V,U,F){return V===null||V.tag!==6?(V=Nc(U,L.mode,F),V.return=L,V):(V=o(V,U),V.return=L,V)}function R(L,V,U,F){var it=U.type;return it===D?Q(L,V,U.props.children,F,U.key):V!==null&&(V.elementType===it||typeof it=="object"&&it!==null&&it.$$typeof===H&&pp(it)===V.type)?(V=o(V,U.props),Ws(V,U),V.return=L,V):(V=ml(U.type,U.key,U.props,null,L.mode,F),Ws(V,U),V.return=L,V)}function k(L,V,U,F){return V===null||V.tag!==4||V.stateNode.containerInfo!==U.containerInfo||V.stateNode.implementation!==U.implementation?(V=jc(U,L.mode,F),V.return=L,V):(V=o(V,U.children||[]),V.return=L,V)}function Q(L,V,U,F,it){return V===null||V.tag!==7?(V=Ga(U,L.mode,F,it),V.return=L,V):(V=o(V,U),V.return=L,V)}function I(L,V,U){if(typeof V=="string"&&V!==""||typeof V=="number"||typeof V=="bigint")return V=Nc(""+V,L.mode,U),V.return=L,V;if(typeof V=="object"&&V!==null){switch(V.$$typeof){case b:return U=ml(V.type,V.key,V.props,null,L.mode,U),Ws(U,V),U.return=L,U;case T:return V=jc(V,L.mode,U),V.return=L,V;case H:var F=V._init;return V=F(V._payload),I(L,V,U)}if(ut(V)||lt(V))return V=Ga(V,L.mode,U,null),V.return=L,V;if(typeof V.then=="function")return I(L,Nl(V),U);if(V.$$typeof===j)return I(L,vl(L,V),U);jl(L,V)}return null}function P(L,V,U,F){var it=V!==null?V.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return it!==null?null:x(L,V,""+U,F);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case b:return U.key===it?R(L,V,U,F):null;case T:return U.key===it?k(L,V,U,F):null;case H:return it=U._init,U=it(U._payload),P(L,V,U,F)}if(ut(U)||lt(U))return it!==null?null:Q(L,V,U,F,null);if(typeof U.then=="function")return P(L,V,Nl(U),F);if(U.$$typeof===j)return P(L,V,vl(L,U),F);jl(L,U)}return null}function Y(L,V,U,F,it){if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return L=L.get(U)||null,x(V,L,""+F,it);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case b:return L=L.get(F.key===null?U:F.key)||null,R(V,L,F,it);case T:return L=L.get(F.key===null?U:F.key)||null,k(V,L,F,it);case H:var Mt=F._init;return F=Mt(F._payload),Y(L,V,U,F,it)}if(ut(F)||lt(F))return L=L.get(U)||null,Q(V,L,F,it,null);if(typeof F.then=="function")return Y(L,V,U,Nl(F),it);if(F.$$typeof===j)return Y(L,V,U,vl(V,F),it);jl(V,F)}return null}function gt(L,V,U,F){for(var it=null,Mt=null,ot=V,pt=V=0,fe=null;ot!==null&&pt<U.length;pt++){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var _t=P(L,ot,U[pt],F);if(_t===null){ot===null&&(ot=fe);break}t&&ot&&_t.alternate===null&&e(L,ot),V=f(_t,V,pt),Mt===null?it=_t:Mt.sibling=_t,Mt=_t,ot=fe}if(pt===U.length)return i(L,ot),Vt&&qa(L,pt),it;if(ot===null){for(;pt<U.length;pt++)ot=I(L,U[pt],F),ot!==null&&(V=f(ot,V,pt),Mt===null?it=ot:Mt.sibling=ot,Mt=ot);return Vt&&qa(L,pt),it}for(ot=r(ot);pt<U.length;pt++)fe=Y(ot,L,pt,U[pt],F),fe!==null&&(t&&fe.alternate!==null&&ot.delete(fe.key===null?pt:fe.key),V=f(fe,V,pt),Mt===null?it=fe:Mt.sibling=fe,Mt=fe);return t&&ot.forEach(function(wa){return e(L,wa)}),Vt&&qa(L,pt),it}function mt(L,V,U,F){if(U==null)throw Error(l(151));for(var it=null,Mt=null,ot=V,pt=V=0,fe=null,_t=U.next();ot!==null&&!_t.done;pt++,_t=U.next()){ot.index>pt?(fe=ot,ot=null):fe=ot.sibling;var wa=P(L,ot,_t.value,F);if(wa===null){ot===null&&(ot=fe);break}t&&ot&&wa.alternate===null&&e(L,ot),V=f(wa,V,pt),Mt===null?it=wa:Mt.sibling=wa,Mt=wa,ot=fe}if(_t.done)return i(L,ot),Vt&&qa(L,pt),it;if(ot===null){for(;!_t.done;pt++,_t=U.next())_t=I(L,_t.value,F),_t!==null&&(V=f(_t,V,pt),Mt===null?it=_t:Mt.sibling=_t,Mt=_t);return Vt&&qa(L,pt),it}for(ot=r(ot);!_t.done;pt++,_t=U.next())_t=Y(ot,L,pt,_t.value,F),_t!==null&&(t&&_t.alternate!==null&&ot.delete(_t.key===null?pt:_t.key),V=f(_t,V,pt),Mt===null?it=_t:Mt.sibling=_t,Mt=_t);return t&&ot.forEach(function(nw){return e(L,nw)}),Vt&&qa(L,pt),it}function Ht(L,V,U,F){if(typeof U=="object"&&U!==null&&U.type===D&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case b:t:{for(var it=U.key;V!==null;){if(V.key===it){if(it=U.type,it===D){if(V.tag===7){i(L,V.sibling),F=o(V,U.props.children),F.return=L,L=F;break t}}else if(V.elementType===it||typeof it=="object"&&it!==null&&it.$$typeof===H&&pp(it)===V.type){i(L,V.sibling),F=o(V,U.props),Ws(F,U),F.return=L,L=F;break t}i(L,V);break}else e(L,V);V=V.sibling}U.type===D?(F=Ga(U.props.children,L.mode,F,U.key),F.return=L,L=F):(F=ml(U.type,U.key,U.props,null,L.mode,F),Ws(F,U),F.return=L,L=F)}return y(L);case T:t:{for(it=U.key;V!==null;){if(V.key===it)if(V.tag===4&&V.stateNode.containerInfo===U.containerInfo&&V.stateNode.implementation===U.implementation){i(L,V.sibling),F=o(V,U.children||[]),F.return=L,L=F;break t}else{i(L,V);break}else e(L,V);V=V.sibling}F=jc(U,L.mode,F),F.return=L,L=F}return y(L);case H:return it=U._init,U=it(U._payload),Ht(L,V,U,F)}if(ut(U))return gt(L,V,U,F);if(lt(U)){if(it=lt(U),typeof it!="function")throw Error(l(150));return U=it.call(U),mt(L,V,U,F)}if(typeof U.then=="function")return Ht(L,V,Nl(U),F);if(U.$$typeof===j)return Ht(L,V,vl(L,U),F);jl(L,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,V!==null&&V.tag===6?(i(L,V.sibling),F=o(V,U),F.return=L,L=F):(i(L,V),F=Nc(U,L.mode,F),F.return=L,L=F),y(L)):i(L,V)}return function(L,V,U,F){try{$s=0;var it=Ht(L,V,U,F);return Ui=null,it}catch(ot){if(ot===Gs||ot===xl)throw ot;var Mt=Be(29,ot,null,L.mode);return Mt.lanes=F,Mt.return=L,Mt}finally{}}}var ki=gp(!0),yp=gp(!1),$e=K(null),yn=null;function oa(t){var e=t.alternate;J(re,re.current&1),J($e,t),yn===null&&(e===null||Vi.current!==null||e.memoizedState!==null)&&(yn=t)}function vp(t){if(t.tag===22){if(J(re,re.current),J($e,t),yn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(yn=t)}}else ca()}function ca(){J(re,re.current),J($e,$e.current)}function Pn(t){tt($e),yn===t&&(yn=null),tt(re)}var re=K(0);function _l(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||tf(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function fu(t,e,i,r){e=t.memoizedState,i=i(r,e),i=i==null?e:g({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var du={enqueueSetState:function(t,e,i){t=t._reactInternals;var r=Pe(),o=sa(r);o.payload=e,i!=null&&(o.callback=i),e=ra(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var r=Pe(),o=sa(r);o.tag=1,o.payload=e,i!=null&&(o.callback=i),e=ra(t,o,r),e!==null&&(Ge(e,t,r),qs(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=Pe(),r=sa(i);r.tag=2,e!=null&&(r.callback=e),e=ra(t,r,i),e!==null&&(Ge(e,t,i),qs(e,t,i))}};function bp(t,e,i,r,o,f,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,f,y):e.prototype&&e.prototype.isPureReactComponent?!Vs(i,r)||!Vs(o,f):!0}function xp(t,e,i,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,r),e.state!==t&&du.enqueueReplaceState(e,e.state,null)}function $a(t,e){var i=e;if("ref"in e){i={};for(var r in e)r!=="ref"&&(i[r]=e[r])}if(t=t.defaultProps){i===e&&(i=g({},i));for(var o in t)i[o]===void 0&&(i[o]=t[o])}return i}var Vl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Sp(t){Vl(t)}function Tp(t){console.error(t)}function wp(t){Vl(t)}function zl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function Ap(t,e,i){try{var r=t.onCaughtError;r(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function hu(t,e,i){return i=sa(i),i.tag=3,i.payload={element:null},i.callback=function(){zl(t,e)},i}function Ep(t){return t=sa(t),t.tag=3,t}function Mp(t,e,i,r){var o=i.type.getDerivedStateFromError;if(typeof o=="function"){var f=r.value;t.payload=function(){return o(f)},t.callback=function(){Ap(e,i,r)}}var y=i.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Ap(e,i,r),typeof o!="function"&&(pa===null?pa=new Set([this]):pa.add(this));var x=r.stack;this.componentDidCatch(r.value,{componentStack:x!==null?x:""})})}function nT(t,e,i,r,o){if(i.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=i.alternate,e!==null&&ks(e,i,o,!0),i=$e.current,i!==null){switch(i.tag){case 13:return yn===null?Bu():i.alternate===null&&$t===0&&($t=3),i.flags&=-257,i.flags|=65536,i.lanes=o,r===Gc?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([r]):e.add(r),ku(t,r,o)),!1;case 22:return i.flags|=65536,r===Gc?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([r]):i.add(r)),ku(t,r,o)),!1}throw Error(l(435,i.tag))}return ku(t,r,o),Bu(),!1}if(Vt)return e=$e.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,r!==zc&&(t=Error(l(422),{cause:r}),Us(Ze(t,i)))):(r!==zc&&(e=Error(l(423),{cause:r}),Us(Ze(e,i))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,r=Ze(r,i),o=hu(t.stateNode,r,o),Xc(t,o),$t!==4&&($t=2)),!1;var f=Error(l(520),{cause:r});if(f=Ze(f,i),sr===null?sr=[f]:sr.push(f),$t!==4&&($t=2),e===null)return!0;r=Ze(r,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=o&-o,i.lanes|=t,t=hu(i.stateNode,r,t),Xc(i,t),!1;case 1:if(e=i.type,f=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(pa===null||!pa.has(f))))return i.flags|=65536,o&=-o,i.lanes|=o,o=Ep(o),Mp(o,t,i,r),Xc(i,o),!1}i=i.return}while(i!==null);return!1}var Cp=Error(l(461)),ce=!1;function me(t,e,i,r){e.child=t===null?yp(e,null,i,r):ki(e,t.child,i,r)}function Rp(t,e,i,r,o){i=i.render;var f=e.ref;if("ref"in r){var y={};for(var x in r)x!=="ref"&&(y[x]=r[x])}else y=r;return Qa(e),r=Ic(t,e,i,y,f,o),x=$c(),t!==null&&!ce?(Wc(t,e,o),Gn(t,e,o)):(Vt&&x&&_c(e),e.flags|=1,me(t,e,r,o),e.child)}function Dp(t,e,i,r,o){if(t===null){var f=i.type;return typeof f=="function"&&!Oc(f)&&f.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=f,Op(t,e,f,r,o)):(t=ml(i.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!Su(t,o)){var y=f.memoizedProps;if(i=i.compare,i=i!==null?i:Vs,i(y,r)&&t.ref===e.ref)return Gn(t,e,o)}return e.flags|=1,t=zn(f,r),t.ref=e.ref,t.return=e,e.child=t}function Op(t,e,i,r,o){if(t!==null){var f=t.memoizedProps;if(Vs(f,r)&&t.ref===e.ref)if(ce=!1,e.pendingProps=r=f,Su(t,o))(t.flags&131072)!==0&&(ce=!0);else return e.lanes=t.lanes,Gn(t,e,o)}return mu(t,e,i,r,o)}function Np(t,e,i){var r=e.pendingProps,o=r.children,f=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=f!==null?f.baseLanes|i:i,t!==null){for(o=e.child=t.child,f=0;o!==null;)f=f|o.lanes|o.childLanes,o=o.sibling;e.childLanes=f&~r}else e.childLanes=0,e.child=null;return jp(t,e,r,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&bl(e,f!==null?f.cachePool:null),f!==null?Om(e,f):Zc(),vp(e);else return e.lanes=e.childLanes=536870912,jp(t,e,f!==null?f.baseLanes|i:i,i)}else f!==null?(bl(e,f.cachePool),Om(e,f),ca(),e.memoizedState=null):(t!==null&&bl(e,null),Zc(),ca());return me(t,e,o,i),e.child}function jp(t,e,i,r){var o=Pc();return o=o===null?null:{parent:se._currentValue,pool:o},e.memoizedState={baseLanes:i,cachePool:o},t!==null&&bl(e,null),Zc(),vp(e),t!==null&&ks(t,e,r,!0),null}function Ll(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(l(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function mu(t,e,i,r,o){return Qa(e),i=Ic(t,e,i,r,void 0,o),r=$c(),t!==null&&!ce?(Wc(t,e,o),Gn(t,e,o)):(Vt&&r&&_c(e),e.flags|=1,me(t,e,i,o),e.child)}function _p(t,e,i,r,o,f){return Qa(e),e.updateQueue=null,i=jm(e,r,i,o),Nm(t),r=$c(),t!==null&&!ce?(Wc(t,e,f),Gn(t,e,f)):(Vt&&r&&_c(e),e.flags|=1,me(t,e,i,f),e.child)}function Vp(t,e,i,r,o){if(Qa(e),e.stateNode===null){var f=Di,y=i.contextType;typeof y=="object"&&y!==null&&(f=be(y)),f=new i(r,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=du,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=r,f.state=e.memoizedState,f.refs={},Yc(e),y=i.contextType,f.context=typeof y=="object"&&y!==null?be(y):Di,f.state=e.memoizedState,y=i.getDerivedStateFromProps,typeof y=="function"&&(fu(e,i,y,r),f.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(y=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),y!==f.state&&du.enqueueReplaceState(f,f.state,null),Ks(e,r,f,o),Xs(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){f=e.stateNode;var x=e.memoizedProps,R=$a(i,x);f.props=R;var k=f.context,Q=i.contextType;y=Di,typeof Q=="object"&&Q!==null&&(y=be(Q));var I=i.getDerivedStateFromProps;Q=typeof I=="function"||typeof f.getSnapshotBeforeUpdate=="function",x=e.pendingProps!==x,Q||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(x||k!==y)&&xp(e,f,r,y),ia=!1;var P=e.memoizedState;f.state=P,Ks(e,r,f,o),Xs(),k=e.memoizedState,x||P!==k||ia?(typeof I=="function"&&(fu(e,i,I,r),k=e.memoizedState),(R=ia||bp(e,i,R,r,P,k,y))?(Q||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=k),f.props=r,f.state=k,f.context=y,r=R):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{f=e.stateNode,qc(t,e),y=e.memoizedProps,Q=$a(i,y),f.props=Q,I=e.pendingProps,P=f.context,k=i.contextType,R=Di,typeof k=="object"&&k!==null&&(R=be(k)),x=i.getDerivedStateFromProps,(k=typeof x=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(y!==I||P!==R)&&xp(e,f,r,R),ia=!1,P=e.memoizedState,f.state=P,Ks(e,r,f,o),Xs();var Y=e.memoizedState;y!==I||P!==Y||ia||t!==null&&t.dependencies!==null&&yl(t.dependencies)?(typeof x=="function"&&(fu(e,i,x,r),Y=e.memoizedState),(Q=ia||bp(e,i,Q,r,P,Y,R)||t!==null&&t.dependencies!==null&&yl(t.dependencies))?(k||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(r,Y,R),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(r,Y,R)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||y===t.memoizedProps&&P===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&P===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=Y),f.props=r,f.state=Y,f.context=R,r=Q):(typeof f.componentDidUpdate!="function"||y===t.memoizedProps&&P===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&P===t.memoizedState||(e.flags|=1024),r=!1)}return f=r,Ll(t,e),r=(e.flags&128)!==0,f||r?(f=e.stateNode,i=r&&typeof i.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&r?(e.child=ki(e,t.child,null,o),e.child=ki(e,null,i,o)):me(t,e,i,o),e.memoizedState=f.state,t=e.child):t=Gn(t,e,o),t}function zp(t,e,i,r){return Bs(),e.flags|=256,me(t,e,i,r),e.child}var pu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function gu(t){return{baseLanes:t,cachePool:Tm()}}function yu(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=We),t}function Lp(t,e,i){var r=e.pendingProps,o=!1,f=(e.flags&128)!==0,y;if((y=f)||(y=t!==null&&t.memoizedState===null?!1:(re.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(Vt){if(o?oa(e):ca(),Vt){var x=It,R;if(R=x){t:{for(R=x,x=gn;R.nodeType!==8;){if(!x){x=null;break t}if(R=un(R.nextSibling),R===null){x=null;break t}}x=R}x!==null?(e.memoizedState={dehydrated:x,treeContext:Ya!==null?{id:Ln,overflow:Bn}:null,retryLane:536870912,hydrationErrors:null},R=Be(18,null,null,0),R.stateNode=x,R.return=e,e.child=R,Ee=e,It=null,R=!0):R=!1}R||Ka(e)}if(x=e.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return tf(x)?e.lanes=32:e.lanes=536870912,null;Pn(e)}return x=r.children,r=r.fallback,o?(ca(),o=e.mode,x=Bl({mode:"hidden",children:x},o),r=Ga(r,o,i,null),x.return=e,r.return=e,x.sibling=r,e.child=x,o=e.child,o.memoizedState=gu(i),o.childLanes=yu(t,y,i),e.memoizedState=pu,r):(oa(e),vu(e,x))}if(R=t.memoizedState,R!==null&&(x=R.dehydrated,x!==null)){if(f)e.flags&256?(oa(e),e.flags&=-257,e=bu(t,e,i)):e.memoizedState!==null?(ca(),e.child=t.child,e.flags|=128,e=null):(ca(),o=r.fallback,x=e.mode,r=Bl({mode:"visible",children:r.children},x),o=Ga(o,x,i,null),o.flags|=2,r.return=e,o.return=e,r.sibling=o,e.child=r,ki(e,t.child,null,i),r=e.child,r.memoizedState=gu(i),r.childLanes=yu(t,y,i),e.memoizedState=pu,e=o);else if(oa(e),tf(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var k=y.dgst;y=k,r=Error(l(419)),r.stack="",r.digest=y,Us({value:r,source:null,stack:null}),e=bu(t,e,i)}else if(ce||ks(t,e,i,!1),y=(i&t.childLanes)!==0,ce||y){if(y=Yt,y!==null&&(r=i&-i,r=(r&42)!==0?1:ec(r),r=(r&(y.suspendedLanes|i))!==0?0:r,r!==0&&r!==R.retryLane))throw R.retryLane=r,Ri(t,r),Ge(y,t,r),Cp;x.data==="$?"||Bu(),e=bu(t,e,i)}else x.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=R.treeContext,It=un(x.nextSibling),Ee=e,Vt=!0,Xa=null,gn=!1,t!==null&&(Fe[Ie++]=Ln,Fe[Ie++]=Bn,Fe[Ie++]=Ya,Ln=t.id,Bn=t.overflow,Ya=e),e=vu(e,r.children),e.flags|=4096);return e}return o?(ca(),o=r.fallback,x=e.mode,R=t.child,k=R.sibling,r=zn(R,{mode:"hidden",children:r.children}),r.subtreeFlags=R.subtreeFlags&65011712,k!==null?o=zn(k,o):(o=Ga(o,x,i,null),o.flags|=2),o.return=e,r.return=e,r.sibling=o,e.child=r,r=o,o=e.child,x=t.child.memoizedState,x===null?x=gu(i):(R=x.cachePool,R!==null?(k=se._currentValue,R=R.parent!==k?{parent:k,pool:k}:R):R=Tm(),x={baseLanes:x.baseLanes|i,cachePool:R}),o.memoizedState=x,o.childLanes=yu(t,y,i),e.memoizedState=pu,r):(oa(e),i=t.child,t=i.sibling,i=zn(i,{mode:"visible",children:r.children}),i.return=e,i.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=i,e.memoizedState=null,i)}function vu(t,e){return e=Bl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Bl(t,e){return t=Be(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function bu(t,e,i){return ki(e,t.child,null,i),t=vu(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Bp(t,e,i){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Bc(t.return,e,i)}function xu(t,e,i,r,o){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:i,tailMode:o}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=r,f.tail=i,f.tailMode=o)}function Up(t,e,i){var r=e.pendingProps,o=r.revealOrder,f=r.tail;if(me(t,e,r.children,i),r=re.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Bp(t,i,e);else if(t.tag===19)Bp(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(J(re,r),o){case"forwards":for(i=e.child,o=null;i!==null;)t=i.alternate,t!==null&&_l(t)===null&&(o=i),i=i.sibling;i=o,i===null?(o=e.child,e.child=null):(o=i.sibling,i.sibling=null),xu(e,!1,o,i,f);break;case"backwards":for(i=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&_l(t)===null){e.child=o;break}t=o.sibling,o.sibling=i,i=o,o=t}xu(e,!0,i,null,f);break;case"together":xu(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Gn(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),ma|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(ks(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,i=zn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=zn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Su(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&yl(t)))}function aT(t,e,i){switch(e.tag){case 3:nt(e,e.stateNode.containerInfo),aa(e,se,t.memoizedState.cache),Bs();break;case 27:case 5:qt(e);break;case 4:nt(e,e.stateNode.containerInfo);break;case 10:aa(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(oa(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Lp(t,e,i):(oa(e),t=Gn(t,e,i),t!==null?t.sibling:null);oa(e);break;case 19:var o=(t.flags&128)!==0;if(r=(i&e.childLanes)!==0,r||(ks(t,e,i,!1),r=(i&e.childLanes)!==0),o){if(r)return Up(t,e,i);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),J(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Np(t,e,i);case 24:aa(e,se,t.memoizedState.cache)}return Gn(t,e,i)}function kp(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)ce=!0;else{if(!Su(t,i)&&(e.flags&128)===0)return ce=!1,aT(t,e,i);ce=(t.flags&131072)!==0}else ce=!1,Vt&&(e.flags&1048576)!==0&&pm(e,gl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,o=r._init;if(r=o(r._payload),e.type=r,typeof r=="function")Oc(r)?(t=$a(r,t),e.tag=1,e=Vp(null,e,r,t,i)):(e.tag=0,e=mu(null,e,r,t,i));else{if(r!=null){if(o=r.$$typeof,o===M){e.tag=11,e=Rp(null,e,r,t,i);break t}else if(o===W){e.tag=14,e=Dp(null,e,r,t,i);break t}}throw e=Et(r)||r,Error(l(306,e,""))}}return e;case 0:return mu(t,e,e.type,e.pendingProps,i);case 1:return r=e.type,o=$a(r,e.pendingProps),Vp(t,e,r,o,i);case 3:t:{if(nt(e,e.stateNode.containerInfo),t===null)throw Error(l(387));r=e.pendingProps;var f=e.memoizedState;o=f.element,qc(t,e),Ks(e,r,null,i);var y=e.memoizedState;if(r=y.cache,aa(e,se,r),r!==f.cache&&Uc(e,[se],i,!0),Xs(),r=y.element,f.isDehydrated)if(f={element:r,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=zp(t,e,r,i);break t}else if(r!==o){o=Ze(Error(l(424)),e),Us(o),e=zp(t,e,r,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(It=un(t.firstChild),Ee=e,Vt=!0,Xa=null,gn=!0,i=yp(e,null,r,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(Bs(),r===o){e=Gn(t,e,i);break t}me(t,e,r,i)}e=e.child}return e;case 26:return Ll(t,e),t===null?(i=Yg(e.type,null,e.pendingProps,null))?e.memoizedState=i:Vt||(i=e.type,t=e.pendingProps,r=$l(dt.current).createElement(i),r[ve]=e,r[Re]=t,ge(r,i,t),oe(r),e.stateNode=r):e.memoizedState=Yg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return qt(e),t===null&&Vt&&(r=e.stateNode=Hg(e.type,e.pendingProps,dt.current),Ee=e,gn=!0,o=It,va(e.type)?(ef=o,It=un(r.firstChild)):It=o),me(t,e,e.pendingProps.children,i),Ll(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Vt&&((o=r=It)&&(r=NT(r,e.type,e.pendingProps,gn),r!==null?(e.stateNode=r,Ee=e,It=un(r.firstChild),gn=!1,o=!0):o=!1),o||Ka(e)),qt(e),o=e.type,f=e.pendingProps,y=t!==null?t.memoizedProps:null,r=f.children,$u(o,f)?r=null:y!==null&&$u(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Ic(t,e,F1,null,null,i),mr._currentValue=o),Ll(t,e),me(t,e,r,i),e.child;case 6:return t===null&&Vt&&((t=i=It)&&(i=jT(i,e.pendingProps,gn),i!==null?(e.stateNode=i,Ee=e,It=null,t=!0):t=!1),t||Ka(e)),null;case 13:return Lp(t,e,i);case 4:return nt(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=ki(e,null,r,i):me(t,e,r,i),e.child;case 11:return Rp(t,e,e.type,e.pendingProps,i);case 7:return me(t,e,e.pendingProps,i),e.child;case 8:return me(t,e,e.pendingProps.children,i),e.child;case 12:return me(t,e,e.pendingProps.children,i),e.child;case 10:return r=e.pendingProps,aa(e,e.type,r.value),me(t,e,r.children,i),e.child;case 9:return o=e.type._context,r=e.pendingProps.children,Qa(e),o=be(o),r=r(o),e.flags|=1,me(t,e,r,i),e.child;case 14:return Dp(t,e,e.type,e.pendingProps,i);case 15:return Op(t,e,e.type,e.pendingProps,i);case 19:return Up(t,e,i);case 31:return r=e.pendingProps,i=e.mode,r={mode:r.mode,children:r.children},t===null?(i=Bl(r,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=zn(t.child,r),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Np(t,e,i);case 24:return Qa(e),r=be(se),t===null?(o=Pc(),o===null&&(o=Yt,f=kc(),o.pooledCache=f,f.refCount++,f!==null&&(o.pooledCacheLanes|=i),o=f),e.memoizedState={parent:r,cache:o},Yc(e),aa(e,se,o)):((t.lanes&i)!==0&&(qc(t,e),Ks(e,null,null,i),Xs()),o=t.memoizedState,f=e.memoizedState,o.parent!==r?(o={parent:r,cache:r},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),aa(e,se,r)):(r=f.cache,aa(e,se,r),r!==o.cache&&Uc(e,[se],i,!0))),me(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function Yn(t){t.flags|=4}function Hp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Qg(e)){if(e=$e.current,e!==null&&((Nt&4194048)===Nt?yn!==null:(Nt&62914560)!==Nt&&(Nt&536870912)===0||e!==yn))throw Ys=Gc,wm;t.flags|=8192}}function Ul(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?vh():536870912,t.lanes|=e,Yi|=e)}function Js(t,e){if(!Vt)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var r=null;i!==null;)i.alternate!==null&&(r=i),i=i.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Zt(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,r=0;if(e)for(var o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=i,e}function iT(t,e,i){var r=e.pendingProps;switch(Vc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zt(e),null;case 1:return Zt(e),null;case 3:return i=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),kn(se),Tt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(Ls(e)?Yn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,vm())),Zt(e),null;case 26:return i=e.memoizedState,t===null?(Yn(e),i!==null?(Zt(e),Hp(e,i)):(Zt(e),e.flags&=-16777217)):i?i!==t.memoizedState?(Yn(e),Zt(e),Hp(e,i)):(Zt(e),e.flags&=-16777217):(t.memoizedProps!==r&&Yn(e),Zt(e),e.flags&=-16777217),null;case 27:Ot(e),i=dt.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}t=at.current,Ls(e)?gm(e):(t=Hg(o,r,i),e.stateNode=t,Yn(e))}return Zt(e),null;case 5:if(Ot(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}if(t=at.current,Ls(e))gm(e);else{switch(o=$l(dt.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?o.createElement(i,{is:r.is}):o.createElement(i)}}t[ve]=e,t[Re]=r;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(ge(t,i,r),i){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Yn(e)}}return Zt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&Yn(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(l(166));if(t=dt.current,Ls(e)){if(t=e.stateNode,i=e.memoizedProps,r=null,o=Ee,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}t[ve]=e,t=!!(t.nodeValue===i||r!==null&&r.suppressHydrationWarning===!0||_g(t.nodeValue,i)),t||Ka(e)}else t=$l(t).createTextNode(r),t[ve]=e,e.stateNode=t}return Zt(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ls(e),r!==null&&r.dehydrated!==null){if(t===null){if(!o)throw Error(l(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(l(317));o[ve]=e}else Bs(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Zt(e),o=!1}else o=vm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Pn(e),e):(Pn(e),null)}if(Pn(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=r!==null,t=t!==null&&t.memoizedState!==null,i){r=e.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var f=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(f=r.memoizedState.cachePool.pool),f!==o&&(r.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Ul(e,e.updateQueue),Zt(e),null;case 4:return Tt(),t===null&&Ku(e.stateNode.containerInfo),Zt(e),null;case 10:return kn(e.type),Zt(e),null;case 19:if(tt(re),o=e.memoizedState,o===null)return Zt(e),null;if(r=(e.flags&128)!==0,f=o.rendering,f===null)if(r)Js(o,!1);else{if($t!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=_l(t),f!==null){for(e.flags|=128,Js(o,!1),t=f.updateQueue,e.updateQueue=t,Ul(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)mm(i,t),i=i.sibling;return J(re,re.current&1|2),e.child}t=t.sibling}o.tail!==null&&he()>Pl&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304)}else{if(!r)if(t=_l(f),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Ul(e,t),Js(o,!0),o.tail===null&&o.tailMode==="hidden"&&!f.alternate&&!Vt)return Zt(e),null}else 2*he()-o.renderingStartTime>Pl&&i!==536870912&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304);o.isBackwards?(f.sibling=e.child,e.child=f):(t=o.last,t!==null?t.sibling=f:e.child=f,o.last=f)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=he(),e.sibling=null,t=re.current,J(re,r?t&1|2:t&1),e):(Zt(e),null);case 22:case 23:return Pn(e),Qc(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(i&536870912)!==0&&(e.flags&128)===0&&(Zt(e),e.subtreeFlags&6&&(e.flags|=8192)):Zt(e),i=e.updateQueue,i!==null&&Ul(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==i&&(e.flags|=2048),t!==null&&tt(Fa),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),kn(se),Zt(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function sT(t,e){switch(Vc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return kn(se),Tt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ot(e),null;case 13:if(Pn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));Bs()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return tt(re),null;case 4:return Tt(),null;case 10:return kn(e.type),null;case 22:case 23:return Pn(e),Qc(),t!==null&&tt(Fa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return kn(se),null;case 25:return null;default:return null}}function Pp(t,e){switch(Vc(e),e.tag){case 3:kn(se),Tt();break;case 26:case 27:case 5:Ot(e);break;case 4:Tt();break;case 13:Pn(e);break;case 19:tt(re);break;case 10:kn(e.type);break;case 22:case 23:Pn(e),Qc(),t!==null&&tt(Fa);break;case 24:kn(se)}}function tr(t,e){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var o=r.next;i=o;do{if((i.tag&t)===t){r=void 0;var f=i.create,y=i.inst;r=f(),y.destroy=r}i=i.next}while(i!==o)}}catch(x){Gt(e,e.return,x)}}function ua(t,e,i){try{var r=e.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var f=o.next;r=f;do{if((r.tag&t)===t){var y=r.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,o=e;var R=i,k=x;try{k()}catch(Q){Gt(o,R,Q)}}}r=r.next}while(r!==f)}}catch(Q){Gt(e,e.return,Q)}}function Gp(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{Dm(e,i)}catch(r){Gt(t,t.return,r)}}}function Yp(t,e,i){i.props=$a(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(r){Gt(t,e,r)}}function er(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof i=="function"?t.refCleanup=i(r):i.current=r}}catch(o){Gt(t,e,o)}}function vn(t,e){var i=t.ref,r=t.refCleanup;if(i!==null)if(typeof r=="function")try{r()}catch(o){Gt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(o){Gt(t,e,o)}else i.current=null}function qp(t){var e=t.type,i=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break t;case"img":i.src?r.src=i.src:i.srcSet&&(r.srcset=i.srcSet)}}catch(o){Gt(t,t.return,o)}}function Tu(t,e,i){try{var r=t.stateNode;MT(r,t.type,i,e),r[Re]=e}catch(o){Gt(t,t.return,o)}}function Xp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&va(t.type)||t.tag===4}function wu(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Xp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&va(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Au(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=Il));else if(r!==4&&(r===27&&va(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(Au(t,e,i),t=t.sibling;t!==null;)Au(t,e,i),t=t.sibling}function kl(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(r!==4&&(r===27&&va(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(kl(t,e,i),t=t.sibling;t!==null;)kl(t,e,i),t=t.sibling}function Kp(t){var e=t.stateNode,i=t.memoizedProps;try{for(var r=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);ge(e,r,i),e[ve]=t,e[Re]=i}catch(f){Gt(t,t.return,f)}}var qn=!1,te=!1,Eu=!1,Zp=typeof WeakSet=="function"?WeakSet:Set,ue=null;function rT(t,e){if(t=t.containerInfo,Fu=ao,t=im(t),wc(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var r=i.getSelection&&i.getSelection();if(r&&r.rangeCount!==0){i=r.anchorNode;var o=r.anchorOffset,f=r.focusNode;r=r.focusOffset;try{i.nodeType,f.nodeType}catch(mt){i=null;break t}var y=0,x=-1,R=-1,k=0,Q=0,I=t,P=null;e:for(;;){for(var Y;I!==i||o!==0&&I.nodeType!==3||(x=y+o),I!==f||r!==0&&I.nodeType!==3||(R=y+r),I.nodeType===3&&(y+=I.nodeValue.length),(Y=I.firstChild)!==null;)P=I,I=Y;for(;;){if(I===t)break e;if(P===i&&++k===o&&(x=y),P===f&&++Q===r&&(R=y),(Y=I.nextSibling)!==null)break;I=P,P=I.parentNode}I=Y}i=x===-1||R===-1?null:{start:x,end:R}}else i=null}i=i||{start:0,end:0}}else i=null;for(Iu={focusedElem:t,selectionRange:i},ao=!1,ue=e;ue!==null;)if(e=ue,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ue=t;else for(;ue!==null;){switch(e=ue,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,i=e,o=f.memoizedProps,f=f.memoizedState,r=i.stateNode;try{var gt=$a(i.type,o,i.elementType===i.type);t=r.getSnapshotBeforeUpdate(gt,f),r.__reactInternalSnapshotBeforeUpdate=t}catch(mt){Gt(i,i.return,mt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)Ju(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Ju(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,ue=t;break}ue=e.return}}function Qp(t,e,i){var r=i.flags;switch(i.tag){case 0:case 11:case 15:fa(t,i),r&4&&tr(5,i);break;case 1:if(fa(t,i),r&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(y){Gt(i,i.return,y)}else{var o=$a(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Gt(i,i.return,y)}}r&64&&Gp(i),r&512&&er(i,i.return);break;case 3:if(fa(t,i),r&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Dm(t,e)}catch(y){Gt(i,i.return,y)}}break;case 27:e===null&&r&4&&Kp(i);case 26:case 5:fa(t,i),e===null&&r&4&&qp(i),r&512&&er(i,i.return);break;case 12:fa(t,i);break;case 13:fa(t,i),r&4&&$p(t,i),r&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=pT.bind(null,i),_T(t,i))));break;case 22:if(r=i.memoizedState!==null||qn,!r){e=e!==null&&e.memoizedState!==null||te,o=qn;var f=te;qn=r,(te=e)&&!f?da(t,i,(i.subtreeFlags&8772)!==0):fa(t,i),qn=o,te=f}break;case 30:break;default:fa(t,i)}}function Fp(t){var e=t.alternate;e!==null&&(t.alternate=null,Fp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ic(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ne=!1;function Xn(t,e,i){for(i=i.child;i!==null;)Ip(t,e,i),i=i.sibling}function Ip(t,e,i){if(Ve&&typeof Ve.onCommitFiberUnmount=="function")try{Ve.onCommitFiberUnmount(Ts,i)}catch(f){}switch(i.tag){case 26:te||vn(i,e),Xn(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:te||vn(i,e);var r=Xt,o=Ne;va(i.type)&&(Xt=i.stateNode,Ne=!1),Xn(t,e,i),ur(i.stateNode),Xt=r,Ne=o;break;case 5:te||vn(i,e);case 6:if(r=Xt,o=Ne,Xt=null,Xn(t,e,i),Xt=r,Ne=o,Xt!==null)if(Ne)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(i.stateNode)}catch(f){Gt(i,e,f)}else try{Xt.removeChild(i.stateNode)}catch(f){Gt(i,e,f)}break;case 18:Xt!==null&&(Ne?(t=Xt,Ug(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),vr(t)):Ug(Xt,i.stateNode));break;case 4:r=Xt,o=Ne,Xt=i.stateNode.containerInfo,Ne=!0,Xn(t,e,i),Xt=r,Ne=o;break;case 0:case 11:case 14:case 15:te||ua(2,i,e),te||ua(4,i,e),Xn(t,e,i);break;case 1:te||(vn(i,e),r=i.stateNode,typeof r.componentWillUnmount=="function"&&Yp(i,e,r)),Xn(t,e,i);break;case 21:Xn(t,e,i);break;case 22:te=(r=te)||i.memoizedState!==null,Xn(t,e,i),te=r;break;default:Xn(t,e,i)}}function $p(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{vr(t)}catch(i){Gt(e,e.return,i)}}function lT(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Zp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Zp),e;default:throw Error(l(435,t.tag))}}function Mu(t,e){var i=lT(t);e.forEach(function(r){var o=gT.bind(null,t,r);i.has(r)||(i.add(r),r.then(o,o))})}function Ue(t,e){var i=e.deletions;if(i!==null)for(var r=0;r<i.length;r++){var o=i[r],f=t,y=e,x=y;t:for(;x!==null;){switch(x.tag){case 27:if(va(x.type)){Xt=x.stateNode,Ne=!1;break t}break;case 5:Xt=x.stateNode,Ne=!1;break t;case 3:case 4:Xt=x.stateNode.containerInfo,Ne=!0;break t}x=x.return}if(Xt===null)throw Error(l(160));Ip(f,y,o),Xt=null,Ne=!1,f=o.alternate,f!==null&&(f.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Wp(e,t),e=e.sibling}var cn=null;function Wp(t,e){var i=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ue(e,t),ke(t),r&4&&(ua(3,t,t.return),tr(3,t),ua(5,t,t.return));break;case 1:Ue(e,t),ke(t),r&512&&(te||i===null||vn(i,i.return)),r&64&&qn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?r:i.concat(r))));break;case 26:var o=cn;if(Ue(e,t),ke(t),r&512&&(te||i===null||vn(i,i.return)),r&4){var f=i!==null?i.memoizedState:null;if(r=t.memoizedState,i===null)if(r===null)if(t.stateNode===null){t:{r=t.type,i=t.memoizedProps,o=o.ownerDocument||o;e:switch(r){case"title":f=o.getElementsByTagName("title")[0],(!f||f[Es]||f[ve]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=o.createElement(r),o.head.insertBefore(f,o.querySelector("head > title"))),ge(f,r,i),f[ve]=t,oe(f),r=f;break t;case"link":var y=Kg("link","href",o).get(r+(i.href||""));if(y){for(var x=0;x<y.length;x++)if(f=y[x],f.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&f.getAttribute("rel")===(i.rel==null?null:i.rel)&&f.getAttribute("title")===(i.title==null?null:i.title)&&f.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){y.splice(x,1);break e}}f=o.createElement(r),ge(f,r,i),o.head.appendChild(f);break;case"meta":if(y=Kg("meta","content",o).get(r+(i.content||""))){for(x=0;x<y.length;x++)if(f=y[x],f.getAttribute("content")===(i.content==null?null:""+i.content)&&f.getAttribute("name")===(i.name==null?null:i.name)&&f.getAttribute("property")===(i.property==null?null:i.property)&&f.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&f.getAttribute("charset")===(i.charSet==null?null:i.charSet)){y.splice(x,1);break e}}f=o.createElement(r),ge(f,r,i),o.head.appendChild(f);break;default:throw Error(l(468,r))}f[ve]=t,oe(f),r=f}t.stateNode=r}else Zg(o,t.type,t.stateNode);else t.stateNode=Xg(o,r,t.memoizedProps);else f!==r?(f===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):f.count--,r===null?Zg(o,t.type,t.stateNode):Xg(o,r,t.memoizedProps)):r===null&&t.stateNode!==null&&Tu(t,t.memoizedProps,i.memoizedProps)}break;case 27:Ue(e,t),ke(t),r&512&&(te||i===null||vn(i,i.return)),i!==null&&r&4&&Tu(t,t.memoizedProps,i.memoizedProps);break;case 5:if(Ue(e,t),ke(t),r&512&&(te||i===null||vn(i,i.return)),t.flags&32){o=t.stateNode;try{Si(o,"")}catch(Y){Gt(t,t.return,Y)}}r&4&&t.stateNode!=null&&(o=t.memoizedProps,Tu(t,o,i!==null?i.memoizedProps:o)),r&1024&&(Eu=!0);break;case 6:if(Ue(e,t),ke(t),r&4){if(t.stateNode===null)throw Error(l(162));r=t.memoizedProps,i=t.stateNode;try{i.nodeValue=r}catch(Y){Gt(t,t.return,Y)}}break;case 3:if(to=null,o=cn,cn=Wl(e.containerInfo),Ue(e,t),cn=o,ke(t),r&4&&i!==null&&i.memoizedState.isDehydrated)try{vr(e.containerInfo)}catch(Y){Gt(t,t.return,Y)}Eu&&(Eu=!1,Jp(t));break;case 4:r=cn,cn=Wl(t.stateNode.containerInfo),Ue(e,t),ke(t),cn=r;break;case 12:Ue(e,t),ke(t);break;case 13:Ue(e,t),ke(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(ju=he()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mu(t,r)));break;case 22:o=t.memoizedState!==null;var R=i!==null&&i.memoizedState!==null,k=qn,Q=te;if(qn=k||o,te=Q||R,Ue(e,t),te=Q,qn=k,ke(t),r&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(i===null||R||qn||te||Wa(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){R=i=e;try{if(f=R.stateNode,o)y=f.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=R.stateNode;var I=R.memoizedProps.style,P=I!=null&&I.hasOwnProperty("display")?I.display:null;x.style.display=P==null||typeof P=="boolean"?"":(""+P).trim()}}catch(Y){Gt(R,R.return,Y)}}}else if(e.tag===6){if(i===null){R=e;try{R.stateNode.nodeValue=o?"":R.memoizedProps}catch(Y){Gt(R,R.return,Y)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(i=r.retryQueue,i!==null&&(r.retryQueue=null,Mu(t,i))));break;case 19:Ue(e,t),ke(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Mu(t,r)));break;case 30:break;case 21:break;default:Ue(e,t),ke(t)}}function ke(t){var e=t.flags;if(e&2){try{for(var i,r=t.return;r!==null;){if(Xp(r)){i=r;break}r=r.return}if(i==null)throw Error(l(160));switch(i.tag){case 27:var o=i.stateNode,f=wu(t);kl(t,f,o);break;case 5:var y=i.stateNode;i.flags&32&&(Si(y,""),i.flags&=-33);var x=wu(t);kl(t,x,y);break;case 3:case 4:var R=i.stateNode.containerInfo,k=wu(t);Au(t,k,R);break;default:throw Error(l(161))}}catch(Q){Gt(t,t.return,Q)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function fa(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Qp(t,e.alternate,e),e=e.sibling}function Wa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ua(4,e,e.return),Wa(e);break;case 1:vn(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Yp(e,e.return,i),Wa(e);break;case 27:ur(e.stateNode);case 26:case 5:vn(e,e.return),Wa(e);break;case 22:e.memoizedState===null&&Wa(e);break;case 30:Wa(e);break;default:Wa(e)}t=t.sibling}}function da(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,o=t,f=e,y=f.flags;switch(f.tag){case 0:case 11:case 15:da(o,f,i),tr(4,f);break;case 1:if(da(o,f,i),r=f,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(k){Gt(r,r.return,k)}if(r=f,o=r.updateQueue,o!==null){var x=r.stateNode;try{var R=o.shared.hiddenCallbacks;if(R!==null)for(o.shared.hiddenCallbacks=null,o=0;o<R.length;o++)Rm(R[o],x)}catch(k){Gt(r,r.return,k)}}i&&y&64&&Gp(f),er(f,f.return);break;case 27:Kp(f);case 26:case 5:da(o,f,i),i&&r===null&&y&4&&qp(f),er(f,f.return);break;case 12:da(o,f,i);break;case 13:da(o,f,i),i&&y&4&&$p(o,f);break;case 22:f.memoizedState===null&&da(o,f,i),er(f,f.return);break;case 30:break;default:da(o,f,i)}e=e.sibling}}function Cu(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&Hs(i))}function Ru(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t))}function bn(t,e,i,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)tg(t,e,i,r),e=e.sibling}function tg(t,e,i,r){var o=e.flags;switch(e.tag){case 0:case 11:case 15:bn(t,e,i,r),o&2048&&tr(9,e);break;case 1:bn(t,e,i,r);break;case 3:bn(t,e,i,r),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t)));break;case 12:if(o&2048){bn(t,e,i,r),t=e.stateNode;try{var f=e.memoizedProps,y=f.id,x=f.onPostCommit;typeof x=="function"&&x(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(R){Gt(e,e.return,R)}}else bn(t,e,i,r);break;case 13:bn(t,e,i,r);break;case 23:break;case 22:f=e.stateNode,y=e.alternate,e.memoizedState!==null?f._visibility&2?bn(t,e,i,r):nr(t,e):f._visibility&2?bn(t,e,i,r):(f._visibility|=2,Hi(t,e,i,r,(e.subtreeFlags&10256)!==0)),o&2048&&Cu(y,e);break;case 24:bn(t,e,i,r),o&2048&&Ru(e.alternate,e);break;default:bn(t,e,i,r)}}function Hi(t,e,i,r,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,y=e,x=i,R=r,k=y.flags;switch(y.tag){case 0:case 11:case 15:Hi(f,y,x,R,o),tr(8,y);break;case 23:break;case 22:var Q=y.stateNode;y.memoizedState!==null?Q._visibility&2?Hi(f,y,x,R,o):nr(f,y):(Q._visibility|=2,Hi(f,y,x,R,o)),o&&k&2048&&Cu(y.alternate,y);break;case 24:Hi(f,y,x,R,o),o&&k&2048&&Ru(y.alternate,y);break;default:Hi(f,y,x,R,o)}e=e.sibling}}function nr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,r=e,o=r.flags;switch(r.tag){case 22:nr(i,r),o&2048&&Cu(r.alternate,r);break;case 24:nr(i,r),o&2048&&Ru(r.alternate,r);break;default:nr(i,r)}e=e.sibling}}var ar=8192;function Pi(t){if(t.subtreeFlags&ar)for(t=t.child;t!==null;)eg(t),t=t.sibling}function eg(t){switch(t.tag){case 26:Pi(t),t.flags&ar&&t.memoizedState!==null&&KT(cn,t.memoizedState,t.memoizedProps);break;case 5:Pi(t);break;case 3:case 4:var e=cn;cn=Wl(t.stateNode.containerInfo),Pi(t),cn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ar,ar=16777216,Pi(t),ar=e):Pi(t));break;default:Pi(t)}}function ng(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ir(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];ue=r,ig(r,t)}ng(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ag(t),t=t.sibling}function ag(t){switch(t.tag){case 0:case 11:case 15:ir(t),t.flags&2048&&ua(9,t,t.return);break;case 3:ir(t);break;case 12:ir(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Hl(t)):ir(t);break;default:ir(t)}}function Hl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];ue=r,ig(r,t)}ng(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ua(8,e,e.return),Hl(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,Hl(e));break;default:Hl(e)}t=t.sibling}}function ig(t,e){for(;ue!==null;){var i=ue;switch(i.tag){case 0:case 11:case 15:ua(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var r=i.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Hs(i.memoizedState.cache)}if(r=i.child,r!==null)r.return=i,ue=r;else t:for(i=t;ue!==null;){r=ue;var o=r.sibling,f=r.return;if(Fp(r),r===i){ue=null;break t}if(o!==null){o.return=f,ue=o;break t}ue=f}}}var oT={getCacheForType:function(t){var e=be(se),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},cT=typeof WeakMap=="function"?WeakMap:Map,zt=0,Yt=null,Rt=null,Nt=0,Lt=0,He=null,ha=!1,Gi=!1,Du=!1,Kn=0,$t=0,ma=0,Ja=0,Ou=0,We=0,Yi=0,sr=null,je=null,Nu=!1,ju=0,Pl=1/0,Gl=null,pa=null,pe=0,ga=null,qi=null,Xi=0,_u=0,Vu=null,sg=null,rr=0,zu=null;function Pe(){if((zt&2)!==0&&Nt!==0)return Nt&-Nt;if(B.T!==null){var t=ji;return t!==0?t:Gu()}return Sh()}function rg(){We===0&&(We=(Nt&536870912)===0||Vt?yh():536870912);var t=$e.current;return t!==null&&(t.flags|=32),We}function Ge(t,e,i){(t===Yt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ki(t,0),ya(t,Nt,We,!1)),As(t,i),((zt&2)===0||t!==Yt)&&(t===Yt&&((zt&2)===0&&(Ja|=i),$t===4&&ya(t,Nt,We,!1)),xn(t))}function lg(t,e,i){if((zt&6)!==0)throw Error(l(327));var r=!i&&(e&124)===0&&(e&t.expiredLanes)===0||ws(t,e),o=r?dT(t,e):Uu(t,e,!0),f=r;do{if(o===0){Gi&&!r&&ya(t,e,0,!1);break}else{if(i=t.current.alternate,f&&!uT(i)){o=Uu(t,e,!1),f=!1;continue}if(o===2){if(f=e,t.errorRecoveryDisabledLanes&f)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var x=t;o=sr;var R=x.current.memoizedState.isDehydrated;if(R&&(Ki(x,y).flags|=256),y=Uu(x,y,!1),y!==2){if(Du&&!R){x.errorRecoveryDisabledLanes|=f,Ja|=f,o=4;break t}f=je,je=o,f!==null&&(je===null?je=f:je.push.apply(je,f))}o=y}if(f=!1,o!==2)continue}}if(o===1){Ki(t,0),ya(t,e,0,!0);break}t:{switch(r=t,f=o,f){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:ya(r,e,We,!ha);break t;case 2:je=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(o=ju+300-he(),10<o)){if(ya(r,e,We,!ha),Jr(r,0,!0)!==0)break t;r.timeoutHandle=Lg(og.bind(null,r,i,je,Gl,Nu,e,We,Ja,Yi,ha,f,2,-0,0),o);break t}og(r,i,je,Gl,Nu,e,We,Ja,Yi,ha,f,0,-0,0)}}break}while(!0);xn(t)}function og(t,e,i,r,o,f,y,x,R,k,Q,I,P,Y){if(t.timeoutHandle=-1,I=e.subtreeFlags,(I&8192||(I&16785408)===16785408)&&(hr={stylesheets:null,count:0,unsuspend:XT},eg(e),I=ZT(),I!==null)){t.cancelPendingCommit=I(pg.bind(null,t,e,f,i,r,o,y,x,R,Q,1,P,Y)),ya(t,f,y,!k);return}pg(t,e,f,i,r,o,y,x,R)}function uT(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var r=0;r<i.length;r++){var o=i[r],f=o.getSnapshot;o=o.value;try{if(!Le(f(),o))return!1}catch(y){return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ya(t,e,i,r){e&=~Ou,e&=~Ja,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var o=e;0<o;){var f=31-ze(o),y=1<<f;r[f]=-1,o&=~y}i!==0&&bh(t,i,e)}function Yl(){return(zt&6)===0?(lr(0),!1):!0}function Lu(){if(Rt!==null){if(Lt===0)var t=Rt.return;else t=Rt,Un=Za=null,Jc(t),Ui=null,$s=0,t=Rt;for(;t!==null;)Pp(t.alternate,t),t=t.return;Rt=null}}function Ki(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,RT(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Lu(),Yt=t,Rt=i=zn(t.current,null),Nt=e,Lt=0,He=null,ha=!1,Gi=ws(t,e),Du=!1,Yi=We=Ou=Ja=ma=$t=0,je=sr=null,Nu=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var o=31-ze(r),f=1<<o;e|=t[o],r&=~f}return Kn=e,fl(),i}function cg(t,e){wt=null,B.H=Ol,e===Gs||e===xl?(e=Mm(),Lt=3):e===wm?(e=Mm(),Lt=4):Lt=e===Cp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,He=e,Rt===null&&($t=1,zl(t,Ze(e,t.current)))}function ug(){var t=B.H;return B.H=Ol,t===null?Ol:t}function fg(){var t=B.A;return B.A=oT,t}function Bu(){$t=4,ha||(Nt&4194048)!==Nt&&$e.current!==null||(Gi=!0),(ma&134217727)===0&&(Ja&134217727)===0||Yt===null||ya(Yt,Nt,We,!1)}function Uu(t,e,i){var r=zt;zt|=2;var o=ug(),f=fg();(Yt!==t||Nt!==e)&&(Gl=null,Ki(t,e)),e=!1;var y=$t;t:do try{if(Lt!==0&&Rt!==null){var x=Rt,R=He;switch(Lt){case 8:Lu(),y=6;break t;case 3:case 2:case 9:case 6:$e.current===null&&(e=!0);var k=Lt;if(Lt=0,He=null,Zi(t,x,R,k),i&&Gi){y=0;break t}break;default:k=Lt,Lt=0,He=null,Zi(t,x,R,k)}}fT(),y=$t;break}catch(Q){cg(t,Q)}while(!0);return e&&t.shellSuspendCounter++,Un=Za=null,zt=r,B.H=o,B.A=f,Rt===null&&(Yt=null,Nt=0,fl()),y}function fT(){for(;Rt!==null;)dg(Rt)}function dT(t,e){var i=zt;zt|=2;var r=ug(),o=fg();Yt!==t||Nt!==e?(Gl=null,Pl=he()+500,Ki(t,e)):Gi=ws(t,e);t:do try{if(Lt!==0&&Rt!==null){e=Rt;var f=He;e:switch(Lt){case 1:Lt=0,He=null,Zi(t,e,f,1);break;case 2:case 9:if(Am(f)){Lt=0,He=null,hg(e);break}e=function(){Lt!==2&&Lt!==9||Yt!==t||(Lt=7),xn(t)},f.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:Am(f)?(Lt=0,He=null,hg(e)):(Lt=0,He=null,Zi(t,e,f,7));break;case 5:var y=null;switch(Rt.tag){case 26:y=Rt.memoizedState;case 5:case 27:var x=Rt;if(!y||Qg(y)){Lt=0,He=null;var R=x.sibling;if(R!==null)Rt=R;else{var k=x.return;k!==null?(Rt=k,ql(k)):Rt=null}break e}}Lt=0,He=null,Zi(t,e,f,5);break;case 6:Lt=0,He=null,Zi(t,e,f,6);break;case 8:Lu(),$t=6;break t;default:throw Error(l(462))}}hT();break}catch(Q){cg(t,Q)}while(!0);return Un=Za=null,B.H=r,B.A=o,zt=i,Rt!==null?0:(Yt=null,Nt=0,fl(),$t)}function hT(){for(;Rt!==null&&!Ae();)dg(Rt)}function dg(t){var e=kp(t.alternate,t,Kn);t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function hg(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=_p(i,e,e.pendingProps,e.type,void 0,Nt);break;case 11:e=_p(i,e,e.pendingProps,e.type.render,e.ref,Nt);break;case 5:Jc(e);default:Pp(i,e),e=Rt=mm(e,Kn),e=kp(i,e,Kn)}t.memoizedProps=t.pendingProps,e===null?ql(t):Rt=e}function Zi(t,e,i,r){Un=Za=null,Jc(e),Ui=null,$s=0;var o=e.return;try{if(nT(t,o,e,i,Nt)){$t=1,zl(t,Ze(i,t.current)),Rt=null;return}}catch(f){if(o!==null)throw Rt=o,f;$t=1,zl(t,Ze(i,t.current)),Rt=null;return}e.flags&32768?(Vt||r===1?t=!0:Gi||(Nt&536870912)!==0?t=!1:(ha=t=!0,(r===2||r===9||r===3||r===6)&&(r=$e.current,r!==null&&r.tag===13&&(r.flags|=16384))),mg(e,t)):ql(e)}function ql(t){var e=t;do{if((e.flags&32768)!==0){mg(e,ha);return}t=e.return;var i=iT(e.alternate,e,Kn);if(i!==null){Rt=i;return}if(e=e.sibling,e!==null){Rt=e;return}Rt=e=t}while(e!==null);$t===0&&($t=5)}function mg(t,e){do{var i=sT(t.alternate,t);if(i!==null){i.flags&=32767,Rt=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){Rt=t;return}Rt=t=i}while(t!==null);$t=6,Rt=null}function pg(t,e,i,r,o,f,y,x,R){t.cancelPendingCommit=null;do Xl();while(pe!==0);if((zt&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(f=e.lanes|e.childLanes,f|=Rc,XS(t,i,f,y,x,R),t===Yt&&(Rt=Yt=null,Nt=0),qi=e,ga=t,Xi=i,_u=f,Vu=o,sg=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,yT(di,function(){return xg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=B.T,B.T=null,o=q.p,q.p=2,y=zt,zt|=4;try{rT(t,e,i)}finally{zt=y,q.p=o,B.T=r}}pe=1,gg(),yg(),vg()}}function gg(){if(pe===1){pe=0;var t=ga,e=qi,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{Wp(e,t);var f=Iu,y=im(t.containerInfo),x=f.focusedElem,R=f.selectionRange;if(y!==x&&x&&x.ownerDocument&&am(x.ownerDocument.documentElement,x)){if(R!==null&&wc(x)){var k=R.start,Q=R.end;if(Q===void 0&&(Q=k),"selectionStart"in x)x.selectionStart=k,x.selectionEnd=Math.min(Q,x.value.length);else{var I=x.ownerDocument||document,P=I&&I.defaultView||window;if(P.getSelection){var Y=P.getSelection(),gt=x.textContent.length,mt=Math.min(R.start,gt),Ht=R.end===void 0?mt:Math.min(R.end,gt);!Y.extend&&mt>Ht&&(y=Ht,Ht=mt,mt=y);var L=nm(x,mt),V=nm(x,Ht);if(L&&V&&(Y.rangeCount!==1||Y.anchorNode!==L.node||Y.anchorOffset!==L.offset||Y.focusNode!==V.node||Y.focusOffset!==V.offset)){var U=I.createRange();U.setStart(L.node,L.offset),Y.removeAllRanges(),mt>Ht?(Y.addRange(U),Y.extend(V.node,V.offset)):(U.setEnd(V.node,V.offset),Y.addRange(U))}}}}for(I=[],Y=x;Y=Y.parentNode;)Y.nodeType===1&&I.push({element:Y,left:Y.scrollLeft,top:Y.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<I.length;x++){var F=I[x];F.element.scrollLeft=F.left,F.element.scrollTop=F.top}}ao=!!Fu,Iu=Fu=null}finally{zt=o,q.p=r,B.T=i}}t.current=e,pe=2}}function yg(){if(pe===2){pe=0;var t=ga,e=qi,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=B.T,B.T=null;var r=q.p;q.p=2;var o=zt;zt|=4;try{Qp(t,e.alternate,e)}finally{zt=o,q.p=r,B.T=i}}pe=3}}function vg(){if(pe===4||pe===3){pe=0,ln();var t=ga,e=qi,i=Xi,r=sg;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?pe=5:(pe=0,qi=ga=null,bg(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(pa=null),nc(i),e=e.stateNode,Ve&&typeof Ve.onCommitFiberRoot=="function")try{Ve.onCommitFiberRoot(Ts,e,void 0,(e.current.flags&128)===128)}catch(R){}if(r!==null){e=B.T,o=q.p,q.p=2,B.T=null;try{for(var f=t.onRecoverableError,y=0;y<r.length;y++){var x=r[y];f(x.value,{componentStack:x.stack})}}finally{B.T=e,q.p=o}}(Xi&3)!==0&&Xl(),xn(t),o=t.pendingLanes,(i&4194090)!==0&&(o&42)!==0?t===zu?rr++:(rr=0,zu=t):rr=0,lr(0)}}function bg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Hs(e)))}function Xl(t){return gg(),yg(),vg(),xg()}function xg(){if(pe!==5)return!1;var t=ga,e=_u;_u=0;var i=nc(Xi),r=B.T,o=q.p;try{q.p=32>i?32:i,B.T=null,i=Vu,Vu=null;var f=ga,y=Xi;if(pe=0,qi=ga=null,Xi=0,(zt&6)!==0)throw Error(l(331));var x=zt;if(zt|=4,ag(f.current),tg(f,f.current,y,i),zt=x,lr(0,!1),Ve&&typeof Ve.onPostCommitFiberRoot=="function")try{Ve.onPostCommitFiberRoot(Ts,f)}catch(R){}return!0}finally{q.p=o,B.T=r,bg(t,e)}}function Sg(t,e,i){e=Ze(i,e),e=hu(t.stateNode,e,2),t=ra(t,e,2),t!==null&&(As(t,2),xn(t))}function Gt(t,e,i){if(t.tag===3)Sg(t,t,i);else for(;e!==null;){if(e.tag===3){Sg(e,t,i);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(pa===null||!pa.has(r))){t=Ze(i,t),i=Ep(2),r=ra(e,i,2),r!==null&&(Mp(i,r,e,t),As(r,2),xn(r));break}}e=e.return}}function ku(t,e,i){var r=t.pingCache;if(r===null){r=t.pingCache=new cT;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(i)||(Du=!0,o.add(i),t=mT.bind(null,t,e,i),e.then(t,t))}function mT(t,e,i){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,Yt===t&&(Nt&i)===i&&($t===4||$t===3&&(Nt&62914560)===Nt&&300>he()-ju?(zt&2)===0&&Ki(t,0):Ou|=i,Yi===Nt&&(Yi=0)),xn(t)}function Tg(t,e){e===0&&(e=vh()),t=Ri(t,e),t!==null&&(As(t,e),xn(t))}function pT(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),Tg(t,i)}function gT(t,e){var i=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(i=o.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(l(314))}r!==null&&r.delete(e),Tg(t,i)}function yT(t,e){return jt(t,e)}var Kl=null,Qi=null,Hu=!1,Zl=!1,Pu=!1,ti=0;function xn(t){t!==Qi&&t.next===null&&(Qi===null?Kl=Qi=t:Qi=Qi.next=t),Zl=!0,Hu||(Hu=!0,bT())}function lr(t,e){if(!Pu&&Zl){Pu=!0;do for(var i=!1,r=Kl;r!==null;){if(t!==0){var o=r.pendingLanes;if(o===0)var f=0;else{var y=r.suspendedLanes,x=r.pingedLanes;f=(1<<31-ze(42|t)+1)-1,f&=o&~(y&~x),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(i=!0,Mg(r,f))}else f=Nt,f=Jr(r,r===Yt?f:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(f&3)===0||ws(r,f)||(i=!0,Mg(r,f));r=r.next}while(i);Pu=!1}}function vT(){wg()}function wg(){Zl=Hu=!1;var t=0;ti!==0&&(CT()&&(t=ti),ti=0);for(var e=he(),i=null,r=Kl;r!==null;){var o=r.next,f=Ag(r,e);f===0?(r.next=null,i===null?Kl=o:i.next=o,o===null&&(Qi=i)):(i=r,(t!==0||(f&3)!==0)&&(Zl=!0)),r=o}lr(t)}function Ag(t,e){for(var i=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var y=31-ze(f),x=1<<y,R=o[y];R===-1?((x&i)===0||(x&r)!==0)&&(o[y]=qS(x,e)):R<=e&&(t.expiredLanes|=x),f&=~x}if(e=Yt,i=Nt,i=Jr(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,i===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&de(r),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||ws(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(r!==null&&de(r),nc(i)){case 2:case 8:i=Ir;break;case 32:i=di;break;case 268435456:i=gh;break;default:i=di}return r=Eg.bind(null,t),i=jt(i,r),t.callbackPriority=e,t.callbackNode=i,e}return r!==null&&r!==null&&de(r),t.callbackPriority=2,t.callbackNode=null,2}function Eg(t,e){if(pe!==0&&pe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Xl()&&t.callbackNode!==i)return null;var r=Nt;return r=Jr(t,t===Yt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(lg(t,r,e),Ag(t,he()),t.callbackNode!=null&&t.callbackNode===i?Eg.bind(null,t):null)}function Mg(t,e){if(Xl())return null;lg(t,e,!0)}function bT(){DT(function(){(zt&6)!==0?jt(Fr,vT):wg()})}function Gu(){return ti===0&&(ti=yh()),ti}function Cg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:il(""+t)}function Rg(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function xT(t,e,i,r,o){if(e==="submit"&&i&&i.stateNode===o){var f=Cg((o[Re]||null).action),y=r.submitter;y&&(e=(e=y[Re]||null)?Cg(e.formAction):y.getAttribute("formAction"),e!==null&&(f=e,y=null));var x=new ol("action","action",null,r,o);t.push({event:x,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(ti!==0){var R=y?Rg(o,y):new FormData(o);ou(i,{pending:!0,data:R,method:o.method,action:f},null,R)}}else typeof f=="function"&&(x.preventDefault(),R=y?Rg(o,y):new FormData(o),ou(i,{pending:!0,data:R,method:o.method,action:f},f,R))},currentTarget:o}]})}}for(var Yu=0;Yu<Cc.length;Yu++){var qu=Cc[Yu],ST=qu.toLowerCase(),TT=qu[0].toUpperCase()+qu.slice(1);on(ST,"on"+TT)}on(lm,"onAnimationEnd"),on(om,"onAnimationIteration"),on(cm,"onAnimationStart"),on("dblclick","onDoubleClick"),on("focusin","onFocus"),on("focusout","onBlur"),on(k1,"onTransitionRun"),on(H1,"onTransitionStart"),on(P1,"onTransitionCancel"),on(um,"onTransitionEnd"),vi("onMouseEnter",["mouseout","mouseover"]),vi("onMouseLeave",["mouseout","mouseover"]),vi("onPointerEnter",["pointerout","pointerover"]),vi("onPointerLeave",["pointerout","pointerover"]),Ua("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ua("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ua("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ua("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ua("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ua("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wT=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(or));function Dg(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var r=t[i],o=r.event;r=r.listeners;t:{var f=void 0;if(e)for(var y=r.length-1;0<=y;y--){var x=r[y],R=x.instance,k=x.currentTarget;if(x=x.listener,R!==f&&o.isPropagationStopped())break t;f=x,o.currentTarget=k;try{f(o)}catch(Q){Vl(Q)}o.currentTarget=null,f=R}else for(y=0;y<r.length;y++){if(x=r[y],R=x.instance,k=x.currentTarget,x=x.listener,R!==f&&o.isPropagationStopped())break t;f=x,o.currentTarget=k;try{f(o)}catch(Q){Vl(Q)}o.currentTarget=null,f=R}}}}function Dt(t,e){var i=e[ac];i===void 0&&(i=e[ac]=new Set);var r=t+"__bubble";i.has(r)||(Og(e,t,2,!1),i.add(r))}function Xu(t,e,i){var r=0;e&&(r|=4),Og(i,t,r,e)}var Ql="_reactListening"+Math.random().toString(36).slice(2);function Ku(t){if(!t[Ql]){t[Ql]=!0,wh.forEach(function(i){i!=="selectionchange"&&(wT.has(i)||Xu(i,!1,t),Xu(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ql]||(e[Ql]=!0,Xu("selectionchange",!1,e))}}function Og(t,e,i,r){switch(ty(e)){case 2:var o=IT;break;case 8:o=$T;break;default:o=lf}i=o.bind(null,e,i,t),o=void 0,!mc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,i,{capture:!0,passive:o}):t.addEventListener(e,i,!0):o!==void 0?t.addEventListener(e,i,{passive:o}):t.addEventListener(e,i,!1)}function Zu(t,e,i,r,o){var f=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var x=r.stateNode.containerInfo;if(x===o)break;if(y===4)for(y=r.return;y!==null;){var R=y.tag;if((R===3||R===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;x!==null;){if(y=pi(x),y===null)return;if(R=y.tag,R===5||R===6||R===26||R===27){r=f=y;continue t}x=x.parentNode}}r=r.return}Bh(function(){var k=f,Q=dc(i),I=[];t:{var P=fm.get(t);if(P!==void 0){var Y=ol,gt=t;switch(t){case"keypress":if(rl(i)===0)break t;case"keydown":case"keyup":Y=y1;break;case"focusin":gt="focus",Y=vc;break;case"focusout":gt="blur",Y=vc;break;case"beforeblur":case"afterblur":Y=vc;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Y=Hh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Y=s1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Y=x1;break;case lm:case om:case cm:Y=o1;break;case um:Y=T1;break;case"scroll":case"scrollend":Y=a1;break;case"wheel":Y=A1;break;case"copy":case"cut":case"paste":Y=u1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Y=Gh;break;case"toggle":case"beforetoggle":Y=M1}var mt=(e&4)!==0,Ht=!mt&&(t==="scroll"||t==="scrollend"),L=mt?P!==null?P+"Capture":null:P;mt=[];for(var V=k,U;V!==null;){var F=V;if(U=F.stateNode,F=F.tag,F!==5&&F!==26&&F!==27||U===null||L===null||(F=Cs(V,L),F!=null&&mt.push(cr(V,F,U))),Ht)break;V=V.return}0<mt.length&&(P=new Y(P,gt,null,i,Q),I.push({event:P,listeners:mt}))}}if((e&7)===0){t:{if(P=t==="mouseover"||t==="pointerover",Y=t==="mouseout"||t==="pointerout",P&&i!==fc&&(gt=i.relatedTarget||i.fromElement)&&(pi(gt)||gt[mi]))break t;if((Y||P)&&(P=Q.window===Q?Q:(P=Q.ownerDocument)?P.defaultView||P.parentWindow:window,Y?(gt=i.relatedTarget||i.toElement,Y=k,gt=gt?pi(gt):null,gt!==null&&(Ht=d(gt),mt=gt.tag,gt!==Ht||mt!==5&&mt!==27&&mt!==6)&&(gt=null)):(Y=null,gt=k),Y!==gt)){if(mt=Hh,F="onMouseLeave",L="onMouseEnter",V="mouse",(t==="pointerout"||t==="pointerover")&&(mt=Gh,F="onPointerLeave",L="onPointerEnter",V="pointer"),Ht=Y==null?P:Ms(Y),U=gt==null?P:Ms(gt),P=new mt(F,V+"leave",Y,i,Q),P.target=Ht,P.relatedTarget=U,F=null,pi(Q)===k&&(mt=new mt(L,V+"enter",gt,i,Q),mt.target=U,mt.relatedTarget=Ht,F=mt),Ht=F,Y&&gt)e:{for(mt=Y,L=gt,V=0,U=mt;U;U=Fi(U))V++;for(U=0,F=L;F;F=Fi(F))U++;for(;0<V-U;)mt=Fi(mt),V--;for(;0<U-V;)L=Fi(L),U--;for(;V--;){if(mt===L||L!==null&&mt===L.alternate)break e;mt=Fi(mt),L=Fi(L)}mt=null}else mt=null;Y!==null&&Ng(I,P,Y,mt,!1),gt!==null&&Ht!==null&&Ng(I,Ht,gt,mt,!0)}}t:{if(P=k?Ms(k):window,Y=P.nodeName&&P.nodeName.toLowerCase(),Y==="select"||Y==="input"&&P.type==="file")var it=Ih;else if(Qh(P))if($h)it=L1;else{it=V1;var Mt=_1}else Y=P.nodeName,!Y||Y.toLowerCase()!=="input"||P.type!=="checkbox"&&P.type!=="radio"?k&&uc(k.elementType)&&(it=Ih):it=z1;if(it&&(it=it(t,k))){Fh(I,it,i,Q);break t}Mt&&Mt(t,P,k),t==="focusout"&&k&&P.type==="number"&&k.memoizedProps.value!=null&&cc(P,"number",P.value)}switch(Mt=k?Ms(k):window,t){case"focusin":(Qh(Mt)||Mt.contentEditable==="true")&&(Ei=Mt,Ac=k,zs=null);break;case"focusout":zs=Ac=Ei=null;break;case"mousedown":Ec=!0;break;case"contextmenu":case"mouseup":case"dragend":Ec=!1,sm(I,i,Q);break;case"selectionchange":if(U1)break;case"keydown":case"keyup":sm(I,i,Q)}var ot;if(xc)t:{switch(t){case"compositionstart":var pt="onCompositionStart";break t;case"compositionend":pt="onCompositionEnd";break t;case"compositionupdate":pt="onCompositionUpdate";break t}pt=void 0}else Ai?Kh(t,i)&&(pt="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(pt="onCompositionStart");pt&&(Yh&&i.locale!=="ko"&&(Ai||pt!=="onCompositionStart"?pt==="onCompositionEnd"&&Ai&&(ot=Uh()):(na=Q,pc="value"in na?na.value:na.textContent,Ai=!0)),Mt=Fl(k,pt),0<Mt.length&&(pt=new Ph(pt,t,null,i,Q),I.push({event:pt,listeners:Mt}),ot?pt.data=ot:(ot=Zh(i),ot!==null&&(pt.data=ot)))),(ot=R1?D1(t,i):O1(t,i))&&(pt=Fl(k,"onBeforeInput"),0<pt.length&&(Mt=new Ph("onBeforeInput","beforeinput",null,i,Q),I.push({event:Mt,listeners:pt}),Mt.data=ot)),xT(I,t,k,i,Q)}Dg(I,e)})}function cr(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Fl(t,e){for(var i=e+"Capture",r=[];t!==null;){var o=t,f=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||f===null||(o=Cs(t,i),o!=null&&r.unshift(cr(t,o,f)),o=Cs(t,e),o!=null&&r.push(cr(t,o,f))),t.tag===3)return r;t=t.return}return[]}function Fi(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Ng(t,e,i,r,o){for(var f=e._reactName,y=[];i!==null&&i!==r;){var x=i,R=x.alternate,k=x.stateNode;if(x=x.tag,R!==null&&R===r)break;x!==5&&x!==26&&x!==27||k===null||(R=k,o?(k=Cs(i,f),k!=null&&y.unshift(cr(i,k,R))):o||(k=Cs(i,f),k!=null&&y.push(cr(i,k,R)))),i=i.return}y.length!==0&&t.push({event:e,listeners:y})}var AT=/\r\n?/g,ET=/\u0000|\uFFFD/g;function jg(t){return(typeof t=="string"?t:""+t).replace(AT,`
`).replace(ET,"")}function _g(t,e){return e=jg(e),jg(t)===e}function Il(){}function kt(t,e,i,r,o,f){switch(i){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||Si(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&Si(t,""+r);break;case"className":el(t,"class",r);break;case"tabIndex":el(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":el(t,i,r);break;case"style":zh(t,r,f);break;case"data":if(e!=="object"){el(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=il(""+r),t.setAttribute(i,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(i==="formAction"?(e!=="input"&&kt(t,e,"name",o.name,o,null),kt(t,e,"formEncType",o.formEncType,o,null),kt(t,e,"formMethod",o.formMethod,o,null),kt(t,e,"formTarget",o.formTarget,o,null)):(kt(t,e,"encType",o.encType,o,null),kt(t,e,"method",o.method,o,null),kt(t,e,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=il(""+r),t.setAttribute(i,r);break;case"onClick":r!=null&&(t.onclick=Il);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}i=il(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""+r):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":r===!0?t.setAttribute(i,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,r):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(i,r):t.removeAttribute(i);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(i):t.setAttribute(i,r);break;case"popover":Dt("beforetoggle",t),Dt("toggle",t),tl(t,"popover",r);break;case"xlinkActuate":_n(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":_n(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":_n(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":_n(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":_n(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":_n(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":_n(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":_n(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":_n(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tl(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=e1.get(i)||i,tl(t,i,r))}}function Qu(t,e,i,r,o,f){switch(i){case"style":zh(t,r,f);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"children":typeof r=="string"?Si(t,r):(typeof r=="number"||typeof r=="bigint")&&Si(t,""+r);break;case"onScroll":r!=null&&Dt("scroll",t);break;case"onScrollEnd":r!=null&&Dt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Il);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ah.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(o=i.endsWith("Capture"),e=i.slice(2,o?i.length-7:void 0),f=t[Re]||null,f=f!=null?f[i]:null,typeof f=="function"&&t.removeEventListener(e,f,o),typeof r=="function")){typeof f!="function"&&f!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,r,o);break t}i in t?t[i]=r:r===!0?t.setAttribute(i,""):tl(t,i,r)}}}function ge(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dt("error",t),Dt("load",t);var r=!1,o=!1,f;for(f in i)if(i.hasOwnProperty(f)){var y=i[f];if(y!=null)switch(f){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,f,y,i,null)}}o&&kt(t,e,"srcSet",i.srcSet,i,null),r&&kt(t,e,"src",i.src,i,null);return;case"input":Dt("invalid",t);var x=f=y=o=null,R=null,k=null;for(r in i)if(i.hasOwnProperty(r)){var Q=i[r];if(Q!=null)switch(r){case"name":o=Q;break;case"type":y=Q;break;case"checked":R=Q;break;case"defaultChecked":k=Q;break;case"value":f=Q;break;case"defaultValue":x=Q;break;case"children":case"dangerouslySetInnerHTML":if(Q!=null)throw Error(l(137,e));break;default:kt(t,e,r,Q,i,null)}}Nh(t,f,x,R,k,y,o,!1),nl(t);return;case"select":Dt("invalid",t),r=y=f=null;for(o in i)if(i.hasOwnProperty(o)&&(x=i[o],x!=null))switch(o){case"value":f=x;break;case"defaultValue":y=x;break;case"multiple":r=x;default:kt(t,e,o,x,i,null)}e=f,i=y,t.multiple=!!r,e!=null?xi(t,!!r,e,!1):i!=null&&xi(t,!!r,i,!0);return;case"textarea":Dt("invalid",t),f=o=r=null;for(y in i)if(i.hasOwnProperty(y)&&(x=i[y],x!=null))switch(y){case"value":r=x;break;case"defaultValue":o=x;break;case"children":f=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(l(91));break;default:kt(t,e,y,x,i,null)}_h(t,r,o,f),nl(t);return;case"option":for(R in i)if(i.hasOwnProperty(R)&&(r=i[R],r!=null))switch(R){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:kt(t,e,R,r,i,null)}return;case"dialog":Dt("beforetoggle",t),Dt("toggle",t),Dt("cancel",t),Dt("close",t);break;case"iframe":case"object":Dt("load",t);break;case"video":case"audio":for(r=0;r<or.length;r++)Dt(or[r],t);break;case"image":Dt("error",t),Dt("load",t);break;case"details":Dt("toggle",t);break;case"embed":case"source":case"link":Dt("error",t),Dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in i)if(i.hasOwnProperty(k)&&(r=i[k],r!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:kt(t,e,k,r,i,null)}return;default:if(uc(e)){for(Q in i)i.hasOwnProperty(Q)&&(r=i[Q],r!==void 0&&Qu(t,e,Q,r,i,void 0));return}}for(x in i)i.hasOwnProperty(x)&&(r=i[x],r!=null&&kt(t,e,x,r,i,null))}function MT(t,e,i,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,f=null,y=null,x=null,R=null,k=null,Q=null;for(Y in i){var I=i[Y];if(i.hasOwnProperty(Y)&&I!=null)switch(Y){case"checked":break;case"value":break;case"defaultValue":R=I;default:r.hasOwnProperty(Y)||kt(t,e,Y,null,r,I)}}for(var P in r){var Y=r[P];if(I=i[P],r.hasOwnProperty(P)&&(Y!=null||I!=null))switch(P){case"type":f=Y;break;case"name":o=Y;break;case"checked":k=Y;break;case"defaultChecked":Q=Y;break;case"value":y=Y;break;case"defaultValue":x=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(l(137,e));break;default:Y!==I&&kt(t,e,P,Y,r,I)}}oc(t,y,x,R,k,Q,f,o);return;case"select":Y=y=x=P=null;for(f in i)if(R=i[f],i.hasOwnProperty(f)&&R!=null)switch(f){case"value":break;case"multiple":Y=R;default:r.hasOwnProperty(f)||kt(t,e,f,null,r,R)}for(o in r)if(f=r[o],R=i[o],r.hasOwnProperty(o)&&(f!=null||R!=null))switch(o){case"value":P=f;break;case"defaultValue":x=f;break;case"multiple":y=f;default:f!==R&&kt(t,e,o,f,r,R)}e=x,i=y,r=Y,P!=null?xi(t,!!i,P,!1):!!r!=!!i&&(e!=null?xi(t,!!i,e,!0):xi(t,!!i,i?[]:"",!1));return;case"textarea":Y=P=null;for(x in i)if(o=i[x],i.hasOwnProperty(x)&&o!=null&&!r.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:kt(t,e,x,null,r,o)}for(y in r)if(o=r[y],f=i[y],r.hasOwnProperty(y)&&(o!=null||f!=null))switch(y){case"value":P=o;break;case"defaultValue":Y=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(l(91));break;default:o!==f&&kt(t,e,y,o,r,f)}jh(t,P,Y);return;case"option":for(var gt in i)if(P=i[gt],i.hasOwnProperty(gt)&&P!=null&&!r.hasOwnProperty(gt))switch(gt){case"selected":t.selected=!1;break;default:kt(t,e,gt,null,r,P)}for(R in r)if(P=r[R],Y=i[R],r.hasOwnProperty(R)&&P!==Y&&(P!=null||Y!=null))switch(R){case"selected":t.selected=P&&typeof P!="function"&&typeof P!="symbol";break;default:kt(t,e,R,P,r,Y)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var mt in i)P=i[mt],i.hasOwnProperty(mt)&&P!=null&&!r.hasOwnProperty(mt)&&kt(t,e,mt,null,r,P);for(k in r)if(P=r[k],Y=i[k],r.hasOwnProperty(k)&&P!==Y&&(P!=null||Y!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(P!=null)throw Error(l(137,e));break;default:kt(t,e,k,P,r,Y)}return;default:if(uc(e)){for(var Ht in i)P=i[Ht],i.hasOwnProperty(Ht)&&P!==void 0&&!r.hasOwnProperty(Ht)&&Qu(t,e,Ht,void 0,r,P);for(Q in r)P=r[Q],Y=i[Q],!r.hasOwnProperty(Q)||P===Y||P===void 0&&Y===void 0||Qu(t,e,Q,P,r,Y);return}}for(var L in i)P=i[L],i.hasOwnProperty(L)&&P!=null&&!r.hasOwnProperty(L)&&kt(t,e,L,null,r,P);for(I in r)P=r[I],Y=i[I],!r.hasOwnProperty(I)||P===Y||P==null&&Y==null||kt(t,e,I,P,r,Y)}var Fu=null,Iu=null;function $l(t){return t.nodeType===9?t:t.ownerDocument}function Vg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function zg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function $u(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Wu=null;function CT(){var t=window.event;return t&&t.type==="popstate"?t===Wu?!1:(Wu=t,!0):(Wu=null,!1)}var Lg=typeof setTimeout=="function"?setTimeout:void 0,RT=typeof clearTimeout=="function"?clearTimeout:void 0,Bg=typeof Promise=="function"?Promise:void 0,DT=typeof queueMicrotask=="function"?queueMicrotask:typeof Bg!="undefined"?function(t){return Bg.resolve(null).then(t).catch(OT)}:Lg;function OT(t){setTimeout(function(){throw t})}function va(t){return t==="head"}function Ug(t,e){var i=e,r=0,o=0;do{var f=i.nextSibling;if(t.removeChild(i),f&&f.nodeType===8)if(i=f.data,i==="/$"){if(0<r&&8>r){i=r;var y=t.ownerDocument;if(i&1&&ur(y.documentElement),i&2&&ur(y.body),i&4)for(i=y.head,ur(i),y=i.firstChild;y;){var x=y.nextSibling,R=y.nodeName;y[Es]||R==="SCRIPT"||R==="STYLE"||R==="LINK"&&y.rel.toLowerCase()==="stylesheet"||i.removeChild(y),y=x}}if(o===0){t.removeChild(f),vr(e);return}o--}else i==="$"||i==="$?"||i==="$!"?o++:r=i.charCodeAt(0)-48;else r=0;i=f}while(i);vr(e)}function Ju(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Ju(i),ic(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function NT(t,e,i,r){for(;t.nodeType===1;){var o=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[Es])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=un(t.nextSibling),t===null)break}return null}function jT(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=un(t.nextSibling),t===null))return null;return t}function tf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function _T(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var r=function(){e(),i.removeEventListener("DOMContentLoaded",r)};i.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function un(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ef=null;function kg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function Hg(t,e,i){switch(e=$l(i),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function ur(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ic(t)}var Je=new Map,Pg=new Set;function Wl(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Zn=q.d;q.d={f:VT,r:zT,D:LT,C:BT,L:UT,m:kT,X:PT,S:HT,M:GT};function VT(){var t=Zn.f(),e=Yl();return t||e}function zT(t){var e=gi(t);e!==null&&e.tag===5&&e.type==="form"?lp(e):Zn.r(t)}var Ii=typeof document=="undefined"?null:document;function Gg(t,e,i){var r=Ii;if(r&&typeof e=="string"&&e){var o=Ke(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof i=="string"&&(o+='[crossorigin="'+i+'"]'),Pg.has(o)||(Pg.add(o),t={rel:t,crossOrigin:i,href:e},r.querySelector(o)===null&&(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function LT(t){Zn.D(t),Gg("dns-prefetch",t,null)}function BT(t,e){Zn.C(t,e),Gg("preconnect",t,e)}function UT(t,e,i){Zn.L(t,e,i);var r=Ii;if(r&&t&&e){var o='link[rel="preload"][as="'+Ke(e)+'"]';e==="image"&&i&&i.imageSrcSet?(o+='[imagesrcset="'+Ke(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(o+='[imagesizes="'+Ke(i.imageSizes)+'"]')):o+='[href="'+Ke(t)+'"]';var f=o;switch(e){case"style":f=$i(t);break;case"script":f=Wi(t)}Je.has(f)||(t=g({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),Je.set(f,t),r.querySelector(o)!==null||e==="style"&&r.querySelector(fr(f))||e==="script"&&r.querySelector(dr(f))||(e=r.createElement("link"),ge(e,"link",t),oe(e),r.head.appendChild(e)))}}function kT(t,e){Zn.m(t,e);var i=Ii;if(i&&t){var r=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ke(r)+'"][href="'+Ke(t)+'"]',f=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Wi(t)}if(!Je.has(f)&&(t=g({rel:"modulepreload",href:t},e),Je.set(f,t),i.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(dr(f)))return}r=i.createElement("link"),ge(r,"link",t),oe(r),i.head.appendChild(r)}}}function HT(t,e,i){Zn.S(t,e,i);var r=Ii;if(r&&t){var o=yi(r).hoistableStyles,f=$i(t);e=e||"default";var y=o.get(f);if(!y){var x={loading:0,preload:null};if(y=r.querySelector(fr(f)))x.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},i),(i=Je.get(f))&&nf(t,i);var R=y=r.createElement("link");oe(R),ge(R,"link",t),R._p=new Promise(function(k,Q){R.onload=k,R.onerror=Q}),R.addEventListener("load",function(){x.loading|=1}),R.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Jl(y,e,r)}y={type:"stylesheet",instance:y,count:1,state:x},o.set(f,y)}}}function PT(t,e){Zn.X(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=Wi(t),f=r.get(o);f||(f=i.querySelector(dr(o)),f||(t=g({src:t,async:!0},e),(e=Je.get(o))&&af(t,e),f=i.createElement("script"),oe(f),ge(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(o,f))}}function GT(t,e){Zn.M(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=Wi(t),f=r.get(o);f||(f=i.querySelector(dr(o)),f||(t=g({src:t,async:!0,type:"module"},e),(e=Je.get(o))&&af(t,e),f=i.createElement("script"),oe(f),ge(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(o,f))}}function Yg(t,e,i,r){var o=(o=dt.current)?Wl(o):null;if(!o)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=$i(i.href),i=yi(o).hoistableStyles,r=i.get(e),r||(r={type:"style",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=$i(i.href);var f=yi(o).hoistableStyles,y=f.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,y),(f=o.querySelector(fr(t)))&&!f._p&&(y.instance=f,y.state.loading=5),Je.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Je.set(t,i),f||YT(o,t,i,y.state))),e&&r===null)throw Error(l(528,""));return y}if(e&&r!==null)throw Error(l(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wi(i),i=yi(o).hoistableScripts,r=i.get(e),r||(r={type:"script",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function $i(t){return'href="'+Ke(t)+'"'}function fr(t){return'link[rel="stylesheet"]['+t+"]"}function qg(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function YT(t,e,i,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),ge(e,"link",i),oe(e),t.head.appendChild(e))}function Wi(t){return'[src="'+Ke(t)+'"]'}function dr(t){return"script[async]"+t}function Xg(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Ke(i.href)+'"]');if(r)return e.instance=r,oe(r),r;var o=g({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),oe(r),ge(r,"style",o),Jl(r,i.precedence,t),e.instance=r;case"stylesheet":o=$i(i.href);var f=t.querySelector(fr(o));if(f)return e.state.loading|=4,e.instance=f,oe(f),f;r=qg(i),(o=Je.get(o))&&nf(r,o),f=(t.ownerDocument||t).createElement("link"),oe(f);var y=f;return y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(f,"link",r),e.state.loading|=4,Jl(f,i.precedence,t),e.instance=f;case"script":return f=Wi(i.src),(o=t.querySelector(dr(f)))?(e.instance=o,oe(o),o):(r=i,(o=Je.get(f))&&(r=g({},i),af(r,o)),t=t.ownerDocument||t,o=t.createElement("script"),oe(o),ge(o,"link",r),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,Jl(r,i.precedence,t));return e.instance}function Jl(t,e,i){for(var r=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,f=o,y=0;y<r.length;y++){var x=r[y];if(x.dataset.precedence===e)f=x;else if(f!==o)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function nf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function af(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var to=null;function Kg(t,e,i){if(to===null){var r=new Map,o=to=new Map;o.set(i,r)}else o=to,r=o.get(i),r||(r=new Map,o.set(i,r));if(r.has(t))return r;for(r.set(t,null),i=i.getElementsByTagName(t),o=0;o<i.length;o++){var f=i[o];if(!(f[Es]||f[ve]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var y=f.getAttribute(e)||"";y=t+y;var x=r.get(y);x?x.push(f):r.set(y,[f])}}return r}function Zg(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function qT(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Qg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var hr=null;function XT(){}function KT(t,e,i){if(hr===null)throw Error(l(475));var r=hr;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=$i(i.href),f=t.querySelector(fr(o));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=eo.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=f,oe(f);return}f=t.ownerDocument||t,i=qg(i),(o=Je.get(o))&&nf(i,o),f=f.createElement("link"),oe(f);var y=f;y._p=new Promise(function(x,R){y.onload=x,y.onerror=R}),ge(f,"link",i),e.instance=f}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=eo.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function ZT(){if(hr===null)throw Error(l(475));var t=hr;return t.stylesheets&&t.count===0&&sf(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&sf(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function eo(){if(this.count--,this.count===0){if(this.stylesheets)sf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var no=null;function sf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,no=new Map,e.forEach(QT,t),no=null,eo.call(t))}function QT(t,e){if(!(e.state.loading&4)){var i=no.get(t);if(i)var r=i.get(null);else{i=new Map,no.set(t,i);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<o.length;f++){var y=o[f];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(i.set(y.dataset.precedence,y),r=y)}r&&i.set(null,r)}o=e.instance,y=o.getAttribute("data-precedence"),f=i.get(y)||r,f===r&&i.set(null,o),i.set(y,o),this.count++,r=eo.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),f?f.parentNode.insertBefore(o,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var mr={$$typeof:j,Provider:null,Consumer:null,_currentValue:G,_currentValue2:G,_threadCount:0};function FT(t,e,i,r,o,f,y,x){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=tc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tc(0),this.hiddenUpdates=tc(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=f,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function Fg(t,e,i,r,o,f,y,x,R,k,Q,I){return t=new FT(t,e,i,y,x,R,k,I),e=1,f===!0&&(e|=24),f=Be(3,null,null,e),t.current=f,f.stateNode=t,e=kc(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:r,isDehydrated:i,cache:e},Yc(f),t}function Ig(t){return t?(t=Di,t):Di}function $g(t,e,i,r,o,f){o=Ig(o),r.context===null?r.context=o:r.pendingContext=o,r=sa(e),r.payload={element:i},f=f===void 0?null:f,f!==null&&(r.callback=f),i=ra(t,r,e),i!==null&&(Ge(i,t,e),qs(i,t,e))}function Wg(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function rf(t,e){Wg(t,e),(t=t.alternate)&&Wg(t,e)}function Jg(t){if(t.tag===13){var e=Ri(t,67108864);e!==null&&Ge(e,t,67108864),rf(t,67108864)}}var ao=!0;function IT(t,e,i,r){var o=B.T;B.T=null;var f=q.p;try{q.p=2,lf(t,e,i,r)}finally{q.p=f,B.T=o}}function $T(t,e,i,r){var o=B.T;B.T=null;var f=q.p;try{q.p=8,lf(t,e,i,r)}finally{q.p=f,B.T=o}}function lf(t,e,i,r){if(ao){var o=of(r);if(o===null)Zu(t,e,r,io,i),ey(t,r);else if(JT(o,t,e,i,r))r.stopPropagation();else if(ey(t,r),e&4&&-1<WT.indexOf(t)){for(;o!==null;){var f=gi(o);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var y=Ba(f.pendingLanes);if(y!==0){var x=f;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var R=1<<31-ze(y);x.entanglements[1]|=R,y&=~R}xn(f),(zt&6)===0&&(Pl=he()+500,lr(0))}}break;case 13:x=Ri(f,2),x!==null&&Ge(x,f,2),Yl(),rf(f,2)}if(f=of(r),f===null&&Zu(t,e,r,io,i),f===o)break;o=f}o!==null&&r.stopPropagation()}else Zu(t,e,r,null,i)}}function of(t){return t=dc(t),cf(t)}var io=null;function cf(t){if(io=null,t=pi(t),t!==null){var e=d(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=u(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return io=t,null}function ty(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(La()){case Fr:return 2;case Ir:return 8;case di:case hi:return 32;case gh:return 268435456;default:return 32}default:return 32}}var uf=!1,ba=null,xa=null,Sa=null,pr=new Map,gr=new Map,Ta=[],WT="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ey(t,e){switch(t){case"focusin":case"focusout":ba=null;break;case"dragenter":case"dragleave":xa=null;break;case"mouseover":case"mouseout":Sa=null;break;case"pointerover":case"pointerout":pr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(e.pointerId)}}function yr(t,e,i,r,o,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:i,eventSystemFlags:r,nativeEvent:f,targetContainers:[o]},e!==null&&(e=gi(e),e!==null&&Jg(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function JT(t,e,i,r,o){switch(e){case"focusin":return ba=yr(ba,t,e,i,r,o),!0;case"dragenter":return xa=yr(xa,t,e,i,r,o),!0;case"mouseover":return Sa=yr(Sa,t,e,i,r,o),!0;case"pointerover":var f=o.pointerId;return pr.set(f,yr(pr.get(f)||null,t,e,i,r,o)),!0;case"gotpointercapture":return f=o.pointerId,gr.set(f,yr(gr.get(f)||null,t,e,i,r,o)),!0}return!1}function ny(t){var e=pi(t.target);if(e!==null){var i=d(e);if(i!==null){if(e=i.tag,e===13){if(e=u(i),e!==null){t.blockedOn=e,KS(t.priority,function(){if(i.tag===13){var r=Pe();r=ec(r);var o=Ri(i,r);o!==null&&Ge(o,i,r),rf(i,r)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function so(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=of(t.nativeEvent);if(i===null){i=t.nativeEvent;var r=new i.constructor(i.type,i);fc=r,i.target.dispatchEvent(r),fc=null}else return e=gi(i),e!==null&&Jg(e),t.blockedOn=i,!1;e.shift()}return!0}function ay(t,e,i){so(t)&&i.delete(e)}function tw(){uf=!1,ba!==null&&so(ba)&&(ba=null),xa!==null&&so(xa)&&(xa=null),Sa!==null&&so(Sa)&&(Sa=null),pr.forEach(ay),gr.forEach(ay)}function ro(t,e){t.blockedOn===e&&(t.blockedOn=null,uf||(uf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,tw)))}var lo=null;function iy(t){lo!==t&&(lo=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){lo===t&&(lo=null);for(var e=0;e<t.length;e+=3){var i=t[e],r=t[e+1],o=t[e+2];if(typeof r!="function"){if(cf(r||i)===null)continue;break}var f=gi(i);f!==null&&(t.splice(e,3),e-=3,ou(f,{pending:!0,data:o,method:i.method,action:r},r,o))}}))}function vr(t){function e(R){return ro(R,t)}ba!==null&&ro(ba,t),xa!==null&&ro(xa,t),Sa!==null&&ro(Sa,t),pr.forEach(e),gr.forEach(e);for(var i=0;i<Ta.length;i++){var r=Ta[i];r.blockedOn===t&&(r.blockedOn=null)}for(;0<Ta.length&&(i=Ta[0],i.blockedOn===null);)ny(i),i.blockedOn===null&&Ta.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(r=0;r<i.length;r+=3){var o=i[r],f=i[r+1],y=o[Re]||null;if(typeof f=="function")y||iy(i);else if(y){var x=null;if(f&&f.hasAttribute("formAction")){if(o=f,y=f[Re]||null)x=y.formAction;else if(cf(o)!==null)continue}else x=y.action;typeof x=="function"?i[r+1]=x:(i.splice(r,3),r-=3),iy(i)}}}function ff(t){this._internalRoot=t}oo.prototype.render=ff.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var i=e.current,r=Pe();$g(i,r,t,e,null,null)},oo.prototype.unmount=ff.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;$g(t.current,2,null,t,null,null),Yl(),e[mi]=null}};function oo(t){this._internalRoot=t}oo.prototype.unstable_scheduleHydration=function(t){if(t){var e=Sh();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Ta.length&&e!==0&&e<Ta[i].priority;i++);Ta.splice(i,0,t),i===0&&ny(t)}};var sy=a.version;if(sy!=="19.1.0")throw Error(l(527,sy,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=m(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var ew={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var co=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!co.isDisabled&&co.supportsFiber)try{Ts=co.inject(ew),Ve=co}catch(t){}}return xr.createRoot=function(t,e){if(!c(t))throw Error(l(299));var i=!1,r="",o=Sp,f=Tp,y=wp,x=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(x=e.unstable_transitionCallbacks)),e=Fg(t,1,!1,null,null,i,r,o,f,y,x,null),t[mi]=e.current,Ku(t),new ff(e)},xr.hydrateRoot=function(t,e,i){if(!c(t))throw Error(l(299));var r=!1,o="",f=Sp,y=Tp,x=wp,R=null,k=null;return i!=null&&(i.unstable_strictMode===!0&&(r=!0),i.identifierPrefix!==void 0&&(o=i.identifierPrefix),i.onUncaughtError!==void 0&&(f=i.onUncaughtError),i.onCaughtError!==void 0&&(y=i.onCaughtError),i.onRecoverableError!==void 0&&(x=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(R=i.unstable_transitionCallbacks),i.formState!==void 0&&(k=i.formState)),e=Fg(t,1,!0,e,i!=null?i:null,r,o,f,y,x,R,k),e.context=Ig(null),i=e.current,r=Pe(),r=ec(r),o=sa(r),o.callback=null,ra(i,o,r),i=r,e.current.lanes=i,As(e,i),xn(e),t[mi]=e.current,Ku(t),new oo(e)},xr.version="19.1.0",xr}var vy;function pw(){if(vy)return pf.exports;vy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),pf.exports=mw(),pf.exports}var gw=pw();const y0=w.createContext({});function yw(n){const a=w.useRef(null);return a.current===null&&(a.current=n()),a.current}const Ed=typeof window!="undefined",vw=Ed?w.useLayoutEffect:w.useEffect,Md=w.createContext(null);function Cd(n,a){n.indexOf(a)===-1&&n.push(a)}function Rd(n,a){const s=n.indexOf(a);s>-1&&n.splice(s,1)}const In=(n,a,s)=>s>a?a:s<n?n:s;let Dd=()=>{};const $n={},v0=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function b0(n){return typeof n=="object"&&n!==null}const x0=n=>/^0[^.\s]+$/u.test(n);function Od(n){let a;return()=>(a===void 0&&(a=n()),a)}const rn=n=>n,bw=(n,a)=>s=>a(n(s)),Hr=(...n)=>n.reduce(bw),Or=(n,a,s)=>{const l=a-n;return l===0?1:(s-n)/l};class Nd{constructor(){this.subscriptions=[]}add(a){return Cd(this.subscriptions,a),()=>Rd(this.subscriptions,a)}notify(a,s,l){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](a,s,l);else for(let d=0;d<c;d++){const u=this.subscriptions[d];u&&u(a,s,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Cn=n=>n*1e3,Rn=n=>n/1e3;function S0(n,a){return a?n*(1e3/a):0}const T0=(n,a,s)=>(((1-3*s+3*a)*n+(3*s-6*a))*n+3*a)*n,xw=1e-7,Sw=12;function Tw(n,a,s,l,c){let d,u,h=0;do u=a+(s-a)/2,d=T0(u,l,c)-n,d>0?s=u:a=u;while(Math.abs(d)>xw&&++h<Sw);return u}function Pr(n,a,s,l){if(n===a&&s===l)return rn;const c=d=>Tw(d,0,1,n,s);return d=>d===0||d===1?d:T0(c(d),a,l)}const w0=n=>a=>a<=.5?n(2*a)/2:(2-n(2*(1-a)))/2,A0=n=>a=>1-n(1-a),E0=Pr(.33,1.53,.69,.99),jd=A0(E0),M0=w0(jd),C0=n=>(n*=2)<1?.5*jd(n):.5*(2-Math.pow(2,-10*(n-1))),_d=n=>1-Math.sin(Math.acos(n)),R0=A0(_d),D0=w0(_d),ww=Pr(.42,0,1,1),Aw=Pr(0,0,.58,1),O0=Pr(.42,0,.58,1),Ew=n=>Array.isArray(n)&&typeof n[0]!="number",N0=n=>Array.isArray(n)&&typeof n[0]=="number",Mw={linear:rn,easeIn:ww,easeInOut:O0,easeOut:Aw,circIn:_d,circInOut:D0,circOut:R0,backIn:jd,backInOut:M0,backOut:E0,anticipate:C0},Cw=n=>typeof n=="string",by=n=>{if(N0(n)){Dd(n.length===4);const[a,s,l,c]=n;return Pr(a,s,l,c)}else if(Cw(n))return Mw[n];return n},fo=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],xy={value:null};function Rw(n,a){let s=new Set,l=new Set,c=!1,d=!1;const u=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},m=0;function p(v){u.has(v)&&(g.schedule(v),n()),m++,v(h)}const g={schedule:(v,b=!1,T=!1)=>{const A=T&&c?s:l;return b&&u.add(v),A.has(v)||A.add(v),v},cancel:v=>{l.delete(v),u.delete(v)},process:v=>{if(h=v,c){d=!0;return}c=!0,[s,l]=[l,s],s.forEach(p),a&&xy.value&&xy.value.frameloop[a].push(m),m=0,s.clear(),c=!1,d&&(d=!1,g.process(v))}};return g}const Dw=40;function j0(n,a){let s=!1,l=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,u=fo.reduce((j,M)=>(j[M]=Rw(d,a?M:void 0),j),{}),{setup:h,read:m,resolveKeyframes:p,preUpdate:g,update:v,preRender:b,render:T,postRender:D}=u,A=()=>{const j=$n.useManualTiming?c.timestamp:performance.now();s=!1,$n.useManualTiming||(c.delta=l?1e3/60:Math.max(Math.min(j-c.timestamp,Dw),1)),c.timestamp=j,c.isProcessing=!0,h.process(c),m.process(c),p.process(c),g.process(c),v.process(c),b.process(c),T.process(c),D.process(c),c.isProcessing=!1,s&&a&&(l=!1,n(A))},E=()=>{s=!0,l=!0,c.isProcessing||n(A)};return{schedule:fo.reduce((j,M)=>{const C=u[M];return j[M]=(X,W=!1,H=!1)=>(s||E(),C.schedule(X,W,H)),j},{}),cancel:j=>{for(let M=0;M<fo.length;M++)u[fo[M]].cancel(j)},state:c,steps:u}}const{schedule:Ft,cancel:Ra,state:ye,steps:bf}=j0(typeof requestAnimationFrame!="undefined"?requestAnimationFrame:rn,!0);let Eo;function Ow(){Eo=void 0}const _e={now:()=>(Eo===void 0&&_e.set(ye.isProcessing||$n.useManualTiming?ye.timestamp:performance.now()),Eo),set:n=>{Eo=n,queueMicrotask(Ow)}},_0=n=>a=>typeof a=="string"&&a.startsWith(n),Vd=_0("--"),Nw=_0("var(--"),zd=n=>Nw(n)?jw.test(n.split("/*")[0].trim()):!1,jw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ms={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Nr=Z(N({},ms),{transform:n=>In(0,1,n)}),ho=Z(N({},ms),{default:1}),Er=n=>Math.round(n*1e5)/1e5,Ld=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function _w(n){return n==null}const Vw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Bd=(n,a)=>s=>!!(typeof s=="string"&&Vw.test(s)&&s.startsWith(n)||a&&!_w(s)&&Object.prototype.hasOwnProperty.call(s,a)),V0=(n,a,s)=>l=>{if(typeof l!="string")return l;const[c,d,u,h]=l.match(Ld);return{[n]:parseFloat(c),[a]:parseFloat(d),[s]:parseFloat(u),alpha:h!==void 0?parseFloat(h):1}},zw=n=>In(0,255,n),xf=Z(N({},ms),{transform:n=>Math.round(zw(n))}),si={test:Bd("rgb","red"),parse:V0("red","green","blue"),transform:({red:n,green:a,blue:s,alpha:l=1})=>"rgba("+xf.transform(n)+", "+xf.transform(a)+", "+xf.transform(s)+", "+Er(Nr.transform(l))+")"};function Lw(n){let a="",s="",l="",c="";return n.length>5?(a=n.substring(1,3),s=n.substring(3,5),l=n.substring(5,7),c=n.substring(7,9)):(a=n.substring(1,2),s=n.substring(2,3),l=n.substring(3,4),c=n.substring(4,5),a+=a,s+=s,l+=l,c+=c),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(l,16),alpha:c?parseInt(c,16)/255:1}}const qf={test:Bd("#"),parse:Lw,transform:si.transform},Gr=n=>({test:a=>typeof a=="string"&&a.endsWith(n)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${n}`}),Ma=Gr("deg"),Dn=Gr("%"),yt=Gr("px"),Bw=Gr("vh"),Uw=Gr("vw"),Sy=Z(N({},Dn),{parse:n=>Dn.parse(n)/100,transform:n=>Dn.transform(n*100)}),is={test:Bd("hsl","hue"),parse:V0("hue","saturation","lightness"),transform:({hue:n,saturation:a,lightness:s,alpha:l=1})=>"hsla("+Math.round(n)+", "+Dn.transform(Er(a))+", "+Dn.transform(Er(s))+", "+Er(Nr.transform(l))+")"},Se={test:n=>si.test(n)||qf.test(n)||is.test(n),parse:n=>si.test(n)?si.parse(n):is.test(n)?is.parse(n):qf.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?si.transform(n):is.transform(n)},kw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Hw(n){var a,s;return isNaN(n)&&typeof n=="string"&&(((a=n.match(Ld))==null?void 0:a.length)||0)+(((s=n.match(kw))==null?void 0:s.length)||0)>0}const z0="number",L0="color",Pw="var",Gw="var(",Ty="${}",Yw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function jr(n){const a=n.toString(),s=[],l={color:[],number:[],var:[]},c=[];let d=0;const h=a.replace(Yw,m=>(Se.test(m)?(l.color.push(d),c.push(L0),s.push(Se.parse(m))):m.startsWith(Gw)?(l.var.push(d),c.push(Pw),s.push(m)):(l.number.push(d),c.push(z0),s.push(parseFloat(m))),++d,Ty)).split(Ty);return{values:s,split:h,indexes:l,types:c}}function B0(n){return jr(n).values}function U0(n){const{split:a,types:s}=jr(n),l=a.length;return c=>{let d="";for(let u=0;u<l;u++)if(d+=a[u],c[u]!==void 0){const h=s[u];h===z0?d+=Er(c[u]):h===L0?d+=Se.transform(c[u]):d+=c[u]}return d}}const qw=n=>typeof n=="number"?0:n;function Xw(n){const a=B0(n);return U0(n)(a.map(qw))}const Da={test:Hw,parse:B0,createTransformer:U0,getAnimatableNone:Xw};function Sf(n,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(a-n)*6*s:s<1/2?a:s<2/3?n+(a-n)*(2/3-s)*6:n}function Kw({hue:n,saturation:a,lightness:s,alpha:l}){n/=360,a/=100,s/=100;let c=0,d=0,u=0;if(!a)c=d=u=s;else{const h=s<.5?s*(1+a):s+a-s*a,m=2*s-h;c=Sf(m,h,n+1/3),d=Sf(m,h,n),u=Sf(m,h,n-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(u*255),alpha:l}}function jo(n,a){return s=>s>0?a:n}const Qt=(n,a,s)=>n+(a-n)*s,Tf=(n,a,s)=>{const l=n*n,c=s*(a*a-l)+l;return c<0?0:Math.sqrt(c)},Zw=[qf,si,is],Qw=n=>Zw.find(a=>a.test(n));function wy(n){const a=Qw(n);if(!a)return!1;let s=a.parse(n);return a===is&&(s=Kw(s)),s}const Ay=(n,a)=>{const s=wy(n),l=wy(a);if(!s||!l)return jo(n,a);const c=N({},s);return d=>(c.red=Tf(s.red,l.red,d),c.green=Tf(s.green,l.green,d),c.blue=Tf(s.blue,l.blue,d),c.alpha=Qt(s.alpha,l.alpha,d),si.transform(c))},Xf=new Set(["none","hidden"]);function Fw(n,a){return Xf.has(n)?s=>s<=0?n:a:s=>s>=1?a:n}function Iw(n,a){return s=>Qt(n,a,s)}function Ud(n){return typeof n=="number"?Iw:typeof n=="string"?zd(n)?jo:Se.test(n)?Ay:Jw:Array.isArray(n)?k0:typeof n=="object"?Se.test(n)?Ay:$w:jo}function k0(n,a){const s=[...n],l=s.length,c=n.map((d,u)=>Ud(d)(d,a[u]));return d=>{for(let u=0;u<l;u++)s[u]=c[u](d);return s}}function $w(n,a){const s=N(N({},n),a),l={};for(const c in s)n[c]!==void 0&&a[c]!==void 0&&(l[c]=Ud(n[c])(n[c],a[c]));return c=>{for(const d in l)s[d]=l[d](c);return s}}function Ww(n,a){var c;const s=[],l={color:0,var:0,number:0};for(let d=0;d<a.values.length;d++){const u=a.types[d],h=n.indexes[u][l[u]],m=(c=n.values[h])!=null?c:0;s[d]=m,l[u]++}return s}const Jw=(n,a)=>{const s=Da.createTransformer(a),l=jr(n),c=jr(a);return l.indexes.var.length===c.indexes.var.length&&l.indexes.color.length===c.indexes.color.length&&l.indexes.number.length>=c.indexes.number.length?Xf.has(n)&&!c.values.length||Xf.has(a)&&!l.values.length?Fw(n,a):Hr(k0(Ww(l,c),c.values),s):jo(n,a)};function H0(n,a,s){return typeof n=="number"&&typeof a=="number"&&typeof s=="number"?Qt(n,a,s):Ud(n)(n,a)}const tA=n=>{const a=({timestamp:s})=>n(s);return{start:(s=!0)=>Ft.update(a,s),stop:()=>Ra(a),now:()=>ye.isProcessing?ye.timestamp:_e.now()}},P0=(n,a,s=10)=>{let l="";const c=Math.max(Math.round(a/s),2);for(let d=0;d<c;d++)l+=n(d/(c-1))+", ";return`linear(${l.substring(0,l.length-2)})`},_o=2e4;function kd(n){let a=0;const s=50;let l=n.next(a);for(;!l.done&&a<_o;)a+=s,l=n.next(a);return a>=_o?1/0:a}function eA(n,a=100,s){const l=s(Z(N({},n),{keyframes:[0,a]})),c=Math.min(kd(l),_o);return{type:"keyframes",ease:d=>l.next(c*d).value/a,duration:Rn(c)}}const nA=5;function G0(n,a,s){const l=Math.max(a-nA,0);return S0(s-n(l),a-l)}const Wt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Ey=.001;function aA({duration:n=Wt.duration,bounce:a=Wt.bounce,velocity:s=Wt.velocity,mass:l=Wt.mass}){let c,d,u=1-a;u=In(Wt.minDamping,Wt.maxDamping,u),n=In(Wt.minDuration,Wt.maxDuration,Rn(n)),u<1?(c=p=>{const g=p*u,v=g*n,b=g-s,T=Kf(p,u),D=Math.exp(-v);return Ey-b/T*D},d=p=>{const v=p*u*n,b=v*s+s,T=Math.pow(u,2)*Math.pow(p,2)*n,D=Math.exp(-v),A=Kf(Math.pow(p,2),u);return(-c(p)+Ey>0?-1:1)*((b-T)*D)/A}):(c=p=>{const g=Math.exp(-p*n),v=(p-s)*n+1;return-.001+g*v},d=p=>{const g=Math.exp(-p*n),v=(s-p)*(n*n);return g*v});const h=5/n,m=sA(c,d,h);if(n=Cn(n),isNaN(m))return{stiffness:Wt.stiffness,damping:Wt.damping,duration:n};{const p=Math.pow(m,2)*l;return{stiffness:p,damping:u*2*Math.sqrt(l*p),duration:n}}}const iA=12;function sA(n,a,s){let l=s;for(let c=1;c<iA;c++)l=l-n(l)/a(l);return l}function Kf(n,a){return n*Math.sqrt(1-a*a)}const rA=["duration","bounce"],lA=["stiffness","damping","mass"];function My(n,a){return a.some(s=>n[s]!==void 0)}function oA(n){let a=N({velocity:Wt.velocity,stiffness:Wt.stiffness,damping:Wt.damping,mass:Wt.mass,isResolvedFromDuration:!1},n);if(!My(n,lA)&&My(n,rA))if(n.visualDuration){const s=n.visualDuration,l=2*Math.PI/(s*1.2),c=l*l,d=2*In(.05,1,1-(n.bounce||0))*Math.sqrt(c);a=Z(N({},a),{mass:Wt.mass,stiffness:c,damping:d})}else{const s=aA(n);a=Z(N(N({},a),s),{mass:Wt.mass}),a.isResolvedFromDuration=!0}return a}function Vo(n=Wt.visualDuration,a=Wt.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:a}:n;let{restSpeed:l,restDelta:c}=s;const d=s.keyframes[0],u=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:m,damping:p,mass:g,duration:v,velocity:b,isResolvedFromDuration:T}=oA(Z(N({},s),{velocity:-Rn(s.velocity||0)})),D=b||0,A=p/(2*Math.sqrt(m*g)),E=u-d,_=Rn(Math.sqrt(m/g)),z=Math.abs(E)<5;l||(l=z?Wt.restSpeed.granular:Wt.restSpeed.default),c||(c=z?Wt.restDelta.granular:Wt.restDelta.default);let j;if(A<1){const C=Kf(_,A);j=X=>{const W=Math.exp(-A*_*X);return u-W*((D+A*_*E)/C*Math.sin(C*X)+E*Math.cos(C*X))}}else if(A===1)j=C=>u-Math.exp(-_*C)*(E+(D+_*E)*C);else{const C=_*Math.sqrt(A*A-1);j=X=>{const W=Math.exp(-A*_*X),H=Math.min(C*X,300);return u-W*((D+A*_*E)*Math.sinh(H)+C*E*Math.cosh(H))/C}}const M={calculatedDuration:T&&v||null,next:C=>{const X=j(C);if(T)h.done=C>=v;else{let W=C===0?D:0;A<1&&(W=C===0?Cn(D):G0(j,C,X));const H=Math.abs(W)<=l,$=Math.abs(u-X)<=c;h.done=H&&$}return h.value=h.done?u:X,h},toString:()=>{const C=Math.min(kd(M),_o),X=P0(W=>M.next(C*W).value,C,30);return C+"ms "+X},toTransition:()=>{}};return M}Vo.applyToOptions=n=>{const a=eA(n,100,Vo);return n.ease=a.ease,n.duration=Cn(a.duration),n.type="keyframes",n};function Zf({keyframes:n,velocity:a=0,power:s=.8,timeConstant:l=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:u,min:h,max:m,restDelta:p=.5,restSpeed:g}){const v=n[0],b={done:!1,value:v},T=H=>h!==void 0&&H<h||m!==void 0&&H>m,D=H=>h===void 0?m:m===void 0||Math.abs(h-H)<Math.abs(m-H)?h:m;let A=s*a;const E=v+A,_=u===void 0?E:u(E);_!==E&&(A=_-v);const z=H=>-A*Math.exp(-H/l),j=H=>_+z(H),M=H=>{const $=z(H),ct=j(H);b.done=Math.abs($)<=p,b.value=b.done?_:ct};let C,X;const W=H=>{T(b.value)&&(C=H,X=Vo({keyframes:[b.value,D(b.value)],velocity:G0(j,H,b.value),damping:c,stiffness:d,restDelta:p,restSpeed:g}))};return W(0),{calculatedDuration:null,next:H=>{let $=!1;return!X&&C===void 0&&($=!0,M(H),W(H)),C!==void 0&&H>=C?X.next(H-C):(!$&&M(H),b)}}}function cA(n,a,s){const l=[],c=s||$n.mix||H0,d=n.length-1;for(let u=0;u<d;u++){let h=c(n[u],n[u+1]);if(a){const m=Array.isArray(a)?a[u]||rn:a;h=Hr(m,h)}l.push(h)}return l}function uA(n,a,{clamp:s=!0,ease:l,mixer:c}={}){const d=n.length;if(Dd(d===a.length),d===1)return()=>a[0];if(d===2&&a[0]===a[1])return()=>a[1];const u=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),a=[...a].reverse());const h=cA(a,l,c),m=h.length,p=g=>{if(u&&g<n[0])return a[0];let v=0;if(m>1)for(;v<n.length-2&&!(g<n[v+1]);v++);const b=Or(n[v],n[v+1],g);return h[v](b)};return s?g=>p(In(n[0],n[d-1],g)):p}function fA(n,a){const s=n[n.length-1];for(let l=1;l<=a;l++){const c=Or(0,a,l);n.push(Qt(s,1,c))}}function dA(n){const a=[0];return fA(a,n.length-1),a}function hA(n,a){return n.map(s=>s*a)}function mA(n,a){return n.map(()=>a||O0).splice(0,n.length-1)}function Mr({duration:n=300,keyframes:a,times:s,ease:l="easeInOut"}){const c=Ew(l)?l.map(by):by(l),d={done:!1,value:a[0]},u=hA(s&&s.length===a.length?s:dA(a),n),h=uA(u,a,{ease:Array.isArray(c)?c:mA(a,c)});return{calculatedDuration:n,next:m=>(d.value=h(m),d.done=m>=n,d)}}const pA=n=>n!==null;function Hd(n,{repeat:a,repeatType:s="loop"},l,c=1){const d=n.filter(pA),h=c<0||a&&s!=="loop"&&a%2===1?0:d.length-1;return!h||l===void 0?d[h]:l}const gA={decay:Zf,inertia:Zf,tween:Mr,keyframes:Mr,spring:Vo};function Y0(n){typeof n.type=="string"&&(n.type=gA[n.type])}class Pd{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,s){return this.finished.then(a,s)}}const yA=n=>n/100;class Gd extends Pd{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var l,c;const{motionValue:s}=this.options;s&&s.updatedAt!==_e.now()&&this.tick(_e.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(c=(l=this.options).onStop)==null||c.call(l))},this.options=a,this.initAnimation(),this.play(),a.autoplay===!1&&this.pause()}initAnimation(){const{options:a}=this;Y0(a);const{type:s=Mr,repeat:l=0,repeatDelay:c=0,repeatType:d,velocity:u=0}=a;let{keyframes:h}=a;const m=s||Mr;m!==Mr&&typeof h[0]!="number"&&(this.mixKeyframes=Hr(yA,H0(h[0],h[1])),h=[0,100]);const p=m(Z(N({},a),{keyframes:h}));d==="mirror"&&(this.mirroredGenerator=m(Z(N({},a),{keyframes:[...h].reverse(),velocity:-u}))),p.calculatedDuration===null&&(p.calculatedDuration=kd(p));const{calculatedDuration:g}=p;this.calculatedDuration=g,this.resolvedDuration=g+c,this.totalDuration=this.resolvedDuration*(l+1)-c,this.generator=p}updateTime(a){const s=Math.round(a-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(a,s=!1){const{generator:l,totalDuration:c,mixKeyframes:d,mirroredGenerator:u,resolvedDuration:h,calculatedDuration:m}=this;if(this.startTime===null)return l.next(0);const{delay:p=0,keyframes:g,repeat:v,repeatType:b,repeatDelay:T,type:D,onUpdate:A,finalKeyframe:E}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-c/this.speed,this.startTime)),s?this.currentTime=a:this.updateTime(a);const _=this.currentTime-p*(this.playbackSpeed>=0?1:-1),z=this.playbackSpeed>=0?_<0:_>c;this.currentTime=Math.max(_,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let j=this.currentTime,M=l;if(v){const H=Math.min(this.currentTime,c)/h;let $=Math.floor(H),ct=H%1;!ct&&H>=1&&(ct=1),ct===1&&$--,$=Math.min($,v+1),!!($%2)&&(b==="reverse"?(ct=1-ct,T&&(ct-=T/h)):b==="mirror"&&(M=u)),j=In(0,1,ct)*h}const C=z?{done:!1,value:g[0]}:M.next(j);d&&(C.value=d(C.value));let{done:X}=C;!z&&m!==null&&(X=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const W=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&X);return W&&D!==Zf&&(C.value=Hd(g,this.options,E,this.speed)),A&&A(C.value),W&&this.finish(),C}then(a,s){return this.finished.then(a,s)}get duration(){return Rn(this.calculatedDuration)}get time(){return Rn(this.currentTime)}set time(a){var s;a=Cn(a),this.currentTime=a,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(_e.now());const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Rn(this.currentTime))}play(){var c,d;if(this.isStopped)return;const{driver:a=tA,startTime:s}=this.options;this.driver||(this.driver=a(u=>this.tick(u))),(d=(c=this.options).onPlay)==null||d.call(c);const l=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=l):this.holdTime!==null?this.startTime=l-this.holdTime:this.startTime||(this.startTime=s!=null?s:l),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(_e.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var a,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(a=this.options).onComplete)==null||s.call(a)}cancel(){var a,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(a=this.options).onCancel)==null||s.call(a)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),a.observe(this)}}function vA(n){var a;for(let s=1;s<n.length;s++)(a=n[s])!=null||(n[s]=n[s-1])}const ri=n=>n*180/Math.PI,Qf=n=>{const a=ri(Math.atan2(n[1],n[0]));return Ff(a)},bA={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Qf,rotateZ:Qf,skewX:n=>ri(Math.atan(n[1])),skewY:n=>ri(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},Ff=n=>(n=n%360,n<0&&(n+=360),n),Cy=Qf,Ry=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),Dy=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),xA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ry,scaleY:Dy,scale:n=>(Ry(n)+Dy(n))/2,rotateX:n=>Ff(ri(Math.atan2(n[6],n[5]))),rotateY:n=>Ff(ri(Math.atan2(-n[2],n[0]))),rotateZ:Cy,rotate:Cy,skewX:n=>ri(Math.atan(n[4])),skewY:n=>ri(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function If(n){return n.includes("scale")?1:0}function $f(n,a){if(!n||n==="none")return If(a);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let l,c;if(s)l=xA,c=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);l=bA,c=h}if(!c)return If(a);const d=l[a],u=c[1].split(",").map(TA);return typeof d=="function"?d(u):u[d]}const SA=(n,a)=>{const{transform:s="none"}=getComputedStyle(n);return $f(s,a)};function TA(n){return parseFloat(n.trim())}const ps=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gs=new Set(ps),Oy=n=>n===ms||n===yt,wA=new Set(["x","y","z"]),AA=ps.filter(n=>!wA.has(n));function EA(n){const a=[];return AA.forEach(s=>{const l=n.getValue(s);l!==void 0&&(a.push([s,l.get()]),l.set(s.startsWith("scale")?1:0))}),a}const li={width:({x:n},{paddingLeft:a="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),height:({y:n},{paddingTop:a="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),top:(n,{top:a})=>parseFloat(a),left:(n,{left:a})=>parseFloat(a),bottom:({y:n},{top:a})=>parseFloat(a)+(n.max-n.min),right:({x:n},{left:a})=>parseFloat(a)+(n.max-n.min),x:(n,{transform:a})=>$f(a,"x"),y:(n,{transform:a})=>$f(a,"y")};li.translateX=li.x;li.translateY=li.y;const oi=new Set;let Wf=!1,Jf=!1,td=!1;function q0(){if(Jf){const n=Array.from(oi).filter(l=>l.needsMeasurement),a=new Set(n.map(l=>l.element)),s=new Map;a.forEach(l=>{const c=EA(l);c.length&&(s.set(l,c),l.render())}),n.forEach(l=>l.measureInitialState()),a.forEach(l=>{l.render();const c=s.get(l);c&&c.forEach(([d,u])=>{var h;(h=l.getValue(d))==null||h.set(u)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}Jf=!1,Wf=!1,oi.forEach(n=>n.complete(td)),oi.clear()}function X0(){oi.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Jf=!0)})}function MA(){td=!0,X0(),q0(),td=!1}class Yd{constructor(a,s,l,c,d,u=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=l,this.motionValue=c,this.element=d,this.isAsync=u}scheduleResolve(){this.state="scheduled",this.isAsync?(oi.add(this),Wf||(Wf=!0,Ft.read(X0),Ft.resolveKeyframes(q0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:l,motionValue:c}=this;if(a[0]===null){const d=c==null?void 0:c.get(),u=a[a.length-1];if(d!==void 0)a[0]=d;else if(l&&s){const h=l.readValue(s,u);h!=null&&(a[0]=h)}a[0]===void 0&&(a[0]=u),c&&d===void 0&&c.set(a[0])}vA(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),oi.delete(this)}cancel(){this.state==="scheduled"&&(oi.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const CA=n=>n.startsWith("--");function RA(n,a,s){CA(a)?n.style.setProperty(a,s):n.style[a]=s}const DA=Od(()=>window.ScrollTimeline!==void 0),OA={};function NA(n,a){const s=Od(n);return()=>{var l;return(l=OA[a])!=null?l:s()}}const K0=NA(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(n){return!1}return!0},"linearEasing"),wr=([n,a,s,l])=>`cubic-bezier(${n}, ${a}, ${s}, ${l})`,Ny={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wr([0,.65,.55,1]),circOut:wr([.55,0,1,.45]),backIn:wr([.31,.01,.66,-.59]),backOut:wr([.33,1.53,.69,.99])};function Z0(n,a){if(n)return typeof n=="function"?K0()?P0(n,a):"ease-out":N0(n)?wr(n):Array.isArray(n)?n.map(s=>Z0(s,a)||Ny.easeOut):Ny[n]}function jA(n,a,s,{delay:l=0,duration:c=300,repeat:d=0,repeatType:u="loop",ease:h="easeOut",times:m}={},p=void 0){const g={[a]:s};m&&(g.offset=m);const v=Z0(h,c);Array.isArray(v)&&(g.easing=v);const b={delay:l,duration:c,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:d+1,direction:u==="reverse"?"alternate":"normal"};return p&&(b.pseudoElement=p),n.animate(g,b)}function Q0(n){return typeof n=="function"&&"applyToOptions"in n}function _A(s){var l=s,{type:n}=l,a=et(l,["type"]);var c,d;return Q0(n)&&K0()?n.applyToOptions(a):((c=a.duration)!=null||(a.duration=300),(d=a.ease)!=null||(a.ease="easeOut"),a)}class VA extends Pd{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;const{element:s,name:l,keyframes:c,pseudoElement:d,allowFlatten:u=!1,finalKeyframe:h,onComplete:m}=a;this.isPseudoElement=!!d,this.allowFlatten=u,this.options=a,Dd(typeof a.type!="string");const p=_A(a);this.animation=jA(s,l,c,p,d),p.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const g=Hd(c,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(g):RA(s,l,g),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var a,s;(s=(a=this.animation).finish)==null||s.call(a)}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:a}=this;a==="idle"||a==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var a,s;this.isPseudoElement||(s=(a=this.animation).commitStyles)==null||s.call(a)}get duration(){var s,l;const a=((l=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:l.call(s).duration)||0;return Rn(Number(a))}get time(){return Rn(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=Cn(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:s}){var l;return this.allowFlatten&&((l=this.animation.effect)==null||l.updateTiming({easing:"linear"})),this.animation.onfinish=null,a&&DA()?(this.animation.timeline=a,rn):s(this)}}const F0={anticipate:C0,backInOut:M0,circInOut:D0};function zA(n){return n in F0}function LA(n){typeof n.ease=="string"&&zA(n.ease)&&(n.ease=F0[n.ease])}const jy=10;class BA extends VA{constructor(a){LA(a),Y0(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){var g;const p=this.options,{motionValue:s,onUpdate:l,onComplete:c,element:d}=p,u=et(p,["motionValue","onUpdate","onComplete","element"]);if(!s)return;if(a!==void 0){s.set(a);return}const h=new Gd(Z(N({},u),{autoplay:!1})),m=Cn((g=this.finishedTime)!=null?g:this.time);s.setWithVelocity(h.sample(m-jy).value,h.sample(m).value,jy),h.stop()}}const _y=(n,a)=>a==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Da.test(n)||n==="0")&&!n.startsWith("url("));function UA(n){const a=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==a)return!0}function kA(n,a,s,l){const c=n[0];if(c===null)return!1;if(a==="display"||a==="visibility")return!0;const d=n[n.length-1],u=_y(c,a),h=_y(d,a);return!u||!h?!1:UA(n)||(s==="spring"||Q0(s))&&l}function I0(n){return b0(n)&&"offsetHeight"in n}const HA=new Set(["opacity","clipPath","filter","transform"]),PA=Od(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function GA(n){var p;const{motionValue:a,name:s,repeatDelay:l,repeatType:c,damping:d,type:u}=n;if(!I0((p=a==null?void 0:a.owner)==null?void 0:p.current))return!1;const{onUpdate:h,transformTemplate:m}=a.owner.getProps();return PA()&&s&&HA.has(s)&&(s!=="transform"||!m)&&!h&&!l&&c!=="mirror"&&d!==0&&u!=="inertia"}const YA=40;class qA extends Pd{constructor(b){var T=b,{autoplay:a=!0,delay:s=0,type:l="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:u="loop",keyframes:h,name:m,motionValue:p,element:g}=T,v=et(T,["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"]);var E;super(),this.stop=()=>{var _,z;this._animation&&(this._animation.stop(),(_=this.stopTimeline)==null||_.call(this)),(z=this.keyframeResolver)==null||z.cancel()},this.createdAt=_e.now();const D=N({autoplay:a,delay:s,type:l,repeat:c,repeatDelay:d,repeatType:u,name:m,motionValue:p,element:g},v),A=(g==null?void 0:g.KeyframeResolver)||Yd;this.keyframeResolver=new A(h,(_,z,j)=>this.onKeyframesResolved(_,z,D,!j),m,p,g),(E=this.keyframeResolver)==null||E.scheduleResolve()}onKeyframesResolved(a,s,l,c){this.keyframeResolver=void 0;const{name:d,type:u,velocity:h,delay:m,isHandoff:p,onUpdate:g}=l;this.resolvedAt=_e.now(),kA(a,d,u,h)||(($n.instantAnimations||!m)&&(g==null||g(Hd(a,l,s))),a[0]=a[a.length-1],l.duration=0,l.repeat=0);const v=c?this.resolvedAt?this.resolvedAt-this.createdAt>YA?this.resolvedAt:this.createdAt:this.createdAt:void 0,b=Z(N({startTime:v,finalKeyframe:s},l),{keyframes:a}),T=!p&&GA(b)?new BA(Z(N({},b),{element:b.motionValue.owner.current})):new Gd(b);T.finished.then(()=>this.notifyFinished()).catch(rn),this.pendingTimeline&&(this.stopTimeline=T.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=T}get finished(){return this._animation?this.animation.finished:this._finished}then(a,s){return this.finished.finally(a).then(()=>{})}get animation(){var a;return this._animation||((a=this.keyframeResolver)==null||a.resume(),MA()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var a;this._animation&&this.animation.cancel(),(a=this.keyframeResolver)==null||a.cancel()}}const XA=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function KA(n){const a=XA.exec(n);if(!a)return[,];const[,s,l,c]=a;return[`--${s!=null?s:l}`,c]}function $0(n,a,s=1){const[l,c]=KA(n);if(!l)return;const d=window.getComputedStyle(a).getPropertyValue(l);if(d){const u=d.trim();return v0(u)?parseFloat(u):u}return zd(c)?$0(c,a,s+1):c}function qd(n,a){var s,l;return(l=(s=n==null?void 0:n[a])!=null?s:n==null?void 0:n.default)!=null?l:n}const W0=new Set(["width","height","top","left","right","bottom",...ps]),ZA={test:n=>n==="auto",parse:n=>n},J0=n=>a=>a.test(n),tb=[ms,yt,Dn,Ma,Uw,Bw,ZA],Vy=n=>tb.find(J0(n));function QA(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||x0(n):!0}const FA=new Set(["brightness","contrast","saturate","opacity"]);function IA(n){const[a,s]=n.slice(0,-1).split("(");if(a==="drop-shadow")return n;const[l]=s.match(Ld)||[];if(!l)return n;const c=s.replace(l,"");let d=FA.has(a)?1:0;return l!==s&&(d*=100),a+"("+d+c+")"}const $A=/\b([a-z-]*)\(.*?\)/gu,ed=Z(N({},Da),{getAnimatableNone:n=>{const a=n.match($A);return a?a.map(IA).join(" "):n}}),zy=Z(N({},ms),{transform:Math.round}),WA={rotate:Ma,rotateX:Ma,rotateY:Ma,rotateZ:Ma,scale:ho,scaleX:ho,scaleY:ho,scaleZ:ho,skew:Ma,skewX:Ma,skewY:Ma,distance:yt,translateX:yt,translateY:yt,translateZ:yt,x:yt,y:yt,z:yt,perspective:yt,transformPerspective:yt,opacity:Nr,originX:Sy,originY:Sy,originZ:yt},Xd=Z(N({borderWidth:yt,borderTopWidth:yt,borderRightWidth:yt,borderBottomWidth:yt,borderLeftWidth:yt,borderRadius:yt,radius:yt,borderTopLeftRadius:yt,borderTopRightRadius:yt,borderBottomRightRadius:yt,borderBottomLeftRadius:yt,width:yt,maxWidth:yt,height:yt,maxHeight:yt,top:yt,right:yt,bottom:yt,left:yt,padding:yt,paddingTop:yt,paddingRight:yt,paddingBottom:yt,paddingLeft:yt,margin:yt,marginTop:yt,marginRight:yt,marginBottom:yt,marginLeft:yt,backgroundPositionX:yt,backgroundPositionY:yt},WA),{zIndex:zy,fillOpacity:Nr,strokeOpacity:Nr,numOctaves:zy}),JA=Z(N({},Xd),{color:Se,backgroundColor:Se,outlineColor:Se,fill:Se,stroke:Se,borderColor:Se,borderTopColor:Se,borderRightColor:Se,borderBottomColor:Se,borderLeftColor:Se,filter:ed,WebkitFilter:ed}),eb=n=>JA[n];function nb(n,a){let s=eb(n);return s!==ed&&(s=Da),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const tE=new Set(["auto","none","0"]);function eE(n,a,s){let l=0,c;for(;l<n.length&&!c;){const d=n[l];typeof d=="string"&&!tE.has(d)&&jr(d).values.length&&(c=n[l]),l++}if(c&&s)for(const d of a)n[d]=nb(s,c)}class nE extends Yd{constructor(a,s,l,c,d){super(a,s,l,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:l}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<a.length;m++){let p=a[m];if(typeof p=="string"&&(p=p.trim(),zd(p))){const g=$0(p,s.current);g!==void 0&&(a[m]=g),m===a.length-1&&(this.finalKeyframe=p)}}if(this.resolveNoneKeyframes(),!W0.has(l)||a.length!==2)return;const[c,d]=a,u=Vy(c),h=Vy(d);if(u!==h)if(Oy(u)&&Oy(h))for(let m=0;m<a.length;m++){const p=a[m];typeof p=="string"&&(a[m]=parseFloat(p))}else li[l]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,l=[];for(let c=0;c<a.length;c++)(a[c]===null||QA(a[c]))&&l.push(c);l.length&&eE(a,l,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:l}=this;if(!a||!a.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=li[l](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const c=s[s.length-1];c!==void 0&&a.getValue(l,c).jump(c,!1)}measureEndState(){var h;const{element:a,name:s,unresolvedKeyframes:l}=this;if(!a||!a.current)return;const c=a.getValue(s);c&&c.jump(this.measuredOrigin,!1);const d=l.length-1,u=l[d];l[d]=li[s](a.measureViewportBox(),window.getComputedStyle(a.current)),u!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=u),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([m,p])=>{a.getValue(m).set(p)}),this.resolveNoneKeyframes()}}function aE(n,a,s){var l;if(n instanceof EventTarget)return[n];if(typeof n=="string"){let c=document;const d=(l=s==null?void 0:s[n])!=null?l:c.querySelectorAll(n);return d?Array.from(d):[]}return Array.from(n)}const ab=(n,a)=>a&&typeof n=="number"?a.transform(n):n,Ly=30,iE=n=>!isNaN(parseFloat(n));class sE{constructor(a,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,c=!0)=>{var u,h;const d=_e.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&((u=this.events.change)==null||u.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();c&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=_e.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=iE(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new Nd);const l=this.events[a].add(s);return a==="change"?()=>{l(),Ft.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,l){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-l}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var a;(a=this.events.change)==null||a.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=_e.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>Ly)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Ly);return S0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var a,s;(a=this.dependents)==null||a.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function fs(n,a){return new sE(n,a)}const{schedule:Kd}=j0(queueMicrotask,!1),dn={x:!1,y:!1};function ib(){return dn.x||dn.y}function rE(n){return n==="x"||n==="y"?dn[n]?null:(dn[n]=!0,()=>{dn[n]=!1}):dn.x||dn.y?null:(dn.x=dn.y=!0,()=>{dn.x=dn.y=!1})}function sb(n,a){const s=aE(n),l=new AbortController,c=Z(N({passive:!0},a),{signal:l.signal});return[s,c,()=>l.abort()]}function By(n){return!(n.pointerType==="touch"||ib())}function lE(n,a,s={}){const[l,c,d]=sb(n,s),u=h=>{if(!By(h))return;const{target:m}=h,p=a(m,h);if(typeof p!="function"||!m)return;const g=v=>{By(v)&&(p(v),m.removeEventListener("pointerleave",g))};m.addEventListener("pointerleave",g,c)};return l.forEach(h=>{h.addEventListener("pointerenter",u,c)}),d}const rb=(n,a)=>a?n===a?!0:rb(n,a.parentElement):!1,Zd=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,oE=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function cE(n){return oE.has(n.tagName)||n.tabIndex!==-1}const Mo=new WeakSet;function Uy(n){return a=>{a.key==="Enter"&&n(a)}}function wf(n,a){n.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const uE=(n,a)=>{const s=n.currentTarget;if(!s)return;const l=Uy(()=>{if(Mo.has(s))return;wf(s,"down");const c=Uy(()=>{wf(s,"up")}),d=()=>wf(s,"cancel");s.addEventListener("keyup",c,a),s.addEventListener("blur",d,a)});s.addEventListener("keydown",l,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",l),a)};function ky(n){return Zd(n)&&!ib()}function fE(n,a,s={}){const[l,c,d]=sb(n,s),u=h=>{const m=h.currentTarget;if(!ky(h))return;Mo.add(m);const p=a(m,h),g=(T,D)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",b),Mo.has(m)&&Mo.delete(m),ky(T)&&typeof p=="function"&&p(T,{success:D})},v=T=>{g(T,m===window||m===document||s.useGlobalTarget||rb(m,T.target))},b=T=>{g(T,!1)};window.addEventListener("pointerup",v,c),window.addEventListener("pointercancel",b,c)};return l.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",u,c),I0(h)&&(h.addEventListener("focus",p=>uE(p,c)),!cE(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function lb(n){return b0(n)&&"ownerSVGElement"in n}function dE(n){return lb(n)&&n.tagName==="svg"}const Te=n=>!!(n&&n.getVelocity),hE=[...tb,Se,Da],mE=n=>hE.find(J0(n)),ob=w.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function pE(n=!0){const a=w.useContext(Md);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:l,register:c}=a,d=w.useId();w.useEffect(()=>{if(n)return c(d)},[n]);const u=w.useCallback(()=>n&&l&&l(d),[d,l,n]);return!s&&l?[!1,u]:[!0]}const cb=w.createContext({strict:!1}),Hy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ds={};for(const n in Hy)ds[n]={isEnabled:a=>Hy[n].some(s=>!!a[s])};function gE(n){for(const a in n)ds[a]=N(N({},ds[a]),n[a])}const yE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function zo(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||yE.has(n)}let ub=n=>!zo(n);function vE(n){n&&(ub=a=>a.startsWith("on")?!zo(a):n(a))}try{vE(require("@emotion/is-prop-valid").default)}catch(n){}function bE(n,a,s){const l={};for(const c in n)c==="values"&&typeof n.values=="object"||(ub(c)||s===!0&&zo(c)||!a&&!zo(c)||n.draggable&&c.startsWith("onDrag"))&&(l[c]=n[c]);return l}function xE(n){if(typeof Proxy=="undefined")return n;const a=new Map,s=(...l)=>n(...l);return new Proxy(s,{get:(l,c)=>c==="create"?n:(a.has(c)||a.set(c,n(c)),a.get(c))})}const Yo=w.createContext({});function qo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function _r(n){return typeof n=="string"||Array.isArray(n)}const Qd=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Fd=["initial",...Qd];function Xo(n){return qo(n.animate)||Fd.some(a=>_r(n[a]))}function fb(n){return!!(Xo(n)||n.variants)}function SE(n,a){if(Xo(n)){const{initial:s,animate:l}=n;return{initial:s===!1||_r(s)?s:void 0,animate:_r(l)?l:void 0}}return n.inherit!==!1?a:{}}function TE(n){const{initial:a,animate:s}=SE(n,w.useContext(Yo));return w.useMemo(()=>({initial:a,animate:s}),[Py(a),Py(s)])}function Py(n){return Array.isArray(n)?n.join(" "):n}const wE=Symbol.for("motionComponentSymbol");function ss(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function AE(n,a,s){return w.useCallback(l=>{l&&n.onMount&&n.onMount(l),a&&(l?a.mount(l):a.unmount()),s&&(typeof s=="function"?s(l):ss(s)&&(s.current=l))},[a])}const Id=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),EE="framerAppearId",db="data-"+Id(EE),hb=w.createContext({});function ME(n,a,s,l,c){var A,E;const{visualElement:d}=w.useContext(Yo),u=w.useContext(cb),h=w.useContext(Md),m=w.useContext(ob).reducedMotion,p=w.useRef(null);l=l||u.renderer,!p.current&&l&&(p.current=l(n,{visualState:a,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:m}));const g=p.current,v=w.useContext(hb);g&&!g.projection&&c&&(g.type==="html"||g.type==="svg")&&CE(p.current,s,c,v);const b=w.useRef(!1);w.useInsertionEffect(()=>{g&&b.current&&g.update(s,h)});const T=s[db],D=w.useRef(!!T&&!((A=window.MotionHandoffIsComplete)!=null&&A.call(window,T))&&((E=window.MotionHasOptimisedAnimation)==null?void 0:E.call(window,T)));return vw(()=>{g&&(b.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),Kd.render(g.render),D.current&&g.animationState&&g.animationState.animateChanges())}),w.useEffect(()=>{g&&(!D.current&&g.animationState&&g.animationState.animateChanges(),D.current&&(queueMicrotask(()=>{var _;(_=window.MotionHandoffMarkAsComplete)==null||_.call(window,T)}),D.current=!1))}),g}function CE(n,a,s,l){const{layoutId:c,layout:d,drag:u,dragConstraints:h,layoutScroll:m,layoutRoot:p,layoutCrossfade:g}=a;n.projection=new s(n.latestValues,a["data-framer-portal-id"]?void 0:mb(n.parent)),n.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!u||h&&ss(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,crossfade:g,layoutScroll:m,layoutRoot:p})}function mb(n){if(n)return n.options.allowProjection!==!1?n.projection:mb(n.parent)}function RE({preloadedFeatures:n,createVisualElement:a,useRender:s,useVisualState:l,Component:c}){var h,m;n&&gE(n);function d(p,g){let v;const b=Z(N(N({},w.useContext(ob)),p),{layoutId:DE(p)}),{isStatic:T}=b,D=TE(p),A=l(p,T);if(!T&&Ed){OE();const E=NE(b);v=E.MeasureLayout,D.visualElement=ME(c,A,b,a,E.ProjectionNode)}return S.jsxs(Yo.Provider,{value:D,children:[v&&D.visualElement?S.jsx(v,N({visualElement:D.visualElement},b)):null,s(c,p,AE(A,D.visualElement,g),A,T,D.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${(m=(h=c.displayName)!=null?h:c.name)!=null?m:""})`}`;const u=w.forwardRef(d);return u[wE]=c,u}function DE({layoutId:n}){const a=w.useContext(y0).id;return a&&n!==void 0?a+"-"+n:n}function OE(n,a){w.useContext(cb).strict}function NE(n){const{drag:a,layout:s}=ds;if(!a&&!s)return{};const l=N(N({},a),s);return{MeasureLayout:a!=null&&a.isEnabled(n)||s!=null&&s.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const Vr={};function jE(n){for(const a in n)Vr[a]=n[a],Vd(a)&&(Vr[a].isCSSVariable=!0)}function pb(n,{layout:a,layoutId:s}){return gs.has(n)||n.startsWith("origin")||(a||s!==void 0)&&(!!Vr[n]||n==="opacity")}const _E={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},VE=ps.length;function zE(n,a,s){let l="",c=!0;for(let d=0;d<VE;d++){const u=ps[d],h=n[u];if(h===void 0)continue;let m=!0;if(typeof h=="number"?m=h===(u.startsWith("scale")?1:0):m=parseFloat(h)===0,!m||s){const p=ab(h,Xd[u]);if(!m){c=!1;const g=_E[u]||u;l+=`${g}(${p}) `}s&&(a[u]=p)}}return l=l.trim(),s?l=s(a,c?"":l):c&&(l="none"),l}function $d(n,a,s){const{style:l,vars:c,transformOrigin:d}=n;let u=!1,h=!1;for(const m in a){const p=a[m];if(gs.has(m)){u=!0;continue}else if(Vd(m)){c[m]=p;continue}else{const g=ab(p,Xd[m]);m.startsWith("origin")?(h=!0,d[m]=g):l[m]=g}}if(a.transform||(u||s?l.transform=zE(a,n.transform,s):l.transform&&(l.transform="none")),h){const{originX:m="50%",originY:p="50%",originZ:g=0}=d;l.transformOrigin=`${m} ${p} ${g}`}}const Wd=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function gb(n,a,s){for(const l in a)!Te(a[l])&&!pb(l,s)&&(n[l]=a[l])}function LE({transformTemplate:n},a){return w.useMemo(()=>{const s=Wd();return $d(s,a,n),Object.assign({},s.vars,s.style)},[a])}function BE(n,a){const s=n.style||{},l={};return gb(l,s,n),Object.assign(l,LE(n,a)),l}function UE(n,a){const s={},l=BE(n,a);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=l,s}const kE={offset:"stroke-dashoffset",array:"stroke-dasharray"},HE={offset:"strokeDashoffset",array:"strokeDasharray"};function PE(n,a,s=1,l=0,c=!0){n.pathLength=1;const d=c?kE:HE;n[d.offset]=yt.transform(-l);const u=yt.transform(a),h=yt.transform(s);n[d.array]=`${u} ${h}`}function yb(n,v,m,p,g){var b=v,{attrX:a,attrY:s,attrScale:l,pathLength:c,pathSpacing:d=1,pathOffset:u=0}=b,h=et(b,["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"]);var A,E;if($d(n,h,p),m){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:T,style:D}=n;T.transform&&(D.transform=T.transform,delete T.transform),(D.transform||T.transformOrigin)&&(D.transformOrigin=(A=T.transformOrigin)!=null?A:"50% 50%",delete T.transformOrigin),D.transform&&(D.transformBox=(E=g==null?void 0:g.transformBox)!=null?E:"fill-box",delete T.transformBox),a!==void 0&&(T.x=a),s!==void 0&&(T.y=s),l!==void 0&&(T.scale=l),c!==void 0&&PE(T,c,d,u,!1)}const vb=()=>Z(N({},Wd()),{attrs:{}}),bb=n=>typeof n=="string"&&n.toLowerCase()==="svg";function GE(n,a,s,l){const c=w.useMemo(()=>{const d=vb();return yb(d,a,bb(l),n.transformTemplate,n.style),Z(N({},d.attrs),{style:N({},d.style)})},[a]);if(n.style){const d={};gb(d,n.style,n),c.style=N(N({},d),c.style)}return c}const YE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Jd(n){return typeof n!="string"||n.includes("-")?!1:!!(YE.indexOf(n)>-1||/[A-Z]/u.test(n))}function qE(n=!1){return(s,l,c,{latestValues:d},u)=>{const m=(Jd(s)?GE:UE)(l,d,u,s),p=bE(l,typeof s=="string",n),g=s!==w.Fragment?Z(N(N({},p),m),{ref:c}):{},{children:v}=l,b=w.useMemo(()=>Te(v)?v.get():v,[v]);return w.createElement(s,Z(N({},g),{children:b}))}}function Gy(n){const a=[{},{}];return n==null||n.values.forEach((s,l)=>{a[0][l]=s.get(),a[1][l]=s.getVelocity()}),a}function th(n,a,s,l){if(typeof a=="function"){const[c,d]=Gy(l);a=a(s!==void 0?s:n.custom,c,d)}if(typeof a=="string"&&(a=n.variants&&n.variants[a]),typeof a=="function"){const[c,d]=Gy(l);a=a(s!==void 0?s:n.custom,c,d)}return a}function Co(n){return Te(n)?n.get():n}function XE({scrapeMotionValuesFromProps:n,createRenderState:a},s,l,c){return{latestValues:KE(s,l,c,n),renderState:a()}}const xb=n=>(a,s)=>{const l=w.useContext(Yo),c=w.useContext(Md),d=()=>XE(n,a,l,c);return s?d():yw(d)};function KE(n,a,s,l){const c={},d=l(n,{});for(const T in d)c[T]=Co(d[T]);let{initial:u,animate:h}=n;const m=Xo(n),p=fb(n);a&&p&&!m&&n.inherit!==!1&&(u===void 0&&(u=a.initial),h===void 0&&(h=a.animate));let g=s?s.initial===!1:!1;g=g||u===!1;const v=g?h:u;if(v&&typeof v!="boolean"&&!qo(v)){const T=Array.isArray(v)?v:[v];for(let D=0;D<T.length;D++){const A=th(n,T[D]);if(A){const b=A,{transitionEnd:E,transition:_}=b,z=et(b,["transitionEnd","transition"]);for(const j in z){let M=z[j];if(Array.isArray(M)){const C=g?M.length-1:0;M=M[C]}M!==null&&(c[j]=M)}for(const j in E)c[j]=E[j]}}}return c}function eh(n,a,s){var d;const{style:l}=n,c={};for(const u in l)(Te(l[u])||a.style&&Te(a.style[u])||pb(u,n)||((d=s==null?void 0:s.getValue(u))==null?void 0:d.liveStyle)!==void 0)&&(c[u]=l[u]);return c}const ZE={useVisualState:xb({scrapeMotionValuesFromProps:eh,createRenderState:Wd})};function Sb(n,a,s){const l=eh(n,a,s);for(const c in n)if(Te(n[c])||Te(a[c])){const d=ps.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;l[d]=n[c]}return l}const QE={useVisualState:xb({scrapeMotionValuesFromProps:Sb,createRenderState:vb})};function FE(n,a){return function(l,{forwardMotionProps:c}={forwardMotionProps:!1}){const d=Jd(l)?QE:ZE,u=Z(N({},d),{preloadedFeatures:n,useRender:qE(c),createVisualElement:a,Component:l});return RE(u)}}function zr(n,a,s){const l=n.getProps();return th(l,a,s!==void 0?s:l.custom,n)}const nd=n=>Array.isArray(n);function IE(n,a,s){n.hasValue(a)?n.getValue(a).set(s):n.addValue(a,fs(s))}function $E(n){return nd(n)?n[n.length-1]||0:n}function WE(n,a){let u=zr(n,a)||{},{transitionEnd:l={},transition:c={}}=u,d=et(u,["transitionEnd","transition"]);d=N(N({},d),l);for(const h in d){const m=$E(d[h]);IE(n,h,m)}}function JE(n){return!!(Te(n)&&n.add)}function ad(n,a){const s=n.getValue("willChange");if(JE(s))return s.add(a);if(!s&&$n.WillChange){const l=new $n.WillChange("auto");n.addValue("willChange",l),l.add(a)}}function Tb(n){return n.props[db]}const t2=n=>n!==null;function e2(n,{repeat:a,repeatType:s="loop"},l){const c=n.filter(t2),d=a&&s!=="loop"&&a%2===1?0:c.length-1;return c[d]}const n2={type:"spring",stiffness:500,damping:25,restSpeed:10},a2=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),i2={type:"keyframes",duration:.8},s2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},r2=(n,{keyframes:a})=>a.length>2?i2:gs.has(n)?n.startsWith("scale")?a2(a[1]):n2:s2;function l2(v){var b=v,{when:n,delay:a,delayChildren:s,staggerChildren:l,staggerDirection:c,repeat:d,repeatType:u,repeatDelay:h,from:m,elapsed:p}=b,g=et(b,["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"]);return!!Object.keys(g).length}const nh=(n,a,s,l={},c,d)=>u=>{const h=qd(l,n)||{},m=h.delay||l.delay||0;let{elapsed:p=0}=l;p=p-Cn(m);const g=Z(N({keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity()},h),{delay:-p,onUpdate:b=>{a.set(b),h.onUpdate&&h.onUpdate(b)},onComplete:()=>{u(),h.onComplete&&h.onComplete()},name:n,motionValue:a,element:d?void 0:c});l2(h)||Object.assign(g,r2(n,g)),g.duration&&(g.duration=Cn(g.duration)),g.repeatDelay&&(g.repeatDelay=Cn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let v=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(v=!0)),($n.instantAnimations||$n.skipAnimations)&&(v=!0,g.duration=0,g.delay=0),g.allowFlatten=!h.type&&!h.ease,v&&!d&&a.get()!==void 0){const b=e2(g.keyframes,h);if(b!==void 0){Ft.update(()=>{g.onUpdate(b),g.onComplete()});return}}return h.isSync?new Gd(g):new qA(g)};function o2({protectedKeys:n,needsAnimating:a},s){const l=n.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,l}function wb(n,a,{delay:s=0,transitionOverride:l,type:c}={}){var v;let g=a,{transition:d=n.getDefaultTransition(),transitionEnd:u}=g,h=et(g,["transition","transitionEnd"]);l&&(d=l);const m=[],p=c&&n.animationState&&n.animationState.getState()[c];for(const b in h){const T=n.getValue(b,(v=n.latestValues[b])!=null?v:null),D=h[b];if(D===void 0||p&&o2(p,b))continue;const A=N({delay:s},qd(d||{},b)),E=T.get();if(E!==void 0&&!T.isAnimating&&!Array.isArray(D)&&D===E&&!A.velocity)continue;let _=!1;if(window.MotionHandoffAnimation){const j=Tb(n);if(j){const M=window.MotionHandoffAnimation(j,b,Ft);M!==null&&(A.startTime=M,_=!0)}}ad(n,b),T.start(nh(b,T,D,n.shouldReduceMotion&&W0.has(b)?{type:!1}:A,n,_));const z=T.animation;z&&m.push(z)}return u&&Promise.all(m).then(()=>{Ft.update(()=>{u&&WE(n,u)})}),m}function id(n,a,s={}){var m;const l=zr(n,a,s.type==="exit"?(m=n.presenceContext)==null?void 0:m.custom:void 0);let{transition:c=n.getDefaultTransition()||{}}=l||{};s.transitionOverride&&(c=s.transitionOverride);const d=l?()=>Promise.all(wb(n,l,s)):()=>Promise.resolve(),u=n.variantChildren&&n.variantChildren.size?(p=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:b}=c;return c2(n,a,g+p,v,b,s)}:()=>Promise.resolve(),{when:h}=c;if(h){const[p,g]=h==="beforeChildren"?[d,u]:[u,d];return p().then(()=>g())}else return Promise.all([d(),u(s.delay)])}function c2(n,a,s=0,l=0,c=1,d){const u=[],h=(n.variantChildren.size-1)*l,m=c===1?(p=0)=>p*l:(p=0)=>h-p*l;return Array.from(n.variantChildren).sort(u2).forEach((p,g)=>{p.notify("AnimationStart",a),u.push(id(p,a,Z(N({},d),{delay:s+m(g)})).then(()=>p.notify("AnimationComplete",a)))}),Promise.all(u)}function u2(n,a){return n.sortNodePosition(a)}function f2(n,a,s={}){n.notify("AnimationStart",a);let l;if(Array.isArray(a)){const c=a.map(d=>id(n,d,s));l=Promise.all(c)}else if(typeof a=="string")l=id(n,a,s);else{const c=typeof a=="function"?zr(n,a,s.custom):a;l=Promise.all(wb(n,c,s))}return l.then(()=>{n.notify("AnimationComplete",a)})}function Ab(n,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==n.length)return!1;for(let l=0;l<s;l++)if(a[l]!==n[l])return!1;return!0}const d2=Fd.length;function Eb(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?Eb(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const a={};for(let s=0;s<d2;s++){const l=Fd[s],c=n.props[l];(_r(c)||c===!1)&&(a[l]=c)}return a}const h2=[...Qd].reverse(),m2=Qd.length;function p2(n){return a=>Promise.all(a.map(({animation:s,options:l})=>f2(n,s,l)))}function g2(n){let a=p2(n),s=Yy(),l=!0;const c=m=>(p,g)=>{var b;const v=zr(n,g,m==="exit"?(b=n.presenceContext)==null?void 0:b.custom:void 0);if(v){const T=v,{transition:D,transitionEnd:A}=T,E=et(T,["transition","transitionEnd"]);p=N(N(N({},p),E),A)}return p};function d(m){a=m(n)}function u(m){const{props:p}=n,g=Eb(n.parent)||{},v=[],b=new Set;let T={},D=1/0;for(let E=0;E<m2;E++){const _=h2[E],z=s[_],j=p[_]!==void 0?p[_]:g[_],M=_r(j),C=_===m?z.isActive:null;C===!1&&(D=E);let X=j===g[_]&&j!==p[_]&&M;if(X&&l&&n.manuallyAnimateOnMount&&(X=!1),z.protectedKeys=N({},T),!z.isActive&&C===null||!j&&!z.prevProp||qo(j)||typeof j=="boolean")continue;const W=y2(z.prevProp,j);let H=W||_===m&&z.isActive&&!X&&M||E>D&&M,$=!1;const ct=Array.isArray(j)?j:[j];let ht=ct.reduce(c(_),{});C===!1&&(ht={});const{prevResolvedValues:lt={}}=z,bt=N(N({},lt),ht),Et=q=>{H=!0,b.has(q)&&($=!0,b.delete(q)),z.needsAnimating[q]=!0;const G=n.getValue(q);G&&(G.liveStyle=!1)};for(const q in bt){const G=ht[q],ft=lt[q];if(T.hasOwnProperty(q))continue;let O=!1;nd(G)&&nd(ft)?O=!Ab(G,ft):O=G!==ft,O?G!=null?Et(q):b.add(q):G!==void 0&&b.has(q)?Et(q):z.protectedKeys[q]=!0}z.prevProp=j,z.prevResolvedValues=ht,z.isActive&&(T=N(N({},T),ht)),l&&n.blockInitialAnimation&&(H=!1),H&&(!(X&&W)||$)&&v.push(...ct.map(q=>({animation:q,options:{type:_}})))}if(b.size){const E={};if(typeof p.initial!="boolean"){const _=zr(n,Array.isArray(p.initial)?p.initial[0]:p.initial);_&&_.transition&&(E.transition=_.transition)}b.forEach(_=>{const z=n.getBaseTarget(_),j=n.getValue(_);j&&(j.liveStyle=!0),E[_]=z!=null?z:null}),v.push({animation:E})}let A=!!v.length;return l&&(p.initial===!1||p.initial===p.animate)&&!n.manuallyAnimateOnMount&&(A=!1),l=!1,A?a(v):Promise.resolve()}function h(m,p){var v;if(s[m].isActive===p)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(b=>{var T;return(T=b.animationState)==null?void 0:T.setActive(m,p)}),s[m].isActive=p;const g=u(m);for(const b in s)s[b].protectedKeys={};return g}return{animateChanges:u,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=Yy(),l=!0}}}function y2(n,a){return typeof a=="string"?a!==n:Array.isArray(a)?!Ab(a,n):!1}function ei(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Yy(){return{animate:ei(!0),whileInView:ei(),whileHover:ei(),whileTap:ei(),whileDrag:ei(),whileFocus:ei(),exit:ei()}}class _a{constructor(a){this.isMounted=!1,this.node=a}update(){}}class v2 extends _a{constructor(a){super(a),a.animationState||(a.animationState=g2(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();qo(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)==null||a.call(this)}}let b2=0;class x2 extends _a{constructor(){super(...arguments),this.id=b2++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===l)return;const c=this.node.animationState.setActive("exit",!a);s&&!a&&c.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const S2={animation:{Feature:v2},exit:{Feature:x2}};function Lr(n,a,s,l={passive:!0}){return n.addEventListener(a,s,l),()=>n.removeEventListener(a,s)}function Yr(n){return{point:{x:n.pageX,y:n.pageY}}}const T2=n=>a=>Zd(a)&&n(a,Yr(a));function Cr(n,a,s,l){return Lr(n,a,T2(s),l)}function Mb({top:n,left:a,right:s,bottom:l}){return{x:{min:a,max:s},y:{min:n,max:l}}}function w2({x:n,y:a}){return{top:a.min,right:n.max,bottom:a.max,left:n.min}}function A2(n,a){if(!a)return n;const s=a({x:n.left,y:n.top}),l=a({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:l.y,right:l.x}}const Cb=1e-4,E2=1-Cb,M2=1+Cb,Rb=.01,C2=0-Rb,R2=0+Rb;function Me(n){return n.max-n.min}function D2(n,a,s){return Math.abs(n-a)<=s}function qy(n,a,s,l=.5){n.origin=l,n.originPoint=Qt(a.min,a.max,n.origin),n.scale=Me(s)/Me(a),n.translate=Qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=E2&&n.scale<=M2||isNaN(n.scale))&&(n.scale=1),(n.translate>=C2&&n.translate<=R2||isNaN(n.translate))&&(n.translate=0)}function Rr(n,a,s,l){qy(n.x,a.x,s.x,l?l.originX:void 0),qy(n.y,a.y,s.y,l?l.originY:void 0)}function Xy(n,a,s){n.min=s.min+a.min,n.max=n.min+Me(a)}function O2(n,a,s){Xy(n.x,a.x,s.x),Xy(n.y,a.y,s.y)}function Ky(n,a,s){n.min=a.min-s.min,n.max=n.min+Me(a)}function Dr(n,a,s){Ky(n.x,a.x,s.x),Ky(n.y,a.y,s.y)}const Zy=()=>({translate:0,scale:1,origin:0,originPoint:0}),rs=()=>({x:Zy(),y:Zy()}),Qy=()=>({min:0,max:0}),ee=()=>({x:Qy(),y:Qy()});function sn(n){return[n("x"),n("y")]}function Af(n){return n===void 0||n===1}function sd({scale:n,scaleX:a,scaleY:s}){return!Af(n)||!Af(a)||!Af(s)}function ii(n){return sd(n)||Db(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Db(n){return Fy(n.x)||Fy(n.y)}function Fy(n){return n&&n!=="0%"}function Lo(n,a,s){const l=n-s,c=a*l;return s+c}function Iy(n,a,s,l,c){return c!==void 0&&(n=Lo(n,c,l)),Lo(n,s,l)+a}function rd(n,a=0,s=1,l,c){n.min=Iy(n.min,a,s,l,c),n.max=Iy(n.max,a,s,l,c)}function Ob(n,{x:a,y:s}){rd(n.x,a.translate,a.scale,a.originPoint),rd(n.y,s.translate,s.scale,s.originPoint)}const $y=.999999999999,Wy=1.0000000000001;function N2(n,a,s,l=!1){const c=s.length;if(!c)return;a.x=a.y=1;let d,u;for(let h=0;h<c;h++){d=s[h],u=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&os(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),u&&(a.x*=u.x.scale,a.y*=u.y.scale,Ob(n,u)),l&&ii(d.latestValues)&&os(n,d.latestValues))}a.x<Wy&&a.x>$y&&(a.x=1),a.y<Wy&&a.y>$y&&(a.y=1)}function ls(n,a){n.min=n.min+a,n.max=n.max+a}function Jy(n,a,s,l,c=.5){const d=Qt(n.min,n.max,c);rd(n,a,s,d,l)}function os(n,a){Jy(n.x,a.x,a.scaleX,a.scale,a.originX),Jy(n.y,a.y,a.scaleY,a.scale,a.originY)}function Nb(n,a){return Mb(A2(n.getBoundingClientRect(),a))}function j2(n,a,s){const l=Nb(n,s),{scroll:c}=a;return c&&(ls(l.x,c.offset.x),ls(l.y,c.offset.y)),l}const jb=({current:n})=>n?n.ownerDocument.defaultView:null,tv=(n,a)=>Math.abs(n-a);function _2(n,a){const s=tv(n.x,a.x),l=tv(n.y,a.y);return Math.sqrt(df(s,2)+df(l,2))}class _b{constructor(a,s,{transformPagePoint:l,contextWindow:c,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=Mf(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,T=_2(v.offset,{x:0,y:0})>=3;if(!b&&!T)return;const{point:D}=v,{timestamp:A}=ye;this.history.push(Z(N({},D),{timestamp:A}));const{onStart:E,onMove:_}=this.handlers;b||(E&&E(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),_&&_(this.lastMoveEvent,v)},this.handlePointerMove=(v,b)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=Ef(b,this.transformPagePoint),Ft.update(this.updatePoint,!0)},this.handlePointerUp=(v,b)=>{this.end();const{onEnd:T,onSessionEnd:D,resumeAnimation:A}=this.handlers;if(this.dragSnapToOrigin&&A&&A(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const E=Mf(v.type==="pointercancel"?this.lastMoveEventInfo:Ef(b,this.transformPagePoint),this.history);this.startEvent&&T&&T(v,E),D&&D(v,E)},!Zd(a))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=l,this.contextWindow=c||window;const u=Yr(a),h=Ef(u,this.transformPagePoint),{point:m}=h,{timestamp:p}=ye;this.history=[Z(N({},m),{timestamp:p})];const{onSessionStart:g}=s;g&&g(a,Mf(h,this.history)),this.removeListeners=Hr(Cr(this.contextWindow,"pointermove",this.handlePointerMove),Cr(this.contextWindow,"pointerup",this.handlePointerUp),Cr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Ra(this.updatePoint)}}function Ef(n,a){return a?{point:a(n.point)}:n}function ev(n,a){return{x:n.x-a.x,y:n.y-a.y}}function Mf({point:n},a){return{point:n,delta:ev(n,Vb(a)),offset:ev(n,V2(a)),velocity:z2(a,.1)}}function V2(n){return n[0]}function Vb(n){return n[n.length-1]}function z2(n,a){if(n.length<2)return{x:0,y:0};let s=n.length-1,l=null;const c=Vb(n);for(;s>=0&&(l=n[s],!(c.timestamp-l.timestamp>Cn(a)));)s--;if(!l)return{x:0,y:0};const d=Rn(c.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const u={x:(c.x-l.x)/d,y:(c.y-l.y)/d};return u.x===1/0&&(u.x=0),u.y===1/0&&(u.y=0),u}function L2(n,{min:a,max:s},l){return a!==void 0&&n<a?n=l?Qt(a,n,l.min):Math.max(n,a):s!==void 0&&n>s&&(n=l?Qt(s,n,l.max):Math.min(n,s)),n}function nv(n,a,s){return{min:a!==void 0?n.min+a:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function B2(n,{top:a,left:s,bottom:l,right:c}){return{x:nv(n.x,s,c),y:nv(n.y,a,l)}}function av(n,a){let s=a.min-n.min,l=a.max-n.max;return a.max-a.min<n.max-n.min&&([s,l]=[l,s]),{min:s,max:l}}function U2(n,a){return{x:av(n.x,a.x),y:av(n.y,a.y)}}function k2(n,a){let s=.5;const l=Me(n),c=Me(a);return c>l?s=Or(a.min,a.max-l,n.min):l>c&&(s=Or(n.min,n.max-c,a.min)),In(0,1,s)}function H2(n,a){const s={};return a.min!==void 0&&(s.min=a.min-n.min),a.max!==void 0&&(s.max=a.max-n.min),s}const ld=.35;function P2(n=ld){return n===!1?n=0:n===!0&&(n=ld),{x:iv(n,"left","right"),y:iv(n,"top","bottom")}}function iv(n,a,s){return{min:sv(n,a),max:sv(n,s)}}function sv(n,a){return typeof n=="number"?n:n[a]||0}const G2=new WeakMap;class Y2{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const c=g=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Yr(g).point)},d=(g,v)=>{const{drag:b,dragPropagation:T,onDragStart:D}=this.getProps();if(b&&!T&&(this.openDragLock&&this.openDragLock(),this.openDragLock=rE(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),sn(E=>{let _=this.getAxisMotionValue(E).get()||0;if(Dn.test(_)){const{projection:z}=this.visualElement;if(z&&z.layout){const j=z.layout.layoutBox[E];j&&(_=Me(j)*(parseFloat(_)/100))}}this.originPoint[E]=_}),D&&Ft.postRender(()=>D(g,v)),ad(this.visualElement,"transform");const{animationState:A}=this.visualElement;A&&A.setActive("whileDrag",!0)},u=(g,v)=>{const{dragPropagation:b,dragDirectionLock:T,onDirectionLock:D,onDrag:A}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:E}=v;if(T&&this.currentDirection===null){this.currentDirection=q2(E),this.currentDirection!==null&&D&&D(this.currentDirection);return}this.updateAxis("x",v.point,E),this.updateAxis("y",v.point,E),this.visualElement.render(),A&&A(g,v)},h=(g,v)=>this.stop(g,v),m=()=>sn(g=>{var v;return this.getAnimationState(g)==="paused"&&((v=this.getAxisMotionValue(g).animation)==null?void 0:v.play())}),{dragSnapToOrigin:p}=this.getProps();this.panSession=new _b(a,{onSessionStart:c,onStart:d,onMove:u,onSessionEnd:h,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:p,contextWindow:jb(this.visualElement)})}stop(a,s){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:c}=s;this.startAnimation(c);const{onDragEnd:d}=this.getProps();d&&Ft.postRender(()=>d(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,l){const{drag:c}=this.getProps();if(!l||!mo(a,c,this.currentDirection))return;const d=this.getAxisMotionValue(a);let u=this.originPoint[a]+l[a];this.constraints&&this.constraints[a]&&(u=L2(u,this.constraints[a],this.elastic[a])),d.set(u)}resolveConstraints(){var d;const{dragConstraints:a,dragElastic:s}=this.getProps(),l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,c=this.constraints;a&&ss(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&l?this.constraints=B2(l.layoutBox,a):this.constraints=!1,this.elastic=P2(s),c!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&sn(u=>{this.constraints!==!1&&this.getAxisMotionValue(u)&&(this.constraints[u]=H2(l.layoutBox[u],this.constraints[u]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!ss(a))return!1;const l=a.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=j2(l,c.root,this.visualElement.getTransformPagePoint());let u=U2(c.layout.layoutBox,d);if(s){const h=s(w2(u));this.hasMutatedConstraints=!!h,h&&(u=Mb(h))}return u}startAnimation(a){const{drag:s,dragMomentum:l,dragElastic:c,dragTransition:d,dragSnapToOrigin:u,onDragTransitionEnd:h}=this.getProps(),m=this.constraints||{},p=sn(g=>{if(!mo(g,s,this.currentDirection))return;let v=m&&m[g]||{};u&&(v={min:0,max:0});const b=c?200:1e6,T=c?40:1e7,D=N(N({type:"inertia",velocity:l?a[g]:0,bounceStiffness:b,bounceDamping:T,timeConstant:750,restDelta:1,restSpeed:10},d),v);return this.startAxisValueAnimation(g,D)});return Promise.all(p).then(h)}startAxisValueAnimation(a,s){const l=this.getAxisMotionValue(a);return ad(this.visualElement,a),l.start(nh(a,l,0,s,this.visualElement,!1))}stopAnimation(){sn(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){sn(a=>{var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,l=this.visualElement.getProps(),c=l[s];return c||this.visualElement.getValue(a,(l.initial?l.initial[a]:void 0)||0)}snapToCursor(a){sn(s=>{const{drag:l}=this.getProps();if(!mo(s,l,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(s);if(c&&c.layout){const{min:u,max:h}=c.layout.layoutBox[s];d.set(a[s]-Qt(u,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:l}=this.visualElement;if(!ss(s)||!l||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};sn(u=>{const h=this.getAxisMotionValue(u);if(h&&this.constraints!==!1){const m=h.get();c[u]=k2({min:m,max:m},this.constraints[u])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),sn(u=>{if(!mo(u,a,null))return;const h=this.getAxisMotionValue(u),{min:m,max:p}=this.constraints[u];h.set(Qt(m,p,c[u]))})}addListeners(){if(!this.visualElement.current)return;G2.set(this.visualElement,this);const a=this.visualElement.current,s=Cr(a,"pointerdown",m=>{const{drag:p,dragListener:g=!0}=this.getProps();p&&g&&this.start(m)}),l=()=>{const{dragConstraints:m}=this.getProps();ss(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",l);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Ft.read(l);const u=Lr(window,"resize",()=>this.scalePositionWithinConstraints()),h=c.addEventListener("didUpdate",({delta:m,hasLayoutChanged:p})=>{this.isDragging&&p&&(sn(g=>{const v=this.getAxisMotionValue(g);v&&(this.originPoint[g]+=m[g].translate,v.set(v.get()+m[g].translate))}),this.visualElement.render())});return()=>{u(),s(),d(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:l=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:u=ld,dragMomentum:h=!0}=a;return Z(N({},a),{drag:s,dragDirectionLock:l,dragPropagation:c,dragConstraints:d,dragElastic:u,dragMomentum:h})}}function mo(n,a,s){return(a===!0||a===n)&&(s===null||s===n)}function q2(n,a=10){let s=null;return Math.abs(n.y)>a?s="y":Math.abs(n.x)>a&&(s="x"),s}class X2 extends _a{constructor(a){super(a),this.removeGroupControls=rn,this.removeListeners=rn,this.controls=new Y2(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||rn}unmount(){this.removeGroupControls(),this.removeListeners()}}const rv=n=>(a,s)=>{n&&Ft.postRender(()=>n(a,s))};class K2 extends _a{constructor(){super(...arguments),this.removePointerDownListener=rn}onPointerDown(a){this.session=new _b(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:jb(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:l,onPanEnd:c}=this.node.getProps();return{onSessionStart:rv(a),onStart:rv(s),onMove:l,onEnd:(d,u)=>{delete this.session,c&&Ft.postRender(()=>c(d,u))}}}mount(){this.removePointerDownListener=Cr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function lv(n,a){return a.max===a.min?0:n/(a.max-a.min)*100}const Sr={correct:(n,a)=>{if(!a.target)return n;if(typeof n=="string")if(yt.test(n))n=parseFloat(n);else return n;const s=lv(n,a.target.x),l=lv(n,a.target.y);return`${s}% ${l}%`}},Z2={correct:(n,{treeScale:a,projectionDelta:s})=>{const l=n,c=Da.parse(n);if(c.length>5)return l;const d=Da.createTransformer(n),u=typeof c[0]!="number"?1:0,h=s.x.scale*a.x,m=s.y.scale*a.y;c[0+u]/=h,c[1+u]/=m;const p=Qt(h,m,.5);return typeof c[2+u]=="number"&&(c[2+u]/=p),typeof c[3+u]=="number"&&(c[3+u]/=p),d(c)}};class Q2 extends w.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l,layoutId:c}=this.props,{projection:d}=a;jE(F2),d&&(s.group&&s.group.add(d),l&&l.register&&c&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions(Z(N({},d.options),{onExitComplete:()=>this.safeToRemove()}))),Ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:l,drag:c,isPresent:d}=this.props,{projection:u}=l;return u&&(u.isPresent=d,c||a.layoutDependency!==s||s===void 0||a.isPresent!==d?u.willUpdate():this.safeToRemove(),a.isPresent!==d&&(d?u.promote():u.relegate()||Ft.postRender(()=>{const h=u.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),Kd.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l}=this.props,{projection:c}=a;c&&(c.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(c),l&&l.deregister&&l.deregister(c))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function zb(n){const[a,s]=pE(),l=w.useContext(y0);return S.jsx(Q2,Z(N({},n),{layoutGroup:l,switchLayoutGroup:w.useContext(hb),isPresent:a,safeToRemove:s}))}const F2={borderRadius:Z(N({},Sr),{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Sr,borderTopRightRadius:Sr,borderBottomLeftRadius:Sr,borderBottomRightRadius:Sr,boxShadow:Z2};function I2(n,a,s){const l=Te(n)?n:fs(n);return l.start(nh("",l,a,s)),l.animation}const $2=(n,a)=>n.depth-a.depth;class W2{constructor(){this.children=[],this.isDirty=!1}add(a){Cd(this.children,a),this.isDirty=!0}remove(a){Rd(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort($2),this.isDirty=!1,this.children.forEach(a)}}function J2(n,a){const s=_e.now(),l=({timestamp:c})=>{const d=c-s;d>=a&&(Ra(l),n(d-a))};return Ft.setup(l,!0),()=>Ra(l)}const Lb=["TopLeft","TopRight","BottomLeft","BottomRight"],tM=Lb.length,ov=n=>typeof n=="string"?parseFloat(n):n,cv=n=>typeof n=="number"||yt.test(n);function eM(n,a,s,l,c,d){var u,h,m,p;c?(n.opacity=Qt(0,(u=s.opacity)!=null?u:1,nM(l)),n.opacityExit=Qt((h=a.opacity)!=null?h:1,0,aM(l))):d&&(n.opacity=Qt((m=a.opacity)!=null?m:1,(p=s.opacity)!=null?p:1,l));for(let g=0;g<tM;g++){const v=`border${Lb[g]}Radius`;let b=uv(a,v),T=uv(s,v);if(b===void 0&&T===void 0)continue;b||(b=0),T||(T=0),b===0||T===0||cv(b)===cv(T)?(n[v]=Math.max(Qt(ov(b),ov(T),l),0),(Dn.test(T)||Dn.test(b))&&(n[v]+="%")):n[v]=T}(a.rotate||s.rotate)&&(n.rotate=Qt(a.rotate||0,s.rotate||0,l))}function uv(n,a){return n[a]!==void 0?n[a]:n.borderRadius}const nM=Bb(0,.5,R0),aM=Bb(.5,.95,rn);function Bb(n,a,s){return l=>l<n?0:l>a?1:s(Or(n,a,l))}function fv(n,a){n.min=a.min,n.max=a.max}function tn(n,a){fv(n.x,a.x),fv(n.y,a.y)}function dv(n,a){n.translate=a.translate,n.scale=a.scale,n.originPoint=a.originPoint,n.origin=a.origin}function hv(n,a,s,l,c){return n-=a,n=Lo(n,1/s,l),c!==void 0&&(n=Lo(n,1/c,l)),n}function iM(n,a=0,s=1,l=.5,c,d=n,u=n){if(Dn.test(a)&&(a=parseFloat(a),a=Qt(u.min,u.max,a/100)-u.min),typeof a!="number")return;let h=Qt(d.min,d.max,l);n===d&&(h-=a),n.min=hv(n.min,a,s,h,c),n.max=hv(n.max,a,s,h,c)}function mv(n,a,[s,l,c],d,u){iM(n,a[s],a[l],a[c],a.scale,d,u)}const sM=["x","scaleX","originX"],rM=["y","scaleY","originY"];function pv(n,a,s,l){mv(n.x,a,sM,s?s.x:void 0,l?l.x:void 0),mv(n.y,a,rM,s?s.y:void 0,l?l.y:void 0)}function gv(n){return n.translate===0&&n.scale===1}function Ub(n){return gv(n.x)&&gv(n.y)}function yv(n,a){return n.min===a.min&&n.max===a.max}function lM(n,a){return yv(n.x,a.x)&&yv(n.y,a.y)}function vv(n,a){return Math.round(n.min)===Math.round(a.min)&&Math.round(n.max)===Math.round(a.max)}function kb(n,a){return vv(n.x,a.x)&&vv(n.y,a.y)}function bv(n){return Me(n.x)/Me(n.y)}function xv(n,a){return n.translate===a.translate&&n.scale===a.scale&&n.originPoint===a.originPoint}class oM{constructor(){this.members=[]}add(a){Cd(this.members,a),a.scheduleRender()}remove(a){if(Rd(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(c=>a===c);if(s===0)return!1;let l;for(let c=s;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(a,s){const l=this.lead;if(a!==l&&(this.prevLead=l,this.lead=a,a.show(),l)){l.instance&&l.scheduleRender(),a.scheduleRender(),a.resumeFrom=l,s&&(a.resumeFrom.preserveOpacity=!0),l.snapshot&&(a.snapshot=l.snapshot,a.snapshot.latestValues=l.animationValues||l.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:c}=a.options;c===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:l}=a;s.onExitComplete&&s.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function cM(n,a,s){let l="";const c=n.x.translate/a.x,d=n.y.translate/a.y,u=(s==null?void 0:s.z)||0;if((c||d||u)&&(l=`translate3d(${c}px, ${d}px, ${u}px) `),(a.x!==1||a.y!==1)&&(l+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:p,rotate:g,rotateX:v,rotateY:b,skewX:T,skewY:D}=s;p&&(l=`perspective(${p}px) ${l}`),g&&(l+=`rotate(${g}deg) `),v&&(l+=`rotateX(${v}deg) `),b&&(l+=`rotateY(${b}deg) `),T&&(l+=`skewX(${T}deg) `),D&&(l+=`skewY(${D}deg) `)}const h=n.x.scale*a.x,m=n.y.scale*a.y;return(h!==1||m!==1)&&(l+=`scale(${h}, ${m})`),l||"none"}const Cf=["","X","Y","Z"],uM={visibility:"hidden"},fM=1e3;let dM=0;function Rf(n,a,s,l){const{latestValues:c}=a;c[n]&&(s[n]=c[n],a.setStaticValue(n,0),l&&(l[n]=0))}function Hb(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:a}=n.options;if(!a)return;const s=Tb(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:c,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Ft,!(c||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&Hb(l)}function Pb({attachResizeListener:n,defaultParent:a,measureScroll:s,checkIsScrollRoot:l,resetTransform:c}){return class{constructor(u={},h=a==null?void 0:a()){this.id=dM++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(pM),this.nodes.forEach(xM),this.nodes.forEach(SM),this.nodes.forEach(gM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=u,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new W2)}addEventListener(u,h){return this.eventHandlers.has(u)||this.eventHandlers.set(u,new Nd),this.eventHandlers.get(u).add(h)}notifyListeners(u,...h){const m=this.eventHandlers.get(u);m&&m.notify(...h)}hasListeners(u){return this.eventHandlers.has(u)}mount(u){if(this.instance)return;this.isSVG=lb(u)&&!dE(u),this.instance=u;const{layoutId:h,layout:m,visualElement:p}=this.options;if(p&&!p.current&&p.mount(u),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||h)&&(this.isLayoutDirty=!0),n){let g;const v=()=>this.root.updateBlockedByResize=!1;n(u,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=J2(v,250),Ro.hasAnimatedSinceResize&&(Ro.hasAnimatedSinceResize=!1,this.nodes.forEach(Tv))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&p&&(h||m)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:v,hasRelativeLayoutChanged:b,layout:T})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const D=this.options.transition||p.getDefaultTransition()||MM,{onLayoutAnimationStart:A,onLayoutAnimationComplete:E}=p.getProps(),_=!this.targetLayout||!kb(this.targetLayout,T),z=!v&&b;if(this.options.layoutRoot||this.resumeFrom||z||v&&(_||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const j=Z(N({},qd(D,"layout")),{onPlay:A,onComplete:E});(p.shouldReduceMotion||this.options.layoutRoot)&&(j.delay=0,j.type=!1),this.startAnimation(j),this.setAnimationOrigin(g,z)}else v||Tv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=T})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const u=this.getStack();u&&u.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ra(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(TM),this.animationId++)}getTransformTemplate(){const{visualElement:u}=this.options;return u&&u.getProps().transformTemplate}willUpdate(u=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Hb(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const v=this.path[g];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:m}=this.options;if(h===void 0&&!m)return;const p=this.getTransformTemplate();this.prevTransformTemplateValue=p?p(this.latestValues,""):void 0,this.updateSnapshot(),u&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Sv);return}this.isUpdating||this.nodes.forEach(vM),this.isUpdating=!1,this.nodes.forEach(bM),this.nodes.forEach(hM),this.nodes.forEach(mM),this.clearAllSnapshots();const h=_e.now();ye.delta=In(0,1e3/60,h-ye.timestamp),ye.timestamp=h,ye.isProcessing=!0,bf.update.process(ye),bf.preRender.process(ye),bf.render.process(ye),ye.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Kd.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yM),this.sharedNodes.forEach(wM)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ft.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ft.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Me(this.snapshot.measuredBox.x)&&!Me(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const u=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,u?u.layoutBox:void 0)}updateScroll(u="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===u&&(h=!1),h&&this.instance){const m=l(this.instance);this.scroll={animationId:this.root.animationId,phase:u,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!c)return;const u=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!Ub(this.projectionDelta),m=this.getTransformTemplate(),p=m?m(this.latestValues,""):void 0,g=p!==this.prevTransformTemplateValue;u&&this.instance&&(h||ii(this.latestValues)||g)&&(c(this.instance,p),this.shouldResetTransform=!1,this.scheduleRender())}measure(u=!0){const h=this.measurePageBox();let m=this.removeElementScroll(h);return u&&(m=this.removeTransform(m)),CM(m),{animationId:this.root.animationId,measuredBox:h,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var p;const{visualElement:u}=this.options;if(!u)return ee();const h=u.measureViewportBox();if(!(((p=this.scroll)==null?void 0:p.wasRoot)||this.path.some(RM))){const{scroll:g}=this.root;g&&(ls(h.x,g.offset.x),ls(h.y,g.offset.y))}return h}removeElementScroll(u){var m;const h=ee();if(tn(h,u),(m=this.scroll)!=null&&m.wasRoot)return h;for(let p=0;p<this.path.length;p++){const g=this.path[p],{scroll:v,options:b}=g;g!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&tn(h,u),ls(h.x,v.offset.x),ls(h.y,v.offset.y))}return h}applyTransform(u,h=!1){const m=ee();tn(m,u);for(let p=0;p<this.path.length;p++){const g=this.path[p];!h&&g.options.layoutScroll&&g.scroll&&g!==g.root&&os(m,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),ii(g.latestValues)&&os(m,g.latestValues)}return ii(this.latestValues)&&os(m,this.latestValues),m}removeTransform(u){const h=ee();tn(h,u);for(let m=0;m<this.path.length;m++){const p=this.path[m];if(!p.instance||!ii(p.latestValues))continue;sd(p.latestValues)&&p.updateSnapshot();const g=ee(),v=p.measurePageBox();tn(g,v),pv(h,p.latestValues,p.snapshot?p.snapshot.layoutBox:void 0,g)}return ii(this.latestValues)&&pv(h,this.latestValues),h}setTargetDelta(u){this.targetDelta=u,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(u){this.options=Z(N(N({},this.options),u),{crossfade:u.crossfade!==void 0?u.crossfade:!0})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ye.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(u=!1){var b;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==h;if(!(u||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:v}=this.options;if(!(!this.layout||!(g||v))){if(this.resolvedRelativeTargetAt=ye.timestamp,!this.targetDelta&&!this.relativeTarget){const T=this.getClosestProjectingParent();T&&T.layout&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.layout.layoutBox,T.layout.layoutBox),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),O2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tn(this.target,this.layout.layoutBox),Ob(this.target,this.targetDelta)):tn(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const T=this.getClosestProjectingParent();T&&!!T.resumingFrom==!!this.resumingFrom&&!T.options.layoutScroll&&T.target&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.target,T.target),tn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||sd(this.parent.latestValues)||Db(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var D;const u=this.getLead(),h=!!this.resumingFrom||this!==u;let m=!0;if((this.isProjectionDirty||(D=this.parent)!=null&&D.isProjectionDirty)&&(m=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===ye.timestamp&&(m=!1),m)return;const{layout:p,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(p||g))return;tn(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,b=this.treeScale.y;N2(this.layoutCorrected,this.treeScale,this.path,h),u.layout&&!u.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(u.target=u.layout.layoutBox,u.targetWithTransforms=ee());const{target:T}=u;if(!T){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(dv(this.prevProjectionDelta.x,this.projectionDelta.x),dv(this.prevProjectionDelta.y,this.projectionDelta.y)),Rr(this.projectionDelta,this.layoutCorrected,T,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==b||!xv(this.projectionDelta.x,this.prevProjectionDelta.x)||!xv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",T))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(u=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),u){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rs(),this.projectionDelta=rs(),this.projectionDeltaWithTransform=rs()}setAnimationOrigin(u,h=!1){const m=this.snapshot,p=m?m.latestValues:{},g=N({},this.latestValues),v=rs();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const b=ee(),T=m?m.source:void 0,D=this.layout?this.layout.source:void 0,A=T!==D,E=this.getStack(),_=!E||E.members.length<=1,z=!!(A&&!_&&this.options.crossfade===!0&&!this.path.some(EM));this.animationProgress=0;let j;this.mixTargetDelta=M=>{const C=M/1e3;wv(v.x,u.x,C),wv(v.y,u.y,C),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dr(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),AM(this.relativeTarget,this.relativeTargetOrigin,b,C),j&&lM(this.relativeTarget,j)&&(this.isProjectionDirty=!1),j||(j=ee()),tn(j,this.relativeTarget)),A&&(this.animationValues=g,eM(g,p,this.latestValues,C,z,_)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(u){var h,m,p;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(p=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||p.stop(),this.pendingAnimation&&(Ra(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ft.update(()=>{Ro.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=fs(0)),this.currentAnimation=I2(this.motionValue,[0,1e3],Z(N({},u),{isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),u.onUpdate&&u.onUpdate(g)},onStop:()=>{},onComplete:()=>{u.onComplete&&u.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const u=this.getStack();u&&u.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fM),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const u=this.getLead();let{targetWithTransforms:h,target:m,layout:p,latestValues:g}=u;if(!(!h||!m||!p)){if(this!==u&&this.layout&&p&&Gb(this.options.animationType,this.layout.layoutBox,p.layoutBox)){m=this.target||ee();const v=Me(this.layout.layoutBox.x);m.x.min=u.target.x.min,m.x.max=m.x.min+v;const b=Me(this.layout.layoutBox.y);m.y.min=u.target.y.min,m.y.max=m.y.min+b}tn(h,m),os(h,g),Rr(this.projectionDeltaWithTransform,this.layoutCorrected,h,g)}}registerSharedNode(u,h){this.sharedNodes.has(u)||this.sharedNodes.set(u,new oM),this.sharedNodes.get(u).add(h);const p=h.options.initialPromotionConfig;h.promote({transition:p?p.transition:void 0,preserveFollowOpacity:p&&p.shouldPreserveFollowOpacity?p.shouldPreserveFollowOpacity(h):void 0})}isLead(){const u=this.getStack();return u?u.lead===this:!0}getLead(){var h;const{layoutId:u}=this.options;return u?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:u}=this.options;return u?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:u}=this.options;if(u)return this.root.sharedNodes.get(u)}promote({needsReset:u,transition:h,preserveFollowOpacity:m}={}){const p=this.getStack();p&&p.promote(this,m),u&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const u=this.getStack();return u?u.relegate(this):!1}resetSkewAndRotation(){const{visualElement:u}=this.options;if(!u)return;let h=!1;const{latestValues:m}=u;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(h=!0),!h)return;const p={};m.z&&Rf("z",u,p,this.animationValues);for(let g=0;g<Cf.length;g++)Rf(`rotate${Cf[g]}`,u,p,this.animationValues),Rf(`skew${Cf[g]}`,u,p,this.animationValues);u.render();for(const g in p)u.setStaticValue(g,p[g]),this.animationValues&&(this.animationValues[g]=p[g]);u.scheduleRender()}getProjectionStyles(u){var T,D;if(!this.instance||this.isSVG)return;if(!this.isVisible)return uM;const h={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=Co(u==null?void 0:u.pointerEvents)||"",h.transform=m?m(this.latestValues,""):"none",h;const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){const A={};return this.options.layoutId&&(A.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,A.pointerEvents=Co(u==null?void 0:u.pointerEvents)||""),this.hasProjected&&!ii(this.latestValues)&&(A.transform=m?m({},""):"none",this.hasProjected=!1),A}const g=p.animationValues||p.latestValues;this.applyTransformsToTarget(),h.transform=cM(this.projectionDeltaWithTransform,this.treeScale,g),m&&(h.transform=m(g,h.transform));const{x:v,y:b}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${b.origin*100}% 0`,p.animationValues?h.opacity=p===this?(D=(T=g.opacity)!=null?T:this.latestValues.opacity)!=null?D:1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:h.opacity=p===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const A in Vr){if(g[A]===void 0)continue;const{correct:E,applyTo:_,isCSSVariable:z}=Vr[A],j=h.transform==="none"?g[A]:E(g[A],p);if(_){const M=_.length;for(let C=0;C<M;C++)h[_[C]]=j}else z?this.options.visualElement.renderState.vars[A]=j:h[A]=j}return this.options.layoutId&&(h.pointerEvents=p===this?Co(u==null?void 0:u.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(u=>{var h;return(h=u.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(Sv),this.root.sharedNodes.clear()}}}function hM(n){n.updateLayout()}function mM(n){var s;const a=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&a&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:c}=n.layout,{animationType:d}=n.options,u=a.source!==n.layout.source;d==="size"?sn(v=>{const b=u?a.measuredBox[v]:a.layoutBox[v],T=Me(b);b.min=l[v].min,b.max=b.min+T}):Gb(d,a.layoutBox,l)&&sn(v=>{const b=u?a.measuredBox[v]:a.layoutBox[v],T=Me(l[v]);b.max=b.min+T,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+T)});const h=rs();Rr(h,l,a.layoutBox);const m=rs();u?Rr(m,n.applyTransform(c,!0),a.measuredBox):Rr(m,l,a.layoutBox);const p=!Ub(h);let g=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:T}=v;if(b&&T){const D=ee();Dr(D,a.layoutBox,b.layoutBox);const A=ee();Dr(A,l,T.layoutBox),kb(D,A)||(g=!0),v.options.layoutRoot&&(n.relativeTarget=A,n.relativeTargetOrigin=D,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:a,delta:m,layoutDelta:h,hasLayoutChanged:p,hasRelativeLayoutChanged:g})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function pM(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function gM(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function yM(n){n.clearSnapshot()}function Sv(n){n.clearMeasurements()}function vM(n){n.isLayoutDirty=!1}function bM(n){const{visualElement:a}=n.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),n.resetTransform()}function Tv(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function xM(n){n.resolveTargetDelta()}function SM(n){n.calcProjection()}function TM(n){n.resetSkewAndRotation()}function wM(n){n.removeLeadSnapshot()}function wv(n,a,s){n.translate=Qt(a.translate,0,s),n.scale=Qt(a.scale,1,s),n.origin=a.origin,n.originPoint=a.originPoint}function Av(n,a,s,l){n.min=Qt(a.min,s.min,l),n.max=Qt(a.max,s.max,l)}function AM(n,a,s,l){Av(n.x,a.x,s.x,l),Av(n.y,a.y,s.y,l)}function EM(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const MM={duration:.45,ease:[.4,0,.1,1]},Ev=n=>typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Mv=Ev("applewebkit/")&&!Ev("chrome/")?Math.round:rn;function Cv(n){n.min=Mv(n.min),n.max=Mv(n.max)}function CM(n){Cv(n.x),Cv(n.y)}function Gb(n,a,s){return n==="position"||n==="preserve-aspect"&&!D2(bv(a),bv(s),.2)}function RM(n){var a;return n!==n.root&&((a=n.scroll)==null?void 0:a.wasRoot)}const DM=Pb({attachResizeListener:(n,a)=>Lr(n,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Df={current:void 0},Yb=Pb({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Df.current){const n=new DM({});n.mount(window),n.setOptions({layoutScroll:!0}),Df.current=n}return Df.current},resetTransform:(n,a)=>{n.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),OM={pan:{Feature:K2},drag:{Feature:X2,ProjectionNode:Yb,MeasureLayout:zb}};function Rv(n,a,s){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",s==="Start");const c="onHover"+s,d=l[c];d&&Ft.postRender(()=>d(a,Yr(a)))}class NM extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=lE(a,(s,l)=>(Rv(this.node,l,"Start"),c=>Rv(this.node,c,"End"))))}unmount(){}}class jM extends _a{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(s){a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Hr(Lr(this.node.current,"focus",()=>this.onFocus()),Lr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Dv(n,a,s){const{props:l}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",s==="Start");const c="onTap"+(s==="End"?"":s),d=l[c];d&&Ft.postRender(()=>d(a,Yr(a)))}class _M extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=fE(a,(s,l)=>(Dv(this.node,l,"Start"),(c,{success:d})=>Dv(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const od=new WeakMap,Of=new WeakMap,VM=n=>{const a=od.get(n.target);a&&a(n)},zM=n=>{n.forEach(VM)};function LM(s){var l=s,{root:n}=l,a=et(l,["root"]);const c=n||document;Of.has(c)||Of.set(c,{});const d=Of.get(c),u=JSON.stringify(a);return d[u]||(d[u]=new IntersectionObserver(zM,N({root:n},a))),d[u]}function BM(n,a,s){const l=LM(a);return od.set(n,s),l.observe(n),()=>{od.delete(n),l.unobserve(n)}}const UM={some:0,all:1};class kM extends _a{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:l,amount:c="some",once:d}=a,u={root:s?s.current:void 0,rootMargin:l,threshold:typeof c=="number"?c:UM[c]},h=m=>{const{isIntersecting:p}=m;if(this.isInView===p||(this.isInView=p,d&&!p&&this.hasEnteredView))return;p&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",p);const{onViewportEnter:g,onViewportLeave:v}=this.node.getProps(),b=p?g:v;b&&b(m)};return BM(this.node.current,u,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver=="undefined")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(HM(a,s))&&this.startObserver()}unmount(){}}function HM({viewport:n={}},{viewport:a={}}={}){return s=>n[s]!==a[s]}const PM={inView:{Feature:kM},tap:{Feature:_M},focus:{Feature:jM},hover:{Feature:NM}},GM={layout:{ProjectionNode:Yb,MeasureLayout:zb}},cd={current:null},qb={current:!1};function YM(){if(qb.current=!0,!!Ed)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),a=()=>cd.current=n.matches;n.addListener(a),a()}else cd.current=!1}const qM=new WeakMap;function XM(n,a,s){for(const l in a){const c=a[l],d=s[l];if(Te(c))n.addValue(l,c);else if(Te(d))n.addValue(l,fs(c,{owner:n}));else if(d!==c)if(n.hasValue(l)){const u=n.getValue(l);u.liveStyle===!0?u.jump(c):u.hasAnimated||u.set(c)}else{const u=n.getStaticValue(l);n.addValue(l,fs(u!==void 0?u:c,{owner:n}))}}for(const l in s)a[l]===void 0&&n.removeValue(l);return a}const Ov=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class KM{scrapeMotionValuesFromProps(a,s,l){return{}}constructor({parent:a,props:s,presenceContext:l,reducedMotionConfig:c,blockInitialAnimation:d,visualState:u},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Yd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const T=_e.now();this.renderScheduledAt<T&&(this.renderScheduledAt=T,Ft.render(this.render,!1,!0))};const{latestValues:m,renderState:p}=u;this.latestValues=m,this.baseTarget=N({},m),this.initialValues=s.initial?N({},m):{},this.renderState=p,this.parent=a,this.props=s,this.presenceContext=l,this.depth=a?a.depth+1:0,this.reducedMotionConfig=c,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=Xo(s),this.isVariantNode=fb(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const b=this.scrapeMotionValuesFromProps(s,{},this),{willChange:g}=b,v=et(b,["willChange"]);for(const T in v){const D=v[T];m[T]!==void 0&&Te(D)&&D.set(m[T],!1)}}mount(a){this.current=a,qM.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,l)=>this.bindToMotionValue(l,s)),qb.current||YM(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:cd.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ra(this.notifyUpdate),Ra(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const l=gs.has(a);l&&this.onBindTransform&&this.onBindTransform();const c=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Ft.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let u;window.MotionCheckAppearSync&&(u=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{c(),d(),u&&u(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in ds){const s=ds[a];if(!s)continue;const{isEnabled:l,Feature:c}=s;if(!this.features[a]&&c&&l(this.props)&&(this.features[a]=new c(this)),this.features[a]){const d=this.features[a];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let l=0;l<Ov.length;l++){const c=Ov[l];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,u=a[d];u&&(this.propEventSubscriptions[c]=this.on(c,u))}this.prevMotionValues=XM(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const l=this.values.get(a);s!==l&&(l&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let l=this.values.get(a);return l===void 0&&s!==void 0&&(l=fs(s===null?void 0:s,{owner:this}),this.addValue(a,l)),l}readValue(a,s){var c;let l=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:(c=this.getBaseTargetFromProps(this.props,a))!=null?c:this.readValueFromInstance(this.current,a,this.options);return l!=null&&(typeof l=="string"&&(v0(l)||x0(l))?l=parseFloat(l):!mE(l)&&Da.test(s)&&(l=nb(a,s)),this.setBaseTarget(a,Te(l)?l.get():l)),Te(l)?l.get():l}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var d;const{initial:s}=this.props;let l;if(typeof s=="string"||typeof s=="object"){const u=th(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);u&&(l=u[a])}if(s&&l!==void 0)return l;const c=this.getBaseTargetFromProps(this.props,a);return c!==void 0&&!Te(c)?c:this.initialValues[a]!==void 0&&l===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new Nd),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class Xb extends KM{constructor(){super(...arguments),this.KeyframeResolver=nE}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:l}){delete s[a],delete l[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;Te(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Kb(n,{style:a,vars:s},l,c){Object.assign(n.style,a,c&&c.getProjectionStyles(l));for(const d in s)n.style.setProperty(d,s[d])}function ZM(n){return window.getComputedStyle(n)}class QM extends Xb{constructor(){super(...arguments),this.type="html",this.renderInstance=Kb}readValueFromInstance(a,s){var l;if(gs.has(s))return(l=this.projection)!=null&&l.isProjecting?If(s):SA(a,s);{const c=ZM(a),d=(Vd(s)?c.getPropertyValue(s):c[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:s}){return Nb(a,s)}build(a,s,l){$d(a,s,l.transformTemplate)}scrapeMotionValuesFromProps(a,s,l){return eh(a,s,l)}}const Zb=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function FM(n,a,s,l){Kb(n,a,void 0,l);for(const c in a.attrs)n.setAttribute(Zb.has(c)?c:Id(c),a.attrs[c])}class IM extends Xb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ee}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(gs.has(s)){const l=eb(s);return l&&l.default||0}return s=Zb.has(s)?s:Id(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,l){return Sb(a,s,l)}build(a,s,l){yb(a,s,this.isSVGTag,l.transformTemplate,l.style)}renderInstance(a,s,l,c){FM(a,s,l,c)}mount(a){this.isSVGTag=bb(a.tagName),super.mount(a)}}const $M=(n,a)=>Jd(n)?new IM(a):new QM(a,{allowProjection:n!==w.Fragment}),WM=FE(N(N(N(N({},S2),PM),OM),GM),$M),Sn=xE(WM);function Nv(n,a){if(typeof n=="function")return n(a);n!=null&&(n.current=a)}function JM(...n){return a=>{let s=!1;const l=n.map(c=>{const d=Nv(c,a);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let c=0;c<l.length;c++){const d=l[c];typeof d=="function"?d():Nv(n[c],null)}}}}function ie(...n){return w.useCallback(JM(...n),n)}function Br(n){const a=tC(n),s=w.forwardRef((l,c)=>{const p=l,{children:d}=p,u=et(p,["children"]),h=w.Children.toArray(d),m=h.find(nC);if(m){const g=m.props.children,v=h.map(b=>b===m?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:b);return S.jsx(a,Z(N({},u),{ref:c,children:w.isValidElement(g)?w.cloneElement(g,void 0,v):null}))}return S.jsx(a,Z(N({},u),{ref:c,children:d}))});return s.displayName=`${n}.Slot`,s}var Qb=Br("Slot");function tC(n){const a=w.forwardRef((s,l)=>{const m=s,{children:c}=m,d=et(m,["children"]),u=w.isValidElement(c)?iC(c):void 0,h=ie(u,l);if(w.isValidElement(c)){const p=aC(d,c.props);return c.type!==w.Fragment&&(p.ref=h),w.cloneElement(c,p)}return w.Children.count(c)>1?w.Children.only(null):null});return a.displayName=`${n}.SlotClone`,a}var eC=Symbol("radix.slottable");function nC(n){return w.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===eC}function aC(n,a){const s=N({},a);for(const l in a){const c=n[l],d=a[l];/^on[A-Z]/.test(l)?c&&d?s[l]=(...h)=>{const m=d(...h);return c(...h),m}:c&&(s[l]=c):l==="style"?s[l]=N(N({},c),d):l==="className"&&(s[l]=[c,d].filter(Boolean).join(" "))}return N(N({},n),s)}function iC(n){var l,c;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Fb(n){var a,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(a=0;a<c;a++)n[a]&&(s=Fb(n[a]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function Ib(){for(var n,a,s=0,l="",c=arguments.length;s<c;s++)(n=arguments[s])&&(a=Fb(n))&&(l&&(l+=" "),l+=a);return l}const jv=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,_v=Ib,$b=(n,a)=>s=>{var l;if((a==null?void 0:a.variants)==null)return _v(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:c,defaultVariants:d}=a,u=Object.keys(c).map(p=>{const g=s==null?void 0:s[p],v=d==null?void 0:d[p];if(g===null)return null;const b=jv(g)||jv(v);return c[p][b]}),h=s&&Object.entries(s).reduce((p,g)=>{let[v,b]=g;return b===void 0||(p[v]=b),p},{}),m=a==null||(l=a.compoundVariants)===null||l===void 0?void 0:l.reduce((p,g)=>{let D=g,{class:v,className:b}=D,T=et(D,["class","className"]);return Object.entries(T).every(A=>{let[E,_]=A;return Array.isArray(_)?_.includes(N(N({},d),h)[E]):N(N({},d),h)[E]===_})?[...p,v,b]:p},[]);return _v(n,u,m,s==null?void 0:s.class,s==null?void 0:s.className)},ah="-",sC=n=>{const a=lC(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:u=>{const h=u.split(ah);return h[0]===""&&h.length!==1&&h.shift(),Wb(h,a)||rC(u)},getConflictingClassGroupIds:(u,h)=>{const m=s[u]||[];return h&&l[u]?[...m,...l[u]]:m}}},Wb=(n,a)=>{var u;if(n.length===0)return a.classGroupId;const s=n[0],l=a.nextPart.get(s),c=l?Wb(n.slice(1),l):void 0;if(c)return c;if(a.validators.length===0)return;const d=n.join(ah);return(u=a.validators.find(({validator:h})=>h(d)))==null?void 0:u.classGroupId},Vv=/^\[(.+)\]$/,rC=n=>{if(Vv.test(n)){const a=Vv.exec(n)[1],s=a==null?void 0:a.substring(0,a.indexOf(":"));if(s)return"arbitrary.."+s}},lC=n=>{const{theme:a,classGroups:s}=n,l={nextPart:new Map,validators:[]};for(const c in s)ud(s[c],l,c,a);return l},ud=(n,a,s,l)=>{n.forEach(c=>{if(typeof c=="string"){const d=c===""?a:zv(a,c);d.classGroupId=s;return}if(typeof c=="function"){if(oC(c)){ud(c(l),a,s,l);return}a.validators.push({validator:c,classGroupId:s});return}Object.entries(c).forEach(([d,u])=>{ud(u,zv(a,d),s,l)})})},zv=(n,a)=>{let s=n;return a.split(ah).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},oC=n=>n.isThemeGetter,cC=n=>{if(n<1)return{get:()=>{},set:()=>{}};let a=0,s=new Map,l=new Map;const c=(d,u)=>{s.set(d,u),a++,a>n&&(a=0,l=s,s=new Map)};return{get(d){let u=s.get(d);if(u!==void 0)return u;if((u=l.get(d))!==void 0)return c(d,u),u},set(d,u){s.has(d)?s.set(d,u):c(d,u)}}},fd="!",dd=":",uC=dd.length,fC=n=>{const{prefix:a,experimentalParseClassName:s}=n;let l=c=>{const d=[];let u=0,h=0,m=0,p;for(let D=0;D<c.length;D++){let A=c[D];if(u===0&&h===0){if(A===dd){d.push(c.slice(m,D)),m=D+uC;continue}if(A==="/"){p=D;continue}}A==="["?u++:A==="]"?u--:A==="("?h++:A===")"&&h--}const g=d.length===0?c:c.substring(m),v=dC(g),b=v!==g,T=p&&p>m?p-m:void 0;return{modifiers:d,hasImportantModifier:b,baseClassName:v,maybePostfixModifierPosition:T}};if(a){const c=a+dd,d=l;l=u=>u.startsWith(c)?d(u.substring(c.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:u,maybePostfixModifierPosition:void 0}}if(s){const c=l;l=d=>s({className:d,parseClassName:c})}return l},dC=n=>n.endsWith(fd)?n.substring(0,n.length-1):n.startsWith(fd)?n.substring(1):n,hC=n=>{const a=Object.fromEntries(n.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;const c=[];let d=[];return l.forEach(u=>{u[0]==="["||a[u]?(c.push(...d.sort(),u),d=[]):d.push(u)}),c.push(...d.sort()),c}},mC=n=>N({cache:cC(n.cacheSize),parseClassName:fC(n),sortModifiers:hC(n)},sC(n)),pC=/\s+/,gC=(n,a)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:c,sortModifiers:d}=a,u=[],h=n.trim().split(pC);let m="";for(let p=h.length-1;p>=0;p-=1){const g=h[p],{isExternal:v,modifiers:b,hasImportantModifier:T,baseClassName:D,maybePostfixModifierPosition:A}=s(g);if(v){m=g+(m.length>0?" "+m:m);continue}let E=!!A,_=l(E?D.substring(0,A):D);if(!_){if(!E){m=g+(m.length>0?" "+m:m);continue}if(_=l(D),!_){m=g+(m.length>0?" "+m:m);continue}E=!1}const z=d(b).join(":"),j=T?z+fd:z,M=j+_;if(u.includes(M))continue;u.push(M);const C=c(_,E);for(let X=0;X<C.length;++X){const W=C[X];u.push(j+W)}m=g+(m.length>0?" "+m:m)}return m};function yC(){let n=0,a,s,l="";for(;n<arguments.length;)(a=arguments[n++])&&(s=Jb(a))&&(l&&(l+=" "),l+=s);return l}const Jb=n=>{if(typeof n=="string")return n;let a,s="";for(let l=0;l<n.length;l++)n[l]&&(a=Jb(n[l]))&&(s&&(s+=" "),s+=a);return s};function vC(n,...a){let s,l,c,d=u;function u(m){const p=a.reduce((g,v)=>v(g),n());return s=mC(p),l=s.cache.get,c=s.cache.set,d=h,h(m)}function h(m){const p=l(m);if(p)return p;const g=gC(m,s);return c(m,g),g}return function(){return d(yC.apply(null,arguments))}}const le=n=>{const a=s=>s[n]||[];return a.isThemeGetter=!0,a},tx=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ex=/^\((?:(\w[\w-]*):)?(.+)\)$/i,bC=/^\d+\/\d+$/,xC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,SC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,TC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,wC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,AC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ji=n=>bC.test(n),At=n=>!!n&&!Number.isNaN(Number(n)),Aa=n=>!!n&&Number.isInteger(Number(n)),Nf=n=>n.endsWith("%")&&At(n.slice(0,-1)),Qn=n=>xC.test(n),EC=()=>!0,MC=n=>SC.test(n)&&!TC.test(n),nx=()=>!1,CC=n=>wC.test(n),RC=n=>AC.test(n),DC=n=>!st(n)&&!rt(n),OC=n=>ys(n,sx,nx),st=n=>tx.test(n),ni=n=>ys(n,rx,MC),jf=n=>ys(n,zC,At),Lv=n=>ys(n,ax,nx),NC=n=>ys(n,ix,RC),po=n=>ys(n,lx,CC),rt=n=>ex.test(n),Tr=n=>vs(n,rx),jC=n=>vs(n,LC),Bv=n=>vs(n,ax),_C=n=>vs(n,sx),VC=n=>vs(n,ix),go=n=>vs(n,lx,!0),ys=(n,a,s)=>{const l=tx.exec(n);return l?l[1]?a(l[1]):s(l[2]):!1},vs=(n,a,s=!1)=>{const l=ex.exec(n);return l?l[1]?a(l[1]):s:!1},ax=n=>n==="position"||n==="percentage",ix=n=>n==="image"||n==="url",sx=n=>n==="length"||n==="size"||n==="bg-size",rx=n=>n==="length",zC=n=>n==="number",LC=n=>n==="family-name",lx=n=>n==="shadow",BC=()=>{const n=le("color"),a=le("font"),s=le("text"),l=le("font-weight"),c=le("tracking"),d=le("leading"),u=le("breakpoint"),h=le("container"),m=le("spacing"),p=le("radius"),g=le("shadow"),v=le("inset-shadow"),b=le("text-shadow"),T=le("drop-shadow"),D=le("blur"),A=le("perspective"),E=le("aspect"),_=le("ease"),z=le("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...M(),rt,st],X=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto","contain","none"],H=()=>[rt,st,m],$=()=>[Ji,"full","auto",...H()],ct=()=>[Aa,"none","subgrid",rt,st],ht=()=>["auto",{span:["full",Aa,rt,st]},Aa,rt,st],lt=()=>[Aa,"auto",rt,st],bt=()=>["auto","min","max","fr",rt,st],Et=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ut=()=>["start","end","center","stretch","center-safe","end-safe"],B=()=>["auto",...H()],q=()=>[Ji,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...H()],G=()=>[n,rt,st],ft=()=>[...M(),Bv,Lv,{position:[rt,st]}],O=()=>["no-repeat",{repeat:["","x","y","space","round"]}],K=()=>["auto","cover","contain",_C,OC,{size:[rt,st]}],tt=()=>[Nf,Tr,ni],J=()=>["","none","full",p,rt,st],at=()=>["",At,Tr,ni],vt=()=>["solid","dashed","dotted","double"],dt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],xt=()=>[At,Nf,Bv,Lv],nt=()=>["","none",D,rt,st],Tt=()=>["none",At,rt,st],qt=()=>["none",At,rt,st],Ot=()=>[At,rt,st],Ct=()=>[Ji,"full",...H()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qn],breakpoint:[Qn],color:[EC],container:[Qn],"drop-shadow":[Qn],ease:["in","out","in-out"],font:[DC],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qn],shadow:[Qn],spacing:["px",At],text:[Qn],"text-shadow":[Qn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ji,st,rt,E]}],container:["container"],columns:[{columns:[At,st,rt,h]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:X()}],"overflow-x":[{"overflow-x":X()}],"overflow-y":[{"overflow-y":X()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:$()}],"inset-x":[{"inset-x":$()}],"inset-y":[{"inset-y":$()}],start:[{start:$()}],end:[{end:$()}],top:[{top:$()}],right:[{right:$()}],bottom:[{bottom:$()}],left:[{left:$()}],visibility:["visible","invisible","collapse"],z:[{z:[Aa,"auto",rt,st]}],basis:[{basis:[Ji,"full","auto",h,...H()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[At,Ji,"auto","initial","none",st]}],grow:[{grow:["",At,rt,st]}],shrink:[{shrink:["",At,rt,st]}],order:[{order:[Aa,"first","last","none",rt,st]}],"grid-cols":[{"grid-cols":ct()}],"col-start-end":[{col:ht()}],"col-start":[{"col-start":lt()}],"col-end":[{"col-end":lt()}],"grid-rows":[{"grid-rows":ct()}],"row-start-end":[{row:ht()}],"row-start":[{"row-start":lt()}],"row-end":[{"row-end":lt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":bt()}],"auto-rows":[{"auto-rows":bt()}],gap:[{gap:H()}],"gap-x":[{"gap-x":H()}],"gap-y":[{"gap-y":H()}],"justify-content":[{justify:[...Et(),"normal"]}],"justify-items":[{"justify-items":[...ut(),"normal"]}],"justify-self":[{"justify-self":["auto",...ut()]}],"align-content":[{content:["normal",...Et()]}],"align-items":[{items:[...ut(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ut(),{baseline:["","last"]}]}],"place-content":[{"place-content":Et()}],"place-items":[{"place-items":[...ut(),"baseline"]}],"place-self":[{"place-self":["auto",...ut()]}],p:[{p:H()}],px:[{px:H()}],py:[{py:H()}],ps:[{ps:H()}],pe:[{pe:H()}],pt:[{pt:H()}],pr:[{pr:H()}],pb:[{pb:H()}],pl:[{pl:H()}],m:[{m:B()}],mx:[{mx:B()}],my:[{my:B()}],ms:[{ms:B()}],me:[{me:B()}],mt:[{mt:B()}],mr:[{mr:B()}],mb:[{mb:B()}],ml:[{ml:B()}],"space-x":[{"space-x":H()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":H()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[h,"screen",...q()]}],"min-w":[{"min-w":[h,"screen","none",...q()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[u]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",s,Tr,ni]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,rt,jf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Nf,st]}],"font-family":[{font:[jC,st,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[c,rt,st]}],"line-clamp":[{"line-clamp":[At,"none",rt,jf]}],leading:[{leading:[d,...H()]}],"list-image":[{"list-image":["none",rt,st]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",rt,st]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:G()}],"text-color":[{text:G()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...vt(),"wavy"]}],"text-decoration-thickness":[{decoration:[At,"from-font","auto",rt,ni]}],"text-decoration-color":[{decoration:G()}],"underline-offset":[{"underline-offset":[At,"auto",rt,st]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:H()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",rt,st]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",rt,st]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ft()}],"bg-repeat":[{bg:O()}],"bg-size":[{bg:K()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Aa,rt,st],radial:["",rt,st],conic:[Aa,rt,st]},VC,NC]}],"bg-color":[{bg:G()}],"gradient-from-pos":[{from:tt()}],"gradient-via-pos":[{via:tt()}],"gradient-to-pos":[{to:tt()}],"gradient-from":[{from:G()}],"gradient-via":[{via:G()}],"gradient-to":[{to:G()}],rounded:[{rounded:J()}],"rounded-s":[{"rounded-s":J()}],"rounded-e":[{"rounded-e":J()}],"rounded-t":[{"rounded-t":J()}],"rounded-r":[{"rounded-r":J()}],"rounded-b":[{"rounded-b":J()}],"rounded-l":[{"rounded-l":J()}],"rounded-ss":[{"rounded-ss":J()}],"rounded-se":[{"rounded-se":J()}],"rounded-ee":[{"rounded-ee":J()}],"rounded-es":[{"rounded-es":J()}],"rounded-tl":[{"rounded-tl":J()}],"rounded-tr":[{"rounded-tr":J()}],"rounded-br":[{"rounded-br":J()}],"rounded-bl":[{"rounded-bl":J()}],"border-w":[{border:at()}],"border-w-x":[{"border-x":at()}],"border-w-y":[{"border-y":at()}],"border-w-s":[{"border-s":at()}],"border-w-e":[{"border-e":at()}],"border-w-t":[{"border-t":at()}],"border-w-r":[{"border-r":at()}],"border-w-b":[{"border-b":at()}],"border-w-l":[{"border-l":at()}],"divide-x":[{"divide-x":at()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":at()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...vt(),"hidden","none"]}],"divide-style":[{divide:[...vt(),"hidden","none"]}],"border-color":[{border:G()}],"border-color-x":[{"border-x":G()}],"border-color-y":[{"border-y":G()}],"border-color-s":[{"border-s":G()}],"border-color-e":[{"border-e":G()}],"border-color-t":[{"border-t":G()}],"border-color-r":[{"border-r":G()}],"border-color-b":[{"border-b":G()}],"border-color-l":[{"border-l":G()}],"divide-color":[{divide:G()}],"outline-style":[{outline:[...vt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[At,rt,st]}],"outline-w":[{outline:["",At,Tr,ni]}],"outline-color":[{outline:G()}],shadow:[{shadow:["","none",g,go,po]}],"shadow-color":[{shadow:G()}],"inset-shadow":[{"inset-shadow":["none",v,go,po]}],"inset-shadow-color":[{"inset-shadow":G()}],"ring-w":[{ring:at()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:G()}],"ring-offset-w":[{"ring-offset":[At,ni]}],"ring-offset-color":[{"ring-offset":G()}],"inset-ring-w":[{"inset-ring":at()}],"inset-ring-color":[{"inset-ring":G()}],"text-shadow":[{"text-shadow":["none",b,go,po]}],"text-shadow-color":[{"text-shadow":G()}],opacity:[{opacity:[At,rt,st]}],"mix-blend":[{"mix-blend":[...dt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":dt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[At]}],"mask-image-linear-from-pos":[{"mask-linear-from":xt()}],"mask-image-linear-to-pos":[{"mask-linear-to":xt()}],"mask-image-linear-from-color":[{"mask-linear-from":G()}],"mask-image-linear-to-color":[{"mask-linear-to":G()}],"mask-image-t-from-pos":[{"mask-t-from":xt()}],"mask-image-t-to-pos":[{"mask-t-to":xt()}],"mask-image-t-from-color":[{"mask-t-from":G()}],"mask-image-t-to-color":[{"mask-t-to":G()}],"mask-image-r-from-pos":[{"mask-r-from":xt()}],"mask-image-r-to-pos":[{"mask-r-to":xt()}],"mask-image-r-from-color":[{"mask-r-from":G()}],"mask-image-r-to-color":[{"mask-r-to":G()}],"mask-image-b-from-pos":[{"mask-b-from":xt()}],"mask-image-b-to-pos":[{"mask-b-to":xt()}],"mask-image-b-from-color":[{"mask-b-from":G()}],"mask-image-b-to-color":[{"mask-b-to":G()}],"mask-image-l-from-pos":[{"mask-l-from":xt()}],"mask-image-l-to-pos":[{"mask-l-to":xt()}],"mask-image-l-from-color":[{"mask-l-from":G()}],"mask-image-l-to-color":[{"mask-l-to":G()}],"mask-image-x-from-pos":[{"mask-x-from":xt()}],"mask-image-x-to-pos":[{"mask-x-to":xt()}],"mask-image-x-from-color":[{"mask-x-from":G()}],"mask-image-x-to-color":[{"mask-x-to":G()}],"mask-image-y-from-pos":[{"mask-y-from":xt()}],"mask-image-y-to-pos":[{"mask-y-to":xt()}],"mask-image-y-from-color":[{"mask-y-from":G()}],"mask-image-y-to-color":[{"mask-y-to":G()}],"mask-image-radial":[{"mask-radial":[rt,st]}],"mask-image-radial-from-pos":[{"mask-radial-from":xt()}],"mask-image-radial-to-pos":[{"mask-radial-to":xt()}],"mask-image-radial-from-color":[{"mask-radial-from":G()}],"mask-image-radial-to-color":[{"mask-radial-to":G()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[At]}],"mask-image-conic-from-pos":[{"mask-conic-from":xt()}],"mask-image-conic-to-pos":[{"mask-conic-to":xt()}],"mask-image-conic-from-color":[{"mask-conic-from":G()}],"mask-image-conic-to-color":[{"mask-conic-to":G()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ft()}],"mask-repeat":[{mask:O()}],"mask-size":[{mask:K()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",rt,st]}],filter:[{filter:["","none",rt,st]}],blur:[{blur:nt()}],brightness:[{brightness:[At,rt,st]}],contrast:[{contrast:[At,rt,st]}],"drop-shadow":[{"drop-shadow":["","none",T,go,po]}],"drop-shadow-color":[{"drop-shadow":G()}],grayscale:[{grayscale:["",At,rt,st]}],"hue-rotate":[{"hue-rotate":[At,rt,st]}],invert:[{invert:["",At,rt,st]}],saturate:[{saturate:[At,rt,st]}],sepia:[{sepia:["",At,rt,st]}],"backdrop-filter":[{"backdrop-filter":["","none",rt,st]}],"backdrop-blur":[{"backdrop-blur":nt()}],"backdrop-brightness":[{"backdrop-brightness":[At,rt,st]}],"backdrop-contrast":[{"backdrop-contrast":[At,rt,st]}],"backdrop-grayscale":[{"backdrop-grayscale":["",At,rt,st]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[At,rt,st]}],"backdrop-invert":[{"backdrop-invert":["",At,rt,st]}],"backdrop-opacity":[{"backdrop-opacity":[At,rt,st]}],"backdrop-saturate":[{"backdrop-saturate":[At,rt,st]}],"backdrop-sepia":[{"backdrop-sepia":["",At,rt,st]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":H()}],"border-spacing-x":[{"border-spacing-x":H()}],"border-spacing-y":[{"border-spacing-y":H()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",rt,st]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[At,"initial",rt,st]}],ease:[{ease:["linear","initial",_,rt,st]}],delay:[{delay:[At,rt,st]}],animate:[{animate:["none",z,rt,st]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[A,rt,st]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:Tt()}],"rotate-x":[{"rotate-x":Tt()}],"rotate-y":[{"rotate-y":Tt()}],"rotate-z":[{"rotate-z":Tt()}],scale:[{scale:qt()}],"scale-x":[{"scale-x":qt()}],"scale-y":[{"scale-y":qt()}],"scale-z":[{"scale-z":qt()}],"scale-3d":["scale-3d"],skew:[{skew:Ot()}],"skew-x":[{"skew-x":Ot()}],"skew-y":[{"skew-y":Ot()}],transform:[{transform:[rt,st,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ct()}],"translate-x":[{"translate-x":Ct()}],"translate-y":[{"translate-y":Ct()}],"translate-z":[{"translate-z":Ct()}],"translate-none":["translate-none"],accent:[{accent:G()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:G()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",rt,st]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":H()}],"scroll-mx":[{"scroll-mx":H()}],"scroll-my":[{"scroll-my":H()}],"scroll-ms":[{"scroll-ms":H()}],"scroll-me":[{"scroll-me":H()}],"scroll-mt":[{"scroll-mt":H()}],"scroll-mr":[{"scroll-mr":H()}],"scroll-mb":[{"scroll-mb":H()}],"scroll-ml":[{"scroll-ml":H()}],"scroll-p":[{"scroll-p":H()}],"scroll-px":[{"scroll-px":H()}],"scroll-py":[{"scroll-py":H()}],"scroll-ps":[{"scroll-ps":H()}],"scroll-pe":[{"scroll-pe":H()}],"scroll-pt":[{"scroll-pt":H()}],"scroll-pr":[{"scroll-pr":H()}],"scroll-pb":[{"scroll-pb":H()}],"scroll-pl":[{"scroll-pl":H()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",rt,st]}],fill:[{fill:["none",...G()]}],"stroke-w":[{stroke:[At,Tr,ni,jf]}],stroke:[{stroke:["none",...G()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},UC=vC(BC);function Kt(...n){return UC(Ib(n))}const kC=$b("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function en(d){var u=d,{className:n,variant:a,size:s,asChild:l=!1}=u,c=et(u,["className","variant","size","asChild"]);const h=l?Qb:"button";return S.jsx(h,N({"data-slot":"button",className:Kt(kC({variant:a,size:s,className:n}))},c))}function Tn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card",className:Kt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n)},a))}function wn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-header",className:Kt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n)},a))}function An(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-title",className:Kt("leading-none font-semibold",n)},a))}function En(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",N({"data-slot":"card-content",className:Kt("px-6",n)},a))}function ts(l){var c=l,{className:n,type:a}=c,s=et(c,["className","type"]);return S.jsx("input",N({type:a,"data-slot":"input",className:Kt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n)},s))}var qr=g0();const HC=m0(qr);var PC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pt=PC.reduce((n,a)=>{const s=Br(`Primitive.${a}`),l=w.forwardRef((c,d)=>{const p=c,{asChild:u}=p,h=et(p,["asChild"]),m=u?s:a;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(m,Z(N({},h),{ref:d}))});return l.displayName=`Primitive.${a}`,Z(N({},n),{[a]:l})},{});function GC(n,a){n&&qr.flushSync(()=>n.dispatchEvent(a))}var YC="Label",ox=w.forwardRef((n,a)=>S.jsx(Pt.label,Z(N({},n),{ref:a,onMouseDown:s=>{var c;s.target.closest("button, input, select, textarea")||((c=n.onMouseDown)==null||c.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}})));ox.displayName=YC;var qC=ox;function ai(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(qC,N({"data-slot":"label",className:Kt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",n)},a))}function Uv(n,[a,s]){return Math.min(s,Math.max(a,n))}function Bt(n,a,{checkForDefaultPrevented:s=!0}={}){return function(c){if(n==null||n(c),s===!1||!c.defaultPrevented)return a==null?void 0:a(c)}}function Xr(n,a=[]){let s=[];function l(d,u){const h=w.createContext(u),m=s.length;s=[...s,u];const p=v=>{var z;const _=v,{scope:b,children:T}=_,D=et(_,["scope","children"]),A=((z=b==null?void 0:b[n])==null?void 0:z[m])||h,E=w.useMemo(()=>D,Object.values(D));return S.jsx(A.Provider,{value:E,children:T})};p.displayName=d+"Provider";function g(v,b){var A;const T=((A=b==null?void 0:b[n])==null?void 0:A[m])||h,D=w.useContext(T);if(D)return D;if(u!==void 0)return u;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[p,g]}const c=()=>{const d=s.map(u=>w.createContext(u));return function(h){const m=(h==null?void 0:h[n])||d;return w.useMemo(()=>({[`__scope${n}`]:Z(N({},h),{[n]:m})}),[h,m])}};return c.scopeName=n,[l,XC(c,...a)]}function XC(...n){const a=n[0];if(n.length===1)return a;const s=()=>{const l=n.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(d){const u=l.reduce((h,{useScope:m,scopeName:p})=>{const v=m(d)[`__scope${p}`];return N(N({},h),v)},{});return w.useMemo(()=>({[`__scope${a.scopeName}`]:u}),[u])}};return s.scopeName=a.scopeName,s}function cx(n){const a=n+"CollectionProvider",[s,l]=Xr(a),[c,d]=s(a,{collectionRef:{current:null},itemMap:new Map}),u=A=>{const{scope:E,children:_}=A,z=Ea.useRef(null),j=Ea.useRef(new Map).current;return S.jsx(c,{scope:E,itemMap:j,collectionRef:z,children:_})};u.displayName=a;const h=n+"CollectionSlot",m=Br(h),p=Ea.forwardRef((A,E)=>{const{scope:_,children:z}=A,j=d(h,_),M=ie(E,j.collectionRef);return S.jsx(m,{ref:M,children:z})});p.displayName=h;const g=n+"CollectionItemSlot",v="data-radix-collection-item",b=Br(g),T=Ea.forwardRef((A,E)=>{const W=A,{scope:_,children:z}=W,j=et(W,["scope","children"]),M=Ea.useRef(null),C=ie(E,M),X=d(g,_);return Ea.useEffect(()=>(X.itemMap.set(M,N({ref:M},j)),()=>void X.itemMap.delete(M))),S.jsx(b,{[v]:"",ref:C,children:z})});T.displayName=g;function D(A){const E=d(n+"CollectionConsumer",A);return Ea.useCallback(()=>{const z=E.collectionRef.current;if(!z)return[];const j=Array.from(z.querySelectorAll(`[${v}]`));return Array.from(E.itemMap.values()).sort((X,W)=>j.indexOf(X.ref.current)-j.indexOf(W.ref.current))},[E.collectionRef,E.itemMap])}return[{Provider:u,Slot:p,ItemSlot:T},D,l]}var KC=w.createContext(void 0);function ih(n){const a=w.useContext(KC);return n||a||"ltr"}function Oa(n){const a=w.useRef(n);return w.useEffect(()=>{a.current=n}),w.useMemo(()=>(...s)=>{var l;return(l=a.current)==null?void 0:l.call(a,...s)},[])}function ZC(n,a=globalThis==null?void 0:globalThis.document){const s=Oa(n);w.useEffect(()=>{const l=c=>{c.key==="Escape"&&s(c)};return a.addEventListener("keydown",l,{capture:!0}),()=>a.removeEventListener("keydown",l,{capture:!0})},[s,a])}var QC="DismissableLayer",hd="dismissableLayer.update",FC="dismissableLayer.pointerDownOutside",IC="dismissableLayer.focusOutside",kv,ux=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),fx=w.forwardRef((n,a)=>{var H;const W=n,{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:u,onDismiss:h}=W,m=et(W,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),p=w.useContext(ux),[g,v]=w.useState(null),b=(H=g==null?void 0:g.ownerDocument)!=null?H:globalThis==null?void 0:globalThis.document,[,T]=w.useState({}),D=ie(a,$=>v($)),A=Array.from(p.layers),[E]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),_=A.indexOf(E),z=g?A.indexOf(g):-1,j=p.layersWithOutsidePointerEventsDisabled.size>0,M=z>=_,C=JC($=>{const ct=$.target,ht=[...p.branches].some(lt=>lt.contains(ct));!M||ht||(c==null||c($),u==null||u($),$.defaultPrevented||h==null||h())},b),X=tR($=>{const ct=$.target;[...p.branches].some(lt=>lt.contains(ct))||(d==null||d($),u==null||u($),$.defaultPrevented||h==null||h())},b);return ZC($=>{z===p.layers.size-1&&(l==null||l($),!$.defaultPrevented&&h&&($.preventDefault(),h()))},b),w.useEffect(()=>{if(g)return s&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(kv=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(g)),p.layers.add(g),Hv(),()=>{s&&p.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=kv)}},[g,b,s,p]),w.useEffect(()=>()=>{g&&(p.layers.delete(g),p.layersWithOutsidePointerEventsDisabled.delete(g),Hv())},[g,p]),w.useEffect(()=>{const $=()=>T({});return document.addEventListener(hd,$),()=>document.removeEventListener(hd,$)},[]),S.jsx(Pt.div,Z(N({},m),{ref:D,style:N({pointerEvents:j?M?"auto":"none":void 0},n.style),onFocusCapture:Bt(n.onFocusCapture,X.onFocusCapture),onBlurCapture:Bt(n.onBlurCapture,X.onBlurCapture),onPointerDownCapture:Bt(n.onPointerDownCapture,C.onPointerDownCapture)}))});fx.displayName=QC;var $C="DismissableLayerBranch",WC=w.forwardRef((n,a)=>{const s=w.useContext(ux),l=w.useRef(null),c=ie(a,l);return w.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),S.jsx(Pt.div,Z(N({},n),{ref:c}))});WC.displayName=$C;function JC(n,a=globalThis==null?void 0:globalThis.document){const s=Oa(n),l=w.useRef(!1),c=w.useRef(()=>{});return w.useEffect(()=>{const d=h=>{if(h.target&&!l.current){let m=function(){dx(FC,s,p,{discrete:!0})};const p={originalEvent:h};h.pointerType==="touch"?(a.removeEventListener("click",c.current),c.current=m,a.addEventListener("click",c.current,{once:!0})):m()}else a.removeEventListener("click",c.current);l.current=!1},u=window.setTimeout(()=>{a.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(u),a.removeEventListener("pointerdown",d),a.removeEventListener("click",c.current)}},[a,s]),{onPointerDownCapture:()=>l.current=!0}}function tR(n,a=globalThis==null?void 0:globalThis.document){const s=Oa(n),l=w.useRef(!1);return w.useEffect(()=>{const c=d=>{d.target&&!l.current&&dx(IC,s,{originalEvent:d},{discrete:!1})};return a.addEventListener("focusin",c),()=>a.removeEventListener("focusin",c)},[a,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Hv(){const n=new CustomEvent(hd);document.dispatchEvent(n)}function dx(n,a,s,{discrete:l}){const c=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});a&&c.addEventListener(n,a,{once:!0}),l?GC(c,d):c.dispatchEvent(d)}var _f=0;function eR(){w.useEffect(()=>{var a,s;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(a=n[0])!=null?a:Pv()),document.body.insertAdjacentElement("beforeend",(s=n[1])!=null?s:Pv()),_f++,()=>{_f===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),_f--}},[])}function Pv(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Vf="focusScope.autoFocusOnMount",zf="focusScope.autoFocusOnUnmount",Gv={bubbles:!1,cancelable:!0},nR="FocusScope",hx=w.forwardRef((n,a)=>{const A=n,{loop:s=!1,trapped:l=!1,onMountAutoFocus:c,onUnmountAutoFocus:d}=A,u=et(A,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[h,m]=w.useState(null),p=Oa(c),g=Oa(d),v=w.useRef(null),b=ie(a,E=>m(E)),T=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(l){let E=function(M){if(T.paused||!h)return;const C=M.target;h.contains(C)?v.current=C:Ca(v.current,{select:!0})},_=function(M){if(T.paused||!h)return;const C=M.relatedTarget;C!==null&&(h.contains(C)||Ca(v.current,{select:!0}))},z=function(M){if(document.activeElement===document.body)for(const X of M)X.removedNodes.length>0&&Ca(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",_);const j=new MutationObserver(z);return h&&j.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",_),j.disconnect()}}},[l,h,T.paused]),w.useEffect(()=>{if(h){qv.add(T);const E=document.activeElement;if(!h.contains(E)){const z=new CustomEvent(Vf,Gv);h.addEventListener(Vf,p),h.dispatchEvent(z),z.defaultPrevented||(aR(oR(mx(h)),{select:!0}),document.activeElement===E&&Ca(h))}return()=>{h.removeEventListener(Vf,p),setTimeout(()=>{const z=new CustomEvent(zf,Gv);h.addEventListener(zf,g),h.dispatchEvent(z),z.defaultPrevented||Ca(E!=null?E:document.body,{select:!0}),h.removeEventListener(zf,g),qv.remove(T)},0)}}},[h,p,g,T]);const D=w.useCallback(E=>{if(!s&&!l||T.paused)return;const _=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,z=document.activeElement;if(_&&z){const j=E.currentTarget,[M,C]=iR(j);M&&C?!E.shiftKey&&z===C?(E.preventDefault(),s&&Ca(M,{select:!0})):E.shiftKey&&z===M&&(E.preventDefault(),s&&Ca(C,{select:!0})):z===j&&E.preventDefault()}},[s,l,T.paused]);return S.jsx(Pt.div,Z(N({tabIndex:-1},u),{ref:b,onKeyDown:D}))});hx.displayName=nR;function aR(n,{select:a=!1}={}){const s=document.activeElement;for(const l of n)if(Ca(l,{select:a}),document.activeElement!==s)return}function iR(n){const a=mx(n),s=Yv(a,n),l=Yv(a.reverse(),n);return[s,l]}function mx(n){const a=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const c=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||c?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)a.push(s.currentNode);return a}function Yv(n,a){for(const s of n)if(!sR(s,{upTo:a}))return s}function sR(n,{upTo:a}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(a!==void 0&&n===a)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function rR(n){return n instanceof HTMLInputElement&&"select"in n}function Ca(n,{select:a=!1}={}){if(n&&n.focus){const s=document.activeElement;n.focus({preventScroll:!0}),n!==s&&rR(n)&&a&&n.select()}}var qv=lR();function lR(){let n=[];return{add(a){const s=n[0];a!==s&&(s==null||s.pause()),n=Xv(n,a),n.unshift(a)},remove(a){var s;n=Xv(n,a),(s=n[0])==null||s.resume()}}}function Xv(n,a){const s=[...n],l=s.indexOf(a);return l!==-1&&s.splice(l,1),s}function oR(n){return n.filter(a=>a.tagName!=="A")}var we=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},cR=p0[" useId ".trim().toString()]||(()=>{}),uR=0;function Kr(n){const[a,s]=w.useState(cR());return we(()=>{s(l=>l!=null?l:String(uR++))},[n]),n||(a?`radix-${a}`:"")}const fR=["top","right","bottom","left"],Na=Math.min,Ye=Math.max,Bo=Math.round,yo=Math.floor,On=n=>({x:n,y:n}),dR={left:"right",right:"left",bottom:"top",top:"bottom"},hR={start:"end",end:"start"};function md(n,a,s){return Ye(n,Na(a,s))}function Wn(n,a){return typeof n=="function"?n(a):n}function Jn(n){return n.split("-")[0]}function bs(n){return n.split("-")[1]}function sh(n){return n==="x"?"y":"x"}function rh(n){return n==="y"?"height":"width"}function Fn(n){return["top","bottom"].includes(Jn(n))?"y":"x"}function lh(n){return sh(Fn(n))}function mR(n,a,s){s===void 0&&(s=!1);const l=bs(n),c=lh(n),d=rh(c);let u=c==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return a.reference[d]>a.floating[d]&&(u=Uo(u)),[u,Uo(u)]}function pR(n){const a=Uo(n);return[pd(n),a,pd(a)]}function pd(n){return n.replace(/start|end/g,a=>hR[a])}function gR(n,a,s){const l=["left","right"],c=["right","left"],d=["top","bottom"],u=["bottom","top"];switch(n){case"top":case"bottom":return s?a?c:l:a?l:c;case"left":case"right":return a?d:u;default:return[]}}function yR(n,a,s,l){const c=bs(n);let d=gR(Jn(n),s==="start",l);return c&&(d=d.map(u=>u+"-"+c),a&&(d=d.concat(d.map(pd)))),d}function Uo(n){return n.replace(/left|right|bottom|top/g,a=>dR[a])}function vR(n){return N({top:0,right:0,bottom:0,left:0},n)}function px(n){return typeof n!="number"?vR(n):{top:n,right:n,bottom:n,left:n}}function ko(n){const{x:a,y:s,width:l,height:c}=n;return{width:l,height:c,top:s,left:a,right:a+l,bottom:s+c,x:a,y:s}}function Kv(n,a,s){let{reference:l,floating:c}=n;const d=Fn(a),u=lh(a),h=rh(u),m=Jn(a),p=d==="y",g=l.x+l.width/2-c.width/2,v=l.y+l.height/2-c.height/2,b=l[h]/2-c[h]/2;let T;switch(m){case"top":T={x:g,y:l.y-c.height};break;case"bottom":T={x:g,y:l.y+l.height};break;case"right":T={x:l.x+l.width,y:v};break;case"left":T={x:l.x-c.width,y:v};break;default:T={x:l.x,y:l.y}}switch(bs(a)){case"start":T[u]-=b*(s&&p?-1:1);break;case"end":T[u]+=b*(s&&p?-1:1);break}return T}const bR=(n,a,s)=>fn(null,null,function*(){const{placement:l="bottom",strategy:c="absolute",middleware:d=[],platform:u}=s,h=d.filter(Boolean),m=yield u.isRTL==null?void 0:u.isRTL(a);let p=yield u.getElementRects({reference:n,floating:a,strategy:c}),{x:g,y:v}=Kv(p,l,m),b=l,T={},D=0;for(let A=0;A<h.length;A++){const{name:E,fn:_}=h[A],{x:z,y:j,data:M,reset:C}=yield _({x:g,y:v,initialPlacement:l,placement:b,strategy:c,middlewareData:T,rects:p,platform:u,elements:{reference:n,floating:a}});g=z!=null?z:g,v=j!=null?j:v,T=Z(N({},T),{[E]:N(N({},T[E]),M)}),C&&D<=50&&(D++,typeof C=="object"&&(C.placement&&(b=C.placement),C.rects&&(p=C.rects===!0?yield u.getElementRects({reference:n,floating:a,strategy:c}):C.rects),{x:g,y:v}=Kv(p,b,m)),A=-1)}return{x:g,y:v,placement:b,strategy:c,middlewareData:T}});function Ur(n,a){return fn(this,null,function*(){var s;a===void 0&&(a={});const{x:l,y:c,platform:d,rects:u,elements:h,strategy:m}=n,{boundary:p="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:b=!1,padding:T=0}=Wn(a,n),D=px(T),E=h[b?v==="floating"?"reference":"floating":v],_=ko(yield d.getClippingRect({element:(s=yield d.isElement==null?void 0:d.isElement(E))==null||s?E:E.contextElement||(yield d.getDocumentElement==null?void 0:d.getDocumentElement(h.floating)),boundary:p,rootBoundary:g,strategy:m})),z=v==="floating"?{x:l,y:c,width:u.floating.width,height:u.floating.height}:u.reference,j=yield d.getOffsetParent==null?void 0:d.getOffsetParent(h.floating),M=(yield d.isElement==null?void 0:d.isElement(j))?(yield d.getScale==null?void 0:d.getScale(j))||{x:1,y:1}:{x:1,y:1},C=ko(d.convertOffsetParentRelativeRectToViewportRelativeRect?yield d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:z,offsetParent:j,strategy:m}):z);return{top:(_.top-C.top+D.top)/M.y,bottom:(C.bottom-_.bottom+D.bottom)/M.y,left:(_.left-C.left+D.left)/M.x,right:(C.right-_.right+D.right)/M.x}})}const xR=n=>({name:"arrow",options:n,fn(s){return fn(this,null,function*(){const{x:l,y:c,placement:d,rects:u,platform:h,elements:m,middlewareData:p}=s,{element:g,padding:v=0}=Wn(n,s)||{};if(g==null)return{};const b=px(v),T={x:l,y:c},D=lh(d),A=rh(D),E=yield h.getDimensions(g),_=D==="y",z=_?"top":"left",j=_?"bottom":"right",M=_?"clientHeight":"clientWidth",C=u.reference[A]+u.reference[D]-T[D]-u.floating[A],X=T[D]-u.reference[D],W=yield h.getOffsetParent==null?void 0:h.getOffsetParent(g);let H=W?W[M]:0;(!H||!(yield h.isElement==null?void 0:h.isElement(W)))&&(H=m.floating[M]||u.floating[A]);const $=C/2-X/2,ct=H/2-E[A]/2-1,ht=Na(b[z],ct),lt=Na(b[j],ct),bt=ht,Et=H-E[A]-lt,ut=H/2-E[A]/2+$,B=md(bt,ut,Et),q=!p.arrow&&bs(d)!=null&&ut!==B&&u.reference[A]/2-(ut<bt?ht:lt)-E[A]/2<0,G=q?ut<bt?ut-bt:ut-Et:0;return{[D]:T[D]+G,data:N({[D]:B,centerOffset:ut-B-G},q&&{alignmentOffset:G}),reset:q}})}}),SR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,fn(s){return fn(this,null,function*(){var l,c;const{placement:d,middlewareData:u,rects:h,initialPlacement:m,platform:p,elements:g}=s,B=Wn(n,s),{mainAxis:v=!0,crossAxis:b=!0,fallbackPlacements:T,fallbackStrategy:D="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:E=!0}=B,_=et(B,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((l=u.arrow)!=null&&l.alignmentOffset)return{};const z=Jn(d),j=Fn(m),M=Jn(m)===m,C=yield p.isRTL==null?void 0:p.isRTL(g.floating),X=T||(M||!E?[Uo(m)]:pR(m)),W=A!=="none";!T&&W&&X.push(...yR(m,E,A,C));const H=[m,...X],$=yield Ur(s,_),ct=[];let ht=((c=u.flip)==null?void 0:c.overflows)||[];if(v&&ct.push($[z]),b){const q=mR(d,h,C);ct.push($[q[0]],$[q[1]])}if(ht=[...ht,{placement:d,overflows:ct}],!ct.every(q=>q<=0)){var lt,bt;const q=(((lt=u.flip)==null?void 0:lt.index)||0)+1,G=H[q];if(G){var Et;const O=b==="alignment"?j!==Fn(G):!1,K=((Et=ht[0])==null?void 0:Et.overflows[0])>0;if(!O||K)return{data:{index:q,overflows:ht},reset:{placement:G}}}let ft=(bt=ht.filter(O=>O.overflows[0]<=0).sort((O,K)=>O.overflows[1]-K.overflows[1])[0])==null?void 0:bt.placement;if(!ft)switch(D){case"bestFit":{var ut;const O=(ut=ht.filter(K=>{if(W){const tt=Fn(K.placement);return tt===j||tt==="y"}return!0}).map(K=>[K.placement,K.overflows.filter(tt=>tt>0).reduce((tt,J)=>tt+J,0)]).sort((K,tt)=>K[1]-tt[1])[0])==null?void 0:ut[0];O&&(ft=O);break}case"initialPlacement":ft=m;break}if(d!==ft)return{reset:{placement:ft}}}return{}})}}};function Zv(n,a){return{top:n.top-a.height,right:n.right-a.width,bottom:n.bottom-a.height,left:n.left-a.width}}function Qv(n){return fR.some(a=>n[a]>=0)}const TR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,fn(s){return fn(this,null,function*(){const{rects:l}=s,u=Wn(n,s),{strategy:c="referenceHidden"}=u,d=et(u,["strategy"]);switch(c){case"referenceHidden":{const h=yield Ur(s,Z(N({},d),{elementContext:"reference"})),m=Zv(h,l.reference);return{data:{referenceHiddenOffsets:m,referenceHidden:Qv(m)}}}case"escaped":{const h=yield Ur(s,Z(N({},d),{altBoundary:!0})),m=Zv(h,l.floating);return{data:{escapedOffsets:m,escaped:Qv(m)}}}default:return{}}})}}};function wR(n,a){return fn(this,null,function*(){const{placement:s,platform:l,elements:c}=n,d=yield l.isRTL==null?void 0:l.isRTL(c.floating),u=Jn(s),h=bs(s),m=Fn(s)==="y",p=["left","top"].includes(u)?-1:1,g=d&&m?-1:1,v=Wn(a,n);let{mainAxis:b,crossAxis:T,alignmentAxis:D}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return h&&typeof D=="number"&&(T=h==="end"?D*-1:D),m?{x:T*g,y:b*p}:{x:b*p,y:T*g}})}const AR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,fn(s){return fn(this,null,function*(){var l,c;const{x:d,y:u,placement:h,middlewareData:m}=s,p=yield wR(s,n);return h===((l=m.offset)==null?void 0:l.placement)&&(c=m.arrow)!=null&&c.alignmentOffset?{}:{x:d+p.x,y:u+p.y,data:Z(N({},p),{placement:h})}})}}},ER=function(n){return n===void 0&&(n={}),{name:"shift",options:n,fn(s){return fn(this,null,function*(){const{x:l,y:c,placement:d}=s,_=Wn(n,s),{mainAxis:u=!0,crossAxis:h=!1,limiter:m={fn:z=>{let{x:j,y:M}=z;return{x:j,y:M}}}}=_,p=et(_,["mainAxis","crossAxis","limiter"]),g={x:l,y:c},v=yield Ur(s,p),b=Fn(Jn(d)),T=sh(b);let D=g[T],A=g[b];if(u){const z=T==="y"?"top":"left",j=T==="y"?"bottom":"right",M=D+v[z],C=D-v[j];D=md(M,D,C)}if(h){const z=b==="y"?"top":"left",j=b==="y"?"bottom":"right",M=A+v[z],C=A-v[j];A=md(M,A,C)}const E=m.fn(Z(N({},s),{[T]:D,[b]:A}));return Z(N({},E),{data:{x:E.x-l,y:E.y-c,enabled:{[T]:u,[b]:h}}})})}}},MR=function(n){return n===void 0&&(n={}),{options:n,fn(a){const{x:s,y:l,placement:c,rects:d,middlewareData:u}=a,{offset:h=0,mainAxis:m=!0,crossAxis:p=!0}=Wn(n,a),g={x:s,y:l},v=Fn(c),b=sh(v);let T=g[b],D=g[v];const A=Wn(h,a),E=typeof A=="number"?{mainAxis:A,crossAxis:0}:N({mainAxis:0,crossAxis:0},A);if(m){const j=b==="y"?"height":"width",M=d.reference[b]-d.floating[j]+E.mainAxis,C=d.reference[b]+d.reference[j]-E.mainAxis;T<M?T=M:T>C&&(T=C)}if(p){var _,z;const j=b==="y"?"width":"height",M=["top","left"].includes(Jn(c)),C=d.reference[v]-d.floating[j]+(M&&((_=u.offset)==null?void 0:_[v])||0)+(M?0:E.crossAxis),X=d.reference[v]+d.reference[j]+(M?0:((z=u.offset)==null?void 0:z[v])||0)-(M?E.crossAxis:0);D<C?D=C:D>X&&(D=X)}return{[b]:T,[v]:D}}}},CR=function(n){return n===void 0&&(n={}),{name:"size",options:n,fn(s){return fn(this,null,function*(){var l,c;const{placement:d,rects:u,platform:h,elements:m}=s,ht=Wn(n,s),{apply:p=()=>{}}=ht,g=et(ht,["apply"]),v=yield Ur(s,g),b=Jn(d),T=bs(d),D=Fn(d)==="y",{width:A,height:E}=u.floating;let _,z;b==="top"||b==="bottom"?(_=b,z=T===((yield h.isRTL==null?void 0:h.isRTL(m.floating))?"start":"end")?"left":"right"):(z=b,_=T==="end"?"top":"bottom");const j=E-v.top-v.bottom,M=A-v.left-v.right,C=Na(E-v[_],j),X=Na(A-v[z],M),W=!s.middlewareData.shift;let H=C,$=X;if((l=s.middlewareData.shift)!=null&&l.enabled.x&&($=M),(c=s.middlewareData.shift)!=null&&c.enabled.y&&(H=j),W&&!T){const lt=Ye(v.left,0),bt=Ye(v.right,0),Et=Ye(v.top,0),ut=Ye(v.bottom,0);D?$=A-2*(lt!==0||bt!==0?lt+bt:Ye(v.left,v.right)):H=E-2*(Et!==0||ut!==0?Et+ut:Ye(v.top,v.bottom))}yield p(Z(N({},s),{availableWidth:$,availableHeight:H}));const ct=yield h.getDimensions(m.floating);return A!==ct.width||E!==ct.height?{reset:{rects:!0}}:{}})}}};function Ko(){return typeof window!="undefined"}function xs(n){return gx(n)?(n.nodeName||"").toLowerCase():"#document"}function qe(n){var a;return(n==null||(a=n.ownerDocument)==null?void 0:a.defaultView)||window}function jn(n){var a;return(a=(gx(n)?n.ownerDocument:n.document)||window.document)==null?void 0:a.documentElement}function gx(n){return Ko()?n instanceof Node||n instanceof qe(n).Node:!1}function mn(n){return Ko()?n instanceof Element||n instanceof qe(n).Element:!1}function Nn(n){return Ko()?n instanceof HTMLElement||n instanceof qe(n).HTMLElement:!1}function Fv(n){return!Ko()||typeof ShadowRoot=="undefined"?!1:n instanceof ShadowRoot||n instanceof qe(n).ShadowRoot}function Zr(n){const{overflow:a,overflowX:s,overflowY:l,display:c}=pn(n);return/auto|scroll|overlay|hidden|clip/.test(a+l+s)&&!["inline","contents"].includes(c)}function RR(n){return["table","td","th"].includes(xs(n))}function Zo(n){return[":popover-open",":modal"].some(a=>{try{return n.matches(a)}catch(s){return!1}})}function oh(n){const a=ch(),s=mn(n)?pn(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function DR(n){let a=ja(n);for(;Nn(a)&&!hs(a);){if(oh(a))return a;if(Zo(a))return null;a=ja(a)}return null}function ch(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function hs(n){return["html","body","#document"].includes(xs(n))}function pn(n){return qe(n).getComputedStyle(n)}function Qo(n){return mn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function ja(n){if(xs(n)==="html")return n;const a=n.assignedSlot||n.parentNode||Fv(n)&&n.host||jn(n);return Fv(a)?a.host:a}function yx(n){const a=ja(n);return hs(a)?n.ownerDocument?n.ownerDocument.body:n.body:Nn(a)&&Zr(a)?a:yx(a)}function kr(n,a,s){var l;a===void 0&&(a=[]),s===void 0&&(s=!0);const c=yx(n),d=c===((l=n.ownerDocument)==null?void 0:l.body),u=qe(c);if(d){const h=gd(u);return a.concat(u,u.visualViewport||[],Zr(c)?c:[],h&&s?kr(h):[])}return a.concat(c,kr(c,[],s))}function gd(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function vx(n){const a=pn(n);let s=parseFloat(a.width)||0,l=parseFloat(a.height)||0;const c=Nn(n),d=c?n.offsetWidth:s,u=c?n.offsetHeight:l,h=Bo(s)!==d||Bo(l)!==u;return h&&(s=d,l=u),{width:s,height:l,$:h}}function uh(n){return mn(n)?n:n.contextElement}function cs(n){const a=uh(n);if(!Nn(a))return On(1);const s=a.getBoundingClientRect(),{width:l,height:c,$:d}=vx(a);let u=(d?Bo(s.width):s.width)/l,h=(d?Bo(s.height):s.height)/c;return(!u||!Number.isFinite(u))&&(u=1),(!h||!Number.isFinite(h))&&(h=1),{x:u,y:h}}const OR=On(0);function bx(n){const a=qe(n);return!ch()||!a.visualViewport?OR:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function NR(n,a,s){return a===void 0&&(a=!1),!s||a&&s!==qe(n)?!1:a}function ci(n,a,s,l){a===void 0&&(a=!1),s===void 0&&(s=!1);const c=n.getBoundingClientRect(),d=uh(n);let u=On(1);a&&(l?mn(l)&&(u=cs(l)):u=cs(n));const h=NR(d,s,l)?bx(d):On(0);let m=(c.left+h.x)/u.x,p=(c.top+h.y)/u.y,g=c.width/u.x,v=c.height/u.y;if(d){const b=qe(d),T=l&&mn(l)?qe(l):l;let D=b,A=gd(D);for(;A&&l&&T!==D;){const E=cs(A),_=A.getBoundingClientRect(),z=pn(A),j=_.left+(A.clientLeft+parseFloat(z.paddingLeft))*E.x,M=_.top+(A.clientTop+parseFloat(z.paddingTop))*E.y;m*=E.x,p*=E.y,g*=E.x,v*=E.y,m+=j,p+=M,D=qe(A),A=gd(D)}}return ko({width:g,height:v,x:m,y:p})}function fh(n,a){const s=Qo(n).scrollLeft;return a?a.left+s:ci(jn(n)).left+s}function xx(n,a,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),c=l.left+a.scrollLeft-(s?0:fh(n,l)),d=l.top+a.scrollTop;return{x:c,y:d}}function jR(n){let{elements:a,rect:s,offsetParent:l,strategy:c}=n;const d=c==="fixed",u=jn(l),h=a?Zo(a.floating):!1;if(l===u||h&&d)return s;let m={scrollLeft:0,scrollTop:0},p=On(1);const g=On(0),v=Nn(l);if((v||!v&&!d)&&((xs(l)!=="body"||Zr(u))&&(m=Qo(l)),Nn(l))){const T=ci(l);p=cs(l),g.x=T.x+l.clientLeft,g.y=T.y+l.clientTop}const b=u&&!v&&!d?xx(u,m,!0):On(0);return{width:s.width*p.x,height:s.height*p.y,x:s.x*p.x-m.scrollLeft*p.x+g.x+b.x,y:s.y*p.y-m.scrollTop*p.y+g.y+b.y}}function _R(n){return Array.from(n.getClientRects())}function VR(n){const a=jn(n),s=Qo(n),l=n.ownerDocument.body,c=Ye(a.scrollWidth,a.clientWidth,l.scrollWidth,l.clientWidth),d=Ye(a.scrollHeight,a.clientHeight,l.scrollHeight,l.clientHeight);let u=-s.scrollLeft+fh(n);const h=-s.scrollTop;return pn(l).direction==="rtl"&&(u+=Ye(a.clientWidth,l.clientWidth)-c),{width:c,height:d,x:u,y:h}}function zR(n,a){const s=qe(n),l=jn(n),c=s.visualViewport;let d=l.clientWidth,u=l.clientHeight,h=0,m=0;if(c){d=c.width,u=c.height;const p=ch();(!p||p&&a==="fixed")&&(h=c.offsetLeft,m=c.offsetTop)}return{width:d,height:u,x:h,y:m}}function LR(n,a){const s=ci(n,!0,a==="fixed"),l=s.top+n.clientTop,c=s.left+n.clientLeft,d=Nn(n)?cs(n):On(1),u=n.clientWidth*d.x,h=n.clientHeight*d.y,m=c*d.x,p=l*d.y;return{width:u,height:h,x:m,y:p}}function Iv(n,a,s){let l;if(a==="viewport")l=zR(n,s);else if(a==="document")l=VR(jn(n));else if(mn(a))l=LR(a,s);else{const c=bx(n);l={x:a.x-c.x,y:a.y-c.y,width:a.width,height:a.height}}return ko(l)}function Sx(n,a){const s=ja(n);return s===a||!mn(s)||hs(s)?!1:pn(s).position==="fixed"||Sx(s,a)}function BR(n,a){const s=a.get(n);if(s)return s;let l=kr(n,[],!1).filter(h=>mn(h)&&xs(h)!=="body"),c=null;const d=pn(n).position==="fixed";let u=d?ja(n):n;for(;mn(u)&&!hs(u);){const h=pn(u),m=oh(u);!m&&h.position==="fixed"&&(c=null),(d?!m&&!c:!m&&h.position==="static"&&!!c&&["absolute","fixed"].includes(c.position)||Zr(u)&&!m&&Sx(n,u))?l=l.filter(g=>g!==u):c=h,u=ja(u)}return a.set(n,l),l}function UR(n){let{element:a,boundary:s,rootBoundary:l,strategy:c}=n;const u=[...s==="clippingAncestors"?Zo(a)?[]:BR(a,this._c):[].concat(s),l],h=u[0],m=u.reduce((p,g)=>{const v=Iv(a,g,c);return p.top=Ye(v.top,p.top),p.right=Na(v.right,p.right),p.bottom=Na(v.bottom,p.bottom),p.left=Ye(v.left,p.left),p},Iv(a,h,c));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function kR(n){const{width:a,height:s}=vx(n);return{width:a,height:s}}function HR(n,a,s){const l=Nn(a),c=jn(a),d=s==="fixed",u=ci(n,!0,d,a);let h={scrollLeft:0,scrollTop:0};const m=On(0);function p(){m.x=fh(c)}if(l||!l&&!d)if((xs(a)!=="body"||Zr(c))&&(h=Qo(a)),l){const T=ci(a,!0,d,a);m.x=T.x+a.clientLeft,m.y=T.y+a.clientTop}else c&&p();d&&!l&&c&&p();const g=c&&!l&&!d?xx(c,h):On(0),v=u.left+h.scrollLeft-m.x-g.x,b=u.top+h.scrollTop-m.y-g.y;return{x:v,y:b,width:u.width,height:u.height}}function Lf(n){return pn(n).position==="static"}function $v(n,a){if(!Nn(n)||pn(n).position==="fixed")return null;if(a)return a(n);let s=n.offsetParent;return jn(n)===s&&(s=s.ownerDocument.body),s}function Tx(n,a){const s=qe(n);if(Zo(n))return s;if(!Nn(n)){let c=ja(n);for(;c&&!hs(c);){if(mn(c)&&!Lf(c))return c;c=ja(c)}return s}let l=$v(n,a);for(;l&&RR(l)&&Lf(l);)l=$v(l,a);return l&&hs(l)&&Lf(l)&&!oh(l)?s:l||DR(n)||s}const PR=function(n){return fn(this,null,function*(){const a=this.getOffsetParent||Tx,s=this.getDimensions,l=yield s(n.floating);return{reference:HR(n.reference,yield a(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}})};function GR(n){return pn(n).direction==="rtl"}const YR={convertOffsetParentRelativeRectToViewportRelativeRect:jR,getDocumentElement:jn,getClippingRect:UR,getOffsetParent:Tx,getElementRects:PR,getClientRects:_R,getDimensions:kR,getScale:cs,isElement:mn,isRTL:GR};function wx(n,a){return n.x===a.x&&n.y===a.y&&n.width===a.width&&n.height===a.height}function qR(n,a){let s=null,l;const c=jn(n);function d(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function u(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),d();const p=n.getBoundingClientRect(),{left:g,top:v,width:b,height:T}=p;if(h||a(),!b||!T)return;const D=yo(v),A=yo(c.clientWidth-(g+b)),E=yo(c.clientHeight-(v+T)),_=yo(g),j={rootMargin:-D+"px "+-A+"px "+-E+"px "+-_+"px",threshold:Ye(0,Na(1,m))||1};let M=!0;function C(X){const W=X[0].intersectionRatio;if(W!==m){if(!M)return u();W?u(!1,W):l=setTimeout(()=>{u(!1,1e-7)},1e3)}W===1&&!wx(p,n.getBoundingClientRect())&&u(),M=!1}try{s=new IntersectionObserver(C,Z(N({},j),{root:c.ownerDocument}))}catch(X){s=new IntersectionObserver(C,j)}s.observe(n)}return u(!0),d}function XR(n,a,s,l){l===void 0&&(l={});const{ancestorScroll:c=!0,ancestorResize:d=!0,elementResize:u=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=l,p=uh(n),g=c||d?[...p?kr(p):[],...kr(a)]:[];g.forEach(_=>{c&&_.addEventListener("scroll",s,{passive:!0}),d&&_.addEventListener("resize",s)});const v=p&&h?qR(p,s):null;let b=-1,T=null;u&&(T=new ResizeObserver(_=>{let[z]=_;z&&z.target===p&&T&&(T.unobserve(a),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var j;(j=T)==null||j.observe(a)})),s()}),p&&!m&&T.observe(p),T.observe(a));let D,A=m?ci(n):null;m&&E();function E(){const _=ci(n);A&&!wx(A,_)&&s(),A=_,D=requestAnimationFrame(E)}return s(),()=>{var _;g.forEach(z=>{c&&z.removeEventListener("scroll",s),d&&z.removeEventListener("resize",s)}),v==null||v(),(_=T)==null||_.disconnect(),T=null,m&&cancelAnimationFrame(D)}}const KR=AR,ZR=ER,QR=SR,FR=CR,IR=TR,Wv=xR,$R=MR,WR=(n,a,s)=>{const l=new Map,c=N({platform:YR},s),d=Z(N({},c.platform),{_c:l});return bR(n,a,Z(N({},c),{platform:d}))};var Do=typeof document!="undefined"?w.useLayoutEffect:w.useEffect;function Ho(n,a){if(n===a)return!0;if(typeof n!=typeof a)return!1;if(typeof n=="function"&&n.toString()===a.toString())return!0;let s,l,c;if(n&&a&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==a.length)return!1;for(l=s;l--!==0;)if(!Ho(n[l],a[l]))return!1;return!0}if(c=Object.keys(n),s=c.length,s!==Object.keys(a).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(a,c[l]))return!1;for(l=s;l--!==0;){const d=c[l];if(!(d==="_owner"&&n.$$typeof)&&!Ho(n[d],a[d]))return!1}return!0}return n!==n&&a!==a}function Ax(n){return typeof window=="undefined"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Jv(n,a){const s=Ax(n);return Math.round(a*s)/s}function Bf(n){const a=w.useRef(n);return Do(()=>{a.current=n}),a}function JR(n){n===void 0&&(n={});const{placement:a="bottom",strategy:s="absolute",middleware:l=[],platform:c,elements:{reference:d,floating:u}={},transform:h=!0,whileElementsMounted:m,open:p}=n,[g,v]=w.useState({x:0,y:0,strategy:s,placement:a,middlewareData:{},isPositioned:!1}),[b,T]=w.useState(l);Ho(b,l)||T(l);const[D,A]=w.useState(null),[E,_]=w.useState(null),z=w.useCallback(G=>{G!==X.current&&(X.current=G,A(G))},[]),j=w.useCallback(G=>{G!==W.current&&(W.current=G,_(G))},[]),M=d||D,C=u||E,X=w.useRef(null),W=w.useRef(null),H=w.useRef(g),$=m!=null,ct=Bf(m),ht=Bf(c),lt=Bf(p),bt=w.useCallback(()=>{if(!X.current||!W.current)return;const G={placement:a,strategy:s,middleware:b};ht.current&&(G.platform=ht.current),WR(X.current,W.current,G).then(ft=>{const O=Z(N({},ft),{isPositioned:lt.current!==!1});Et.current&&!Ho(H.current,O)&&(H.current=O,qr.flushSync(()=>{v(O)}))})},[b,a,s,ht,lt]);Do(()=>{p===!1&&H.current.isPositioned&&(H.current.isPositioned=!1,v(G=>Z(N({},G),{isPositioned:!1})))},[p]);const Et=w.useRef(!1);Do(()=>(Et.current=!0,()=>{Et.current=!1}),[]),Do(()=>{if(M&&(X.current=M),C&&(W.current=C),M&&C){if(ct.current)return ct.current(M,C,bt);bt()}},[M,C,bt,ct,$]);const ut=w.useMemo(()=>({reference:X,floating:W,setReference:z,setFloating:j}),[z,j]),B=w.useMemo(()=>({reference:M,floating:C}),[M,C]),q=w.useMemo(()=>{const G={position:s,left:0,top:0};if(!B.floating)return G;const ft=Jv(B.floating,g.x),O=Jv(B.floating,g.y);return h?N(Z(N({},G),{transform:"translate("+ft+"px, "+O+"px)"}),Ax(B.floating)>=1.5&&{willChange:"transform"}):{position:s,left:ft,top:O}},[s,h,B.floating,g.x,g.y]);return w.useMemo(()=>Z(N({},g),{update:bt,refs:ut,elements:B,floatingStyles:q}),[g,bt,ut,B,q])}const tD=n=>{function a(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:c}=typeof n=="function"?n(s):n;return l&&a(l)?l.current!=null?Wv({element:l.current,padding:c}).fn(s):{}:l?Wv({element:l,padding:c}).fn(s):{}}}},eD=(n,a)=>Z(N({},KR(n)),{options:[n,a]}),nD=(n,a)=>Z(N({},ZR(n)),{options:[n,a]}),aD=(n,a)=>Z(N({},$R(n)),{options:[n,a]}),iD=(n,a)=>Z(N({},QR(n)),{options:[n,a]}),sD=(n,a)=>Z(N({},FR(n)),{options:[n,a]}),rD=(n,a)=>Z(N({},IR(n)),{options:[n,a]}),lD=(n,a)=>Z(N({},tD(n)),{options:[n,a]});var oD="Arrow",Ex=w.forwardRef((n,a)=>{const u=n,{children:s,width:l=10,height:c=5}=u,d=et(u,["children","width","height"]);return S.jsx(Pt.svg,Z(N({},d),{ref:a,width:l,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:S.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Ex.displayName=oD;var cD=Ex;function uD(n){const[a,s]=w.useState(void 0);return we(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const d=c[0];let u,h;if("borderBoxSize"in d){const m=d.borderBoxSize,p=Array.isArray(m)?m[0]:m;u=p.inlineSize,h=p.blockSize}else u=n.offsetWidth,h=n.offsetHeight;s({width:u,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),a}var dh="Popper",[Mx,Cx]=Xr(dh),[fD,Rx]=Mx(dh),Dx=n=>{const{__scopePopper:a,children:s}=n,[l,c]=w.useState(null);return S.jsx(fD,{scope:a,anchor:l,onAnchorChange:c,children:s})};Dx.displayName=dh;var Ox="PopperAnchor",Nx=w.forwardRef((n,a)=>{const m=n,{__scopePopper:s,virtualRef:l}=m,c=et(m,["__scopePopper","virtualRef"]),d=Rx(Ox,s),u=w.useRef(null),h=ie(a,u);return w.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||u.current)}),l?null:S.jsx(Pt.div,Z(N({},c),{ref:h}))});Nx.displayName=Ox;var hh="PopperContent",[dD,hD]=Mx(hh),jx=w.forwardRef((n,a)=>{var nt,Tt,qt,Ot,Ct,jt,de,Ae;const xt=n,{__scopePopper:s,side:l="bottom",sideOffset:c=0,align:d="center",alignOffset:u=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:b=!1,updatePositionStrategy:T="optimized",onPlaced:D}=xt,A=et(xt,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),E=Rx(hh,s),[_,z]=w.useState(null),j=ie(a,ln=>z(ln)),[M,C]=w.useState(null),X=uD(M),W=(nt=X==null?void 0:X.width)!=null?nt:0,H=(Tt=X==null?void 0:X.height)!=null?Tt:0,$=l+(d!=="center"?"-"+d:""),ct=typeof g=="number"?g:N({top:0,right:0,bottom:0,left:0},g),ht=Array.isArray(p)?p:[p],lt=ht.length>0,bt={padding:ct,boundary:ht.filter(pD),altBoundary:lt},{refs:Et,floatingStyles:ut,placement:B,isPositioned:q,middlewareData:G}=JR({strategy:"fixed",placement:$,whileElementsMounted:(...ln)=>XR(...ln,{animationFrame:T==="always"}),elements:{reference:E.anchor},middleware:[eD({mainAxis:c+H,alignmentAxis:u}),m&&nD(N({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?aD():void 0},bt)),m&&iD(N({},bt)),sD(Z(N({},bt),{apply:({elements:ln,rects:he,availableWidth:La,availableHeight:Fr})=>{const{width:Ir,height:di}=he.reference,hi=ln.floating.style;hi.setProperty("--radix-popper-available-width",`${La}px`),hi.setProperty("--radix-popper-available-height",`${Fr}px`),hi.setProperty("--radix-popper-anchor-width",`${Ir}px`),hi.setProperty("--radix-popper-anchor-height",`${di}px`)}})),M&&lD({element:M,padding:h}),gD({arrowWidth:W,arrowHeight:H}),b&&rD(N({strategy:"referenceHidden"},bt))]}),[ft,O]=zx(B),K=Oa(D);we(()=>{q&&(K==null||K())},[q,K]);const tt=(qt=G.arrow)==null?void 0:qt.x,J=(Ot=G.arrow)==null?void 0:Ot.y,at=((Ct=G.arrow)==null?void 0:Ct.centerOffset)!==0,[vt,dt]=w.useState();return we(()=>{_&&dt(window.getComputedStyle(_).zIndex)},[_]),S.jsx("div",{ref:Et.setFloating,"data-radix-popper-content-wrapper":"",style:N(Z(N({},ut),{transform:q?ut.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:vt,"--radix-popper-transform-origin":[(jt=G.transformOrigin)==null?void 0:jt.x,(de=G.transformOrigin)==null?void 0:de.y].join(" ")}),((Ae=G.hide)==null?void 0:Ae.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:n.dir,children:S.jsx(dD,{scope:s,placedSide:ft,onArrowChange:C,arrowX:tt,arrowY:J,shouldHideArrow:at,children:S.jsx(Pt.div,Z(N({"data-side":ft,"data-align":O},A),{ref:j,style:Z(N({},A.style),{animation:q?void 0:"none"})}))})})});jx.displayName=hh;var _x="PopperArrow",mD={top:"bottom",right:"left",bottom:"top",left:"right"},Vx=w.forwardRef(function(a,s){const h=a,{__scopePopper:l}=h,c=et(h,["__scopePopper"]),d=hD(_x,l),u=mD[d.placedSide];return S.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[u]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:S.jsx(cD,Z(N({},c),{ref:s,style:Z(N({},c.style),{display:"block"})}))})});Vx.displayName=_x;function pD(n){return n!==null}var gD=n=>({name:"transformOrigin",options:n,fn(a){var E,_,z,j,M;const{placement:s,rects:l,middlewareData:c}=a,u=((E=c.arrow)==null?void 0:E.centerOffset)!==0,h=u?0:n.arrowWidth,m=u?0:n.arrowHeight,[p,g]=zx(s),v={start:"0%",center:"50%",end:"100%"}[g],b=((z=(_=c.arrow)==null?void 0:_.x)!=null?z:0)+h/2,T=((M=(j=c.arrow)==null?void 0:j.y)!=null?M:0)+m/2;let D="",A="";return p==="bottom"?(D=u?v:`${b}px`,A=`${-m}px`):p==="top"?(D=u?v:`${b}px`,A=`${l.floating.height+m}px`):p==="right"?(D=`${-m}px`,A=u?v:`${T}px`):p==="left"&&(D=`${l.floating.width+m}px`,A=u?v:`${T}px`),{data:{x:D,y:A}}}});function zx(n){const[a,s="center"]=n.split("-");return[a,s]}var yD=Dx,vD=Nx,bD=jx,xD=Vx,SD="Portal",Lx=w.forwardRef((n,a)=>{var m;const h=n,{container:s}=h,l=et(h,["container"]),[c,d]=w.useState(!1);we(()=>d(!0),[]);const u=s||c&&((m=globalThis==null?void 0:globalThis.document)==null?void 0:m.body);return u?HC.createPortal(S.jsx(Pt.div,Z(N({},l),{ref:a})),u):null});Lx.displayName=SD;var TD=p0[" useInsertionEffect ".trim().toString()]||we;function Po({prop:n,defaultProp:a,onChange:s=()=>{},caller:l}){const[c,d,u]=wD({defaultProp:a,onChange:s}),h=n!==void 0,m=h?n:c;{const g=w.useRef(n!==void 0);w.useEffect(()=>{const v=g.current;v!==h&&console.warn(`${l} is changing from ${v?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=h},[h,l])}const p=w.useCallback(g=>{var v;if(h){const b=AD(g)?g(n):g;b!==n&&((v=u.current)==null||v.call(u,b))}else d(g)},[h,n,d,u]);return[m,p]}function wD({defaultProp:n,onChange:a}){const[s,l]=w.useState(n),c=w.useRef(s),d=w.useRef(a);return TD(()=>{d.current=a},[a]),w.useEffect(()=>{var u;c.current!==s&&((u=d.current)==null||u.call(d,s),c.current=s)},[s,c]),[s,l,d]}function AD(n){return typeof n=="function"}function ED(n){const a=w.useRef({value:n,previous:n});return w.useMemo(()=>(a.current.value!==n&&(a.current.previous=a.current.value,a.current.value=n),a.current.previous),[n])}var Bx=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),MD="VisuallyHidden",CD=w.forwardRef((n,a)=>S.jsx(Pt.span,Z(N({},n),{ref:a,style:N(N({},Bx),n.style)})));CD.displayName=MD;var RD=function(n){if(typeof document=="undefined")return null;var a=Array.isArray(n)?n[0]:n;return a.ownerDocument.body},es=new WeakMap,vo=new WeakMap,bo={},Uf=0,Ux=function(n){return n&&(n.host||Ux(n.parentNode))},DD=function(n,a){return a.map(function(s){if(n.contains(s))return s;var l=Ux(s);return l&&n.contains(l)?l:(console.error("aria-hidden",s,"in not contained inside",n,". Doing nothing"),null)}).filter(function(s){return!!s})},OD=function(n,a,s,l){var c=DD(a,Array.isArray(n)?n:[n]);bo[s]||(bo[s]=new WeakMap);var d=bo[s],u=[],h=new Set,m=new Set(c),p=function(v){!v||h.has(v)||(h.add(v),p(v.parentNode))};c.forEach(p);var g=function(v){!v||m.has(v)||Array.prototype.forEach.call(v.children,function(b){if(h.has(b))g(b);else try{var T=b.getAttribute(l),D=T!==null&&T!=="false",A=(es.get(b)||0)+1,E=(d.get(b)||0)+1;es.set(b,A),d.set(b,E),u.push(b),A===1&&D&&vo.set(b,!0),E===1&&b.setAttribute(s,"true"),D||b.setAttribute(l,"true")}catch(_){console.error("aria-hidden: cannot operate on ",b,_)}})};return g(a),h.clear(),Uf++,function(){u.forEach(function(v){var b=es.get(v)-1,T=d.get(v)-1;es.set(v,b),d.set(v,T),b||(vo.has(v)||v.removeAttribute(l),vo.delete(v)),T||v.removeAttribute(s)}),Uf--,Uf||(es=new WeakMap,es=new WeakMap,vo=new WeakMap,bo={})}},ND=function(n,a,s){s===void 0&&(s="data-aria-hidden");var l=Array.from(Array.isArray(n)?n:[n]),c=RD(n);return c?(l.push.apply(l,Array.from(c.querySelectorAll("[aria-live]"))),OD(l,c,s,"aria-hidden")):function(){return null}},Mn=function(){return Mn=Object.assign||function(a){for(var s,l=1,c=arguments.length;l<c;l++){s=arguments[l];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(a[d]=s[d])}return a},Mn.apply(this,arguments)};function kx(n,a){var s={};for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(n);c<l.length;c++)a.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(n,l[c])&&(s[l[c]]=n[l[c]]);return s}function jD(n,a,s){if(s||arguments.length===2)for(var l=0,c=a.length,d;l<c;l++)(d||!(l in a))&&(d||(d=Array.prototype.slice.call(a,0,l)),d[l]=a[l]);return n.concat(d||Array.prototype.slice.call(a))}var Oo="right-scroll-bar-position",No="width-before-scroll-bar",_D="with-scroll-bars-hidden",VD="--removed-body-scroll-bar-size";function kf(n,a){return typeof n=="function"?n(a):n&&(n.current=a),n}function zD(n,a){var s=w.useState(function(){return{value:n,callback:a,facade:{get current(){return s.value},set current(l){var c=s.value;c!==l&&(s.value=l,s.callback(l,c))}}}})[0];return s.callback=a,s.facade}var LD=typeof window!="undefined"?w.useLayoutEffect:w.useEffect,t0=new WeakMap;function BD(n,a){var s=zD(null,function(l){return n.forEach(function(c){return kf(c,l)})});return LD(function(){var l=t0.get(s);if(l){var c=new Set(l),d=new Set(n),u=s.current;c.forEach(function(h){d.has(h)||kf(h,null)}),d.forEach(function(h){c.has(h)||kf(h,u)})}t0.set(s,n)},[n]),s}function UD(n){return n}function kD(n,a){a===void 0&&(a=UD);var s=[],l=!1,c={read:function(){if(l)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:n},useMedium:function(d){var u=a(d,l);return s.push(u),function(){s=s.filter(function(h){return h!==u})}},assignSyncMedium:function(d){for(l=!0;s.length;){var u=s;s=[],u.forEach(d)}s={push:function(h){return d(h)},filter:function(){return s}}},assignMedium:function(d){l=!0;var u=[];if(s.length){var h=s;s=[],h.forEach(d),u=s}var m=function(){var g=u;u=[],g.forEach(d)},p=function(){return Promise.resolve().then(m)};p(),s={push:function(g){u.push(g),p()},filter:function(g){return u=u.filter(g),s}}}};return c}function HD(n){n===void 0&&(n={});var a=kD(null);return a.options=Mn({async:!0,ssr:!1},n),a}var Hx=function(n){var a=n.sideCar,s=kx(n,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var l=a.read();if(!l)throw new Error("Sidecar medium not found");return w.createElement(l,Mn({},s))};Hx.isSideCarExport=!0;function PD(n,a){return n.useMedium(a),Hx}var Px=HD(),Hf=function(){},Fo=w.forwardRef(function(n,a){var s=w.useRef(null),l=w.useState({onScrollCapture:Hf,onWheelCapture:Hf,onTouchMoveCapture:Hf}),c=l[0],d=l[1],u=n.forwardProps,h=n.children,m=n.className,p=n.removeScrollBar,g=n.enabled,v=n.shards,b=n.sideCar,T=n.noIsolation,D=n.inert,A=n.allowPinchZoom,E=n.as,_=E===void 0?"div":E,z=n.gapMode,j=kx(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=b,C=BD([s,a]),X=Mn(Mn({},j),c);return w.createElement(w.Fragment,null,g&&w.createElement(M,{sideCar:Px,removeScrollBar:p,shards:v,noIsolation:T,inert:D,setCallbacks:d,allowPinchZoom:!!A,lockRef:s,gapMode:z}),u?w.cloneElement(w.Children.only(h),Mn(Mn({},X),{ref:C})):w.createElement(_,Mn({},X,{className:m,ref:C}),h))});Fo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Fo.classNames={fullWidth:No,zeroRight:Oo};var GD=function(){if(typeof __webpack_nonce__!="undefined")return __webpack_nonce__};function YD(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var a=GD();return a&&n.setAttribute("nonce",a),n}function qD(n,a){n.styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a))}function XD(n){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(n)}var KD=function(){var n=0,a=null;return{add:function(s){n==0&&(a=YD())&&(qD(a,s),XD(a)),n++},remove:function(){n--,!n&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},ZD=function(){var n=KD();return function(a,s){w.useEffect(function(){return n.add(a),function(){n.remove()}},[a&&s])}},Gx=function(){var n=ZD(),a=function(s){var l=s.styles,c=s.dynamic;return n(l,c),null};return a},QD={left:0,top:0,right:0,gap:0},Pf=function(n){return parseInt(n||"",10)||0},FD=function(n){var a=window.getComputedStyle(document.body),s=a[n==="padding"?"paddingLeft":"marginLeft"],l=a[n==="padding"?"paddingTop":"marginTop"],c=a[n==="padding"?"paddingRight":"marginRight"];return[Pf(s),Pf(l),Pf(c)]},ID=function(n){if(n===void 0&&(n="margin"),typeof window=="undefined")return QD;var a=FD(n),s=document.documentElement.clientWidth,l=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,l-s+a[2]-a[0])}},$D=Gx(),us="data-scroll-locked",WD=function(n,a,s,l){var c=n.left,d=n.top,u=n.right,h=n.gap;return s===void 0&&(s="margin"),`
  .`.concat(_D,` {
   overflow: hidden `).concat(l,`;
   padding-right: `).concat(h,"px ").concat(l,`;
  }
  body[`).concat(us,`] {
    overflow: hidden `).concat(l,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(l,";"),s==="margin"&&`
    padding-left: `.concat(c,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(u,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(l,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(l,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Oo,` {
    right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(No,` {
    margin-right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(Oo," .").concat(Oo,` {
    right: 0 `).concat(l,`;
  }
  
  .`).concat(No," .").concat(No,` {
    margin-right: 0 `).concat(l,`;
  }
  
  body[`).concat(us,`] {
    `).concat(VD,": ").concat(h,`px;
  }
`)},e0=function(){var n=parseInt(document.body.getAttribute(us)||"0",10);return isFinite(n)?n:0},JD=function(){w.useEffect(function(){return document.body.setAttribute(us,(e0()+1).toString()),function(){var n=e0()-1;n<=0?document.body.removeAttribute(us):document.body.setAttribute(us,n.toString())}},[])},tO=function(n){var a=n.noRelative,s=n.noImportant,l=n.gapMode,c=l===void 0?"margin":l;JD();var d=w.useMemo(function(){return ID(c)},[c]);return w.createElement($D,{styles:WD(d,!a,c,s?"":"!important")})},yd=!1;if(typeof window!="undefined")try{var xo=Object.defineProperty({},"passive",{get:function(){return yd=!0,!0}});window.addEventListener("test",xo,xo),window.removeEventListener("test",xo,xo)}catch(n){yd=!1}var ns=yd?{passive:!1}:!1,eO=function(n){return n.tagName==="TEXTAREA"},Yx=function(n,a){if(!(n instanceof Element))return!1;var s=window.getComputedStyle(n);return s[a]!=="hidden"&&!(s.overflowY===s.overflowX&&!eO(n)&&s[a]==="visible")},nO=function(n){return Yx(n,"overflowY")},aO=function(n){return Yx(n,"overflowX")},n0=function(n,a){var s=a.ownerDocument,l=a;do{typeof ShadowRoot!="undefined"&&l instanceof ShadowRoot&&(l=l.host);var c=qx(n,l);if(c){var d=Xx(n,l),u=d[1],h=d[2];if(u>h)return!0}l=l.parentNode}while(l&&l!==s.body);return!1},iO=function(n){var a=n.scrollTop,s=n.scrollHeight,l=n.clientHeight;return[a,s,l]},sO=function(n){var a=n.scrollLeft,s=n.scrollWidth,l=n.clientWidth;return[a,s,l]},qx=function(n,a){return n==="v"?nO(a):aO(a)},Xx=function(n,a){return n==="v"?iO(a):sO(a)},rO=function(n,a){return n==="h"&&a==="rtl"?-1:1},lO=function(n,a,s,l,c){var d=rO(n,window.getComputedStyle(a).direction),u=d*l,h=s.target,m=a.contains(h),p=!1,g=u>0,v=0,b=0;do{var T=Xx(n,h),D=T[0],A=T[1],E=T[2],_=A-E-d*D;(D||_)&&qx(n,h)&&(v+=_,b+=D),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!m&&h!==document.body||m&&(a.contains(h)||a===h));return(g&&Math.abs(v)<1||!g&&Math.abs(b)<1)&&(p=!0),p},So=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},a0=function(n){return[n.deltaX,n.deltaY]},i0=function(n){return n&&"current"in n?n.current:n},oO=function(n,a){return n[0]===a[0]&&n[1]===a[1]},cO=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},uO=0,as=[];function fO(n){var a=w.useRef([]),s=w.useRef([0,0]),l=w.useRef(),c=w.useState(uO++)[0],d=w.useState(Gx)[0],u=w.useRef(n);w.useEffect(function(){u.current=n},[n]),w.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(c));var A=jD([n.lockRef.current],(n.shards||[]).map(i0),!0).filter(Boolean);return A.forEach(function(E){return E.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),A.forEach(function(E){return E.classList.remove("allow-interactivity-".concat(c))})}}},[n.inert,n.lockRef.current,n.shards]);var h=w.useCallback(function(A,E){if("touches"in A&&A.touches.length===2||A.type==="wheel"&&A.ctrlKey)return!u.current.allowPinchZoom;var _=So(A),z=s.current,j="deltaX"in A?A.deltaX:z[0]-_[0],M="deltaY"in A?A.deltaY:z[1]-_[1],C,X=A.target,W=Math.abs(j)>Math.abs(M)?"h":"v";if("touches"in A&&W==="h"&&X.type==="range")return!1;var H=n0(W,X);if(!H)return!0;if(H?C=W:(C=W==="v"?"h":"v",H=n0(W,X)),!H)return!1;if(!l.current&&"changedTouches"in A&&(j||M)&&(l.current=C),!C)return!0;var $=l.current||C;return lO($,E,A,$==="h"?j:M)},[]),m=w.useCallback(function(A){var E=A;if(!(!as.length||as[as.length-1]!==d)){var _="deltaY"in E?a0(E):So(E),z=a.current.filter(function(C){return C.name===E.type&&(C.target===E.target||E.target===C.shadowParent)&&oO(C.delta,_)})[0];if(z&&z.should){E.cancelable&&E.preventDefault();return}if(!z){var j=(u.current.shards||[]).map(i0).filter(Boolean).filter(function(C){return C.contains(E.target)}),M=j.length>0?h(E,j[0]):!u.current.noIsolation;M&&E.cancelable&&E.preventDefault()}}},[]),p=w.useCallback(function(A,E,_,z){var j={name:A,delta:E,target:_,should:z,shadowParent:dO(_)};a.current.push(j),setTimeout(function(){a.current=a.current.filter(function(M){return M!==j})},1)},[]),g=w.useCallback(function(A){s.current=So(A),l.current=void 0},[]),v=w.useCallback(function(A){p(A.type,a0(A),A.target,h(A,n.lockRef.current))},[]),b=w.useCallback(function(A){p(A.type,So(A),A.target,h(A,n.lockRef.current))},[]);w.useEffect(function(){return as.push(d),n.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:b}),document.addEventListener("wheel",m,ns),document.addEventListener("touchmove",m,ns),document.addEventListener("touchstart",g,ns),function(){as=as.filter(function(A){return A!==d}),document.removeEventListener("wheel",m,ns),document.removeEventListener("touchmove",m,ns),document.removeEventListener("touchstart",g,ns)}},[]);var T=n.removeScrollBar,D=n.inert;return w.createElement(w.Fragment,null,D?w.createElement(d,{styles:cO(c)}):null,T?w.createElement(tO,{gapMode:n.gapMode}):null)}function dO(n){for(var a=null;n!==null;)n instanceof ShadowRoot&&(a=n.host,n=n.host),n=n.parentNode;return a}const hO=PD(Px,fO);var Kx=w.forwardRef(function(n,a){return w.createElement(Fo,Mn({},n,{ref:a,sideCar:hO}))});Kx.classNames=Fo.classNames;var mO=[" ","Enter","ArrowUp","ArrowDown"],pO=[" ","Enter"],ui="Select",[Io,$o,gO]=cx(ui),[Ss,l3]=Xr(ui,[gO,Cx]),Wo=Cx(),[yO,Va]=Ss(ui),[vO,bO]=Ss(ui),Zx=n=>{const{__scopeSelect:a,children:s,open:l,defaultOpen:c,onOpenChange:d,value:u,defaultValue:h,onValueChange:m,dir:p,name:g,autoComplete:v,disabled:b,required:T,form:D}=n,A=Wo(a),[E,_]=w.useState(null),[z,j]=w.useState(null),[M,C]=w.useState(!1),X=ih(p),[W,H]=Po({prop:l,defaultProp:c!=null?c:!1,onChange:d,caller:ui}),[$,ct]=Po({prop:u,defaultProp:h,onChange:m,caller:ui}),ht=w.useRef(null),lt=E?D||!!E.closest("form"):!0,[bt,Et]=w.useState(new Set),ut=Array.from(bt).map(B=>B.props.value).join(";");return S.jsx(yD,Z(N({},A),{children:S.jsxs(yO,{required:T,scope:a,trigger:E,onTriggerChange:_,valueNode:z,onValueNodeChange:j,valueNodeHasChildren:M,onValueNodeHasChildrenChange:C,contentId:Kr(),value:$,onValueChange:ct,open:W,onOpenChange:H,dir:X,triggerPointerDownPosRef:ht,disabled:b,children:[S.jsx(Io.Provider,{scope:a,children:S.jsx(vO,{scope:n.__scopeSelect,onNativeOptionAdd:w.useCallback(B=>{Et(q=>new Set(q).add(B))},[]),onNativeOptionRemove:w.useCallback(B=>{Et(q=>{const G=new Set(q);return G.delete(B),G})},[]),children:s})}),lt?S.jsxs(pS,{"aria-hidden":!0,required:T,tabIndex:-1,name:g,autoComplete:v,value:$,onChange:B=>ct(B.target.value),disabled:b,form:D,children:[$===void 0?S.jsx("option",{value:""}):null,Array.from(bt)]},ut):null]})}))};Zx.displayName=ui;var Qx="SelectTrigger",Fx=w.forwardRef((n,a)=>{const A=n,{__scopeSelect:s,disabled:l=!1}=A,c=et(A,["__scopeSelect","disabled"]),d=Wo(s),u=Va(Qx,s),h=u.disabled||l,m=ie(a,u.onTriggerChange),p=$o(s),g=w.useRef("touch"),[v,b,T]=yS(E=>{const _=p().filter(M=>!M.disabled),z=_.find(M=>M.value===u.value),j=vS(_,E,z);j!==void 0&&u.onValueChange(j.value)}),D=E=>{h||(u.onOpenChange(!0),T()),E&&(u.triggerPointerDownPosRef.current={x:Math.round(E.pageX),y:Math.round(E.pageY)})};return S.jsx(vD,Z(N({asChild:!0},d),{children:S.jsx(Pt.button,Z(N({type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":gS(u.value)?"":void 0},c),{ref:m,onClick:Bt(c.onClick,E=>{E.currentTarget.focus(),g.current!=="mouse"&&D(E)}),onPointerDown:Bt(c.onPointerDown,E=>{g.current=E.pointerType;const _=E.target;_.hasPointerCapture(E.pointerId)&&_.releasePointerCapture(E.pointerId),E.button===0&&E.ctrlKey===!1&&E.pointerType==="mouse"&&(D(E),E.preventDefault())}),onKeyDown:Bt(c.onKeyDown,E=>{const _=v.current!=="";!(E.ctrlKey||E.altKey||E.metaKey)&&E.key.length===1&&b(E.key),!(_&&E.key===" ")&&mO.includes(E.key)&&(D(),E.preventDefault())})}))}))});Fx.displayName=Qx;var Ix="SelectValue",$x=w.forwardRef((n,a)=>{const b=n,{__scopeSelect:s,className:l,style:c,children:d,placeholder:u=""}=b,h=et(b,["__scopeSelect","className","style","children","placeholder"]),m=Va(Ix,s),{onValueNodeHasChildrenChange:p}=m,g=d!==void 0,v=ie(a,m.onValueNodeChange);return we(()=>{p(g)},[p,g]),S.jsx(Pt.span,Z(N({},h),{ref:v,style:{pointerEvents:"none"},children:gS(m.value)?S.jsx(S.Fragment,{children:u}):d}))});$x.displayName=Ix;var xO="SelectIcon",Wx=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s,children:l}=d,c=et(d,["__scopeSelect","children"]);return S.jsx(Pt.span,Z(N({"aria-hidden":!0},c),{ref:a,children:l||"▼"}))});Wx.displayName=xO;var SO="SelectPortal",Jx=n=>S.jsx(Lx,N({asChild:!0},n));Jx.displayName=SO;var fi="SelectContent",tS=w.forwardRef((n,a)=>{const s=Va(fi,n.__scopeSelect),[l,c]=w.useState();if(we(()=>{c(new DocumentFragment)},[]),!s.open){const d=l;return d?qr.createPortal(S.jsx(eS,{scope:n.__scopeSelect,children:S.jsx(Io.Slot,{scope:n.__scopeSelect,children:S.jsx("div",{children:n.children})})}),d):null}return S.jsx(nS,Z(N({},n),{ref:a}))});tS.displayName=fi;var hn=10,[eS,za]=Ss(fi),TO="SelectContentImpl",wO=Br("SelectContent.RemoveScroll"),nS=w.forwardRef((n,a)=>{const xt=n,{__scopeSelect:s,position:l="item-aligned",onCloseAutoFocus:c,onEscapeKeyDown:d,onPointerDownOutside:u,side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:D,hideWhenDetached:A,avoidCollisions:E}=xt,_=et(xt,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),z=Va(fi,s),[j,M]=w.useState(null),[C,X]=w.useState(null),W=ie(a,nt=>M(nt)),[H,$]=w.useState(null),[ct,ht]=w.useState(null),lt=$o(s),[bt,Et]=w.useState(!1),ut=w.useRef(!1);w.useEffect(()=>{if(j)return ND(j)},[j]),eR();const B=w.useCallback(nt=>{const[Tt,...qt]=lt().map(jt=>jt.ref.current),[Ot]=qt.slice(-1),Ct=document.activeElement;for(const jt of nt)if(jt===Ct||(jt==null||jt.scrollIntoView({block:"nearest"}),jt===Tt&&C&&(C.scrollTop=0),jt===Ot&&C&&(C.scrollTop=C.scrollHeight),jt==null||jt.focus(),document.activeElement!==Ct))return},[lt,C]),q=w.useCallback(()=>B([H,j]),[B,H,j]);w.useEffect(()=>{bt&&q()},[bt,q]);const{onOpenChange:G,triggerPointerDownPosRef:ft}=z;w.useEffect(()=>{if(j){let nt={x:0,y:0};const Tt=Ot=>{var Ct,jt,de,Ae;nt={x:Math.abs(Math.round(Ot.pageX)-((jt=(Ct=ft.current)==null?void 0:Ct.x)!=null?jt:0)),y:Math.abs(Math.round(Ot.pageY)-((Ae=(de=ft.current)==null?void 0:de.y)!=null?Ae:0))}},qt=Ot=>{nt.x<=10&&nt.y<=10?Ot.preventDefault():j.contains(Ot.target)||G(!1),document.removeEventListener("pointermove",Tt),ft.current=null};return ft.current!==null&&(document.addEventListener("pointermove",Tt),document.addEventListener("pointerup",qt,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",Tt),document.removeEventListener("pointerup",qt,{capture:!0})}}},[j,G,ft]),w.useEffect(()=>{const nt=()=>G(!1);return window.addEventListener("blur",nt),window.addEventListener("resize",nt),()=>{window.removeEventListener("blur",nt),window.removeEventListener("resize",nt)}},[G]);const[O,K]=yS(nt=>{const Tt=lt().filter(Ct=>!Ct.disabled),qt=Tt.find(Ct=>Ct.ref.current===document.activeElement),Ot=vS(Tt,nt,qt);Ot&&setTimeout(()=>Ot.ref.current.focus())}),tt=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(z.value!==void 0&&z.value===Tt||Ot)&&($(nt),Ot&&(ut.current=!0))},[z.value]),J=w.useCallback(()=>j==null?void 0:j.focus(),[j]),at=w.useCallback((nt,Tt,qt)=>{const Ot=!ut.current&&!qt;(z.value!==void 0&&z.value===Tt||Ot)&&ht(nt)},[z.value]),vt=l==="popper"?vd:aS,dt=vt===vd?{side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:D,hideWhenDetached:A,avoidCollisions:E}:{};return S.jsx(eS,{scope:s,content:j,viewport:C,onViewportChange:X,itemRefCallback:tt,selectedItem:H,onItemLeave:J,itemTextRefCallback:at,focusSelectedItem:q,selectedItemText:ct,position:l,isPositioned:bt,searchRef:O,children:S.jsx(Kx,{as:wO,allowPinchZoom:!0,children:S.jsx(hx,{asChild:!0,trapped:z.open,onMountAutoFocus:nt=>{nt.preventDefault()},onUnmountAutoFocus:Bt(c,nt=>{var Tt;(Tt=z.trigger)==null||Tt.focus({preventScroll:!0}),nt.preventDefault()}),children:S.jsx(fx,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:u,onFocusOutside:nt=>nt.preventDefault(),onDismiss:()=>z.onOpenChange(!1),children:S.jsx(vt,Z(N(N({role:"listbox",id:z.contentId,"data-state":z.open?"open":"closed",dir:z.dir,onContextMenu:nt=>nt.preventDefault()},_),dt),{onPlaced:()=>Et(!0),ref:W,style:N({display:"flex",flexDirection:"column",outline:"none"},_.style),onKeyDown:Bt(_.onKeyDown,nt=>{const Tt=nt.ctrlKey||nt.altKey||nt.metaKey;if(nt.key==="Tab"&&nt.preventDefault(),!Tt&&nt.key.length===1&&K(nt.key),["ArrowUp","ArrowDown","Home","End"].includes(nt.key)){let Ot=lt().filter(Ct=>!Ct.disabled).map(Ct=>Ct.ref.current);if(["ArrowUp","End"].includes(nt.key)&&(Ot=Ot.slice().reverse()),["ArrowUp","ArrowDown"].includes(nt.key)){const Ct=nt.target,jt=Ot.indexOf(Ct);Ot=Ot.slice(jt+1)}setTimeout(()=>B(Ot)),nt.preventDefault()}})}))})})})})});nS.displayName=TO;var AO="SelectItemAlignedPosition",aS=w.forwardRef((n,a)=>{const W=n,{__scopeSelect:s,onPlaced:l}=W,c=et(W,["__scopeSelect","onPlaced"]),d=Va(fi,s),u=za(fi,s),[h,m]=w.useState(null),[p,g]=w.useState(null),v=ie(a,H=>g(H)),b=$o(s),T=w.useRef(!1),D=w.useRef(!0),{viewport:A,selectedItem:E,selectedItemText:_,focusSelectedItem:z}=u,j=w.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&p&&A&&E&&_){const H=d.trigger.getBoundingClientRect(),$=p.getBoundingClientRect(),ct=d.valueNode.getBoundingClientRect(),ht=_.getBoundingClientRect();if(d.dir!=="rtl"){const Ct=ht.left-$.left,jt=ct.left-Ct,de=H.left-jt,Ae=H.width+de,ln=Math.max(Ae,$.width),he=window.innerWidth-hn,La=Uv(jt,[hn,Math.max(hn,he-ln)]);h.style.minWidth=Ae+"px",h.style.left=La+"px"}else{const Ct=$.right-ht.right,jt=window.innerWidth-ct.right-Ct,de=window.innerWidth-H.right-jt,Ae=H.width+de,ln=Math.max(Ae,$.width),he=window.innerWidth-hn,La=Uv(jt,[hn,Math.max(hn,he-ln)]);h.style.minWidth=Ae+"px",h.style.right=La+"px"}const lt=b(),bt=window.innerHeight-hn*2,Et=A.scrollHeight,ut=window.getComputedStyle(p),B=parseInt(ut.borderTopWidth,10),q=parseInt(ut.paddingTop,10),G=parseInt(ut.borderBottomWidth,10),ft=parseInt(ut.paddingBottom,10),O=B+q+Et+ft+G,K=Math.min(E.offsetHeight*5,O),tt=window.getComputedStyle(A),J=parseInt(tt.paddingTop,10),at=parseInt(tt.paddingBottom,10),vt=H.top+H.height/2-hn,dt=bt-vt,xt=E.offsetHeight/2,nt=E.offsetTop+xt,Tt=B+q+nt,qt=O-Tt;if(Tt<=vt){const Ct=lt.length>0&&E===lt[lt.length-1].ref.current;h.style.bottom="0px";const jt=p.clientHeight-A.offsetTop-A.offsetHeight,de=Math.max(dt,xt+(Ct?at:0)+jt+G),Ae=Tt+de;h.style.height=Ae+"px"}else{const Ct=lt.length>0&&E===lt[0].ref.current;h.style.top="0px";const de=Math.max(vt,B+A.offsetTop+(Ct?J:0)+xt)+qt;h.style.height=de+"px",A.scrollTop=Tt-vt+A.offsetTop}h.style.margin=`${hn}px 0`,h.style.minHeight=K+"px",h.style.maxHeight=bt+"px",l==null||l(),requestAnimationFrame(()=>T.current=!0)}},[b,d.trigger,d.valueNode,h,p,A,E,_,d.dir,l]);we(()=>j(),[j]);const[M,C]=w.useState();we(()=>{p&&C(window.getComputedStyle(p).zIndex)},[p]);const X=w.useCallback(H=>{H&&D.current===!0&&(j(),z==null||z(),D.current=!1)},[j,z]);return S.jsx(MO,{scope:s,contentWrapper:h,shouldExpandOnScrollRef:T,onScrollButtonChange:X,children:S.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:S.jsx(Pt.div,Z(N({},c),{ref:v,style:N({boxSizing:"border-box",maxHeight:"100%"},c.style)}))})})});aS.displayName=AO;var EO="SelectPopperPosition",vd=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s,align:l="start",collisionPadding:c=hn}=h,d=et(h,["__scopeSelect","align","collisionPadding"]),u=Wo(s);return S.jsx(bD,Z(N(N({},u),d),{ref:a,align:l,collisionPadding:c,style:Z(N({boxSizing:"border-box"},d.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});vd.displayName=EO;var[MO,mh]=Ss(fi,{}),bd="SelectViewport",iS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,nonce:l}=p,c=et(p,["__scopeSelect","nonce"]),d=za(bd,s),u=mh(bd,s),h=ie(a,d.onViewportChange),m=w.useRef(0);return S.jsxs(S.Fragment,{children:[S.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),S.jsx(Io.Slot,{scope:s,children:S.jsx(Pt.div,Z(N({"data-radix-select-viewport":"",role:"presentation"},c),{ref:h,style:N({position:"relative",flex:1,overflow:"hidden auto"},c.style),onScroll:Bt(c.onScroll,g=>{const v=g.currentTarget,{contentWrapper:b,shouldExpandOnScrollRef:T}=u;if(T!=null&&T.current&&b){const D=Math.abs(m.current-v.scrollTop);if(D>0){const A=window.innerHeight-hn*2,E=parseFloat(b.style.minHeight),_=parseFloat(b.style.height),z=Math.max(E,_);if(z<A){const j=z+D,M=Math.min(A,j),C=j-M;b.style.height=M+"px",b.style.bottom==="0px"&&(v.scrollTop=C>0?C:0,b.style.justifyContent="flex-end")}}}m.current=v.scrollTop})}))})]})});iS.displayName=bd;var sS="SelectGroup",[CO,RO]=Ss(sS),DO=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),c=Kr();return S.jsx(CO,{scope:s,id:c,children:S.jsx(Pt.div,Z(N({role:"group","aria-labelledby":c},l),{ref:a}))})});DO.displayName=sS;var rS="SelectLabel",OO=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]),c=RO(rS,s);return S.jsx(Pt.div,Z(N({id:c.id},l),{ref:a}))});OO.displayName=rS;var Go="SelectItem",[NO,lS]=Ss(Go),oS=w.forwardRef((n,a)=>{const z=n,{__scopeSelect:s,value:l,disabled:c=!1,textValue:d}=z,u=et(z,["__scopeSelect","value","disabled","textValue"]),h=Va(Go,s),m=za(Go,s),p=h.value===l,[g,v]=w.useState(d!=null?d:""),[b,T]=w.useState(!1),D=ie(a,j=>{var M;return(M=m.itemRefCallback)==null?void 0:M.call(m,j,l,c)}),A=Kr(),E=w.useRef("touch"),_=()=>{c||(h.onValueChange(l),h.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return S.jsx(NO,{scope:s,value:l,disabled:c,textId:A,isSelected:p,onItemTextChange:w.useCallback(j=>{v(M=>{var C;return M||((C=j==null?void 0:j.textContent)!=null?C:"").trim()})},[]),children:S.jsx(Io.ItemSlot,{scope:s,value:l,disabled:c,textValue:g,children:S.jsx(Pt.div,Z(N({role:"option","aria-labelledby":A,"data-highlighted":b?"":void 0,"aria-selected":p&&b,"data-state":p?"checked":"unchecked","aria-disabled":c||void 0,"data-disabled":c?"":void 0,tabIndex:c?void 0:-1},u),{ref:D,onFocus:Bt(u.onFocus,()=>T(!0)),onBlur:Bt(u.onBlur,()=>T(!1)),onClick:Bt(u.onClick,()=>{E.current!=="mouse"&&_()}),onPointerUp:Bt(u.onPointerUp,()=>{E.current==="mouse"&&_()}),onPointerDown:Bt(u.onPointerDown,j=>{E.current=j.pointerType}),onPointerMove:Bt(u.onPointerMove,j=>{var M;E.current=j.pointerType,c?(M=m.onItemLeave)==null||M.call(m):E.current==="mouse"&&j.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Bt(u.onPointerLeave,j=>{var M;j.currentTarget===document.activeElement&&((M=m.onItemLeave)==null||M.call(m))}),onKeyDown:Bt(u.onKeyDown,j=>{var C;((C=m.searchRef)==null?void 0:C.current)!==""&&j.key===" "||(pO.includes(j.key)&&_(),j.key===" "&&j.preventDefault())})}))})})});oS.displayName=Go;var Ar="SelectItemText",cS=w.forwardRef((n,a)=>{const _=n,{__scopeSelect:s,className:l,style:c}=_,d=et(_,["__scopeSelect","className","style"]),u=Va(Ar,s),h=za(Ar,s),m=lS(Ar,s),p=bO(Ar,s),[g,v]=w.useState(null),b=ie(a,z=>v(z),m.onItemTextChange,z=>{var j;return(j=h.itemTextRefCallback)==null?void 0:j.call(h,z,m.value,m.disabled)}),T=g==null?void 0:g.textContent,D=w.useMemo(()=>S.jsx("option",{value:m.value,disabled:m.disabled,children:T},m.value),[m.disabled,m.value,T]),{onNativeOptionAdd:A,onNativeOptionRemove:E}=p;return we(()=>(A(D),()=>E(D)),[A,E,D]),S.jsxs(S.Fragment,{children:[S.jsx(Pt.span,Z(N({id:m.textId},d),{ref:b})),m.isSelected&&u.valueNode&&!u.valueNodeHasChildren?qr.createPortal(d.children,u.valueNode):null]})});cS.displayName=Ar;var uS="SelectItemIndicator",fS=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=et(d,["__scopeSelect"]);return lS(uS,s).isSelected?S.jsx(Pt.span,Z(N({"aria-hidden":!0},l),{ref:a})):null});fS.displayName=uS;var xd="SelectScrollUpButton",dS=w.forwardRef((n,a)=>{const s=za(xd,n.__scopeSelect),l=mh(xd,n.__scopeSelect),[c,d]=w.useState(!1),u=ie(a,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollTop>0;d(p)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),c?S.jsx(mS,Z(N({},n),{ref:u,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop-m.offsetHeight)}})):null});dS.displayName=xd;var Sd="SelectScrollDownButton",hS=w.forwardRef((n,a)=>{const s=za(Sd,n.__scopeSelect),l=mh(Sd,n.__scopeSelect),[c,d]=w.useState(!1),u=ie(a,l.onScrollButtonChange);return we(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollHeight-m.clientHeight,g=Math.ceil(m.scrollTop)<p;d(g)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),c?S.jsx(mS,Z(N({},n),{ref:u,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop+m.offsetHeight)}})):null});hS.displayName=Sd;var mS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,onAutoScroll:l}=p,c=et(p,["__scopeSelect","onAutoScroll"]),d=za("SelectScrollButton",s),u=w.useRef(null),h=$o(s),m=w.useCallback(()=>{u.current!==null&&(window.clearInterval(u.current),u.current=null)},[]);return w.useEffect(()=>()=>m(),[m]),we(()=>{var v;const g=h().find(b=>b.ref.current===document.activeElement);(v=g==null?void 0:g.ref.current)==null||v.scrollIntoView({block:"nearest"})},[h]),S.jsx(Pt.div,Z(N({"aria-hidden":!0},c),{ref:a,style:N({flexShrink:0},c.style),onPointerDown:Bt(c.onPointerDown,()=>{u.current===null&&(u.current=window.setInterval(l,50))}),onPointerMove:Bt(c.onPointerMove,()=>{var g;(g=d.onItemLeave)==null||g.call(d),u.current===null&&(u.current=window.setInterval(l,50))}),onPointerLeave:Bt(c.onPointerLeave,()=>{m()})}))}),jO="SelectSeparator",_O=w.forwardRef((n,a)=>{const c=n,{__scopeSelect:s}=c,l=et(c,["__scopeSelect"]);return S.jsx(Pt.div,Z(N({"aria-hidden":!0},l),{ref:a}))});_O.displayName=jO;var Td="SelectArrow",VO=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s}=h,l=et(h,["__scopeSelect"]),c=Wo(s),d=Va(Td,s),u=za(Td,s);return d.open&&u.position==="popper"?S.jsx(xD,Z(N(N({},c),l),{ref:a})):null});VO.displayName=Td;var zO="SelectBubbleInput",pS=w.forwardRef((c,l)=>{var d=c,{__scopeSelect:n,value:a}=d,s=et(d,["__scopeSelect","value"]);const u=w.useRef(null),h=ie(l,u),m=ED(a);return w.useEffect(()=>{const p=u.current;if(!p)return;const g=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(g,"value").set;if(m!==a&&b){const T=new Event("change",{bubbles:!0});b.call(p,a),p.dispatchEvent(T)}},[m,a]),S.jsx(Pt.select,Z(N({},s),{style:N(N({},Bx),s.style),ref:h,defaultValue:a}))});pS.displayName=zO;function gS(n){return n===""||n===void 0}function yS(n){const a=Oa(n),s=w.useRef(""),l=w.useRef(0),c=w.useCallback(u=>{const h=s.current+u;a(h),function m(p){s.current=p,window.clearTimeout(l.current),p!==""&&(l.current=window.setTimeout(()=>m(""),1e3))}(h)},[a]),d=w.useCallback(()=>{s.current="",window.clearTimeout(l.current)},[]);return w.useEffect(()=>()=>window.clearTimeout(l.current),[]),[s,c,d]}function vS(n,a,s){const c=a.length>1&&Array.from(a).every(p=>p===a[0])?a[0]:a,d=s?n.indexOf(s):-1;let u=LO(n,Math.max(d,0));c.length===1&&(u=u.filter(p=>p!==s));const m=u.find(p=>p.textValue.toLowerCase().startsWith(c.toLowerCase()));return m!==s?m:void 0}function LO(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var BO=Zx,UO=Fx,kO=$x,HO=Wx,PO=Jx,GO=tS,YO=iS,qO=oS,XO=cS,KO=fS,ZO=dS,QO=hS;/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FO=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),IO=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,s,l)=>l?l.toUpperCase():s.toLowerCase()),s0=n=>{const a=IO(n);return a.charAt(0).toUpperCase()+a.slice(1)},bS=(...n)=>n.filter((a,s,l)=>!!a&&a.trim()!==""&&l.indexOf(a)===s).join(" ").trim(),$O=n=>{for(const a in n)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var WO={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JO=w.forwardRef((p,m)=>{var g=p,{color:n="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u}=g,h=et(g,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return w.createElement("svg",N(N(Z(N({ref:m},WO),{width:a,height:a,stroke:n,strokeWidth:l?Number(s)*24/Number(a):s,className:bS("lucide",c)}),!d&&!$O(h)&&{"aria-hidden":"true"}),h),[...u.map(([v,b])=>w.createElement(v,b)),...Array.isArray(d)?d:[d]])});/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=(n,a)=>{const s=w.forwardRef((u,d)=>{var h=u,{className:l}=h,c=et(h,["className"]);return w.createElement(JO,N({ref:d,iconNode:a,className:bS(`lucide-${FO(s0(n))}`,`lucide-${n}`,l)},c))});return s.displayName=s0(n),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],eN=Ce("building",tN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],aN=Ce("calendar",nN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],sN=Ce("check",iN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xS=Ce("chevron-down",rN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],oN=Ce("chevron-up",lN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],r0=Ce("circle-alert",cN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],l0=Ce("circle-check-big",uN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],o0=Ce("log-out",fN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dN=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],hN=Ce("refresh-cw",dN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],pN=Ce("search",mN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gN=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],yN=Ce("settings",gN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vN=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],bN=Ce("sparkles",vN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],SN=Ce("user-plus",xN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TN=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],wN=Ce("user",TN);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AN=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],EN=Ce("users",AN);function MN(a){var n=et(a,[]);return S.jsx(BO,N({"data-slot":"select"},n))}function CN(a){var n=et(a,[]);return S.jsx(kO,N({"data-slot":"select-value"},n))}function RN(c){var d=c,{className:n,size:a="default",children:s}=d,l=et(d,["className","size","children"]);return S.jsxs(UO,Z(N({"data-slot":"select-trigger","data-size":a,className:Kt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},l),{children:[s,S.jsx(HO,{asChild:!0,children:S.jsx(xS,{className:"size-4 opacity-50"})})]}))}function DN(c){var d=c,{className:n,children:a,position:s="popper"}=d,l=et(d,["className","children","position"]);return S.jsx(PO,{children:S.jsxs(GO,Z(N({"data-slot":"select-content",className:Kt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:s},l),{children:[S.jsx(NN,{}),S.jsx(YO,{className:Kt("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),S.jsx(jN,{})]}))})}function ON(l){var c=l,{className:n,children:a}=c,s=et(c,["className","children"]);return S.jsxs(qO,Z(N({"data-slot":"select-item",className:Kt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n)},s),{children:[S.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:S.jsx(KO,{children:S.jsx(sN,{className:"size-4"})})}),S.jsx(XO,{children:a})]}))}function NN(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(ZO,Z(N({"data-slot":"select-scroll-up-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:S.jsx(oN,{className:"size-4"})}))}function jN(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(QO,Z(N({"data-slot":"select-scroll-down-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:S.jsx(xS,{className:"size-4"})}))}function c0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:S.jsx("table",N({"data-slot":"table",className:Kt("w-full caption-bottom text-sm",n)},a))})}function u0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("thead",N({"data-slot":"table-header",className:Kt("[&_tr]:border-b",n)},a))}function f0(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("tbody",N({"data-slot":"table-body",className:Kt("[&_tr:last-child]:border-0",n)},a))}function Gf(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("tr",N({"data-slot":"table-row",className:Kt("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",n)},a))}function nn(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("th",N({"data-slot":"table-head",className:Kt("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}function an(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx("td",N({"data-slot":"table-cell",className:Kt("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}const _N=$b("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d0(c){var d=c,{className:n,variant:a,asChild:s=!1}=d,l=et(d,["className","variant","asChild"]);const u=s?Qb:"span";return S.jsx(u,N({"data-slot":"badge",className:Kt(_N({variant:a}),n)},l))}var Yf="rovingFocusGroup.onEntryFocus",VN={bubbles:!1,cancelable:!0},Qr="RovingFocusGroup",[wd,SS,zN]=cx(Qr),[LN,TS]=Xr(Qr,[zN]),[BN,UN]=LN(Qr),wS=w.forwardRef((n,a)=>S.jsx(wd.Provider,{scope:n.__scopeRovingFocusGroup,children:S.jsx(wd.Slot,{scope:n.__scopeRovingFocusGroup,children:S.jsx(kN,Z(N({},n),{ref:a}))})}));wS.displayName=Qr;var kN=w.forwardRef((n,a)=>{const H=n,{__scopeRovingFocusGroup:s,orientation:l,loop:c=!1,dir:d,currentTabStopId:u,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:g=!1}=H,v=et(H,["__scopeRovingFocusGroup","orientation","loop","dir","currentTabStopId","defaultCurrentTabStopId","onCurrentTabStopIdChange","onEntryFocus","preventScrollOnEntryFocus"]),b=w.useRef(null),T=ie(a,b),D=ih(d),[A,E]=Po({prop:u,defaultProp:h!=null?h:null,onChange:m,caller:Qr}),[_,z]=w.useState(!1),j=Oa(p),M=SS(s),C=w.useRef(!1),[X,W]=w.useState(0);return w.useEffect(()=>{const $=b.current;if($)return $.addEventListener(Yf,j),()=>$.removeEventListener(Yf,j)},[j]),S.jsx(BN,{scope:s,orientation:l,dir:D,loop:c,currentTabStopId:A,onItemFocus:w.useCallback($=>E($),[E]),onItemShiftTab:w.useCallback(()=>z(!0),[]),onFocusableItemAdd:w.useCallback(()=>W($=>$+1),[]),onFocusableItemRemove:w.useCallback(()=>W($=>$-1),[]),children:S.jsx(Pt.div,Z(N({tabIndex:_||X===0?-1:0,"data-orientation":l},v),{ref:T,style:N({outline:"none"},n.style),onMouseDown:Bt(n.onMouseDown,()=>{C.current=!0}),onFocus:Bt(n.onFocus,$=>{const ct=!C.current;if($.target===$.currentTarget&&ct&&!_){const ht=new CustomEvent(Yf,VN);if($.currentTarget.dispatchEvent(ht),!ht.defaultPrevented){const lt=M().filter(q=>q.focusable),bt=lt.find(q=>q.active),Et=lt.find(q=>q.id===A),B=[bt,Et,...lt].filter(Boolean).map(q=>q.ref.current);MS(B,g)}}C.current=!1}),onBlur:Bt(n.onBlur,()=>z(!1))}))})}),AS="RovingFocusGroupItem",ES=w.forwardRef((n,a)=>{const E=n,{__scopeRovingFocusGroup:s,focusable:l=!0,active:c=!1,tabStopId:d,children:u}=E,h=et(E,["__scopeRovingFocusGroup","focusable","active","tabStopId","children"]),m=Kr(),p=d||m,g=UN(AS,s),v=g.currentTabStopId===p,b=SS(s),{onFocusableItemAdd:T,onFocusableItemRemove:D,currentTabStopId:A}=g;return w.useEffect(()=>{if(l)return T(),()=>D()},[l,T,D]),S.jsx(wd.ItemSlot,{scope:s,id:p,focusable:l,active:c,children:S.jsx(Pt.span,Z(N({tabIndex:v?0:-1,"data-orientation":g.orientation},h),{ref:a,onMouseDown:Bt(n.onMouseDown,_=>{l?g.onItemFocus(p):_.preventDefault()}),onFocus:Bt(n.onFocus,()=>g.onItemFocus(p)),onKeyDown:Bt(n.onKeyDown,_=>{if(_.key==="Tab"&&_.shiftKey){g.onItemShiftTab();return}if(_.target!==_.currentTarget)return;const z=GN(_,g.orientation,g.dir);if(z!==void 0){if(_.metaKey||_.ctrlKey||_.altKey||_.shiftKey)return;_.preventDefault();let M=b().filter(C=>C.focusable).map(C=>C.ref.current);if(z==="last")M.reverse();else if(z==="prev"||z==="next"){z==="prev"&&M.reverse();const C=M.indexOf(_.currentTarget);M=g.loop?YN(M,C+1):M.slice(C+1)}setTimeout(()=>MS(M))}}),children:typeof u=="function"?u({isCurrentTabStop:v,hasTabStop:A!=null}):u}))})});ES.displayName=AS;var HN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function PN(n,a){return a!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function GN(n,a,s){const l=PN(n.key,s);if(!(a==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(a==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return HN[l]}function MS(n,a=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:a}),document.activeElement!==s))return}function YN(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var qN=wS,XN=ES;function KN(n,a){return w.useReducer((s,l)=>{const c=a[s][l];return c!=null?c:s},n)}var CS=n=>{const{present:a,children:s}=n,l=ZN(a),c=typeof s=="function"?s({present:l.isPresent}):w.Children.only(s),d=ie(l.ref,QN(c));return typeof s=="function"||l.isPresent?w.cloneElement(c,{ref:d}):null};CS.displayName="Presence";function ZN(n){const[a,s]=w.useState(),l=w.useRef(null),c=w.useRef(n),d=w.useRef("none"),u=n?"mounted":"unmounted",[h,m]=KN(u,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const p=To(l.current);d.current=h==="mounted"?p:"none"},[h]),we(()=>{const p=l.current,g=c.current;if(g!==n){const b=d.current,T=To(p);n?m("MOUNT"):T==="none"||(p==null?void 0:p.display)==="none"?m("UNMOUNT"):m(g&&b!==T?"ANIMATION_OUT":"UNMOUNT"),c.current=n}},[n,m]),we(()=>{var p;if(a){let g;const v=(p=a.ownerDocument.defaultView)!=null?p:window,b=D=>{const E=To(l.current).includes(D.animationName);if(D.target===a&&E&&(m("ANIMATION_END"),!c.current)){const _=a.style.animationFillMode;a.style.animationFillMode="forwards",g=v.setTimeout(()=>{a.style.animationFillMode==="forwards"&&(a.style.animationFillMode=_)})}},T=D=>{D.target===a&&(d.current=To(l.current))};return a.addEventListener("animationstart",T),a.addEventListener("animationcancel",b),a.addEventListener("animationend",b),()=>{v.clearTimeout(g),a.removeEventListener("animationstart",T),a.removeEventListener("animationcancel",b),a.removeEventListener("animationend",b)}}else m("ANIMATION_END")},[a,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(p=>{l.current=p?getComputedStyle(p):null,s(p)},[])}}function To(n){return(n==null?void 0:n.animationName)||"none"}function QN(n){var l,c;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var Jo="Tabs",[FN,o3]=Xr(Jo,[TS]),RS=TS(),[IN,ph]=FN(Jo),DS=w.forwardRef((n,a)=>{const T=n,{__scopeTabs:s,value:l,onValueChange:c,defaultValue:d,orientation:u="horizontal",dir:h,activationMode:m="automatic"}=T,p=et(T,["__scopeTabs","value","onValueChange","defaultValue","orientation","dir","activationMode"]),g=ih(h),[v,b]=Po({prop:l,onChange:c,defaultProp:d!=null?d:"",caller:Jo});return S.jsx(IN,{scope:s,baseId:Kr(),value:v,onValueChange:b,orientation:u,dir:g,activationMode:m,children:S.jsx(Pt.div,Z(N({dir:g,"data-orientation":u},p),{ref:a}))})});DS.displayName=Jo;var OS="TabsList",NS=w.forwardRef((n,a)=>{const h=n,{__scopeTabs:s,loop:l=!0}=h,c=et(h,["__scopeTabs","loop"]),d=ph(OS,s),u=RS(s);return S.jsx(qN,Z(N({asChild:!0},u),{orientation:d.orientation,dir:d.dir,loop:l,children:S.jsx(Pt.div,Z(N({role:"tablist","aria-orientation":d.orientation},c),{ref:a}))}))});NS.displayName=OS;var jS="TabsTrigger",_S=w.forwardRef((n,a)=>{const v=n,{__scopeTabs:s,value:l,disabled:c=!1}=v,d=et(v,["__scopeTabs","value","disabled"]),u=ph(jS,s),h=RS(s),m=LS(u.baseId,l),p=BS(u.baseId,l),g=l===u.value;return S.jsx(XN,Z(N({asChild:!0},h),{focusable:!c,active:g,children:S.jsx(Pt.button,Z(N({type:"button",role:"tab","aria-selected":g,"aria-controls":p,"data-state":g?"active":"inactive","data-disabled":c?"":void 0,disabled:c,id:m},d),{ref:a,onMouseDown:Bt(n.onMouseDown,b=>{!c&&b.button===0&&b.ctrlKey===!1?u.onValueChange(l):b.preventDefault()}),onKeyDown:Bt(n.onKeyDown,b=>{[" ","Enter"].includes(b.key)&&u.onValueChange(l)}),onFocus:Bt(n.onFocus,()=>{const b=u.activationMode!=="manual";!g&&!c&&b&&u.onValueChange(l)})}))}))});_S.displayName=jS;var VS="TabsContent",zS=w.forwardRef((n,a)=>{const b=n,{__scopeTabs:s,value:l,forceMount:c,children:d}=b,u=et(b,["__scopeTabs","value","forceMount","children"]),h=ph(VS,s),m=LS(h.baseId,l),p=BS(h.baseId,l),g=l===h.value,v=w.useRef(g);return w.useEffect(()=>{const T=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(T)},[]),S.jsx(CS,{present:c||g,children:({present:T})=>S.jsx(Pt.div,Z(N({"data-state":g?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":m,hidden:!T,id:p,tabIndex:0},u),{ref:a,style:Z(N({},n.style),{animationDuration:v.current?"0s":void 0}),children:T&&d}))})});zS.displayName=VS;function LS(n,a){return`${n}-trigger-${a}`}function BS(n,a){return`${n}-content-${a}`}var $N=DS,WN=NS,JN=_S,t3=zS;function e3(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx($N,N({"data-slot":"tabs",className:Kt("flex flex-col gap-2",n)},a))}function n3(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(WN,N({"data-slot":"tabs-list",className:Kt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n)},a))}function wo(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(JN,N({"data-slot":"tabs-trigger",className:Kt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},a))}function Ao(s){var l=s,{className:n}=l,a=et(l,["className"]);return S.jsx(t3,N({"data-slot":"tabs-content",className:Kt("flex-1 outline-none",n)},a))}function h0(n,a){const[s,l]=w.useState(()=>{try{const d=window.localStorage.getItem(n);if(d){const u=JSON.parse(d);return Array.isArray(u)&&n.includes("employees")?u.filter(h=>h.id&&h.name&&h.department&&h.startMonth):u}return a}catch(d){console.error(`خطأ في قراءة ${n} من localStorage:`,d);try{const u=`${n}_backup`,h=window.localStorage.getItem(u);if(h)return console.log(`استرداد البيانات من النسخة الاحتياطية: ${u}`),JSON.parse(h)}catch(u){console.error("خطأ في استرداد النسخة الاحتياطية:",u)}return a}});return[s,d=>{try{const u=d instanceof Function?d(s):d;if(s&&JSON.stringify(s)!==JSON.stringify(a)){const h=`${n}_backup`;window.localStorage.setItem(h,JSON.stringify(s))}l(u),window.localStorage.setItem(n,JSON.stringify(u)),window.localStorage.setItem(`${n}_lastUpdate`,new Date().toISOString())}catch(u){if(console.error(`خطأ في حفظ ${n} في localStorage:`,u),u.name==="QuotaExceededError"){console.log("محاولة تنظيف localStorage...");try{Object.keys(localStorage).forEach(h=>{h.includes("_backup")&&!h.includes(n)&&localStorage.removeItem(h)}),window.localStorage.setItem(n,JSON.stringify(valueToStore)),l(valueToStore)}catch(h){console.error("فشل في الحفظ حتى بعد التنظيف:",h)}}}}]}function a3(){const[n,a]=w.useState(!0),[s,l]=h0("work-attendance-employees",[]),[c,d]=w.useState({name:"",department:"",startMonth:""}),[u,h]=h0("work-attendance-search-filters",{name:"",department:"",startMonth:"",status:""}),m=["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"];w.useEffect(()=>{const M=setTimeout(()=>{a(!1)},4e3);return()=>clearTimeout(M)},[]),w.useEffect(()=>{const M=setInterval(()=>{if(s.length>0)try{const C=localStorage.getItem("work-attendance-employees_lastUpdate"),X=new Date().toISOString();(!C||new Date(X)-new Date(C)>3e4)&&(localStorage.setItem("work-attendance-employees",JSON.stringify(s)),localStorage.setItem("work-attendance-employees_lastUpdate",X),console.log("تم الحفظ التلقائي للبيانات"))}catch(C){console.error("خطأ في الحفظ التلقائي:",C)}},3e4);return()=>clearInterval(M)},[s]),w.useEffect(()=>{const M=C=>{try{localStorage.setItem("work-attendance-employees",JSON.stringify(s)),localStorage.setItem("work-attendance-search-filters",JSON.stringify(u)),console.log("تم حفظ البيانات قبل إغلاق النافذة")}catch(X){console.error("خطأ في حفظ البيانات قبل الإغلاق:",X)}};return window.addEventListener("beforeunload",M),()=>window.removeEventListener("beforeunload",M)},[s,u]),w.useEffect(()=>{const M=C=>{(C.ctrlKey&&C.key==="q"||C.altKey&&C.key==="F4")&&(C.preventDefault(),z())};return document.addEventListener("keydown",M),()=>document.removeEventListener("keydown",M)},[]);const p=M=>{const C=new Date().getMonth(),X=m.indexOf(M);return(C-X+12)%12>=3},g=M=>{if(M.preventDefault(),c.name&&c.department&&c.startMonth){const C=Z(N({id:Date.now()},c),{status:p(c.startMonth),createdAt:new Date().toISOString(),lastModified:new Date().toISOString()});l([...s,C]),d({name:"",department:"",startMonth:""}),A(`✅ تم حفظ بيانات الموظف "${C.name}" بنجاح!`,"success"),setTimeout(()=>{const X=localStorage.getItem("work-attendance-employees");X&&JSON.parse(X).find(W=>W.id===C.id)&&console.log("تأكيد: تم حفظ البيانات في التخزين المحلي")},100)}},v=()=>{l(s.map(M=>Z(N({},M),{status:p(M.startMonth)})))},b=()=>{const M={employees:s,exportDate:new Date().toISOString(),version:"1.0"},C=JSON.stringify(M,null,2),X=new Blob([C],{type:"application/json"}),W=document.createElement("a");W.href=URL.createObjectURL(X),W.download=`work-attendance-backup-${new Date().toISOString().split("T")[0]}.json`,W.click(),A("📁 تم تصدير البيانات بنجاح!","success")},T=(M=null)=>{const C=M||s;if(C.length===0){A("❌ لا توجد بيانات للتصدير!","error");return}const X="\uFEFF"+[["الرقم","اسم الموظف","القسم","أول شهر عمل","حالة الاستحقاق","تاريخ التصدير"].join(","),...C.map(($,ct)=>[ct+1,`"${$.name}"`,`"${$.department}"`,`"${$.startMonth}"`,$.status?"مطلوب كشف":"تم استلام الكشف",`"${new Date().toLocaleDateString("ar-SA")}"`].join(","))].join(`
`),W=new Blob([X],{type:"application/vnd.ms-excel;charset=utf-8"}),H=document.createElement("a");H.href=URL.createObjectURL(W),H.download=`كشف-الحضور-${new Date().toISOString().split("T")[0]}.csv`,H.click(),URL.revokeObjectURL(H.href),A(`📊 تم تصدير ${C.length} موظف إلى Excel بنجاح!`,"success")},D=M=>{const C=M.target.files[0];if(!C)return;const X=new FileReader;X.onload=W=>{try{const H=JSON.parse(W.target.result);H.employees&&Array.isArray(H.employees)?(l(H.employees),A("📥 تم استيراد البيانات بنجاح!","success")):A("❌ ملف غير صالح!","error")}catch(H){console.error("خطأ في استيراد البيانات:",H),A("❌ خطأ في قراءة الملف!","error")}},X.readAsText(C),M.target.value=""},A=(M,C="success")=>{const X=document.createElement("div"),W=C==="success"?"bg-green-500":"bg-red-500";X.className=`fixed top-4 right-4 ${W} text-white px-6 py-3 rounded-lg shadow-lg z-50`,X.textContent=M,document.body.appendChild(X),setTimeout(()=>{document.body.contains(X)&&document.body.removeChild(X)},3e3)},E=()=>{window.confirm("هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!")&&(l([]),h({name:"",department:"",startMonth:"",status:""}),A("🗑️ تم مسح جميع البيانات!","success"))},_=M=>{const C=s.find(X=>X.id===M);C&&window.confirm(`هل أنت متأكد من حذف الموظف "${C.name}"؟`)&&(l(s.filter(X=>X.id!==M)),A(`🗑️ تم حذف الموظف "${C.name}" بنجاح!`,"success"))},z=()=>{window.confirm(`هل تريد إغلاق البرنامج؟

سيتم حفظ جميع البيانات تلقائياً.`)&&(A("👋 شكراً لاستخدام البرنامج!","success"),setTimeout(()=>{try{window.close()}catch(M){try{self.close()}catch(C){window.location.href="about:blank"}}},1e3))},j=s.filter(M=>(!u.name||M.name.includes(u.name))&&(!u.department||M.department.includes(u.department))&&(!u.startMonth||M.startMonth.includes(u.startMonth))&&(!u.status||u.status.includes("مطلوب")&&M.status||u.status.includes("تم")&&!M.status));return n?S.jsx("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:S.jsxs(Sn.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1},className:"text-center",children:[S.jsx(Sn.h1,{className:"text-6xl font-bold text-white mb-8 typewriter",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"🌟 برنامج استحقاق كشف عمل 🌟"}),S.jsx(Sn.p,{className:"text-2xl text-white mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:"👨‍💻 المبرمج: علي عاجل خشان المحنّة"}),S.jsx(Sn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:S.jsx(en,{onClick:()=>a(!1),className:"pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4",children:"ابدأ الآن"})})]})}):S.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900",children:[S.jsxs("div",{className:"flex",children:[S.jsxs(Sn.div,{initial:{x:-300},animate:{x:0},className:"w-80 min-h-screen glassmorphism p-6",children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-2xl font-bold gradient-text mb-2",children:"برنامج استحقاق كشف عمل"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة"})]}),S.jsxs(en,{variant:"destructive",className:"w-full mb-6 hover-lift",onClick:z,children:[S.jsx(o0,{className:"mr-2 h-4 w-4"}),"خروج"]})]}),S.jsx("div",{className:"flex-1 p-8",children:S.jsxs(e3,{defaultValue:"input",className:"w-full",children:[S.jsxs(n3,{className:"grid w-full grid-cols-4 mb-8",children:[S.jsxs(wo,{value:"input",className:"flex items-center gap-2",children:[S.jsx(SN,{className:"h-4 w-4"}),"إدخال البيانات"]}),S.jsxs(wo,{value:"employees",className:"flex items-center gap-2",children:[S.jsx(EN,{className:"h-4 w-4"}),"المستحقين للكشوفات"]}),S.jsxs(wo,{value:"search",className:"flex items-center gap-2",children:[S.jsx(pN,{className:"h-4 w-4"}),"البحث المتقدم"]}),S.jsxs(wo,{value:"settings",className:"flex items-center gap-2",children:[S.jsx(yN,{className:"h-4 w-4"}),"الإعدادات"]})]}),S.jsx(Ao,{value:"input",children:S.jsx(Sn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(Tn,{className:"max-w-2xl mx-auto neomorphism hover-lift",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-center gradient-text text-2xl",children:"📥 إدخال بيانات موظف جديد"})}),S.jsx(En,{children:S.jsxs("form",{onSubmit:g,className:"space-y-6",children:[S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"name",className:"flex items-center gap-2",children:[S.jsx(wN,{className:"h-4 w-4"}),"اسم الموظف"]}),S.jsx(ts,{id:"name",value:c.name,onChange:M=>d(Z(N({},c),{name:M.target.value})),placeholder:"أدخل اسم الموظف",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"department",className:"flex items-center gap-2",children:[S.jsx(eN,{className:"h-4 w-4"}),"القسم"]}),S.jsx(ts,{id:"department",value:c.department,onChange:M=>d(Z(N({},c),{department:M.target.value})),placeholder:"أدخل اسم القسم",className:"text-right",required:!0})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsxs(ai,{htmlFor:"startMonth",className:"flex items-center gap-2",children:[S.jsx(aN,{className:"h-4 w-4"}),"أول شهر للكشف 🌙"]}),S.jsxs(MN,{value:c.startMonth,onValueChange:M=>d(Z(N({},c),{startMonth:M})),required:!0,children:[S.jsx(RN,{children:S.jsx(CN,{placeholder:"اختر الشهر"})}),S.jsx(DN,{children:m.map(M=>S.jsx(ON,{value:M,children:M},M))})]})]}),S.jsx(en,{type:"submit",className:"w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6",children:"➕ حفظ البيانات"})]})})]})})}),S.jsx(Ao,{value:"employees",children:S.jsx(Sn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:S.jsxs(Tn,{className:"neomorphism",children:[S.jsxs(wn,{className:"flex flex-row items-center justify-between",children:[S.jsx(An,{className:"gradient-text text-2xl",children:"📄 المستحقين للكشوفات"}),S.jsxs(en,{onClick:v,className:"bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500",children:[S.jsx(hN,{className:"mr-2 h-4 w-4"}),"🔁 تحديث الكشفات"]})]}),S.jsx(En,{children:s.length===0?S.jsxs("div",{className:"text-center py-12",children:[S.jsx(bN,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),S.jsx("p",{className:"text-gray-500 text-lg",children:"لا توجد بيانات موظفين حتى الآن"})]}):S.jsxs(c0,{children:[S.jsx(u0,{children:S.jsxs(Gf,{children:[S.jsx(nn,{className:"text-right",children:"#"}),S.jsx(nn,{className:"text-right",children:"اسم الموظف"}),S.jsx(nn,{className:"text-right",children:"القسم"}),S.jsx(nn,{className:"text-right",children:"أول شهر"}),S.jsx(nn,{className:"text-right",children:"الحالة"}),S.jsx(nn,{className:"text-right",children:"إجراءات"})]})}),S.jsx(f0,{children:s.map((M,C)=>S.jsxs(Sn.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:C*.1},className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[S.jsx(an,{children:C+1}),S.jsx(an,{className:"font-medium",children:M.name}),S.jsx(an,{children:M.department}),S.jsx(an,{children:M.startMonth}),S.jsx(an,{children:S.jsx(d0,{className:M.status?"status-required":"status-completed",children:M.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),S.jsx(an,{children:S.jsx(en,{variant:"destructive",size:"sm",onClick:()=>_(M.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},M.id))})]})})]})})}),S.jsx(Ao,{value:"search",children:S.jsxs(Sn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"gradient-text text-2xl",children:"🔍 البحث المتقدم"})}),S.jsxs(En,{children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[S.jsxs("div",{children:[S.jsx(ai,{children:"البحث بالاسم"}),S.jsx(ts,{placeholder:"🔎 اسم الموظف",value:u.name,onChange:M=>h(Z(N({},u),{name:M.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"القسم"}),S.jsx(ts,{placeholder:"🏢 اسم القسم",value:u.department,onChange:M=>h(Z(N({},u),{department:M.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"أول شهر"}),S.jsx(ts,{placeholder:"🗓️ اسم الشهر",value:u.startMonth,onChange:M=>h(Z(N({},u),{startMonth:M.target.value})),className:"text-right"})]}),S.jsxs("div",{children:[S.jsx(ai,{children:"الحالة"}),S.jsx(ts,{placeholder:"🔍 الحالة (مطلوب/تم)",value:u.status,onChange:M=>h(Z(N({},u),{status:M.target.value})),className:"text-right"})]})]}),S.jsxs("div",{className:"flex flex-wrap gap-4 mt-6 pt-4 border-t",children:[S.jsx(en,{onClick:()=>T(j),className:"bg-green-500 hover:bg-green-600 text-white flex items-center gap-2",disabled:j.length===0,children:"📊 تصدير إلى Excel"}),S.jsx(en,{onClick:()=>T(),className:"bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2",children:"📋 تصدير جميع البيانات"}),S.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:["📈 عدد النتائج: ",j.length," من أصل ",s.length]})]})]})]}),S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{children:"نتائج البحث"})}),S.jsx(En,{children:j.length===0?S.jsx("div",{className:"text-center py-8",children:S.jsx("p",{className:"text-gray-500",children:"🚫 لا توجد بيانات مطابقة!"})}):S.jsxs(c0,{children:[S.jsx(u0,{children:S.jsxs(Gf,{children:[S.jsx(nn,{className:"text-right",children:"#"}),S.jsx(nn,{className:"text-right",children:"اسم الموظف"}),S.jsx(nn,{className:"text-right",children:"القسم"}),S.jsx(nn,{className:"text-right",children:"أول شهر"}),S.jsx(nn,{className:"text-right",children:"الحالة"}),S.jsx(nn,{className:"text-right",children:"إجراءات"})]})}),S.jsx(f0,{children:j.map((M,C)=>S.jsxs(Gf,{children:[S.jsx(an,{children:C+1}),S.jsx(an,{className:"font-medium",children:M.name}),S.jsx(an,{children:M.department}),S.jsx(an,{children:M.startMonth}),S.jsx(an,{children:S.jsx(d0,{className:M.status?"status-required":"status-completed",children:M.status?S.jsxs(S.Fragment,{children:[S.jsx(r0,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):S.jsxs(S.Fragment,{children:[S.jsx(l0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),S.jsx(an,{children:S.jsx(en,{variant:"destructive",size:"sm",onClick:()=>_(M.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},M.id))})]})})]})]})}),S.jsx(Ao,{value:"settings",children:S.jsx(Sn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto space-y-6",children:S.jsxs(Tn,{className:"neomorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"gradient-text text-2xl",children:"⚙️ الإعدادات"})}),S.jsx(En,{className:"space-y-6",children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[S.jsxs(Tn,{className:"glassmorphism md:col-span-2",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"💾 إدارة البيانات"})}),S.jsxs(En,{children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[S.jsx(en,{onClick:b,className:"bg-blue-500 hover:bg-blue-600 text-white",children:"📁 تصدير البيانات"}),S.jsxs("div",{children:[S.jsx("input",{type:"file",accept:".json",onChange:D,style:{display:"none"},id:"import-file"}),S.jsx(en,{onClick:()=>document.getElementById("import-file").click(),className:"bg-green-500 hover:bg-green-600 text-white w-full",children:"📥 استيراد البيانات"})]}),S.jsx(en,{onClick:E,className:"bg-red-500 hover:bg-red-600 text-white",children:"🗑️ مسح جميع البيانات"})]}),S.jsxs("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[S.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 ",S.jsx("strong",{children:"نصائح:"})]}),S.jsxs("ul",{className:"text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1",children:[S.jsx("li",{children:"• يتم حفظ البيانات تلقائياً في متصفحك"}),S.jsx("li",{children:"• استخدم التصدير لإنشاء نسخة احتياطية"}),S.jsx("li",{children:"• يمكنك استيراد البيانات من ملف JSON"}),S.jsx("li",{children:"• البيانات محفوظة حتى لو أغلقت المتصفح"}),S.jsx("li",{children:"• يتم إنشاء نسخة احتياطية تلقائياً عند كل تحديث"})]})]}),S.jsxs("div",{className:"mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[S.jsx("p",{className:"text-sm text-green-700 dark:text-green-300 font-semibold mb-2",children:"📊 معلومات التخزين:"}),S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[S.jsx("div",{children:S.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["👥 عدد الموظفين المحفوظين: ",S.jsx("strong",{children:s.length})]})}),S.jsx("div",{children:S.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["💾 حجم البيانات: ",S.jsxs("strong",{children:[Math.round(JSON.stringify(s).length/1024)," KB"]})]})}),S.jsx("div",{children:S.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["🕒 آخر تحديث: ",S.jsx("strong",{children:localStorage.getItem("work-attendance-employees_lastUpdate")?new Date(localStorage.getItem("work-attendance-employees_lastUpdate")).toLocaleString("ar-SA"):"غير محدد"})]})}),S.jsx("div",{children:S.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["🔄 الحفظ التلقائي: ",S.jsx("strong",{children:"مفعل"})]})})]})]})]})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"📝 نبذة عن البرنامج"})}),S.jsx(En,{children:S.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات."})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"👨‍💻 عن المبرمج"})}),S.jsx(En,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"علي عاجل خشان المحنّة"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"مبرمج محترف بخبرة واسعة في:"}),S.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[S.jsx("li",{children:"• VB.NET / C# / Java"}),S.jsx("li",{children:"• JavaScript / React / Node.js"}),S.jsx("li",{children:"• Microsoft Access و Excel VBA"}),S.jsx("li",{children:"• أنظمة الأرشفة الذكية والتحكم الآلي"})]})]})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"🆔 إصدار البرنامج"})}),S.jsx(En,{children:S.jsxs("div",{className:"space-y-2",children:[S.jsx("p",{className:"font-semibold",children:"v2.0.0"}),S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات."})]})})]}),S.jsxs(Tn,{className:"glassmorphism",children:[S.jsx(wn,{children:S.jsx(An,{className:"text-lg",children:"🎖️ حقوق التصميم"})}),S.jsx(En,{children:S.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"جميع الحقوق محفوظة © 2025"})})]})]})})]})})})]})})]}),S.jsx(en,{variant:"destructive",className:"fixed bottom-5 left-5 z-50 rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all duration-300",onClick:z,title:"إغلاق البرنامج (Ctrl+Q)",children:S.jsx(o0,{className:"h-6 w-6"})})]})}gw.createRoot(document.getElementById("root")).render(S.jsx(w.StrictMode,{children:S.jsx(a3,{})}))});export default i3();
