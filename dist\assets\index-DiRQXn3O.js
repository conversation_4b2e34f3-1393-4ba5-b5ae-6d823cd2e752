var aw=Object.defineProperty,iw=Object.defineProperties;var sw=Object.getOwnPropertyDescriptors;var mo=Object.getOwnPropertySymbols;var dy=Object.prototype.hasOwnProperty,hy=Object.prototype.propertyIsEnumerable;var pf=Math.pow,fy=(n,a,s)=>a in n?aw(n,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[a]=s,N=(n,a)=>{for(var s in a||(a={}))dy.call(a,s)&&fy(n,s,a[s]);if(mo)for(var s of mo(a))hy.call(a,s)&&fy(n,s,a[s]);return n},Z=(n,a)=>iw(n,sw(a));var it=(n,a)=>{var s={};for(var l in n)dy.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&mo)for(var l of mo(n))a.indexOf(l)<0&&hy.call(n,l)&&(s[l]=n[l]);return s};var rw=(n,a)=>()=>(a||n((a={exports:{}}).exports,a),a.exports);var dn=(n,a,s)=>new Promise((l,c)=>{var d=m=>{try{h(s.next(m))}catch(p){c(p)}},f=m=>{try{h(s.throw(m))}catch(p){c(p)}},h=m=>m.done?l(m.value):Promise.resolve(m.value).then(d,f);h((s=s.apply(n,a)).next())});var ij=rw(kS=>{function lw(n,a){for(var s=0;s<a.length;s++){const l=a[s];if(typeof l!="string"&&!Array.isArray(l)){for(const c in l)if(c!=="default"&&!(c in n)){const d=Object.getOwnPropertyDescriptor(l,c);d&&Object.defineProperty(n,c,d.get?d:{enumerable:!0,get:()=>l[c]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))l(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function s(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(c){if(c.ep)return;c.ep=!0;const d=s(c);fetch(c.href,d)}})();function m0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var gf={exports:{}},xr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my;function ow(){if(my)return xr;my=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(l,c,d){var f=null;if(d!==void 0&&(f=""+d),c.key!==void 0&&(f=""+c.key),"key"in c){d={};for(var h in c)h!=="key"&&(d[h]=c[h])}else d=c;return c=d.ref,{$$typeof:n,type:l,key:f,ref:c!==void 0?c:null,props:d}}return xr.Fragment=a,xr.jsx=s,xr.jsxs=s,xr}var py;function cw(){return py||(py=1,gf.exports=ow()),gf.exports}var x=cw(),yf={exports:{}},Tt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gy;function uw(){if(gy)return Tt;gy=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function b(R){return R===null||typeof R!="object"?null:(R=v&&R[v]||R["@@iterator"],typeof R=="function"?R:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,A={};function E(R,K,at){this.props=R,this.context=K,this.refs=A,this.updater=at||T}E.prototype.isReactComponent={},E.prototype.setState=function(R,K){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,K,"setState")},E.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function O(){}O.prototype=E.prototype;function V(R,K,at){this.props=R,this.context=K,this.refs=A,this.updater=at||T}var D=V.prototype=new O;D.constructor=V,M(D,E.prototype),D.isPureReactComponent=!0;var L=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},$=Object.prototype.hasOwnProperty;function et(R,K,at,tt,rt,bt){return at=bt.ref,{$$typeof:n,type:R,key:K,ref:at!==void 0?at:null,props:bt}}function q(R,K){return et(R.type,K,void 0,void 0,void 0,R.props)}function W(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function Y(R){var K={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(at){return K[at]})}var J=/\/+/g;function nt(R,K){return typeof R=="object"&&R!==null&&R.key!=null?Y(""+R.key):K.toString(36)}function ut(){}function yt(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(ut,ut):(R.status="pending",R.then(function(K){R.status==="pending"&&(R.status="fulfilled",R.value=K)},function(K){R.status==="pending"&&(R.status="rejected",R.reason=K)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function ft(R,K,at,tt,rt){var bt=typeof R;(bt==="undefined"||bt==="boolean")&&(R=null);var mt=!1;if(R===null)mt=!0;else switch(bt){case"bigint":case"string":case"number":mt=!0;break;case"object":switch(R.$$typeof){case n:case a:mt=!0;break;case g:return mt=R._init,ft(mt(R._payload),K,at,tt,rt)}}if(mt)return rt=rt(R),mt=tt===""?"."+nt(R,0):tt,L(rt)?(at="",mt!=null&&(at=mt.replace(J,"$&/")+"/"),ft(rt,K,at,"",function(wt){return wt})):rt!=null&&(W(rt)&&(rt=q(rt,at+(rt.key==null||R&&R.key===rt.key?"":(""+rt.key).replace(J,"$&/")+"/")+mt)),K.push(rt)),1;mt=0;var St=tt===""?".":tt+":";if(L(R))for(var st=0;st<R.length;st++)tt=R[st],bt=St+nt(tt,st),mt+=ft(tt,K,at,bt,rt);else if(st=b(R),typeof st=="function")for(R=st.call(R),st=0;!(tt=R.next()).done;)tt=tt.value,bt=St+nt(tt,st++),mt+=ft(tt,K,at,bt,rt);else if(bt==="object"){if(typeof R.then=="function")return ft(yt(R),K,at,tt,rt);throw K=String(R),Error("Objects are not valid as a React child (found: "+(K==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":K)+"). If you meant to render a collection of children, use an array instead.")}return mt}function B(R,K,at){if(R==null)return R;var tt=[],rt=0;return ft(R,tt,"","",function(bt){return K.call(at,bt,rt++)}),tt}function X(R){if(R._status===-1){var K=R._result;K=K(),K.then(function(at){(R._status===0||R._status===-1)&&(R._status=1,R._result=at)},function(at){(R._status===0||R._status===-1)&&(R._status=2,R._result=at)}),R._status===-1&&(R._status=0,R._result=K)}if(R._status===1)return R._result.default;throw R._result}var P=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var K=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(K))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function ht(){}return Tt.Children={map:B,forEach:function(R,K,at){B(R,function(){K.apply(this,arguments)},at)},count:function(R){var K=0;return B(R,function(){K++}),K},toArray:function(R){return B(R,function(K){return K})||[]},only:function(R){if(!W(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},Tt.Component=E,Tt.Fragment=s,Tt.Profiler=c,Tt.PureComponent=V,Tt.StrictMode=l,Tt.Suspense=m,Tt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,Tt.__COMPILER_RUNTIME={__proto__:null,c:function(R){return j.H.useMemoCache(R)}},Tt.cache=function(R){return function(){return R.apply(null,arguments)}},Tt.cloneElement=function(R,K,at){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var tt=M({},R.props),rt=R.key,bt=void 0;if(K!=null)for(mt in K.ref!==void 0&&(bt=void 0),K.key!==void 0&&(rt=""+K.key),K)!$.call(K,mt)||mt==="key"||mt==="__self"||mt==="__source"||mt==="ref"&&K.ref===void 0||(tt[mt]=K[mt]);var mt=arguments.length-2;if(mt===1)tt.children=at;else if(1<mt){for(var St=Array(mt),st=0;st<mt;st++)St[st]=arguments[st+2];tt.children=St}return et(R.type,rt,void 0,void 0,bt,tt)},Tt.createContext=function(R){return R={$$typeof:f,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:d,_context:R},R},Tt.createElement=function(R,K,at){var tt,rt={},bt=null;if(K!=null)for(tt in K.key!==void 0&&(bt=""+K.key),K)$.call(K,tt)&&tt!=="key"&&tt!=="__self"&&tt!=="__source"&&(rt[tt]=K[tt]);var mt=arguments.length-2;if(mt===1)rt.children=at;else if(1<mt){for(var St=Array(mt),st=0;st<mt;st++)St[st]=arguments[st+2];rt.children=St}if(R&&R.defaultProps)for(tt in mt=R.defaultProps,mt)rt[tt]===void 0&&(rt[tt]=mt[tt]);return et(R,bt,void 0,void 0,null,rt)},Tt.createRef=function(){return{current:null}},Tt.forwardRef=function(R){return{$$typeof:h,render:R}},Tt.isValidElement=W,Tt.lazy=function(R){return{$$typeof:g,_payload:{_status:-1,_result:R},_init:X}},Tt.memo=function(R,K){return{$$typeof:p,type:R,compare:K===void 0?null:K}},Tt.startTransition=function(R){var K=j.T,at={};j.T=at;try{var tt=R(),rt=j.S;rt!==null&&rt(at,tt),typeof tt=="object"&&tt!==null&&typeof tt.then=="function"&&tt.then(ht,P)}catch(bt){P(bt)}finally{j.T=K}},Tt.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},Tt.use=function(R){return j.H.use(R)},Tt.useActionState=function(R,K,at){return j.H.useActionState(R,K,at)},Tt.useCallback=function(R,K){return j.H.useCallback(R,K)},Tt.useContext=function(R){return j.H.useContext(R)},Tt.useDebugValue=function(){},Tt.useDeferredValue=function(R,K){return j.H.useDeferredValue(R,K)},Tt.useEffect=function(R,K,at){var tt=j.H;if(typeof at=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return tt.useEffect(R,K)},Tt.useId=function(){return j.H.useId()},Tt.useImperativeHandle=function(R,K,at){return j.H.useImperativeHandle(R,K,at)},Tt.useInsertionEffect=function(R,K){return j.H.useInsertionEffect(R,K)},Tt.useLayoutEffect=function(R,K){return j.H.useLayoutEffect(R,K)},Tt.useMemo=function(R,K){return j.H.useMemo(R,K)},Tt.useOptimistic=function(R,K){return j.H.useOptimistic(R,K)},Tt.useReducer=function(R,K,at){return j.H.useReducer(R,K,at)},Tt.useRef=function(R){return j.H.useRef(R)},Tt.useState=function(R){return j.H.useState(R)},Tt.useSyncExternalStore=function(R,K,at){return j.H.useSyncExternalStore(R,K,at)},Tt.useTransition=function(){return j.H.useTransition()},Tt.version="19.1.0",Tt}var yy;function Nd(){return yy||(yy=1,yf.exports=uw()),yf.exports}var w=Nd();const Ea=m0(w),p0=lw({__proto__:null,default:Ea},[w]);var vf={exports:{}},br={},xf={exports:{}},bf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vy;function fw(){return vy||(vy=1,function(n){function a(B,X){var P=B.length;B.push(X);t:for(;0<P;){var ht=P-1>>>1,R=B[ht];if(0<c(R,X))B[ht]=X,B[P]=R,P=ht;else break t}}function s(B){return B.length===0?null:B[0]}function l(B){if(B.length===0)return null;var X=B[0],P=B.pop();if(P!==X){B[0]=P;t:for(var ht=0,R=B.length,K=R>>>1;ht<K;){var at=2*(ht+1)-1,tt=B[at],rt=at+1,bt=B[rt];if(0>c(tt,P))rt<R&&0>c(bt,tt)?(B[ht]=bt,B[rt]=P,ht=rt):(B[ht]=tt,B[at]=P,ht=at);else if(rt<R&&0>c(bt,P))B[ht]=bt,B[rt]=P,ht=rt;else break t}}return X}function c(B,X){var P=B.sortIndex-X.sortIndex;return P!==0?P:B.id-X.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();n.unstable_now=function(){return f.now()-h}}var m=[],p=[],g=1,v=null,b=3,T=!1,M=!1,A=!1,E=!1,O=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate!="undefined"?setImmediate:null;function L(B){for(var X=s(p);X!==null;){if(X.callback===null)l(p);else if(X.startTime<=B)l(p),X.sortIndex=X.expirationTime,a(m,X);else break;X=s(p)}}function j(B){if(A=!1,L(B),!M)if(s(m)!==null)M=!0,$||($=!0,nt());else{var X=s(p);X!==null&&ft(j,X.startTime-B)}}var $=!1,et=-1,q=5,W=-1;function Y(){return E?!0:!(n.unstable_now()-W<q)}function J(){if(E=!1,$){var B=n.unstable_now();W=B;var X=!0;try{t:{M=!1,A&&(A=!1,V(et),et=-1),T=!0;var P=b;try{e:{for(L(B),v=s(m);v!==null&&!(v.expirationTime>B&&Y());){var ht=v.callback;if(typeof ht=="function"){v.callback=null,b=v.priorityLevel;var R=ht(v.expirationTime<=B);if(B=n.unstable_now(),typeof R=="function"){v.callback=R,L(B),X=!0;break e}v===s(m)&&l(m),L(B)}else l(m);v=s(m)}if(v!==null)X=!0;else{var K=s(p);K!==null&&ft(j,K.startTime-B),X=!1}}break t}finally{v=null,b=P,T=!1}X=void 0}}finally{X?nt():$=!1}}}var nt;if(typeof D=="function")nt=function(){D(J)};else if(typeof MessageChannel!="undefined"){var ut=new MessageChannel,yt=ut.port2;ut.port1.onmessage=J,nt=function(){yt.postMessage(null)}}else nt=function(){O(J,0)};function ft(B,X){et=O(function(){B(n.unstable_now())},X)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(B){switch(b){case 1:case 2:case 3:var X=3;break;default:X=b}var P=b;b=X;try{return B()}finally{b=P}},n.unstable_requestPaint=function(){E=!0},n.unstable_runWithPriority=function(B,X){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var P=b;b=B;try{return X()}finally{b=P}},n.unstable_scheduleCallback=function(B,X,P){var ht=n.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?ht+P:ht):P=ht,B){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=P+R,B={id:g++,callback:X,priorityLevel:B,startTime:P,expirationTime:R,sortIndex:-1},P>ht?(B.sortIndex=P,a(p,B),s(m)===null&&B===s(p)&&(A?(V(et),et=-1):A=!0,ft(j,P-ht))):(B.sortIndex=R,a(m,B),M||T||(M=!0,$||($=!0,nt()))),B},n.unstable_shouldYield=Y,n.unstable_wrapCallback=function(B){var X=b;return function(){var P=b;b=X;try{return B.apply(this,arguments)}finally{b=P}}}}(bf)),bf}var xy;function dw(){return xy||(xy=1,xf.exports=fw()),xf.exports}var Sf={exports:{}},we={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var by;function hw(){if(by)return we;by=1;var n=Nd();function a(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)p+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(m,p,g){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:v==null?null:""+v,children:m,containerInfo:p,implementation:g}}var f=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return we.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,we.createPortal=function(m,p){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(a(299));return d(m,p,null,g)},we.flushSync=function(m){var p=f.T,g=l.p;try{if(f.T=null,l.p=2,m)return m()}finally{f.T=p,l.p=g,l.d.f()}},we.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,l.d.C(m,p))},we.prefetchDNS=function(m){typeof m=="string"&&l.d.D(m)},we.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,T=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;g==="style"?l.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:v,integrity:b,fetchPriority:T}):g==="script"&&l.d.X(m,{crossOrigin:v,integrity:b,fetchPriority:T,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},we.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var g=h(p.as,p.crossOrigin);l.d.M(m,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&l.d.M(m)},we.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var g=p.as,v=h(g,p.crossOrigin);l.d.L(m,g,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},we.preloadModule=function(m,p){if(typeof m=="string")if(p){var g=h(p.as,p.crossOrigin);l.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else l.d.m(m)},we.requestFormReset=function(m){l.d.r(m)},we.unstable_batchedUpdates=function(m,p){return m(p)},we.useFormState=function(m,p,g){return f.H.useFormState(m,p,g)},we.useFormStatus=function(){return f.H.useHostTransitionStatus()},we.version="19.1.0",we}var Sy;function g0(){if(Sy)return Sf.exports;Sy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),Sf.exports=hw(),Sf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ty;function mw(){if(Ty)return br;Ty=1;var n=dw(),a=Nd(),s=g0();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(d(t)!==t)throw Error(l(188))}function m(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(l(188));return e!==t?null:t}for(var i=t,r=e;;){var o=i.return;if(o===null)break;var u=o.alternate;if(u===null){if(r=o.return,r!==null){i=r;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===i)return h(o),t;if(u===r)return h(o),e;u=u.sibling}throw Error(l(188))}if(i.return!==r.return)i=o,r=u;else{for(var y=!1,S=o.child;S;){if(S===i){y=!0,i=o,r=u;break}if(S===r){y=!0,r=o,i=u;break}S=S.sibling}if(!y){for(S=u.child;S;){if(S===i){y=!0,i=u,r=o;break}if(S===r){y=!0,r=u,i=o;break}S=S.sibling}if(!y)throw Error(l(189))}}if(i.alternate!==r)throw Error(l(190))}if(i.tag!==3)throw Error(l(188));return i.stateNode.current===i?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var g=Object.assign,v=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),D=Symbol.for("react.context"),L=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),$=Symbol.for("react.suspense_list"),et=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),Y=Symbol.for("react.memo_cache_sentinel"),J=Symbol.iterator;function nt(t){return t===null||typeof t!="object"?null:(t=J&&t[J]||t["@@iterator"],typeof t=="function"?t:null)}var ut=Symbol.for("react.client.reference");function yt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ut?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case M:return"Fragment";case E:return"Profiler";case A:return"StrictMode";case j:return"Suspense";case $:return"SuspenseList";case W:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case T:return"Portal";case D:return(t.displayName||"Context")+".Provider";case V:return(t._context.displayName||"Context")+".Consumer";case L:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case et:return e=t.displayName||null,e!==null?e:yt(t.type)||"Memo";case q:e=t._payload,t=t._init;try{return yt(t(e))}catch(i){}}return null}var ft=Array.isArray,B=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},ht=[],R=-1;function K(t){return{current:t}}function at(t){0>R||(t.current=ht[R],ht[R]=null,R--)}function tt(t,e){R++,ht[R]=t.current,t.current=e}var rt=K(null),bt=K(null),mt=K(null),St=K(null);function st(t,e){switch(tt(mt,e),tt(bt,t),tt(rt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Ug(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Ug(e),t=Hg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}at(rt),tt(rt,t)}function wt(){at(rt),at(bt),at(mt)}function Yt(t){t.memoizedState!==null&&tt(St,t);var e=rt.current,i=Hg(e,t.type);e!==i&&(tt(bt,t),tt(rt,i))}function Dt(t){bt.current===t&&(at(rt),at(bt)),St.current===t&&(at(St),mr._currentValue=P)}var Ct=Object.prototype.hasOwnProperty,jt=n.unstable_scheduleCallback,pe=n.unstable_cancelCallback,Ce=n.unstable_shouldYield,on=n.unstable_requestPaint,ge=n.unstable_now,La=n.unstable_getCurrentPriorityLevel,Wr=n.unstable_ImmediatePriority,Jr=n.unstable_UserBlockingPriority,di=n.unstable_NormalPriority,hi=n.unstable_LowPriority,Sh=n.unstable_IdlePriority,US=n.log,HS=n.unstable_setDisableYieldValue,Ts=null,Be=null;function ta(t){if(typeof US=="function"&&HS(t),Be&&typeof Be.setStrictMode=="function")try{Be.setStrictMode(Ts,t)}catch(e){}}var ke=Math.clz32?Math.clz32:qS,PS=Math.log,GS=Math.LN2;function qS(t){return t>>>=0,t===0?32:31-(PS(t)/GS|0)|0}var tl=256,el=4194304;function Ba(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function nl(t,e,i){var r=t.pendingLanes;if(r===0)return 0;var o=0,u=t.suspendedLanes,y=t.pingedLanes;t=t.warmLanes;var S=r&134217727;return S!==0?(r=S&~u,r!==0?o=Ba(r):(y&=S,y!==0?o=Ba(y):i||(i=S&~t,i!==0&&(o=Ba(i))))):(S=r&~u,S!==0?o=Ba(S):y!==0?o=Ba(y):i||(i=r&~t,i!==0&&(o=Ba(i)))),o===0?0:e!==0&&e!==o&&(e&u)===0&&(u=o&-o,i=e&-e,u>=i||u===32&&(i&4194048)!==0)?e:o}function ws(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function YS(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Th(){var t=tl;return tl<<=1,(tl&4194048)===0&&(tl=256),t}function wh(){var t=el;return el<<=1,(el&62914560)===0&&(el=4194304),t}function ac(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function As(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function XS(t,e,i,r,o,u){var y=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var S=t.entanglements,C=t.expirationTimes,U=t.hiddenUpdates;for(i=y&~i;0<i;){var Q=31-ke(i),I=1<<Q;S[Q]=0,C[Q]=-1;var H=U[Q];if(H!==null)for(U[Q]=null,Q=0;Q<H.length;Q++){var G=H[Q];G!==null&&(G.lane&=-536870913)}i&=~I}r!==0&&Ah(t,r,0),u!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=u&~(y&~e))}function Ah(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-ke(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|i&4194090}function Eh(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var r=31-ke(i),o=1<<r;o&e|t[r]&e&&(t[r]|=e),i&=~o}}function ic(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function sc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Mh(){var t=X.p;return t!==0?t:(t=window.event,t===void 0?32:sy(t.type))}function KS(t,e){var i=X.p;try{return X.p=t,e()}finally{X.p=i}}var ea=Math.random().toString(36).slice(2),Se="__reactFiber$"+ea,Oe="__reactProps$"+ea,mi="__reactContainer$"+ea,rc="__reactEvents$"+ea,ZS="__reactListeners$"+ea,QS="__reactHandles$"+ea,Ch="__reactResources$"+ea,Es="__reactMarker$"+ea;function lc(t){delete t[Se],delete t[Oe],delete t[rc],delete t[ZS],delete t[QS]}function pi(t){var e=t[Se];if(e)return e;for(var i=t.parentNode;i;){if(e=i[mi]||i[Se]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=Yg(t);t!==null;){if(i=t[Se])return i;t=Yg(t)}return e}t=i,i=t.parentNode}return null}function gi(t){if(t=t[Se]||t[mi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ms(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function yi(t){var e=t[Ch];return e||(e=t[Ch]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ue(t){t[Es]=!0}var Rh=new Set,Nh={};function ka(t,e){vi(t,e),vi(t+"Capture",e)}function vi(t,e){for(Nh[t]=e,t=0;t<e.length;t++)Rh.add(e[t])}var FS=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Dh={},Oh={};function IS(t){return Ct.call(Oh,t)?!0:Ct.call(Dh,t)?!1:FS.test(t)?Oh[t]=!0:(Dh[t]=!0,!1)}function al(t,e,i){if(IS(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function il(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function _n(t,e,i,r){if(r===null)t.removeAttribute(i);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+r)}}var oc,jh;function xi(t){if(oc===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);oc=e&&e[1]||"",jh=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+oc+t+jh}var cc=!1;function uc(t,e){if(!t||cc)return"";cc=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var I=function(){throw Error()};if(Object.defineProperty(I.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(I,[])}catch(G){var H=G}Reflect.construct(t,[],I)}else{try{I.call()}catch(G){H=G}t.call(I.prototype)}}else{try{throw Error()}catch(G){H=G}(I=t())&&typeof I.catch=="function"&&I.catch(function(){})}}catch(G){if(G&&H&&typeof G.stack=="string")return[G.stack,H.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=r.DetermineComponentFrameRoot(),y=u[0],S=u[1];if(y&&S){var C=y.split(`
`),U=S.split(`
`);for(o=r=0;r<C.length&&!C[r].includes("DetermineComponentFrameRoot");)r++;for(;o<U.length&&!U[o].includes("DetermineComponentFrameRoot");)o++;if(r===C.length||o===U.length)for(r=C.length-1,o=U.length-1;1<=r&&0<=o&&C[r]!==U[o];)o--;for(;1<=r&&0<=o;r--,o--)if(C[r]!==U[o]){if(r!==1||o!==1)do if(r--,o--,0>o||C[r]!==U[o]){var Q=`
`+C[r].replace(" at new "," at ");return t.displayName&&Q.includes("<anonymous>")&&(Q=Q.replace("<anonymous>",t.displayName)),Q}while(1<=r&&0<=o);break}}}finally{cc=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?xi(i):""}function $S(t){switch(t.tag){case 26:case 27:case 5:return xi(t.type);case 16:return xi("Lazy");case 13:return xi("Suspense");case 19:return xi("SuspenseList");case 0:case 15:return uc(t.type,!1);case 11:return uc(t.type.render,!1);case 1:return uc(t.type,!0);case 31:return xi("Activity");default:return""}}function _h(t){try{var e="";do e+=$S(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Fe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Vh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function WS(t){var e=Vh(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof i!="undefined"&&typeof i.get=="function"&&typeof i.set=="function"){var o=i.get,u=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(y){r=""+y,u.call(this,y)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function sl(t){t._valueTracker||(t._valueTracker=WS(t))}function zh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),r="";return t&&(r=Vh(t)?t.checked?"true":"false":t.value),t=r,t!==i?(e.setValue(t),!0):!1}function rl(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var JS=/[\n"\\]/g;function Ie(t){return t.replace(JS,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function fc(t,e,i,r,o,u,y,S){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Fe(e)):t.value!==""+Fe(e)&&(t.value=""+Fe(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?dc(t,y,Fe(e)):i!=null?dc(t,y,Fe(i)):r!=null&&t.removeAttribute("value"),o==null&&u!=null&&(t.defaultChecked=!!u),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),S!=null&&typeof S!="function"&&typeof S!="symbol"&&typeof S!="boolean"?t.name=""+Fe(S):t.removeAttribute("name")}function Lh(t,e,i,r,o,u,y,S){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||i!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;i=i!=null?""+Fe(i):"",e=e!=null?""+Fe(e):i,S||e===t.value||(t.value=e),t.defaultValue=e}r=r!=null?r:o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=S?t.checked:!!r,t.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function dc(t,e,i){e==="number"&&rl(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function bi(t,e,i,r){if(t=t.options,e){e={};for(var o=0;o<i.length;o++)e["$"+i[o]]=!0;for(i=0;i<t.length;i++)o=e.hasOwnProperty("$"+t[i].value),t[i].selected!==o&&(t[i].selected=o),o&&r&&(t[i].defaultSelected=!0)}else{for(i=""+Fe(i),e=null,o=0;o<t.length;o++){if(t[o].value===i){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Bh(t,e,i){if(e!=null&&(e=""+Fe(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Fe(i):""}function kh(t,e,i,r){if(e==null){if(r!=null){if(i!=null)throw Error(l(92));if(ft(r)){if(1<r.length)throw Error(l(93));r=r[0]}i=r}i==null&&(i=""),e=i}i=Fe(e),t.defaultValue=i,r=t.textContent,r===i&&r!==""&&r!==null&&(t.value=r)}function Si(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var t1=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Uh(t,e,i){var r=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,i):typeof i!="number"||i===0||t1.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function Hh(t,e,i){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,i!=null){for(var r in i)!i.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var o in e)r=e[o],e.hasOwnProperty(o)&&i[o]!==r&&Uh(t,o,r)}else for(var u in e)e.hasOwnProperty(u)&&Uh(t,u,e[u])}function hc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var e1=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),n1=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ll(t){return n1.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var mc=null;function pc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ti=null,wi=null;function Ph(t){var e=gi(t);if(e&&(t=e.stateNode)){var i=t[Oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(fc(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Ie(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var r=i[e];if(r!==t&&r.form===t.form){var o=r[Oe]||null;if(!o)throw Error(l(90));fc(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<i.length;e++)r=i[e],r.form===t.form&&zh(r)}break t;case"textarea":Bh(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&bi(t,!!i.multiple,e,!1)}}}var gc=!1;function Gh(t,e,i){if(gc)return t(e,i);gc=!0;try{var r=t(e);return r}finally{if(gc=!1,(Ti!==null||wi!==null)&&(Kl(),Ti&&(e=Ti,t=wi,wi=Ti=null,Ph(e),t)))for(e=0;e<t.length;e++)Ph(t[e])}}function Cs(t,e){var i=t.stateNode;if(i===null)return null;var r=i[Oe]||null;if(r===null)return null;i=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(l(231,e,typeof i));return i}var Vn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),yc=!1;if(Vn)try{var Rs={};Object.defineProperty(Rs,"passive",{get:function(){yc=!0}}),window.addEventListener("test",Rs,Rs),window.removeEventListener("test",Rs,Rs)}catch(t){yc=!1}var na=null,vc=null,ol=null;function qh(){if(ol)return ol;var t,e=vc,i=e.length,r,o="value"in na?na.value:na.textContent,u=o.length;for(t=0;t<i&&e[t]===o[t];t++);var y=i-t;for(r=1;r<=y&&e[i-r]===o[u-r];r++);return ol=o.slice(t,1<r?1-r:void 0)}function cl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ul(){return!0}function Yh(){return!1}function je(t){function e(i,r,o,u,y){this._reactName=i,this._targetInst=o,this.type=r,this.nativeEvent=u,this.target=y,this.currentTarget=null;for(var S in t)t.hasOwnProperty(S)&&(i=t[S],this[S]=i?i(u):u[S]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?ul:Yh,this.isPropagationStopped=Yh,this}return g(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ul)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ul)},persist:function(){},isPersistent:ul}),e}var Ua={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fl=je(Ua),Ns=g({},Ua,{view:0,detail:0}),a1=je(Ns),xc,bc,Ds,dl=g({},Ns,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Tc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ds&&(Ds&&t.type==="mousemove"?(xc=t.screenX-Ds.screenX,bc=t.screenY-Ds.screenY):bc=xc=0,Ds=t),xc)},movementY:function(t){return"movementY"in t?t.movementY:bc}}),Xh=je(dl),i1=g({},dl,{dataTransfer:0}),s1=je(i1),r1=g({},Ns,{relatedTarget:0}),Sc=je(r1),l1=g({},Ua,{animationName:0,elapsedTime:0,pseudoElement:0}),o1=je(l1),c1=g({},Ua,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),u1=je(c1),f1=g({},Ua,{data:0}),Kh=je(f1),d1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},h1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},m1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function p1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=m1[t])?!!e[t]:!1}function Tc(){return p1}var g1=g({},Ns,{key:function(t){if(t.key){var e=d1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=cl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?h1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Tc,charCode:function(t){return t.type==="keypress"?cl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?cl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),y1=je(g1),v1=g({},dl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Zh=je(v1),x1=g({},Ns,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Tc}),b1=je(x1),S1=g({},Ua,{propertyName:0,elapsedTime:0,pseudoElement:0}),T1=je(S1),w1=g({},dl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),A1=je(w1),E1=g({},Ua,{newState:0,oldState:0}),M1=je(E1),C1=[9,13,27,32],wc=Vn&&"CompositionEvent"in window,Os=null;Vn&&"documentMode"in document&&(Os=document.documentMode);var R1=Vn&&"TextEvent"in window&&!Os,Qh=Vn&&(!wc||Os&&8<Os&&11>=Os),Fh=" ",Ih=!1;function $h(t,e){switch(t){case"keyup":return C1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ai=!1;function N1(t,e){switch(t){case"compositionend":return Wh(e);case"keypress":return e.which!==32?null:(Ih=!0,Fh);case"textInput":return t=e.data,t===Fh&&Ih?null:t;default:return null}}function D1(t,e){if(Ai)return t==="compositionend"||!wc&&$h(t,e)?(t=qh(),ol=vc=na=null,Ai=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Qh&&e.locale!=="ko"?null:e.data;default:return null}}var O1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Jh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!O1[t.type]:e==="textarea"}function tm(t,e,i,r){Ti?wi?wi.push(r):wi=[r]:Ti=r,e=Wl(e,"onChange"),0<e.length&&(i=new fl("onChange","change",null,i,r),t.push({event:i,listeners:e}))}var js=null,_s=null;function j1(t){Vg(t,0)}function hl(t){var e=Ms(t);if(zh(e))return t}function em(t,e){if(t==="change")return e}var nm=!1;if(Vn){var Ac;if(Vn){var Ec="oninput"in document;if(!Ec){var am=document.createElement("div");am.setAttribute("oninput","return;"),Ec=typeof am.oninput=="function"}Ac=Ec}else Ac=!1;nm=Ac&&(!document.documentMode||9<document.documentMode)}function im(){js&&(js.detachEvent("onpropertychange",sm),_s=js=null)}function sm(t){if(t.propertyName==="value"&&hl(_s)){var e=[];tm(e,_s,t,pc(t)),Gh(j1,e)}}function _1(t,e,i){t==="focusin"?(im(),js=e,_s=i,js.attachEvent("onpropertychange",sm)):t==="focusout"&&im()}function V1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return hl(_s)}function z1(t,e){if(t==="click")return hl(e)}function L1(t,e){if(t==="input"||t==="change")return hl(e)}function B1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ue=typeof Object.is=="function"?Object.is:B1;function Vs(t,e){if(Ue(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),r=Object.keys(e);if(i.length!==r.length)return!1;for(r=0;r<i.length;r++){var o=i[r];if(!Ct.call(e,o)||!Ue(t[o],e[o]))return!1}return!0}function rm(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function lm(t,e){var i=rm(t);t=0;for(var r;i;){if(i.nodeType===3){if(r=t+i.textContent.length,t<=e&&r>=e)return{node:i,offset:e-t};t=r}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=rm(i)}}function om(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?om(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function cm(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=rl(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch(r){i=!1}if(i)t=e.contentWindow;else break;e=rl(t.document)}return e}function Mc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var k1=Vn&&"documentMode"in document&&11>=document.documentMode,Ei=null,Cc=null,zs=null,Rc=!1;function um(t,e,i){var r=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Rc||Ei==null||Ei!==rl(r)||(r=Ei,"selectionStart"in r&&Mc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zs&&Vs(zs,r)||(zs=r,r=Wl(Cc,"onSelect"),0<r.length&&(e=new fl("onSelect","select",null,e,i),t.push({event:e,listeners:r}),e.target=Ei)))}function Ha(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var Mi={animationend:Ha("Animation","AnimationEnd"),animationiteration:Ha("Animation","AnimationIteration"),animationstart:Ha("Animation","AnimationStart"),transitionrun:Ha("Transition","TransitionRun"),transitionstart:Ha("Transition","TransitionStart"),transitioncancel:Ha("Transition","TransitionCancel"),transitionend:Ha("Transition","TransitionEnd")},Nc={},fm={};Vn&&(fm=document.createElement("div").style,"AnimationEvent"in window||(delete Mi.animationend.animation,delete Mi.animationiteration.animation,delete Mi.animationstart.animation),"TransitionEvent"in window||delete Mi.transitionend.transition);function Pa(t){if(Nc[t])return Nc[t];if(!Mi[t])return t;var e=Mi[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in fm)return Nc[t]=e[i];return t}var dm=Pa("animationend"),hm=Pa("animationiteration"),mm=Pa("animationstart"),U1=Pa("transitionrun"),H1=Pa("transitionstart"),P1=Pa("transitioncancel"),pm=Pa("transitionend"),gm=new Map,Dc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Dc.push("scrollEnd");function cn(t,e){gm.set(t,e),ka(e,[t])}var ym=new WeakMap;function $e(t,e){if(typeof t=="object"&&t!==null){var i=ym.get(t);return i!==void 0?i:(e={value:t,source:e,stack:_h(e)},ym.set(t,e),e)}return{value:t,source:e,stack:_h(e)}}var We=[],Ci=0,Oc=0;function ml(){for(var t=Ci,e=Oc=Ci=0;e<t;){var i=We[e];We[e++]=null;var r=We[e];We[e++]=null;var o=We[e];We[e++]=null;var u=We[e];if(We[e++]=null,r!==null&&o!==null){var y=r.pending;y===null?o.next=o:(o.next=y.next,y.next=o),r.pending=o}u!==0&&vm(i,o,u)}}function pl(t,e,i,r){We[Ci++]=t,We[Ci++]=e,We[Ci++]=i,We[Ci++]=r,Oc|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function jc(t,e,i,r){return pl(t,e,i,r),gl(t)}function Ri(t,e){return pl(t,null,null,e),gl(t)}function vm(t,e,i){t.lanes|=i;var r=t.alternate;r!==null&&(r.lanes|=i);for(var o=!1,u=t.return;u!==null;)u.childLanes|=i,r=u.alternate,r!==null&&(r.childLanes|=i),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(o=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,o&&e!==null&&(o=31-ke(i),t=u.hiddenUpdates,r=t[o],r===null?t[o]=[e]:r.push(e),e.lane=i|536870912),u):null}function gl(t){if(50<rr)throw rr=0,ku=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ni={};function G1(t,e,i,r){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function He(t,e,i,r){return new G1(t,e,i,r)}function _c(t){return t=t.prototype,!(!t||!t.isReactComponent)}function zn(t,e){var i=t.alternate;return i===null?(i=He(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function xm(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function yl(t,e,i,r,o,u){var y=0;if(r=t,typeof t=="function")_c(t)&&(y=1);else if(typeof t=="string")y=YT(t,i,rt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case W:return t=He(31,i,e,o),t.elementType=W,t.lanes=u,t;case M:return Ga(i.children,o,u,e);case A:y=8,o|=24;break;case E:return t=He(12,i,e,o|2),t.elementType=E,t.lanes=u,t;case j:return t=He(13,i,e,o),t.elementType=j,t.lanes=u,t;case $:return t=He(19,i,e,o),t.elementType=$,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case O:case D:y=10;break t;case V:y=9;break t;case L:y=11;break t;case et:y=14;break t;case q:y=16,r=null;break t}y=29,i=Error(l(130,t===null?"null":typeof t,"")),r=null}return e=He(y,i,e,o),e.elementType=t,e.type=r,e.lanes=u,e}function Ga(t,e,i,r){return t=He(7,t,r,e),t.lanes=i,t}function Vc(t,e,i){return t=He(6,t,null,e),t.lanes=i,t}function zc(t,e,i){return e=He(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Di=[],Oi=0,vl=null,xl=0,Je=[],tn=0,qa=null,Ln=1,Bn="";function Ya(t,e){Di[Oi++]=xl,Di[Oi++]=vl,vl=t,xl=e}function bm(t,e,i){Je[tn++]=Ln,Je[tn++]=Bn,Je[tn++]=qa,qa=t;var r=Ln;t=Bn;var o=32-ke(r)-1;r&=~(1<<o),i+=1;var u=32-ke(e)+o;if(30<u){var y=o-o%5;u=(r&(1<<y)-1).toString(32),r>>=y,o-=y,Ln=1<<32-ke(e)+o|i<<o|r,Bn=u+t}else Ln=1<<u|i<<o|r,Bn=t}function Lc(t){t.return!==null&&(Ya(t,1),bm(t,1,0))}function Bc(t){for(;t===vl;)vl=Di[--Oi],Di[Oi]=null,xl=Di[--Oi],Di[Oi]=null;for(;t===qa;)qa=Je[--tn],Je[tn]=null,Bn=Je[--tn],Je[tn]=null,Ln=Je[--tn],Je[tn]=null}var Re=null,It=null,Vt=!1,Xa=null,Sn=!1,kc=Error(l(519));function Ka(t){var e=Error(l(418,""));throw ks($e(e,t)),kc}function Sm(t){var e=t.stateNode,i=t.type,r=t.memoizedProps;switch(e[Se]=t,e[Oe]=r,i){case"dialog":Nt("cancel",e),Nt("close",e);break;case"iframe":case"object":case"embed":Nt("load",e);break;case"video":case"audio":for(i=0;i<or.length;i++)Nt(or[i],e);break;case"source":Nt("error",e);break;case"img":case"image":case"link":Nt("error",e),Nt("load",e);break;case"details":Nt("toggle",e);break;case"input":Nt("invalid",e),Lh(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),sl(e);break;case"select":Nt("invalid",e);break;case"textarea":Nt("invalid",e),kh(e,r.value,r.defaultValue,r.children),sl(e)}i=r.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||r.suppressHydrationWarning===!0||kg(e.textContent,i)?(r.popover!=null&&(Nt("beforetoggle",e),Nt("toggle",e)),r.onScroll!=null&&Nt("scroll",e),r.onScrollEnd!=null&&Nt("scrollend",e),r.onClick!=null&&(e.onclick=Jl),e=!0):e=!1,e||Ka(t)}function Tm(t){for(Re=t.return;Re;)switch(Re.tag){case 5:case 13:Sn=!1;return;case 27:case 3:Sn=!0;return;default:Re=Re.return}}function Ls(t){if(t!==Re)return!1;if(!Vt)return Tm(t),Vt=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||tf(t.type,t.memoizedProps)),i=!i),i&&It&&Ka(t),Tm(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){It=fn(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}It=null}}else e===27?(e=It,va(t.type)?(t=sf,sf=null,It=t):It=e):It=Re?fn(t.stateNode.nextSibling):null;return!0}function Bs(){It=Re=null,Vt=!1}function wm(){var t=Xa;return t!==null&&(ze===null?ze=t:ze.push.apply(ze,t),Xa=null),t}function ks(t){Xa===null?Xa=[t]:Xa.push(t)}var Uc=K(null),Za=null,kn=null;function aa(t,e,i){tt(Uc,e._currentValue),e._currentValue=i}function Un(t){t._currentValue=Uc.current,at(Uc)}function Hc(t,e,i){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===i)break;t=t.return}}function Pc(t,e,i,r){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){var y=o.child;u=u.firstContext;t:for(;u!==null;){var S=u;u=o;for(var C=0;C<e.length;C++)if(S.context===e[C]){u.lanes|=i,S=u.alternate,S!==null&&(S.lanes|=i),Hc(u.return,i,t),r||(y=null);break t}u=S.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(l(341));y.lanes|=i,u=y.alternate,u!==null&&(u.lanes|=i),Hc(y,i,t),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===t){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function Us(t,e,i,r){t=null;for(var o=e,u=!1;o!==null;){if(!u){if((o.flags&524288)!==0)u=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var S=o.type;Ue(o.pendingProps.value,y.value)||(t!==null?t.push(S):t=[S])}}else if(o===St.current){if(y=o.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(mr):t=[mr])}o=o.return}t!==null&&Pc(e,t,i,r),e.flags|=262144}function bl(t){for(t=t.firstContext;t!==null;){if(!Ue(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Qa(t){Za=t,kn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Te(t){return Am(Za,t)}function Sl(t,e){return Za===null&&Qa(t),Am(t,e)}function Am(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},kn===null){if(t===null)throw Error(l(308));kn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else kn=kn.next=e;return i}var q1=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},Y1=n.unstable_scheduleCallback,X1=n.unstable_NormalPriority,se={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Gc(){return{controller:new q1,data:new Map,refCount:0}}function Hs(t){t.refCount--,t.refCount===0&&Y1(X1,function(){t.controller.abort()})}var Ps=null,qc=0,ji=0,_i=null;function K1(t,e){if(Ps===null){var i=Ps=[];qc=0,ji=Xu(),_i={status:"pending",value:void 0,then:function(r){i.push(r)}}}return qc++,e.then(Em,Em),e}function Em(){if(--qc===0&&Ps!==null){_i!==null&&(_i.status="fulfilled");var t=Ps;Ps=null,ji=0,_i=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Z1(t,e){var i=[],r={status:"pending",value:null,reason:null,then:function(o){i.push(o)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var o=0;o<i.length;o++)(0,i[o])(e)},function(o){for(r.status="rejected",r.reason=o,o=0;o<i.length;o++)(0,i[o])(void 0)}),r}var Mm=B.S;B.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&K1(t,e),Mm!==null&&Mm(t,e)};var Fa=K(null);function Yc(){var t=Fa.current;return t!==null?t:qt.pooledCache}function Tl(t,e){e===null?tt(Fa,Fa.current):tt(Fa,e.pool)}function Cm(){var t=Yc();return t===null?null:{parent:se._currentValue,pool:t}}var Gs=Error(l(460)),Rm=Error(l(474)),wl=Error(l(542)),Xc={then:function(){}};function Nm(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Al(){}function Dm(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(Al,Al),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,jm(t),t;default:if(typeof e.status=="string")e.then(Al,Al);else{if(t=qt,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=r}},function(r){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,jm(t),t}throw qs=e,Gs}}var qs=null;function Om(){if(qs===null)throw Error(l(459));var t=qs;return qs=null,t}function jm(t){if(t===Gs||t===wl)throw Error(l(483))}var ia=!1;function Kc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Zc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function sa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ra(t,e,i){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(zt&2)!==0){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,e=gl(t),vm(t,null,i),e}return pl(t,r,e,i),gl(t)}function Ys(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,Eh(t,i)}}function Qc(t,e){var i=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,i===r)){var o=null,u=null;if(i=i.firstBaseUpdate,i!==null){do{var y={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};u===null?o=u=y:u=u.next=y,i=i.next}while(i!==null);u===null?o=u=e:u=u.next=e}else o=u=e;i={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:r.shared,callbacks:r.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var Fc=!1;function Xs(){if(Fc){var t=_i;if(t!==null)throw t}}function Ks(t,e,i,r){Fc=!1;var o=t.updateQueue;ia=!1;var u=o.firstBaseUpdate,y=o.lastBaseUpdate,S=o.shared.pending;if(S!==null){o.shared.pending=null;var C=S,U=C.next;C.next=null,y===null?u=U:y.next=U,y=C;var Q=t.alternate;Q!==null&&(Q=Q.updateQueue,S=Q.lastBaseUpdate,S!==y&&(S===null?Q.firstBaseUpdate=U:S.next=U,Q.lastBaseUpdate=C))}if(u!==null){var I=o.baseState;y=0,Q=U=C=null,S=u;do{var H=S.lane&-536870913,G=H!==S.lane;if(G?(Ot&H)===H:(r&H)===H){H!==0&&H===ji&&(Fc=!0),Q!==null&&(Q=Q.next={lane:0,tag:S.tag,payload:S.payload,callback:null,next:null});t:{var vt=t,pt=S;H=e;var Ht=i;switch(pt.tag){case 1:if(vt=pt.payload,typeof vt=="function"){I=vt.call(Ht,I,H);break t}I=vt;break t;case 3:vt.flags=vt.flags&-65537|128;case 0:if(vt=pt.payload,H=typeof vt=="function"?vt.call(Ht,I,H):vt,H==null)break t;I=g({},I,H);break t;case 2:ia=!0}}H=S.callback,H!==null&&(t.flags|=64,G&&(t.flags|=8192),G=o.callbacks,G===null?o.callbacks=[H]:G.push(H))}else G={lane:H,tag:S.tag,payload:S.payload,callback:S.callback,next:null},Q===null?(U=Q=G,C=I):Q=Q.next=G,y|=H;if(S=S.next,S===null){if(S=o.shared.pending,S===null)break;G=S,S=G.next,G.next=null,o.lastBaseUpdate=G,o.shared.pending=null}}while(!0);Q===null&&(C=I),o.baseState=C,o.firstBaseUpdate=U,o.lastBaseUpdate=Q,u===null&&(o.shared.lanes=0),ma|=y,t.lanes=y,t.memoizedState=I}}function _m(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Vm(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)_m(i[t],e)}var Vi=K(null),El=K(0);function zm(t,e){t=Kn,tt(El,t),tt(Vi,e),Kn=t|e.baseLanes}function Ic(){tt(El,Kn),tt(Vi,Vi.current)}function $c(){Kn=El.current,at(Vi),at(El)}var la=0,At=null,kt=null,ne=null,Ml=!1,zi=!1,Ia=!1,Cl=0,Zs=0,Li=null,Q1=0;function Jt(){throw Error(l(321))}function Wc(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!Ue(t[i],e[i]))return!1;return!0}function Jc(t,e,i,r,o,u){return la=u,At=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,B.H=t===null||t.memoizedState===null?vp:xp,Ia=!1,u=i(r,o),Ia=!1,zi&&(u=Bm(e,i,r,o)),Lm(t),u}function Lm(t){B.H=_l;var e=kt!==null&&kt.next!==null;if(la=0,ne=kt=At=null,Ml=!1,Zs=0,Li=null,e)throw Error(l(300));t===null||fe||(t=t.dependencies,t!==null&&bl(t)&&(fe=!0))}function Bm(t,e,i,r){At=t;var o=0;do{if(zi&&(Li=null),Zs=0,zi=!1,25<=o)throw Error(l(301));if(o+=1,ne=kt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}B.H=eT,u=e(i,r)}while(zi);return u}function F1(){var t=B.H,e=t.useState()[0];return e=typeof e.then=="function"?Qs(e):e,t=t.useState()[0],(kt!==null?kt.memoizedState:null)!==t&&(At.flags|=1024),e}function tu(){var t=Cl!==0;return Cl=0,t}function eu(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function nu(t){if(Ml){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Ml=!1}la=0,ne=kt=At=null,zi=!1,Zs=Cl=0,Li=null}function _e(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?At.memoizedState=ne=t:ne=ne.next=t,ne}function ae(){if(kt===null){var t=At.alternate;t=t!==null?t.memoizedState:null}else t=kt.next;var e=ne===null?At.memoizedState:ne.next;if(e!==null)ne=e,kt=t;else{if(t===null)throw At.alternate===null?Error(l(467)):Error(l(310));kt=t,t={memoizedState:kt.memoizedState,baseState:kt.baseState,baseQueue:kt.baseQueue,queue:kt.queue,next:null},ne===null?At.memoizedState=ne=t:ne=ne.next=t}return ne}function au(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qs(t){var e=Zs;return Zs+=1,Li===null&&(Li=[]),t=Dm(Li,t,e),e=At,(ne===null?e.memoizedState:ne.next)===null&&(e=e.alternate,B.H=e===null||e.memoizedState===null?vp:xp),t}function Rl(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qs(t);if(t.$$typeof===D)return Te(t)}throw Error(l(438,String(t)))}function iu(t){var e=null,i=At.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var r=At.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=au(),At.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),r=0;r<t;r++)i[r]=Y;return e.index++,i}function Hn(t,e){return typeof e=="function"?e(t):e}function Nl(t){var e=ae();return su(e,kt,t)}function su(t,e,i){var r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=i;var o=t.baseQueue,u=r.pending;if(u!==null){if(o!==null){var y=o.next;o.next=u.next,u.next=y}e.baseQueue=o=u,r.pending=null}if(u=t.baseState,o===null)t.memoizedState=u;else{e=o.next;var S=y=null,C=null,U=e,Q=!1;do{var I=U.lane&-536870913;if(I!==U.lane?(Ot&I)===I:(la&I)===I){var H=U.revertLane;if(H===0)C!==null&&(C=C.next={lane:0,revertLane:0,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null}),I===ji&&(Q=!0);else if((la&H)===H){U=U.next,H===ji&&(Q=!0);continue}else I={lane:0,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},C===null?(S=C=I,y=u):C=C.next=I,At.lanes|=H,ma|=H;I=U.action,Ia&&i(u,I),u=U.hasEagerState?U.eagerState:i(u,I)}else H={lane:I,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},C===null?(S=C=H,y=u):C=C.next=H,At.lanes|=I,ma|=I;U=U.next}while(U!==null&&U!==e);if(C===null?y=u:C.next=S,!Ue(u,t.memoizedState)&&(fe=!0,Q&&(i=_i,i!==null)))throw i;t.memoizedState=u,t.baseState=y,t.baseQueue=C,r.lastRenderedState=u}return o===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function ru(t){var e=ae(),i=e.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=t;var r=i.dispatch,o=i.pending,u=e.memoizedState;if(o!==null){i.pending=null;var y=o=o.next;do u=t(u,y.action),y=y.next;while(y!==o);Ue(u,e.memoizedState)||(fe=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),i.lastRenderedState=u}return[u,r]}function km(t,e,i){var r=At,o=ae(),u=Vt;if(u){if(i===void 0)throw Error(l(407));i=i()}else i=e();var y=!Ue((kt||o).memoizedState,i);y&&(o.memoizedState=i,fe=!0),o=o.queue;var S=Pm.bind(null,r,o,t);if(Fs(2048,8,S,[t]),o.getSnapshot!==e||y||ne!==null&&ne.memoizedState.tag&1){if(r.flags|=2048,Bi(9,Dl(),Hm.bind(null,r,o,i,e),null),qt===null)throw Error(l(349));u||(la&124)!==0||Um(r,e,i)}return i}function Um(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=At.updateQueue,e===null?(e=au(),At.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function Hm(t,e,i,r){e.value=i,e.getSnapshot=r,Gm(e)&&qm(t)}function Pm(t,e,i){return i(function(){Gm(e)&&qm(t)})}function Gm(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!Ue(t,i)}catch(r){return!0}}function qm(t){var e=Ri(t,2);e!==null&&Xe(e,t,2)}function lu(t){var e=_e();if(typeof t=="function"){var i=t;if(t=i(),Ia){ta(!0);try{i()}finally{ta(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:t},e}function Ym(t,e,i,r){return t.baseState=i,su(t,kt,typeof r=="function"?r:Hn)}function I1(t,e,i,r,o){if(jl(t))throw Error(l(485));if(t=e.action,t!==null){var u={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){u.listeners.push(y)}};B.T!==null?i(!0):u.isTransition=!1,r(u),i=e.pending,i===null?(u.next=e.pending=u,Xm(e,u)):(u.next=i.next,e.pending=i.next=u)}}function Xm(t,e){var i=e.action,r=e.payload,o=t.state;if(e.isTransition){var u=B.T,y={};B.T=y;try{var S=i(o,r),C=B.S;C!==null&&C(y,S),Km(t,e,S)}catch(U){ou(t,e,U)}finally{B.T=u}}else try{u=i(o,r),Km(t,e,u)}catch(U){ou(t,e,U)}}function Km(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(r){Zm(t,e,r)},function(r){return ou(t,e,r)}):Zm(t,e,i)}function Zm(t,e,i){e.status="fulfilled",e.value=i,Qm(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,Xm(t,i)))}function ou(t,e,i){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=i,Qm(e),e=e.next;while(e!==r)}t.action=null}function Qm(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Fm(t,e){return e}function Im(t,e){if(Vt){var i=qt.formState;if(i!==null){t:{var r=At;if(Vt){if(It){e:{for(var o=It,u=Sn;o.nodeType!==8;){if(!u){o=null;break e}if(o=fn(o.nextSibling),o===null){o=null;break e}}u=o.data,o=u==="F!"||u==="F"?o:null}if(o){It=fn(o.nextSibling),r=o.data==="F!";break t}}Ka(r)}r=!1}r&&(e=i[0])}}return i=_e(),i.memoizedState=i.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fm,lastRenderedState:e},i.queue=r,i=pp.bind(null,At,r),r.dispatch=i,r=lu(!1),u=hu.bind(null,At,!1,r.queue),r=_e(),o={state:e,dispatch:null,action:t,pending:null},r.queue=o,i=I1.bind(null,At,o,u,i),o.dispatch=i,r.memoizedState=t,[e,i,!1]}function $m(t){var e=ae();return Wm(e,kt,t)}function Wm(t,e,i){if(e=su(t,e,Fm)[0],t=Nl(Hn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Qs(e)}catch(y){throw y===Gs?wl:y}else r=e;e=ae();var o=e.queue,u=o.dispatch;return i!==e.memoizedState&&(At.flags|=2048,Bi(9,Dl(),$1.bind(null,o,i),null)),[r,u,t]}function $1(t,e){t.action=e}function Jm(t){var e=ae(),i=kt;if(i!==null)return Wm(e,i,t);ae(),e=e.memoizedState,i=ae();var r=i.queue.dispatch;return i.memoizedState=t,[e,r,!1]}function Bi(t,e,i,r){return t={tag:t,create:i,deps:r,inst:e,next:null},e=At.updateQueue,e===null&&(e=au(),At.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(r=i.next,i.next=t,t.next=r,e.lastEffect=t),t}function Dl(){return{destroy:void 0,resource:void 0}}function tp(){return ae().memoizedState}function Ol(t,e,i,r){var o=_e();r=r===void 0?null:r,At.flags|=t,o.memoizedState=Bi(1|e,Dl(),i,r)}function Fs(t,e,i,r){var o=ae();r=r===void 0?null:r;var u=o.memoizedState.inst;kt!==null&&r!==null&&Wc(r,kt.memoizedState.deps)?o.memoizedState=Bi(e,u,i,r):(At.flags|=t,o.memoizedState=Bi(1|e,u,i,r))}function ep(t,e){Ol(8390656,8,t,e)}function np(t,e){Fs(2048,8,t,e)}function ap(t,e){return Fs(4,2,t,e)}function ip(t,e){return Fs(4,4,t,e)}function sp(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function rp(t,e,i){i=i!=null?i.concat([t]):null,Fs(4,4,sp.bind(null,e,t),i)}function cu(){}function lp(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;return e!==null&&Wc(e,r[1])?r[0]:(i.memoizedState=[t,e],t)}function op(t,e){var i=ae();e=e===void 0?null:e;var r=i.memoizedState;if(e!==null&&Wc(e,r[1]))return r[0];if(r=t(),Ia){ta(!0);try{t()}finally{ta(!1)}}return i.memoizedState=[r,e],r}function uu(t,e,i){return i===void 0||(la&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=fg(),At.lanes|=t,ma|=t,i)}function cp(t,e,i,r){return Ue(i,e)?i:Vi.current!==null?(t=uu(t,i,r),Ue(t,e)||(fe=!0),t):(la&42)===0?(fe=!0,t.memoizedState=i):(t=fg(),At.lanes|=t,ma|=t,e)}function up(t,e,i,r,o){var u=X.p;X.p=u!==0&&8>u?u:8;var y=B.T,S={};B.T=S,hu(t,!1,e,i);try{var C=o(),U=B.S;if(U!==null&&U(S,C),C!==null&&typeof C=="object"&&typeof C.then=="function"){var Q=Z1(C,r);Is(t,e,Q,Ye(t))}else Is(t,e,r,Ye(t))}catch(I){Is(t,e,{then:function(){},status:"rejected",reason:I},Ye())}finally{X.p=u,B.T=y}}function W1(){}function fu(t,e,i,r){if(t.tag!==5)throw Error(l(476));var o=fp(t).queue;up(t,o,e,P,i===null?W1:function(){return dp(t),i(r)})}function fp(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:P},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function dp(t){var e=fp(t).next.queue;Is(t,e,{},Ye())}function du(){return Te(mr)}function hp(){return ae().memoizedState}function mp(){return ae().memoizedState}function J1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=Ye();t=sa(i);var r=ra(e,t,i);r!==null&&(Xe(r,e,i),Ys(r,e,i)),e={cache:Gc()},t.payload=e;return}e=e.return}}function tT(t,e,i){var r=Ye();i={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},jl(t)?gp(e,i):(i=jc(t,e,i,r),i!==null&&(Xe(i,t,r),yp(i,e,r)))}function pp(t,e,i){var r=Ye();Is(t,e,i,r)}function Is(t,e,i,r){var o={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(jl(t))gp(e,o);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var y=e.lastRenderedState,S=u(y,i);if(o.hasEagerState=!0,o.eagerState=S,Ue(S,y))return pl(t,e,o,0),qt===null&&ml(),!1}catch(C){}finally{}if(i=jc(t,e,o,r),i!==null)return Xe(i,t,r),yp(i,e,r),!0}return!1}function hu(t,e,i,r){if(r={lane:2,revertLane:Xu(),action:r,hasEagerState:!1,eagerState:null,next:null},jl(t)){if(e)throw Error(l(479))}else e=jc(t,i,r,2),e!==null&&Xe(e,t,2)}function jl(t){var e=t.alternate;return t===At||e!==null&&e===At}function gp(t,e){zi=Ml=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function yp(t,e,i){if((i&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,Eh(t,i)}}var _l={readContext:Te,use:Rl,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},vp={readContext:Te,use:Rl,useCallback:function(t,e){return _e().memoizedState=[t,e===void 0?null:e],t},useContext:Te,useEffect:ep,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Ol(4194308,4,sp.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Ol(4194308,4,t,e)},useInsertionEffect:function(t,e){Ol(4,2,t,e)},useMemo:function(t,e){var i=_e();e=e===void 0?null:e;var r=t();if(Ia){ta(!0);try{t()}finally{ta(!1)}}return i.memoizedState=[r,e],r},useReducer:function(t,e,i){var r=_e();if(i!==void 0){var o=i(e);if(Ia){ta(!0);try{i(e)}finally{ta(!1)}}}else o=e;return r.memoizedState=r.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},r.queue=t,t=t.dispatch=tT.bind(null,At,t),[r.memoizedState,t]},useRef:function(t){var e=_e();return t={current:t},e.memoizedState=t},useState:function(t){t=lu(t);var e=t.queue,i=pp.bind(null,At,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:cu,useDeferredValue:function(t,e){var i=_e();return uu(i,t,e)},useTransition:function(){var t=lu(!1);return t=up.bind(null,At,t.queue,!0,!1),_e().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var r=At,o=_e();if(Vt){if(i===void 0)throw Error(l(407));i=i()}else{if(i=e(),qt===null)throw Error(l(349));(Ot&124)!==0||Um(r,e,i)}o.memoizedState=i;var u={value:i,getSnapshot:e};return o.queue=u,ep(Pm.bind(null,r,u,t),[t]),r.flags|=2048,Bi(9,Dl(),Hm.bind(null,r,u,i,e),null),i},useId:function(){var t=_e(),e=qt.identifierPrefix;if(Vt){var i=Bn,r=Ln;i=(r&~(1<<32-ke(r)-1)).toString(32)+i,e="«"+e+"R"+i,i=Cl++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=Q1++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:du,useFormState:Im,useActionState:Im,useOptimistic:function(t){var e=_e();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=hu.bind(null,At,!0,i),i.dispatch=e,[t,e]},useMemoCache:iu,useCacheRefresh:function(){return _e().memoizedState=J1.bind(null,At)}},xp={readContext:Te,use:Rl,useCallback:lp,useContext:Te,useEffect:np,useImperativeHandle:rp,useInsertionEffect:ap,useLayoutEffect:ip,useMemo:op,useReducer:Nl,useRef:tp,useState:function(){return Nl(Hn)},useDebugValue:cu,useDeferredValue:function(t,e){var i=ae();return cp(i,kt.memoizedState,t,e)},useTransition:function(){var t=Nl(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:km,useId:hp,useHostTransitionStatus:du,useFormState:$m,useActionState:$m,useOptimistic:function(t,e){var i=ae();return Ym(i,kt,t,e)},useMemoCache:iu,useCacheRefresh:mp},eT={readContext:Te,use:Rl,useCallback:lp,useContext:Te,useEffect:np,useImperativeHandle:rp,useInsertionEffect:ap,useLayoutEffect:ip,useMemo:op,useReducer:ru,useRef:tp,useState:function(){return ru(Hn)},useDebugValue:cu,useDeferredValue:function(t,e){var i=ae();return kt===null?uu(i,t,e):cp(i,kt.memoizedState,t,e)},useTransition:function(){var t=ru(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Qs(t),e]},useSyncExternalStore:km,useId:hp,useHostTransitionStatus:du,useFormState:Jm,useActionState:Jm,useOptimistic:function(t,e){var i=ae();return kt!==null?Ym(i,kt,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:iu,useCacheRefresh:mp},ki=null,$s=0;function Vl(t){var e=$s;return $s+=1,ki===null&&(ki=[]),Dm(ki,t,e)}function Ws(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zl(t,e){throw e.$$typeof===v?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function bp(t){var e=t._init;return e(t._payload)}function Sp(t){function e(z,_){if(t){var k=z.deletions;k===null?(z.deletions=[_],z.flags|=16):k.push(_)}}function i(z,_){if(!t)return null;for(;_!==null;)e(z,_),_=_.sibling;return null}function r(z){for(var _=new Map;z!==null;)z.key!==null?_.set(z.key,z):_.set(z.index,z),z=z.sibling;return _}function o(z,_){return z=zn(z,_),z.index=0,z.sibling=null,z}function u(z,_,k){return z.index=k,t?(k=z.alternate,k!==null?(k=k.index,k<_?(z.flags|=67108866,_):k):(z.flags|=67108866,_)):(z.flags|=1048576,_)}function y(z){return t&&z.alternate===null&&(z.flags|=67108866),z}function S(z,_,k,F){return _===null||_.tag!==6?(_=Vc(k,z.mode,F),_.return=z,_):(_=o(_,k),_.return=z,_)}function C(z,_,k,F){var lt=k.type;return lt===M?Q(z,_,k.props.children,F,k.key):_!==null&&(_.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===q&&bp(lt)===_.type)?(_=o(_,k.props),Ws(_,k),_.return=z,_):(_=yl(k.type,k.key,k.props,null,z.mode,F),Ws(_,k),_.return=z,_)}function U(z,_,k,F){return _===null||_.tag!==4||_.stateNode.containerInfo!==k.containerInfo||_.stateNode.implementation!==k.implementation?(_=zc(k,z.mode,F),_.return=z,_):(_=o(_,k.children||[]),_.return=z,_)}function Q(z,_,k,F,lt){return _===null||_.tag!==7?(_=Ga(k,z.mode,F,lt),_.return=z,_):(_=o(_,k),_.return=z,_)}function I(z,_,k){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=Vc(""+_,z.mode,k),_.return=z,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case b:return k=yl(_.type,_.key,_.props,null,z.mode,k),Ws(k,_),k.return=z,k;case T:return _=zc(_,z.mode,k),_.return=z,_;case q:var F=_._init;return _=F(_._payload),I(z,_,k)}if(ft(_)||nt(_))return _=Ga(_,z.mode,k,null),_.return=z,_;if(typeof _.then=="function")return I(z,Vl(_),k);if(_.$$typeof===D)return I(z,Sl(z,_),k);zl(z,_)}return null}function H(z,_,k,F){var lt=_!==null?_.key:null;if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return lt!==null?null:S(z,_,""+k,F);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case b:return k.key===lt?C(z,_,k,F):null;case T:return k.key===lt?U(z,_,k,F):null;case q:return lt=k._init,k=lt(k._payload),H(z,_,k,F)}if(ft(k)||nt(k))return lt!==null?null:Q(z,_,k,F,null);if(typeof k.then=="function")return H(z,_,Vl(k),F);if(k.$$typeof===D)return H(z,_,Sl(z,k),F);zl(z,k)}return null}function G(z,_,k,F,lt){if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return z=z.get(k)||null,S(_,z,""+F,lt);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case b:return z=z.get(F.key===null?k:F.key)||null,C(_,z,F,lt);case T:return z=z.get(F.key===null?k:F.key)||null,U(_,z,F,lt);case q:var Mt=F._init;return F=Mt(F._payload),G(z,_,k,F,lt)}if(ft(F)||nt(F))return z=z.get(k)||null,Q(_,z,F,lt,null);if(typeof F.then=="function")return G(z,_,k,Vl(F),lt);if(F.$$typeof===D)return G(z,_,k,Sl(_,F),lt);zl(_,F)}return null}function vt(z,_,k,F){for(var lt=null,Mt=null,dt=_,gt=_=0,he=null;dt!==null&&gt<k.length;gt++){dt.index>gt?(he=dt,dt=null):he=dt.sibling;var _t=H(z,dt,k[gt],F);if(_t===null){dt===null&&(dt=he);break}t&&dt&&_t.alternate===null&&e(z,dt),_=u(_t,_,gt),Mt===null?lt=_t:Mt.sibling=_t,Mt=_t,dt=he}if(gt===k.length)return i(z,dt),Vt&&Ya(z,gt),lt;if(dt===null){for(;gt<k.length;gt++)dt=I(z,k[gt],F),dt!==null&&(_=u(dt,_,gt),Mt===null?lt=dt:Mt.sibling=dt,Mt=dt);return Vt&&Ya(z,gt),lt}for(dt=r(dt);gt<k.length;gt++)he=G(dt,z,gt,k[gt],F),he!==null&&(t&&he.alternate!==null&&dt.delete(he.key===null?gt:he.key),_=u(he,_,gt),Mt===null?lt=he:Mt.sibling=he,Mt=he);return t&&dt.forEach(function(wa){return e(z,wa)}),Vt&&Ya(z,gt),lt}function pt(z,_,k,F){if(k==null)throw Error(l(151));for(var lt=null,Mt=null,dt=_,gt=_=0,he=null,_t=k.next();dt!==null&&!_t.done;gt++,_t=k.next()){dt.index>gt?(he=dt,dt=null):he=dt.sibling;var wa=H(z,dt,_t.value,F);if(wa===null){dt===null&&(dt=he);break}t&&dt&&wa.alternate===null&&e(z,dt),_=u(wa,_,gt),Mt===null?lt=wa:Mt.sibling=wa,Mt=wa,dt=he}if(_t.done)return i(z,dt),Vt&&Ya(z,gt),lt;if(dt===null){for(;!_t.done;gt++,_t=k.next())_t=I(z,_t.value,F),_t!==null&&(_=u(_t,_,gt),Mt===null?lt=_t:Mt.sibling=_t,Mt=_t);return Vt&&Ya(z,gt),lt}for(dt=r(dt);!_t.done;gt++,_t=k.next())_t=G(dt,z,gt,_t.value,F),_t!==null&&(t&&_t.alternate!==null&&dt.delete(_t.key===null?gt:_t.key),_=u(_t,_,gt),Mt===null?lt=_t:Mt.sibling=_t,Mt=_t);return t&&dt.forEach(function(nw){return e(z,nw)}),Vt&&Ya(z,gt),lt}function Ht(z,_,k,F){if(typeof k=="object"&&k!==null&&k.type===M&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case b:t:{for(var lt=k.key;_!==null;){if(_.key===lt){if(lt=k.type,lt===M){if(_.tag===7){i(z,_.sibling),F=o(_,k.props.children),F.return=z,z=F;break t}}else if(_.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===q&&bp(lt)===_.type){i(z,_.sibling),F=o(_,k.props),Ws(F,k),F.return=z,z=F;break t}i(z,_);break}else e(z,_);_=_.sibling}k.type===M?(F=Ga(k.props.children,z.mode,F,k.key),F.return=z,z=F):(F=yl(k.type,k.key,k.props,null,z.mode,F),Ws(F,k),F.return=z,z=F)}return y(z);case T:t:{for(lt=k.key;_!==null;){if(_.key===lt)if(_.tag===4&&_.stateNode.containerInfo===k.containerInfo&&_.stateNode.implementation===k.implementation){i(z,_.sibling),F=o(_,k.children||[]),F.return=z,z=F;break t}else{i(z,_);break}else e(z,_);_=_.sibling}F=zc(k,z.mode,F),F.return=z,z=F}return y(z);case q:return lt=k._init,k=lt(k._payload),Ht(z,_,k,F)}if(ft(k))return vt(z,_,k,F);if(nt(k)){if(lt=nt(k),typeof lt!="function")throw Error(l(150));return k=lt.call(k),pt(z,_,k,F)}if(typeof k.then=="function")return Ht(z,_,Vl(k),F);if(k.$$typeof===D)return Ht(z,_,Sl(z,k),F);zl(z,k)}return typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint"?(k=""+k,_!==null&&_.tag===6?(i(z,_.sibling),F=o(_,k),F.return=z,z=F):(i(z,_),F=Vc(k,z.mode,F),F.return=z,z=F),y(z)):i(z,_)}return function(z,_,k,F){try{$s=0;var lt=Ht(z,_,k,F);return ki=null,lt}catch(dt){if(dt===Gs||dt===wl)throw dt;var Mt=He(29,dt,null,z.mode);return Mt.lanes=F,Mt.return=z,Mt}finally{}}}var Ui=Sp(!0),Tp=Sp(!1),en=K(null),Tn=null;function oa(t){var e=t.alternate;tt(re,re.current&1),tt(en,t),Tn===null&&(e===null||Vi.current!==null||e.memoizedState!==null)&&(Tn=t)}function wp(t){if(t.tag===22){if(tt(re,re.current),tt(en,t),Tn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Tn=t)}}else ca()}function ca(){tt(re,re.current),tt(en,en.current)}function Pn(t){at(en),Tn===t&&(Tn=null),at(re)}var re=K(0);function Ll(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||af(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function mu(t,e,i,r){e=t.memoizedState,i=i(r,e),i=i==null?e:g({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var pu={enqueueSetState:function(t,e,i){t=t._reactInternals;var r=Ye(),o=sa(r);o.payload=e,i!=null&&(o.callback=i),e=ra(t,o,r),e!==null&&(Xe(e,t,r),Ys(e,t,r))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var r=Ye(),o=sa(r);o.tag=1,o.payload=e,i!=null&&(o.callback=i),e=ra(t,o,r),e!==null&&(Xe(e,t,r),Ys(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=Ye(),r=sa(i);r.tag=2,e!=null&&(r.callback=e),e=ra(t,r,i),e!==null&&(Xe(e,t,i),Ys(e,t,i))}};function Ap(t,e,i,r,o,u,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,u,y):e.prototype&&e.prototype.isPureReactComponent?!Vs(i,r)||!Vs(o,u):!0}function Ep(t,e,i,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,r),e.state!==t&&pu.enqueueReplaceState(e,e.state,null)}function $a(t,e){var i=e;if("ref"in e){i={};for(var r in e)r!=="ref"&&(i[r]=e[r])}if(t=t.defaultProps){i===e&&(i=g({},i));for(var o in t)i[o]===void 0&&(i[o]=t[o])}return i}var Bl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Mp(t){Bl(t)}function Cp(t){console.error(t)}function Rp(t){Bl(t)}function kl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function Np(t,e,i){try{var r=t.onCaughtError;r(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function gu(t,e,i){return i=sa(i),i.tag=3,i.payload={element:null},i.callback=function(){kl(t,e)},i}function Dp(t){return t=sa(t),t.tag=3,t}function Op(t,e,i,r){var o=i.type.getDerivedStateFromError;if(typeof o=="function"){var u=r.value;t.payload=function(){return o(u)},t.callback=function(){Np(e,i,r)}}var y=i.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Np(e,i,r),typeof o!="function"&&(pa===null?pa=new Set([this]):pa.add(this));var S=r.stack;this.componentDidCatch(r.value,{componentStack:S!==null?S:""})})}function nT(t,e,i,r,o){if(i.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=i.alternate,e!==null&&Us(e,i,o,!0),i=en.current,i!==null){switch(i.tag){case 13:return Tn===null?Hu():i.alternate===null&&$t===0&&($t=3),i.flags&=-257,i.flags|=65536,i.lanes=o,r===Xc?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([r]):e.add(r),Gu(t,r,o)),!1;case 22:return i.flags|=65536,r===Xc?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([r]):i.add(r)),Gu(t,r,o)),!1}throw Error(l(435,i.tag))}return Gu(t,r,o),Hu(),!1}if(Vt)return e=en.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,r!==kc&&(t=Error(l(422),{cause:r}),ks($e(t,i)))):(r!==kc&&(e=Error(l(423),{cause:r}),ks($e(e,i))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,r=$e(r,i),o=gu(t.stateNode,r,o),Qc(t,o),$t!==4&&($t=2)),!1;var u=Error(l(520),{cause:r});if(u=$e(u,i),sr===null?sr=[u]:sr.push(u),$t!==4&&($t=2),e===null)return!0;r=$e(r,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=o&-o,i.lanes|=t,t=gu(i.stateNode,r,t),Qc(i,t),!1;case 1:if(e=i.type,u=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(pa===null||!pa.has(u))))return i.flags|=65536,o&=-o,i.lanes|=o,o=Dp(o),Op(o,t,i,r),Qc(i,o),!1}i=i.return}while(i!==null);return!1}var jp=Error(l(461)),fe=!1;function ye(t,e,i,r){e.child=t===null?Tp(e,null,i,r):Ui(e,t.child,i,r)}function _p(t,e,i,r,o){i=i.render;var u=e.ref;if("ref"in r){var y={};for(var S in r)S!=="ref"&&(y[S]=r[S])}else y=r;return Qa(e),r=Jc(t,e,i,y,u,o),S=tu(),t!==null&&!fe?(eu(t,e,o),Gn(t,e,o)):(Vt&&S&&Lc(e),e.flags|=1,ye(t,e,r,o),e.child)}function Vp(t,e,i,r,o){if(t===null){var u=i.type;return typeof u=="function"&&!_c(u)&&u.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=u,zp(t,e,u,r,o)):(t=yl(i.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Au(t,o)){var y=u.memoizedProps;if(i=i.compare,i=i!==null?i:Vs,i(y,r)&&t.ref===e.ref)return Gn(t,e,o)}return e.flags|=1,t=zn(u,r),t.ref=e.ref,t.return=e,e.child=t}function zp(t,e,i,r,o){if(t!==null){var u=t.memoizedProps;if(Vs(u,r)&&t.ref===e.ref)if(fe=!1,e.pendingProps=r=u,Au(t,o))(t.flags&131072)!==0&&(fe=!0);else return e.lanes=t.lanes,Gn(t,e,o)}return yu(t,e,i,r,o)}function Lp(t,e,i){var r=e.pendingProps,o=r.children,u=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=u!==null?u.baseLanes|i:i,t!==null){for(o=e.child=t.child,u=0;o!==null;)u=u|o.lanes|o.childLanes,o=o.sibling;e.childLanes=u&~r}else e.childLanes=0,e.child=null;return Bp(t,e,r,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Tl(e,u!==null?u.cachePool:null),u!==null?zm(e,u):Ic(),wp(e);else return e.lanes=e.childLanes=536870912,Bp(t,e,u!==null?u.baseLanes|i:i,i)}else u!==null?(Tl(e,u.cachePool),zm(e,u),ca(),e.memoizedState=null):(t!==null&&Tl(e,null),Ic(),ca());return ye(t,e,o,i),e.child}function Bp(t,e,i,r){var o=Yc();return o=o===null?null:{parent:se._currentValue,pool:o},e.memoizedState={baseLanes:i,cachePool:o},t!==null&&Tl(e,null),Ic(),wp(e),t!==null&&Us(t,e,r,!0),null}function Ul(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(l(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function yu(t,e,i,r,o){return Qa(e),i=Jc(t,e,i,r,void 0,o),r=tu(),t!==null&&!fe?(eu(t,e,o),Gn(t,e,o)):(Vt&&r&&Lc(e),e.flags|=1,ye(t,e,i,o),e.child)}function kp(t,e,i,r,o,u){return Qa(e),e.updateQueue=null,i=Bm(e,r,i,o),Lm(t),r=tu(),t!==null&&!fe?(eu(t,e,u),Gn(t,e,u)):(Vt&&r&&Lc(e),e.flags|=1,ye(t,e,i,u),e.child)}function Up(t,e,i,r,o){if(Qa(e),e.stateNode===null){var u=Ni,y=i.contextType;typeof y=="object"&&y!==null&&(u=Te(y)),u=new i(r,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=pu,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=r,u.state=e.memoizedState,u.refs={},Kc(e),y=i.contextType,u.context=typeof y=="object"&&y!==null?Te(y):Ni,u.state=e.memoizedState,y=i.getDerivedStateFromProps,typeof y=="function"&&(mu(e,i,y,r),u.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(y=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),y!==u.state&&pu.enqueueReplaceState(u,u.state,null),Ks(e,r,u,o),Xs(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){u=e.stateNode;var S=e.memoizedProps,C=$a(i,S);u.props=C;var U=u.context,Q=i.contextType;y=Ni,typeof Q=="object"&&Q!==null&&(y=Te(Q));var I=i.getDerivedStateFromProps;Q=typeof I=="function"||typeof u.getSnapshotBeforeUpdate=="function",S=e.pendingProps!==S,Q||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(S||U!==y)&&Ep(e,u,r,y),ia=!1;var H=e.memoizedState;u.state=H,Ks(e,r,u,o),Xs(),U=e.memoizedState,S||H!==U||ia?(typeof I=="function"&&(mu(e,i,I,r),U=e.memoizedState),(C=ia||Ap(e,i,C,r,H,U,y))?(Q||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=U),u.props=r,u.state=U,u.context=y,r=C):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{u=e.stateNode,Zc(t,e),y=e.memoizedProps,Q=$a(i,y),u.props=Q,I=e.pendingProps,H=u.context,U=i.contextType,C=Ni,typeof U=="object"&&U!==null&&(C=Te(U)),S=i.getDerivedStateFromProps,(U=typeof S=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(y!==I||H!==C)&&Ep(e,u,r,C),ia=!1,H=e.memoizedState,u.state=H,Ks(e,r,u,o),Xs();var G=e.memoizedState;y!==I||H!==G||ia||t!==null&&t.dependencies!==null&&bl(t.dependencies)?(typeof S=="function"&&(mu(e,i,S,r),G=e.memoizedState),(Q=ia||Ap(e,i,Q,r,H,G,C)||t!==null&&t.dependencies!==null&&bl(t.dependencies))?(U||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(r,G,C),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(r,G,C)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=G),u.props=r,u.state=G,u.context=C,r=Q):(typeof u.componentDidUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),r=!1)}return u=r,Ul(t,e),r=(e.flags&128)!==0,u||r?(u=e.stateNode,i=r&&typeof i.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&r?(e.child=Ui(e,t.child,null,o),e.child=Ui(e,null,i,o)):ye(t,e,i,o),e.memoizedState=u.state,t=e.child):t=Gn(t,e,o),t}function Hp(t,e,i,r){return Bs(),e.flags|=256,ye(t,e,i,r),e.child}var vu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xu(t){return{baseLanes:t,cachePool:Cm()}}function bu(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=nn),t}function Pp(t,e,i){var r=e.pendingProps,o=!1,u=(e.flags&128)!==0,y;if((y=u)||(y=t!==null&&t.memoizedState===null?!1:(re.current&2)!==0),y&&(o=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(Vt){if(o?oa(e):ca(),Vt){var S=It,C;if(C=S){t:{for(C=S,S=Sn;C.nodeType!==8;){if(!S){S=null;break t}if(C=fn(C.nextSibling),C===null){S=null;break t}}S=C}S!==null?(e.memoizedState={dehydrated:S,treeContext:qa!==null?{id:Ln,overflow:Bn}:null,retryLane:536870912,hydrationErrors:null},C=He(18,null,null,0),C.stateNode=S,C.return=e,e.child=C,Re=e,It=null,C=!0):C=!1}C||Ka(e)}if(S=e.memoizedState,S!==null&&(S=S.dehydrated,S!==null))return af(S)?e.lanes=32:e.lanes=536870912,null;Pn(e)}return S=r.children,r=r.fallback,o?(ca(),o=e.mode,S=Hl({mode:"hidden",children:S},o),r=Ga(r,o,i,null),S.return=e,r.return=e,S.sibling=r,e.child=S,o=e.child,o.memoizedState=xu(i),o.childLanes=bu(t,y,i),e.memoizedState=vu,r):(oa(e),Su(e,S))}if(C=t.memoizedState,C!==null&&(S=C.dehydrated,S!==null)){if(u)e.flags&256?(oa(e),e.flags&=-257,e=Tu(t,e,i)):e.memoizedState!==null?(ca(),e.child=t.child,e.flags|=128,e=null):(ca(),o=r.fallback,S=e.mode,r=Hl({mode:"visible",children:r.children},S),o=Ga(o,S,i,null),o.flags|=2,r.return=e,o.return=e,r.sibling=o,e.child=r,Ui(e,t.child,null,i),r=e.child,r.memoizedState=xu(i),r.childLanes=bu(t,y,i),e.memoizedState=vu,e=o);else if(oa(e),af(S)){if(y=S.nextSibling&&S.nextSibling.dataset,y)var U=y.dgst;y=U,r=Error(l(419)),r.stack="",r.digest=y,ks({value:r,source:null,stack:null}),e=Tu(t,e,i)}else if(fe||Us(t,e,i,!1),y=(i&t.childLanes)!==0,fe||y){if(y=qt,y!==null&&(r=i&-i,r=(r&42)!==0?1:ic(r),r=(r&(y.suspendedLanes|i))!==0?0:r,r!==0&&r!==C.retryLane))throw C.retryLane=r,Ri(t,r),Xe(y,t,r),jp;S.data==="$?"||Hu(),e=Tu(t,e,i)}else S.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=C.treeContext,It=fn(S.nextSibling),Re=e,Vt=!0,Xa=null,Sn=!1,t!==null&&(Je[tn++]=Ln,Je[tn++]=Bn,Je[tn++]=qa,Ln=t.id,Bn=t.overflow,qa=e),e=Su(e,r.children),e.flags|=4096);return e}return o?(ca(),o=r.fallback,S=e.mode,C=t.child,U=C.sibling,r=zn(C,{mode:"hidden",children:r.children}),r.subtreeFlags=C.subtreeFlags&65011712,U!==null?o=zn(U,o):(o=Ga(o,S,i,null),o.flags|=2),o.return=e,r.return=e,r.sibling=o,e.child=r,r=o,o=e.child,S=t.child.memoizedState,S===null?S=xu(i):(C=S.cachePool,C!==null?(U=se._currentValue,C=C.parent!==U?{parent:U,pool:U}:C):C=Cm(),S={baseLanes:S.baseLanes|i,cachePool:C}),o.memoizedState=S,o.childLanes=bu(t,y,i),e.memoizedState=vu,r):(oa(e),i=t.child,t=i.sibling,i=zn(i,{mode:"visible",children:r.children}),i.return=e,i.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=i,e.memoizedState=null,i)}function Su(t,e){return e=Hl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Hl(t,e){return t=He(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Tu(t,e,i){return Ui(e,t.child,null,i),t=Su(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Gp(t,e,i){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Hc(t.return,e,i)}function wu(t,e,i,r,o){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:i,tailMode:o}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=r,u.tail=i,u.tailMode=o)}function qp(t,e,i){var r=e.pendingProps,o=r.revealOrder,u=r.tail;if(ye(t,e,r.children,i),r=re.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Gp(t,i,e);else if(t.tag===19)Gp(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(tt(re,r),o){case"forwards":for(i=e.child,o=null;i!==null;)t=i.alternate,t!==null&&Ll(t)===null&&(o=i),i=i.sibling;i=o,i===null?(o=e.child,e.child=null):(o=i.sibling,i.sibling=null),wu(e,!1,o,i,u);break;case"backwards":for(i=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Ll(t)===null){e.child=o;break}t=o.sibling,o.sibling=i,i=o,o=t}wu(e,!0,i,null,u);break;case"together":wu(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Gn(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),ma|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(Us(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,i=zn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=zn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Au(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&bl(t)))}function aT(t,e,i){switch(e.tag){case 3:st(e,e.stateNode.containerInfo),aa(e,se,t.memoizedState.cache),Bs();break;case 27:case 5:Yt(e);break;case 4:st(e,e.stateNode.containerInfo);break;case 10:aa(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(oa(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Pp(t,e,i):(oa(e),t=Gn(t,e,i),t!==null?t.sibling:null);oa(e);break;case 19:var o=(t.flags&128)!==0;if(r=(i&e.childLanes)!==0,r||(Us(t,e,i,!1),r=(i&e.childLanes)!==0),o){if(r)return qp(t,e,i);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),tt(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Lp(t,e,i);case 24:aa(e,se,t.memoizedState.cache)}return Gn(t,e,i)}function Yp(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)fe=!0;else{if(!Au(t,i)&&(e.flags&128)===0)return fe=!1,aT(t,e,i);fe=(t.flags&131072)!==0}else fe=!1,Vt&&(e.flags&1048576)!==0&&bm(e,xl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,o=r._init;if(r=o(r._payload),e.type=r,typeof r=="function")_c(r)?(t=$a(r,t),e.tag=1,e=Up(null,e,r,t,i)):(e.tag=0,e=yu(null,e,r,t,i));else{if(r!=null){if(o=r.$$typeof,o===L){e.tag=11,e=_p(null,e,r,t,i);break t}else if(o===et){e.tag=14,e=Vp(null,e,r,t,i);break t}}throw e=yt(r)||r,Error(l(306,e,""))}}return e;case 0:return yu(t,e,e.type,e.pendingProps,i);case 1:return r=e.type,o=$a(r,e.pendingProps),Up(t,e,r,o,i);case 3:t:{if(st(e,e.stateNode.containerInfo),t===null)throw Error(l(387));r=e.pendingProps;var u=e.memoizedState;o=u.element,Zc(t,e),Ks(e,r,null,i);var y=e.memoizedState;if(r=y.cache,aa(e,se,r),r!==u.cache&&Pc(e,[se],i,!0),Xs(),r=y.element,u.isDehydrated)if(u={element:r,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=Hp(t,e,r,i);break t}else if(r!==o){o=$e(Error(l(424)),e),ks(o),e=Hp(t,e,r,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(It=fn(t.firstChild),Re=e,Vt=!0,Xa=null,Sn=!0,i=Tp(e,null,r,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(Bs(),r===o){e=Gn(t,e,i);break t}ye(t,e,r,i)}e=e.child}return e;case 26:return Ul(t,e),t===null?(i=Qg(e.type,null,e.pendingProps,null))?e.memoizedState=i:Vt||(i=e.type,t=e.pendingProps,r=to(mt.current).createElement(i),r[Se]=e,r[Oe]=t,xe(r,i,t),ue(r),e.stateNode=r):e.memoizedState=Qg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Yt(e),t===null&&Vt&&(r=e.stateNode=Xg(e.type,e.pendingProps,mt.current),Re=e,Sn=!0,o=It,va(e.type)?(sf=o,It=fn(r.firstChild)):It=o),ye(t,e,e.pendingProps.children,i),Ul(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Vt&&((o=r=It)&&(r=OT(r,e.type,e.pendingProps,Sn),r!==null?(e.stateNode=r,Re=e,It=fn(r.firstChild),Sn=!1,o=!0):o=!1),o||Ka(e)),Yt(e),o=e.type,u=e.pendingProps,y=t!==null?t.memoizedProps:null,r=u.children,tf(o,u)?r=null:y!==null&&tf(o,y)&&(e.flags|=32),e.memoizedState!==null&&(o=Jc(t,e,F1,null,null,i),mr._currentValue=o),Ul(t,e),ye(t,e,r,i),e.child;case 6:return t===null&&Vt&&((t=i=It)&&(i=jT(i,e.pendingProps,Sn),i!==null?(e.stateNode=i,Re=e,It=null,t=!0):t=!1),t||Ka(e)),null;case 13:return Pp(t,e,i);case 4:return st(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Ui(e,null,r,i):ye(t,e,r,i),e.child;case 11:return _p(t,e,e.type,e.pendingProps,i);case 7:return ye(t,e,e.pendingProps,i),e.child;case 8:return ye(t,e,e.pendingProps.children,i),e.child;case 12:return ye(t,e,e.pendingProps.children,i),e.child;case 10:return r=e.pendingProps,aa(e,e.type,r.value),ye(t,e,r.children,i),e.child;case 9:return o=e.type._context,r=e.pendingProps.children,Qa(e),o=Te(o),r=r(o),e.flags|=1,ye(t,e,r,i),e.child;case 14:return Vp(t,e,e.type,e.pendingProps,i);case 15:return zp(t,e,e.type,e.pendingProps,i);case 19:return qp(t,e,i);case 31:return r=e.pendingProps,i=e.mode,r={mode:r.mode,children:r.children},t===null?(i=Hl(r,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=zn(t.child,r),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Lp(t,e,i);case 24:return Qa(e),r=Te(se),t===null?(o=Yc(),o===null&&(o=qt,u=Gc(),o.pooledCache=u,u.refCount++,u!==null&&(o.pooledCacheLanes|=i),o=u),e.memoizedState={parent:r,cache:o},Kc(e),aa(e,se,o)):((t.lanes&i)!==0&&(Zc(t,e),Ks(e,null,null,i),Xs()),o=t.memoizedState,u=e.memoizedState,o.parent!==r?(o={parent:r,cache:r},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),aa(e,se,r)):(r=u.cache,aa(e,se,r),r!==o.cache&&Pc(e,[se],i,!0))),ye(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function qn(t){t.flags|=4}function Xp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Jg(e)){if(e=en.current,e!==null&&((Ot&4194048)===Ot?Tn!==null:(Ot&62914560)!==Ot&&(Ot&536870912)===0||e!==Tn))throw qs=Xc,Rm;t.flags|=8192}}function Pl(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?wh():536870912,t.lanes|=e,qi|=e)}function Js(t,e){if(!Vt)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var r=null;i!==null;)i.alternate!==null&&(r=i),i=i.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Zt(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,r=0;if(e)for(var o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)i|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=i,e}function iT(t,e,i){var r=e.pendingProps;switch(Bc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zt(e),null;case 1:return Zt(e),null;case 3:return i=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),Un(se),wt(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(Ls(e)?qn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,wm())),Zt(e),null;case 26:return i=e.memoizedState,t===null?(qn(e),i!==null?(Zt(e),Xp(e,i)):(Zt(e),e.flags&=-16777217)):i?i!==t.memoizedState?(qn(e),Zt(e),Xp(e,i)):(Zt(e),e.flags&=-16777217):(t.memoizedProps!==r&&qn(e),Zt(e),e.flags&=-16777217),null;case 27:Dt(e),i=mt.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&qn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}t=rt.current,Ls(e)?Sm(e):(t=Xg(o,r,i),e.stateNode=t,qn(e))}return Zt(e),null;case 5:if(Dt(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&qn(e);else{if(!r){if(e.stateNode===null)throw Error(l(166));return Zt(e),null}if(t=rt.current,Ls(e))Sm(e);else{switch(o=to(mt.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?o.createElement(i,{is:r.is}):o.createElement(i)}}t[Se]=e,t[Oe]=r;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(xe(t,i,r),i){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&qn(e)}}return Zt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&qn(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(l(166));if(t=mt.current,Ls(e)){if(t=e.stateNode,i=e.memoizedProps,r=null,o=Re,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}t[Se]=e,t=!!(t.nodeValue===i||r!==null&&r.suppressHydrationWarning===!0||kg(t.nodeValue,i)),t||Ka(e)}else t=to(t).createTextNode(r),t[Se]=e,e.stateNode=t}return Zt(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ls(e),r!==null&&r.dehydrated!==null){if(t===null){if(!o)throw Error(l(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(l(317));o[Se]=e}else Bs(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Zt(e),o=!1}else o=wm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(Pn(e),e):(Pn(e),null)}if(Pn(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=r!==null,t=t!==null&&t.memoizedState!==null,i){r=e.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var u=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(u=r.memoizedState.cachePool.pool),u!==o&&(r.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Pl(e,e.updateQueue),Zt(e),null;case 4:return wt(),t===null&&Fu(e.stateNode.containerInfo),Zt(e),null;case 10:return Un(e.type),Zt(e),null;case 19:if(at(re),o=e.memoizedState,o===null)return Zt(e),null;if(r=(e.flags&128)!==0,u=o.rendering,u===null)if(r)Js(o,!1);else{if($t!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Ll(t),u!==null){for(e.flags|=128,Js(o,!1),t=u.updateQueue,e.updateQueue=t,Pl(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)xm(i,t),i=i.sibling;return tt(re,re.current&1|2),e.child}t=t.sibling}o.tail!==null&&ge()>Yl&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304)}else{if(!r)if(t=Ll(u),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Pl(e,t),Js(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!Vt)return Zt(e),null}else 2*ge()-o.renderingStartTime>Yl&&i!==536870912&&(e.flags|=128,r=!0,Js(o,!1),e.lanes=4194304);o.isBackwards?(u.sibling=e.child,e.child=u):(t=o.last,t!==null?t.sibling=u:e.child=u,o.last=u)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=ge(),e.sibling=null,t=re.current,tt(re,r?t&1|2:t&1),e):(Zt(e),null);case 22:case 23:return Pn(e),$c(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(i&536870912)!==0&&(e.flags&128)===0&&(Zt(e),e.subtreeFlags&6&&(e.flags|=8192)):Zt(e),i=e.updateQueue,i!==null&&Pl(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==i&&(e.flags|=2048),t!==null&&at(Fa),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),Un(se),Zt(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function sT(t,e){switch(Bc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Un(se),wt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Dt(e),null;case 13:if(Pn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));Bs()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return at(re),null;case 4:return wt(),null;case 10:return Un(e.type),null;case 22:case 23:return Pn(e),$c(),t!==null&&at(Fa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Un(se),null;case 25:return null;default:return null}}function Kp(t,e){switch(Bc(e),e.tag){case 3:Un(se),wt();break;case 26:case 27:case 5:Dt(e);break;case 4:wt();break;case 13:Pn(e);break;case 19:at(re);break;case 10:Un(e.type);break;case 22:case 23:Pn(e),$c(),t!==null&&at(Fa);break;case 24:Un(se)}}function tr(t,e){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var o=r.next;i=o;do{if((i.tag&t)===t){r=void 0;var u=i.create,y=i.inst;r=u(),y.destroy=r}i=i.next}while(i!==o)}}catch(S){Gt(e,e.return,S)}}function ua(t,e,i){try{var r=e.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var u=o.next;r=u;do{if((r.tag&t)===t){var y=r.inst,S=y.destroy;if(S!==void 0){y.destroy=void 0,o=e;var C=i,U=S;try{U()}catch(Q){Gt(o,C,Q)}}}r=r.next}while(r!==u)}}catch(Q){Gt(e,e.return,Q)}}function Zp(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{Vm(e,i)}catch(r){Gt(t,t.return,r)}}}function Qp(t,e,i){i.props=$a(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(r){Gt(t,e,r)}}function er(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof i=="function"?t.refCleanup=i(r):i.current=r}}catch(o){Gt(t,e,o)}}function wn(t,e){var i=t.ref,r=t.refCleanup;if(i!==null)if(typeof r=="function")try{r()}catch(o){Gt(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(o){Gt(t,e,o)}else i.current=null}function Fp(t){var e=t.type,i=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break t;case"img":i.src?r.src=i.src:i.srcSet&&(r.srcset=i.srcSet)}}catch(o){Gt(t,t.return,o)}}function Eu(t,e,i){try{var r=t.stateNode;MT(r,t.type,i,e),r[Oe]=e}catch(o){Gt(t,t.return,o)}}function Ip(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&va(t.type)||t.tag===4}function Mu(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ip(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&va(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Cu(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=Jl));else if(r!==4&&(r===27&&va(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(Cu(t,e,i),t=t.sibling;t!==null;)Cu(t,e,i),t=t.sibling}function Gl(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(r!==4&&(r===27&&va(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(Gl(t,e,i),t=t.sibling;t!==null;)Gl(t,e,i),t=t.sibling}function $p(t){var e=t.stateNode,i=t.memoizedProps;try{for(var r=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);xe(e,r,i),e[Se]=t,e[Oe]=i}catch(u){Gt(t,t.return,u)}}var Yn=!1,te=!1,Ru=!1,Wp=typeof WeakSet=="function"?WeakSet:Set,de=null;function rT(t,e){if(t=t.containerInfo,Wu=ro,t=cm(t),Mc(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var r=i.getSelection&&i.getSelection();if(r&&r.rangeCount!==0){i=r.anchorNode;var o=r.anchorOffset,u=r.focusNode;r=r.focusOffset;try{i.nodeType,u.nodeType}catch(pt){i=null;break t}var y=0,S=-1,C=-1,U=0,Q=0,I=t,H=null;e:for(;;){for(var G;I!==i||o!==0&&I.nodeType!==3||(S=y+o),I!==u||r!==0&&I.nodeType!==3||(C=y+r),I.nodeType===3&&(y+=I.nodeValue.length),(G=I.firstChild)!==null;)H=I,I=G;for(;;){if(I===t)break e;if(H===i&&++U===o&&(S=y),H===u&&++Q===r&&(C=y),(G=I.nextSibling)!==null)break;I=H,H=I.parentNode}I=G}i=S===-1||C===-1?null:{start:S,end:C}}else i=null}i=i||{start:0,end:0}}else i=null;for(Ju={focusedElem:t,selectionRange:i},ro=!1,de=e;de!==null;)if(e=de,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,de=t;else for(;de!==null;){switch(e=de,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,i=e,o=u.memoizedProps,u=u.memoizedState,r=i.stateNode;try{var vt=$a(i.type,o,i.elementType===i.type);t=r.getSnapshotBeforeUpdate(vt,u),r.__reactInternalSnapshotBeforeUpdate=t}catch(pt){Gt(i,i.return,pt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)nf(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":nf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,de=t;break}de=e.return}}function Jp(t,e,i){var r=i.flags;switch(i.tag){case 0:case 11:case 15:fa(t,i),r&4&&tr(5,i);break;case 1:if(fa(t,i),r&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(y){Gt(i,i.return,y)}else{var o=$a(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(y){Gt(i,i.return,y)}}r&64&&Zp(i),r&512&&er(i,i.return);break;case 3:if(fa(t,i),r&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Vm(t,e)}catch(y){Gt(i,i.return,y)}}break;case 27:e===null&&r&4&&$p(i);case 26:case 5:fa(t,i),e===null&&r&4&&Fp(i),r&512&&er(i,i.return);break;case 12:fa(t,i);break;case 13:fa(t,i),r&4&&ng(t,i),r&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=pT.bind(null,i),_T(t,i))));break;case 22:if(r=i.memoizedState!==null||Yn,!r){e=e!==null&&e.memoizedState!==null||te,o=Yn;var u=te;Yn=r,(te=e)&&!u?da(t,i,(i.subtreeFlags&8772)!==0):fa(t,i),Yn=o,te=u}break;case 30:break;default:fa(t,i)}}function tg(t){var e=t.alternate;e!==null&&(t.alternate=null,tg(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&lc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ve=!1;function Xn(t,e,i){for(i=i.child;i!==null;)eg(t,e,i),i=i.sibling}function eg(t,e,i){if(Be&&typeof Be.onCommitFiberUnmount=="function")try{Be.onCommitFiberUnmount(Ts,i)}catch(u){}switch(i.tag){case 26:te||wn(i,e),Xn(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:te||wn(i,e);var r=Xt,o=Ve;va(i.type)&&(Xt=i.stateNode,Ve=!1),Xn(t,e,i),ur(i.stateNode),Xt=r,Ve=o;break;case 5:te||wn(i,e);case 6:if(r=Xt,o=Ve,Xt=null,Xn(t,e,i),Xt=r,Ve=o,Xt!==null)if(Ve)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(i.stateNode)}catch(u){Gt(i,e,u)}else try{Xt.removeChild(i.stateNode)}catch(u){Gt(i,e,u)}break;case 18:Xt!==null&&(Ve?(t=Xt,qg(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),vr(t)):qg(Xt,i.stateNode));break;case 4:r=Xt,o=Ve,Xt=i.stateNode.containerInfo,Ve=!0,Xn(t,e,i),Xt=r,Ve=o;break;case 0:case 11:case 14:case 15:te||ua(2,i,e),te||ua(4,i,e),Xn(t,e,i);break;case 1:te||(wn(i,e),r=i.stateNode,typeof r.componentWillUnmount=="function"&&Qp(i,e,r)),Xn(t,e,i);break;case 21:Xn(t,e,i);break;case 22:te=(r=te)||i.memoizedState!==null,Xn(t,e,i),te=r;break;default:Xn(t,e,i)}}function ng(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{vr(t)}catch(i){Gt(e,e.return,i)}}function lT(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Wp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Wp),e;default:throw Error(l(435,t.tag))}}function Nu(t,e){var i=lT(t);e.forEach(function(r){var o=gT.bind(null,t,r);i.has(r)||(i.add(r),r.then(o,o))})}function Pe(t,e){var i=e.deletions;if(i!==null)for(var r=0;r<i.length;r++){var o=i[r],u=t,y=e,S=y;t:for(;S!==null;){switch(S.tag){case 27:if(va(S.type)){Xt=S.stateNode,Ve=!1;break t}break;case 5:Xt=S.stateNode,Ve=!1;break t;case 3:case 4:Xt=S.stateNode.containerInfo,Ve=!0;break t}S=S.return}if(Xt===null)throw Error(l(160));eg(u,y,o),Xt=null,Ve=!1,u=o.alternate,u!==null&&(u.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)ag(e,t),e=e.sibling}var un=null;function ag(t,e){var i=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Pe(e,t),Ge(t),r&4&&(ua(3,t,t.return),tr(3,t),ua(5,t,t.return));break;case 1:Pe(e,t),Ge(t),r&512&&(te||i===null||wn(i,i.return)),r&64&&Yn&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?r:i.concat(r))));break;case 26:var o=un;if(Pe(e,t),Ge(t),r&512&&(te||i===null||wn(i,i.return)),r&4){var u=i!==null?i.memoizedState:null;if(r=t.memoizedState,i===null)if(r===null)if(t.stateNode===null){t:{r=t.type,i=t.memoizedProps,o=o.ownerDocument||o;e:switch(r){case"title":u=o.getElementsByTagName("title")[0],(!u||u[Es]||u[Se]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=o.createElement(r),o.head.insertBefore(u,o.querySelector("head > title"))),xe(u,r,i),u[Se]=t,ue(u),r=u;break t;case"link":var y=$g("link","href",o).get(r+(i.href||""));if(y){for(var S=0;S<y.length;S++)if(u=y[S],u.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&u.getAttribute("rel")===(i.rel==null?null:i.rel)&&u.getAttribute("title")===(i.title==null?null:i.title)&&u.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){y.splice(S,1);break e}}u=o.createElement(r),xe(u,r,i),o.head.appendChild(u);break;case"meta":if(y=$g("meta","content",o).get(r+(i.content||""))){for(S=0;S<y.length;S++)if(u=y[S],u.getAttribute("content")===(i.content==null?null:""+i.content)&&u.getAttribute("name")===(i.name==null?null:i.name)&&u.getAttribute("property")===(i.property==null?null:i.property)&&u.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&u.getAttribute("charset")===(i.charSet==null?null:i.charSet)){y.splice(S,1);break e}}u=o.createElement(r),xe(u,r,i),o.head.appendChild(u);break;default:throw Error(l(468,r))}u[Se]=t,ue(u),r=u}t.stateNode=r}else Wg(o,t.type,t.stateNode);else t.stateNode=Ig(o,r,t.memoizedProps);else u!==r?(u===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):u.count--,r===null?Wg(o,t.type,t.stateNode):Ig(o,r,t.memoizedProps)):r===null&&t.stateNode!==null&&Eu(t,t.memoizedProps,i.memoizedProps)}break;case 27:Pe(e,t),Ge(t),r&512&&(te||i===null||wn(i,i.return)),i!==null&&r&4&&Eu(t,t.memoizedProps,i.memoizedProps);break;case 5:if(Pe(e,t),Ge(t),r&512&&(te||i===null||wn(i,i.return)),t.flags&32){o=t.stateNode;try{Si(o,"")}catch(G){Gt(t,t.return,G)}}r&4&&t.stateNode!=null&&(o=t.memoizedProps,Eu(t,o,i!==null?i.memoizedProps:o)),r&1024&&(Ru=!0);break;case 6:if(Pe(e,t),Ge(t),r&4){if(t.stateNode===null)throw Error(l(162));r=t.memoizedProps,i=t.stateNode;try{i.nodeValue=r}catch(G){Gt(t,t.return,G)}}break;case 3:if(ao=null,o=un,un=eo(e.containerInfo),Pe(e,t),un=o,Ge(t),r&4&&i!==null&&i.memoizedState.isDehydrated)try{vr(e.containerInfo)}catch(G){Gt(t,t.return,G)}Ru&&(Ru=!1,ig(t));break;case 4:r=un,un=eo(t.stateNode.containerInfo),Pe(e,t),Ge(t),un=r;break;case 12:Pe(e,t),Ge(t);break;case 13:Pe(e,t),Ge(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(zu=ge()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Nu(t,r)));break;case 22:o=t.memoizedState!==null;var C=i!==null&&i.memoizedState!==null,U=Yn,Q=te;if(Yn=U||o,te=Q||C,Pe(e,t),te=Q,Yn=U,Ge(t),r&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(i===null||C||Yn||te||Wa(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){C=i=e;try{if(u=C.stateNode,o)y=u.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{S=C.stateNode;var I=C.memoizedProps.style,H=I!=null&&I.hasOwnProperty("display")?I.display:null;S.style.display=H==null||typeof H=="boolean"?"":(""+H).trim()}}catch(G){Gt(C,C.return,G)}}}else if(e.tag===6){if(i===null){C=e;try{C.stateNode.nodeValue=o?"":C.memoizedProps}catch(G){Gt(C,C.return,G)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(i=r.retryQueue,i!==null&&(r.retryQueue=null,Nu(t,i))));break;case 19:Pe(e,t),Ge(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Nu(t,r)));break;case 30:break;case 21:break;default:Pe(e,t),Ge(t)}}function Ge(t){var e=t.flags;if(e&2){try{for(var i,r=t.return;r!==null;){if(Ip(r)){i=r;break}r=r.return}if(i==null)throw Error(l(160));switch(i.tag){case 27:var o=i.stateNode,u=Mu(t);Gl(t,u,o);break;case 5:var y=i.stateNode;i.flags&32&&(Si(y,""),i.flags&=-33);var S=Mu(t);Gl(t,S,y);break;case 3:case 4:var C=i.stateNode.containerInfo,U=Mu(t);Cu(t,U,C);break;default:throw Error(l(161))}}catch(Q){Gt(t,t.return,Q)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function ig(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;ig(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function fa(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Jp(t,e.alternate,e),e=e.sibling}function Wa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ua(4,e,e.return),Wa(e);break;case 1:wn(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Qp(e,e.return,i),Wa(e);break;case 27:ur(e.stateNode);case 26:case 5:wn(e,e.return),Wa(e);break;case 22:e.memoizedState===null&&Wa(e);break;case 30:Wa(e);break;default:Wa(e)}t=t.sibling}}function da(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,o=t,u=e,y=u.flags;switch(u.tag){case 0:case 11:case 15:da(o,u,i),tr(4,u);break;case 1:if(da(o,u,i),r=u,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(U){Gt(r,r.return,U)}if(r=u,o=r.updateQueue,o!==null){var S=r.stateNode;try{var C=o.shared.hiddenCallbacks;if(C!==null)for(o.shared.hiddenCallbacks=null,o=0;o<C.length;o++)_m(C[o],S)}catch(U){Gt(r,r.return,U)}}i&&y&64&&Zp(u),er(u,u.return);break;case 27:$p(u);case 26:case 5:da(o,u,i),i&&r===null&&y&4&&Fp(u),er(u,u.return);break;case 12:da(o,u,i);break;case 13:da(o,u,i),i&&y&4&&ng(o,u);break;case 22:u.memoizedState===null&&da(o,u,i),er(u,u.return);break;case 30:break;default:da(o,u,i)}e=e.sibling}}function Du(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&Hs(i))}function Ou(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t))}function An(t,e,i,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sg(t,e,i,r),e=e.sibling}function sg(t,e,i,r){var o=e.flags;switch(e.tag){case 0:case 11:case 15:An(t,e,i,r),o&2048&&tr(9,e);break;case 1:An(t,e,i,r);break;case 3:An(t,e,i,r),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Hs(t)));break;case 12:if(o&2048){An(t,e,i,r),t=e.stateNode;try{var u=e.memoizedProps,y=u.id,S=u.onPostCommit;typeof S=="function"&&S(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(C){Gt(e,e.return,C)}}else An(t,e,i,r);break;case 13:An(t,e,i,r);break;case 23:break;case 22:u=e.stateNode,y=e.alternate,e.memoizedState!==null?u._visibility&2?An(t,e,i,r):nr(t,e):u._visibility&2?An(t,e,i,r):(u._visibility|=2,Hi(t,e,i,r,(e.subtreeFlags&10256)!==0)),o&2048&&Du(y,e);break;case 24:An(t,e,i,r),o&2048&&Ou(e.alternate,e);break;default:An(t,e,i,r)}}function Hi(t,e,i,r,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,y=e,S=i,C=r,U=y.flags;switch(y.tag){case 0:case 11:case 15:Hi(u,y,S,C,o),tr(8,y);break;case 23:break;case 22:var Q=y.stateNode;y.memoizedState!==null?Q._visibility&2?Hi(u,y,S,C,o):nr(u,y):(Q._visibility|=2,Hi(u,y,S,C,o)),o&&U&2048&&Du(y.alternate,y);break;case 24:Hi(u,y,S,C,o),o&&U&2048&&Ou(y.alternate,y);break;default:Hi(u,y,S,C,o)}e=e.sibling}}function nr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,r=e,o=r.flags;switch(r.tag){case 22:nr(i,r),o&2048&&Du(r.alternate,r);break;case 24:nr(i,r),o&2048&&Ou(r.alternate,r);break;default:nr(i,r)}e=e.sibling}}var ar=8192;function Pi(t){if(t.subtreeFlags&ar)for(t=t.child;t!==null;)rg(t),t=t.sibling}function rg(t){switch(t.tag){case 26:Pi(t),t.flags&ar&&t.memoizedState!==null&&KT(un,t.memoizedState,t.memoizedProps);break;case 5:Pi(t);break;case 3:case 4:var e=un;un=eo(t.stateNode.containerInfo),Pi(t),un=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ar,ar=16777216,Pi(t),ar=e):Pi(t));break;default:Pi(t)}}function lg(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ir(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];de=r,cg(r,t)}lg(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)og(t),t=t.sibling}function og(t){switch(t.tag){case 0:case 11:case 15:ir(t),t.flags&2048&&ua(9,t,t.return);break;case 3:ir(t);break;case 12:ir(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ql(t)):ir(t);break;default:ir(t)}}function ql(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];de=r,cg(r,t)}lg(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ua(8,e,e.return),ql(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,ql(e));break;default:ql(e)}t=t.sibling}}function cg(t,e){for(;de!==null;){var i=de;switch(i.tag){case 0:case 11:case 15:ua(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var r=i.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Hs(i.memoizedState.cache)}if(r=i.child,r!==null)r.return=i,de=r;else t:for(i=t;de!==null;){r=de;var o=r.sibling,u=r.return;if(tg(r),r===i){de=null;break t}if(o!==null){o.return=u,de=o;break t}de=u}}}var oT={getCacheForType:function(t){var e=Te(se),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},cT=typeof WeakMap=="function"?WeakMap:Map,zt=0,qt=null,Rt=null,Ot=0,Lt=0,qe=null,ha=!1,Gi=!1,ju=!1,Kn=0,$t=0,ma=0,Ja=0,_u=0,nn=0,qi=0,sr=null,ze=null,Vu=!1,zu=0,Yl=1/0,Xl=null,pa=null,ve=0,ga=null,Yi=null,Xi=0,Lu=0,Bu=null,ug=null,rr=0,ku=null;function Ye(){if((zt&2)!==0&&Ot!==0)return Ot&-Ot;if(B.T!==null){var t=ji;return t!==0?t:Xu()}return Mh()}function fg(){nn===0&&(nn=(Ot&536870912)===0||Vt?Th():536870912);var t=en.current;return t!==null&&(t.flags|=32),nn}function Xe(t,e,i){(t===qt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ki(t,0),ya(t,Ot,nn,!1)),As(t,i),((zt&2)===0||t!==qt)&&(t===qt&&((zt&2)===0&&(Ja|=i),$t===4&&ya(t,Ot,nn,!1)),En(t))}function dg(t,e,i){if((zt&6)!==0)throw Error(l(327));var r=!i&&(e&124)===0&&(e&t.expiredLanes)===0||ws(t,e),o=r?dT(t,e):Pu(t,e,!0),u=r;do{if(o===0){Gi&&!r&&ya(t,e,0,!1);break}else{if(i=t.current.alternate,u&&!uT(i)){o=Pu(t,e,!1),u=!1;continue}if(o===2){if(u=e,t.errorRecoveryDisabledLanes&u)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var S=t;o=sr;var C=S.current.memoizedState.isDehydrated;if(C&&(Ki(S,y).flags|=256),y=Pu(S,y,!1),y!==2){if(ju&&!C){S.errorRecoveryDisabledLanes|=u,Ja|=u,o=4;break t}u=ze,ze=o,u!==null&&(ze===null?ze=u:ze.push.apply(ze,u))}o=y}if(u=!1,o!==2)continue}}if(o===1){Ki(t,0),ya(t,e,0,!0);break}t:{switch(r=t,u=o,u){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:ya(r,e,nn,!ha);break t;case 2:ze=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(o=zu+300-ge(),10<o)){if(ya(r,e,nn,!ha),nl(r,0,!0)!==0)break t;r.timeoutHandle=Pg(hg.bind(null,r,i,ze,Xl,Vu,e,nn,Ja,qi,ha,u,2,-0,0),o);break t}hg(r,i,ze,Xl,Vu,e,nn,Ja,qi,ha,u,0,-0,0)}}break}while(!0);En(t)}function hg(t,e,i,r,o,u,y,S,C,U,Q,I,H,G){if(t.timeoutHandle=-1,I=e.subtreeFlags,(I&8192||(I&16785408)===16785408)&&(hr={stylesheets:null,count:0,unsuspend:XT},rg(e),I=ZT(),I!==null)){t.cancelPendingCommit=I(bg.bind(null,t,e,u,i,r,o,y,S,C,Q,1,H,G)),ya(t,u,y,!U);return}bg(t,e,u,i,r,o,y,S,C)}function uT(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var r=0;r<i.length;r++){var o=i[r],u=o.getSnapshot;o=o.value;try{if(!Ue(u(),o))return!1}catch(y){return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ya(t,e,i,r){e&=~_u,e&=~Ja,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var o=e;0<o;){var u=31-ke(o),y=1<<u;r[u]=-1,o&=~y}i!==0&&Ah(t,i,e)}function Kl(){return(zt&6)===0?(lr(0),!1):!0}function Uu(){if(Rt!==null){if(Lt===0)var t=Rt.return;else t=Rt,kn=Za=null,nu(t),ki=null,$s=0,t=Rt;for(;t!==null;)Kp(t.alternate,t),t=t.return;Rt=null}}function Ki(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,RT(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Uu(),qt=t,Rt=i=zn(t.current,null),Ot=e,Lt=0,qe=null,ha=!1,Gi=ws(t,e),ju=!1,qi=nn=_u=Ja=ma=$t=0,ze=sr=null,Vu=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var o=31-ke(r),u=1<<o;e|=t[o],r&=~u}return Kn=e,ml(),i}function mg(t,e){At=null,B.H=_l,e===Gs||e===wl?(e=Om(),Lt=3):e===Rm?(e=Om(),Lt=4):Lt=e===jp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,qe=e,Rt===null&&($t=1,kl(t,$e(e,t.current)))}function pg(){var t=B.H;return B.H=_l,t===null?_l:t}function gg(){var t=B.A;return B.A=oT,t}function Hu(){$t=4,ha||(Ot&4194048)!==Ot&&en.current!==null||(Gi=!0),(ma&134217727)===0&&(Ja&134217727)===0||qt===null||ya(qt,Ot,nn,!1)}function Pu(t,e,i){var r=zt;zt|=2;var o=pg(),u=gg();(qt!==t||Ot!==e)&&(Xl=null,Ki(t,e)),e=!1;var y=$t;t:do try{if(Lt!==0&&Rt!==null){var S=Rt,C=qe;switch(Lt){case 8:Uu(),y=6;break t;case 3:case 2:case 9:case 6:en.current===null&&(e=!0);var U=Lt;if(Lt=0,qe=null,Zi(t,S,C,U),i&&Gi){y=0;break t}break;default:U=Lt,Lt=0,qe=null,Zi(t,S,C,U)}}fT(),y=$t;break}catch(Q){mg(t,Q)}while(!0);return e&&t.shellSuspendCounter++,kn=Za=null,zt=r,B.H=o,B.A=u,Rt===null&&(qt=null,Ot=0,ml()),y}function fT(){for(;Rt!==null;)yg(Rt)}function dT(t,e){var i=zt;zt|=2;var r=pg(),o=gg();qt!==t||Ot!==e?(Xl=null,Yl=ge()+500,Ki(t,e)):Gi=ws(t,e);t:do try{if(Lt!==0&&Rt!==null){e=Rt;var u=qe;e:switch(Lt){case 1:Lt=0,qe=null,Zi(t,e,u,1);break;case 2:case 9:if(Nm(u)){Lt=0,qe=null,vg(e);break}e=function(){Lt!==2&&Lt!==9||qt!==t||(Lt=7),En(t)},u.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:Nm(u)?(Lt=0,qe=null,vg(e)):(Lt=0,qe=null,Zi(t,e,u,7));break;case 5:var y=null;switch(Rt.tag){case 26:y=Rt.memoizedState;case 5:case 27:var S=Rt;if(!y||Jg(y)){Lt=0,qe=null;var C=S.sibling;if(C!==null)Rt=C;else{var U=S.return;U!==null?(Rt=U,Zl(U)):Rt=null}break e}}Lt=0,qe=null,Zi(t,e,u,5);break;case 6:Lt=0,qe=null,Zi(t,e,u,6);break;case 8:Uu(),$t=6;break t;default:throw Error(l(462))}}hT();break}catch(Q){mg(t,Q)}while(!0);return kn=Za=null,B.H=r,B.A=o,zt=i,Rt!==null?0:(qt=null,Ot=0,ml(),$t)}function hT(){for(;Rt!==null&&!Ce();)yg(Rt)}function yg(t){var e=Yp(t.alternate,t,Kn);t.memoizedProps=t.pendingProps,e===null?Zl(t):Rt=e}function vg(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=kp(i,e,e.pendingProps,e.type,void 0,Ot);break;case 11:e=kp(i,e,e.pendingProps,e.type.render,e.ref,Ot);break;case 5:nu(e);default:Kp(i,e),e=Rt=xm(e,Kn),e=Yp(i,e,Kn)}t.memoizedProps=t.pendingProps,e===null?Zl(t):Rt=e}function Zi(t,e,i,r){kn=Za=null,nu(e),ki=null,$s=0;var o=e.return;try{if(nT(t,o,e,i,Ot)){$t=1,kl(t,$e(i,t.current)),Rt=null;return}}catch(u){if(o!==null)throw Rt=o,u;$t=1,kl(t,$e(i,t.current)),Rt=null;return}e.flags&32768?(Vt||r===1?t=!0:Gi||(Ot&536870912)!==0?t=!1:(ha=t=!0,(r===2||r===9||r===3||r===6)&&(r=en.current,r!==null&&r.tag===13&&(r.flags|=16384))),xg(e,t)):Zl(e)}function Zl(t){var e=t;do{if((e.flags&32768)!==0){xg(e,ha);return}t=e.return;var i=iT(e.alternate,e,Kn);if(i!==null){Rt=i;return}if(e=e.sibling,e!==null){Rt=e;return}Rt=e=t}while(e!==null);$t===0&&($t=5)}function xg(t,e){do{var i=sT(t.alternate,t);if(i!==null){i.flags&=32767,Rt=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){Rt=t;return}Rt=t=i}while(t!==null);$t=6,Rt=null}function bg(t,e,i,r,o,u,y,S,C){t.cancelPendingCommit=null;do Ql();while(ve!==0);if((zt&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(u=e.lanes|e.childLanes,u|=Oc,XS(t,i,u,y,S,C),t===qt&&(Rt=qt=null,Ot=0),Yi=e,ga=t,Xi=i,Lu=u,Bu=o,ug=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,yT(di,function(){return Eg(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=B.T,B.T=null,o=X.p,X.p=2,y=zt,zt|=4;try{rT(t,e,i)}finally{zt=y,X.p=o,B.T=r}}ve=1,Sg(),Tg(),wg()}}function Sg(){if(ve===1){ve=0;var t=ga,e=Yi,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=B.T,B.T=null;var r=X.p;X.p=2;var o=zt;zt|=4;try{ag(e,t);var u=Ju,y=cm(t.containerInfo),S=u.focusedElem,C=u.selectionRange;if(y!==S&&S&&S.ownerDocument&&om(S.ownerDocument.documentElement,S)){if(C!==null&&Mc(S)){var U=C.start,Q=C.end;if(Q===void 0&&(Q=U),"selectionStart"in S)S.selectionStart=U,S.selectionEnd=Math.min(Q,S.value.length);else{var I=S.ownerDocument||document,H=I&&I.defaultView||window;if(H.getSelection){var G=H.getSelection(),vt=S.textContent.length,pt=Math.min(C.start,vt),Ht=C.end===void 0?pt:Math.min(C.end,vt);!G.extend&&pt>Ht&&(y=Ht,Ht=pt,pt=y);var z=lm(S,pt),_=lm(S,Ht);if(z&&_&&(G.rangeCount!==1||G.anchorNode!==z.node||G.anchorOffset!==z.offset||G.focusNode!==_.node||G.focusOffset!==_.offset)){var k=I.createRange();k.setStart(z.node,z.offset),G.removeAllRanges(),pt>Ht?(G.addRange(k),G.extend(_.node,_.offset)):(k.setEnd(_.node,_.offset),G.addRange(k))}}}}for(I=[],G=S;G=G.parentNode;)G.nodeType===1&&I.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof S.focus=="function"&&S.focus(),S=0;S<I.length;S++){var F=I[S];F.element.scrollLeft=F.left,F.element.scrollTop=F.top}}ro=!!Wu,Ju=Wu=null}finally{zt=o,X.p=r,B.T=i}}t.current=e,ve=2}}function Tg(){if(ve===2){ve=0;var t=ga,e=Yi,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=B.T,B.T=null;var r=X.p;X.p=2;var o=zt;zt|=4;try{Jp(t,e.alternate,e)}finally{zt=o,X.p=r,B.T=i}}ve=3}}function wg(){if(ve===4||ve===3){ve=0,on();var t=ga,e=Yi,i=Xi,r=ug;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ve=5:(ve=0,Yi=ga=null,Ag(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(pa=null),sc(i),e=e.stateNode,Be&&typeof Be.onCommitFiberRoot=="function")try{Be.onCommitFiberRoot(Ts,e,void 0,(e.current.flags&128)===128)}catch(C){}if(r!==null){e=B.T,o=X.p,X.p=2,B.T=null;try{for(var u=t.onRecoverableError,y=0;y<r.length;y++){var S=r[y];u(S.value,{componentStack:S.stack})}}finally{B.T=e,X.p=o}}(Xi&3)!==0&&Ql(),En(t),o=t.pendingLanes,(i&4194090)!==0&&(o&42)!==0?t===ku?rr++:(rr=0,ku=t):rr=0,lr(0)}}function Ag(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Hs(e)))}function Ql(t){return Sg(),Tg(),wg(),Eg()}function Eg(){if(ve!==5)return!1;var t=ga,e=Lu;Lu=0;var i=sc(Xi),r=B.T,o=X.p;try{X.p=32>i?32:i,B.T=null,i=Bu,Bu=null;var u=ga,y=Xi;if(ve=0,Yi=ga=null,Xi=0,(zt&6)!==0)throw Error(l(331));var S=zt;if(zt|=4,og(u.current),sg(u,u.current,y,i),zt=S,lr(0,!1),Be&&typeof Be.onPostCommitFiberRoot=="function")try{Be.onPostCommitFiberRoot(Ts,u)}catch(C){}return!0}finally{X.p=o,B.T=r,Ag(t,e)}}function Mg(t,e,i){e=$e(i,e),e=gu(t.stateNode,e,2),t=ra(t,e,2),t!==null&&(As(t,2),En(t))}function Gt(t,e,i){if(t.tag===3)Mg(t,t,i);else for(;e!==null;){if(e.tag===3){Mg(e,t,i);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(pa===null||!pa.has(r))){t=$e(i,t),i=Dp(2),r=ra(e,i,2),r!==null&&(Op(i,r,e,t),As(r,2),En(r));break}}e=e.return}}function Gu(t,e,i){var r=t.pingCache;if(r===null){r=t.pingCache=new cT;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(i)||(ju=!0,o.add(i),t=mT.bind(null,t,e,i),e.then(t,t))}function mT(t,e,i){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,qt===t&&(Ot&i)===i&&($t===4||$t===3&&(Ot&62914560)===Ot&&300>ge()-zu?(zt&2)===0&&Ki(t,0):_u|=i,qi===Ot&&(qi=0)),En(t)}function Cg(t,e){e===0&&(e=wh()),t=Ri(t,e),t!==null&&(As(t,e),En(t))}function pT(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),Cg(t,i)}function gT(t,e){var i=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(i=o.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(l(314))}r!==null&&r.delete(e),Cg(t,i)}function yT(t,e){return jt(t,e)}var Fl=null,Qi=null,qu=!1,Il=!1,Yu=!1,ti=0;function En(t){t!==Qi&&t.next===null&&(Qi===null?Fl=Qi=t:Qi=Qi.next=t),Il=!0,qu||(qu=!0,xT())}function lr(t,e){if(!Yu&&Il){Yu=!0;do for(var i=!1,r=Fl;r!==null;){if(t!==0){var o=r.pendingLanes;if(o===0)var u=0;else{var y=r.suspendedLanes,S=r.pingedLanes;u=(1<<31-ke(42|t)+1)-1,u&=o&~(y&~S),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(i=!0,Og(r,u))}else u=Ot,u=nl(r,r===qt?u:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(u&3)===0||ws(r,u)||(i=!0,Og(r,u));r=r.next}while(i);Yu=!1}}function vT(){Rg()}function Rg(){Il=qu=!1;var t=0;ti!==0&&(CT()&&(t=ti),ti=0);for(var e=ge(),i=null,r=Fl;r!==null;){var o=r.next,u=Ng(r,e);u===0?(r.next=null,i===null?Fl=o:i.next=o,o===null&&(Qi=i)):(i=r,(t!==0||(u&3)!==0)&&(Il=!0)),r=o}lr(t)}function Ng(t,e){for(var i=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var y=31-ke(u),S=1<<y,C=o[y];C===-1?((S&i)===0||(S&r)!==0)&&(o[y]=YS(S,e)):C<=e&&(t.expiredLanes|=S),u&=~S}if(e=qt,i=Ot,i=nl(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,i===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&pe(r),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||ws(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(r!==null&&pe(r),sc(i)){case 2:case 8:i=Jr;break;case 32:i=di;break;case 268435456:i=Sh;break;default:i=di}return r=Dg.bind(null,t),i=jt(i,r),t.callbackPriority=e,t.callbackNode=i,e}return r!==null&&r!==null&&pe(r),t.callbackPriority=2,t.callbackNode=null,2}function Dg(t,e){if(ve!==0&&ve!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Ql()&&t.callbackNode!==i)return null;var r=Ot;return r=nl(t,t===qt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:(dg(t,r,e),Ng(t,ge()),t.callbackNode!=null&&t.callbackNode===i?Dg.bind(null,t):null)}function Og(t,e){if(Ql())return null;dg(t,e,!0)}function xT(){NT(function(){(zt&6)!==0?jt(Wr,vT):Rg()})}function Xu(){return ti===0&&(ti=Th()),ti}function jg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ll(""+t)}function _g(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function bT(t,e,i,r,o){if(e==="submit"&&i&&i.stateNode===o){var u=jg((o[Oe]||null).action),y=r.submitter;y&&(e=(e=y[Oe]||null)?jg(e.formAction):y.getAttribute("formAction"),e!==null&&(u=e,y=null));var S=new fl("action","action",null,r,o);t.push({event:S,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(ti!==0){var C=y?_g(o,y):new FormData(o);fu(i,{pending:!0,data:C,method:o.method,action:u},null,C)}}else typeof u=="function"&&(S.preventDefault(),C=y?_g(o,y):new FormData(o),fu(i,{pending:!0,data:C,method:o.method,action:u},u,C))},currentTarget:o}]})}}for(var Ku=0;Ku<Dc.length;Ku++){var Zu=Dc[Ku],ST=Zu.toLowerCase(),TT=Zu[0].toUpperCase()+Zu.slice(1);cn(ST,"on"+TT)}cn(dm,"onAnimationEnd"),cn(hm,"onAnimationIteration"),cn(mm,"onAnimationStart"),cn("dblclick","onDoubleClick"),cn("focusin","onFocus"),cn("focusout","onBlur"),cn(U1,"onTransitionRun"),cn(H1,"onTransitionStart"),cn(P1,"onTransitionCancel"),cn(pm,"onTransitionEnd"),vi("onMouseEnter",["mouseout","mouseover"]),vi("onMouseLeave",["mouseout","mouseover"]),vi("onPointerEnter",["pointerout","pointerover"]),vi("onPointerLeave",["pointerout","pointerover"]),ka("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ka("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ka("onBeforeInput",["compositionend","keypress","textInput","paste"]),ka("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ka("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ka("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wT=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(or));function Vg(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var r=t[i],o=r.event;r=r.listeners;t:{var u=void 0;if(e)for(var y=r.length-1;0<=y;y--){var S=r[y],C=S.instance,U=S.currentTarget;if(S=S.listener,C!==u&&o.isPropagationStopped())break t;u=S,o.currentTarget=U;try{u(o)}catch(Q){Bl(Q)}o.currentTarget=null,u=C}else for(y=0;y<r.length;y++){if(S=r[y],C=S.instance,U=S.currentTarget,S=S.listener,C!==u&&o.isPropagationStopped())break t;u=S,o.currentTarget=U;try{u(o)}catch(Q){Bl(Q)}o.currentTarget=null,u=C}}}}function Nt(t,e){var i=e[rc];i===void 0&&(i=e[rc]=new Set);var r=t+"__bubble";i.has(r)||(zg(e,t,2,!1),i.add(r))}function Qu(t,e,i){var r=0;e&&(r|=4),zg(i,t,r,e)}var $l="_reactListening"+Math.random().toString(36).slice(2);function Fu(t){if(!t[$l]){t[$l]=!0,Rh.forEach(function(i){i!=="selectionchange"&&(wT.has(i)||Qu(i,!1,t),Qu(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[$l]||(e[$l]=!0,Qu("selectionchange",!1,e))}}function zg(t,e,i,r){switch(sy(e)){case 2:var o=IT;break;case 8:o=$T;break;default:o=uf}i=o.bind(null,e,i,t),o=void 0,!yc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,i,{capture:!0,passive:o}):t.addEventListener(e,i,!0):o!==void 0?t.addEventListener(e,i,{passive:o}):t.addEventListener(e,i,!1)}function Iu(t,e,i,r,o){var u=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var S=r.stateNode.containerInfo;if(S===o)break;if(y===4)for(y=r.return;y!==null;){var C=y.tag;if((C===3||C===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;S!==null;){if(y=pi(S),y===null)return;if(C=y.tag,C===5||C===6||C===26||C===27){r=u=y;continue t}S=S.parentNode}}r=r.return}Gh(function(){var U=u,Q=pc(i),I=[];t:{var H=gm.get(t);if(H!==void 0){var G=fl,vt=t;switch(t){case"keypress":if(cl(i)===0)break t;case"keydown":case"keyup":G=y1;break;case"focusin":vt="focus",G=Sc;break;case"focusout":vt="blur",G=Sc;break;case"beforeblur":case"afterblur":G=Sc;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Xh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=s1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=b1;break;case dm:case hm:case mm:G=o1;break;case pm:G=T1;break;case"scroll":case"scrollend":G=a1;break;case"wheel":G=A1;break;case"copy":case"cut":case"paste":G=u1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=Zh;break;case"toggle":case"beforetoggle":G=M1}var pt=(e&4)!==0,Ht=!pt&&(t==="scroll"||t==="scrollend"),z=pt?H!==null?H+"Capture":null:H;pt=[];for(var _=U,k;_!==null;){var F=_;if(k=F.stateNode,F=F.tag,F!==5&&F!==26&&F!==27||k===null||z===null||(F=Cs(_,z),F!=null&&pt.push(cr(_,F,k))),Ht)break;_=_.return}0<pt.length&&(H=new G(H,vt,null,i,Q),I.push({event:H,listeners:pt}))}}if((e&7)===0){t:{if(H=t==="mouseover"||t==="pointerover",G=t==="mouseout"||t==="pointerout",H&&i!==mc&&(vt=i.relatedTarget||i.fromElement)&&(pi(vt)||vt[mi]))break t;if((G||H)&&(H=Q.window===Q?Q:(H=Q.ownerDocument)?H.defaultView||H.parentWindow:window,G?(vt=i.relatedTarget||i.toElement,G=U,vt=vt?pi(vt):null,vt!==null&&(Ht=d(vt),pt=vt.tag,vt!==Ht||pt!==5&&pt!==27&&pt!==6)&&(vt=null)):(G=null,vt=U),G!==vt)){if(pt=Xh,F="onMouseLeave",z="onMouseEnter",_="mouse",(t==="pointerout"||t==="pointerover")&&(pt=Zh,F="onPointerLeave",z="onPointerEnter",_="pointer"),Ht=G==null?H:Ms(G),k=vt==null?H:Ms(vt),H=new pt(F,_+"leave",G,i,Q),H.target=Ht,H.relatedTarget=k,F=null,pi(Q)===U&&(pt=new pt(z,_+"enter",vt,i,Q),pt.target=k,pt.relatedTarget=Ht,F=pt),Ht=F,G&&vt)e:{for(pt=G,z=vt,_=0,k=pt;k;k=Fi(k))_++;for(k=0,F=z;F;F=Fi(F))k++;for(;0<_-k;)pt=Fi(pt),_--;for(;0<k-_;)z=Fi(z),k--;for(;_--;){if(pt===z||z!==null&&pt===z.alternate)break e;pt=Fi(pt),z=Fi(z)}pt=null}else pt=null;G!==null&&Lg(I,H,G,pt,!1),vt!==null&&Ht!==null&&Lg(I,Ht,vt,pt,!0)}}t:{if(H=U?Ms(U):window,G=H.nodeName&&H.nodeName.toLowerCase(),G==="select"||G==="input"&&H.type==="file")var lt=em;else if(Jh(H))if(nm)lt=L1;else{lt=V1;var Mt=_1}else G=H.nodeName,!G||G.toLowerCase()!=="input"||H.type!=="checkbox"&&H.type!=="radio"?U&&hc(U.elementType)&&(lt=em):lt=z1;if(lt&&(lt=lt(t,U))){tm(I,lt,i,Q);break t}Mt&&Mt(t,H,U),t==="focusout"&&U&&H.type==="number"&&U.memoizedProps.value!=null&&dc(H,"number",H.value)}switch(Mt=U?Ms(U):window,t){case"focusin":(Jh(Mt)||Mt.contentEditable==="true")&&(Ei=Mt,Cc=U,zs=null);break;case"focusout":zs=Cc=Ei=null;break;case"mousedown":Rc=!0;break;case"contextmenu":case"mouseup":case"dragend":Rc=!1,um(I,i,Q);break;case"selectionchange":if(k1)break;case"keydown":case"keyup":um(I,i,Q)}var dt;if(wc)t:{switch(t){case"compositionstart":var gt="onCompositionStart";break t;case"compositionend":gt="onCompositionEnd";break t;case"compositionupdate":gt="onCompositionUpdate";break t}gt=void 0}else Ai?$h(t,i)&&(gt="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(gt="onCompositionStart");gt&&(Qh&&i.locale!=="ko"&&(Ai||gt!=="onCompositionStart"?gt==="onCompositionEnd"&&Ai&&(dt=qh()):(na=Q,vc="value"in na?na.value:na.textContent,Ai=!0)),Mt=Wl(U,gt),0<Mt.length&&(gt=new Kh(gt,t,null,i,Q),I.push({event:gt,listeners:Mt}),dt?gt.data=dt:(dt=Wh(i),dt!==null&&(gt.data=dt)))),(dt=R1?N1(t,i):D1(t,i))&&(gt=Wl(U,"onBeforeInput"),0<gt.length&&(Mt=new Kh("onBeforeInput","beforeinput",null,i,Q),I.push({event:Mt,listeners:gt}),Mt.data=dt)),bT(I,t,U,i,Q)}Vg(I,e)})}function cr(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Wl(t,e){for(var i=e+"Capture",r=[];t!==null;){var o=t,u=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||u===null||(o=Cs(t,i),o!=null&&r.unshift(cr(t,o,u)),o=Cs(t,e),o!=null&&r.push(cr(t,o,u))),t.tag===3)return r;t=t.return}return[]}function Fi(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Lg(t,e,i,r,o){for(var u=e._reactName,y=[];i!==null&&i!==r;){var S=i,C=S.alternate,U=S.stateNode;if(S=S.tag,C!==null&&C===r)break;S!==5&&S!==26&&S!==27||U===null||(C=U,o?(U=Cs(i,u),U!=null&&y.unshift(cr(i,U,C))):o||(U=Cs(i,u),U!=null&&y.push(cr(i,U,C)))),i=i.return}y.length!==0&&t.push({event:e,listeners:y})}var AT=/\r\n?/g,ET=/\u0000|\uFFFD/g;function Bg(t){return(typeof t=="string"?t:""+t).replace(AT,`
`).replace(ET,"")}function kg(t,e){return e=Bg(e),Bg(t)===e}function Jl(){}function Ut(t,e,i,r,o,u){switch(i){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||Si(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&Si(t,""+r);break;case"className":il(t,"class",r);break;case"tabIndex":il(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":il(t,i,r);break;case"style":Hh(t,r,u);break;case"data":if(e!=="object"){il(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=ll(""+r),t.setAttribute(i,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(i==="formAction"?(e!=="input"&&Ut(t,e,"name",o.name,o,null),Ut(t,e,"formEncType",o.formEncType,o,null),Ut(t,e,"formMethod",o.formMethod,o,null),Ut(t,e,"formTarget",o.formTarget,o,null)):(Ut(t,e,"encType",o.encType,o,null),Ut(t,e,"method",o.method,o,null),Ut(t,e,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=ll(""+r),t.setAttribute(i,r);break;case"onClick":r!=null&&(t.onclick=Jl);break;case"onScroll":r!=null&&Nt("scroll",t);break;case"onScrollEnd":r!=null&&Nt("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}i=ll(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""+r):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":r===!0?t.setAttribute(i,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,r):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(i,r):t.removeAttribute(i);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(i):t.setAttribute(i,r);break;case"popover":Nt("beforetoggle",t),Nt("toggle",t),al(t,"popover",r);break;case"xlinkActuate":_n(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":_n(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":_n(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":_n(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":_n(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":_n(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":_n(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":_n(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":_n(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":al(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=e1.get(i)||i,al(t,i,r))}}function $u(t,e,i,r,o,u){switch(i){case"style":Hh(t,r,u);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(l(61));if(i=r.__html,i!=null){if(o.children!=null)throw Error(l(60));t.innerHTML=i}}break;case"children":typeof r=="string"?Si(t,r):(typeof r=="number"||typeof r=="bigint")&&Si(t,""+r);break;case"onScroll":r!=null&&Nt("scroll",t);break;case"onScrollEnd":r!=null&&Nt("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Jl);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Nh.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(o=i.endsWith("Capture"),e=i.slice(2,o?i.length-7:void 0),u=t[Oe]||null,u=u!=null?u[i]:null,typeof u=="function"&&t.removeEventListener(e,u,o),typeof r=="function")){typeof u!="function"&&u!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,r,o);break t}i in t?t[i]=r:r===!0?t.setAttribute(i,""):al(t,i,r)}}}function xe(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Nt("error",t),Nt("load",t);var r=!1,o=!1,u;for(u in i)if(i.hasOwnProperty(u)){var y=i[u];if(y!=null)switch(u){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:Ut(t,e,u,y,i,null)}}o&&Ut(t,e,"srcSet",i.srcSet,i,null),r&&Ut(t,e,"src",i.src,i,null);return;case"input":Nt("invalid",t);var S=u=y=o=null,C=null,U=null;for(r in i)if(i.hasOwnProperty(r)){var Q=i[r];if(Q!=null)switch(r){case"name":o=Q;break;case"type":y=Q;break;case"checked":C=Q;break;case"defaultChecked":U=Q;break;case"value":u=Q;break;case"defaultValue":S=Q;break;case"children":case"dangerouslySetInnerHTML":if(Q!=null)throw Error(l(137,e));break;default:Ut(t,e,r,Q,i,null)}}Lh(t,u,S,C,U,y,o,!1),sl(t);return;case"select":Nt("invalid",t),r=y=u=null;for(o in i)if(i.hasOwnProperty(o)&&(S=i[o],S!=null))switch(o){case"value":u=S;break;case"defaultValue":y=S;break;case"multiple":r=S;default:Ut(t,e,o,S,i,null)}e=u,i=y,t.multiple=!!r,e!=null?bi(t,!!r,e,!1):i!=null&&bi(t,!!r,i,!0);return;case"textarea":Nt("invalid",t),u=o=r=null;for(y in i)if(i.hasOwnProperty(y)&&(S=i[y],S!=null))switch(y){case"value":r=S;break;case"defaultValue":o=S;break;case"children":u=S;break;case"dangerouslySetInnerHTML":if(S!=null)throw Error(l(91));break;default:Ut(t,e,y,S,i,null)}kh(t,r,o,u),sl(t);return;case"option":for(C in i)if(i.hasOwnProperty(C)&&(r=i[C],r!=null))switch(C){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:Ut(t,e,C,r,i,null)}return;case"dialog":Nt("beforetoggle",t),Nt("toggle",t),Nt("cancel",t),Nt("close",t);break;case"iframe":case"object":Nt("load",t);break;case"video":case"audio":for(r=0;r<or.length;r++)Nt(or[r],t);break;case"image":Nt("error",t),Nt("load",t);break;case"details":Nt("toggle",t);break;case"embed":case"source":case"link":Nt("error",t),Nt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(U in i)if(i.hasOwnProperty(U)&&(r=i[U],r!=null))switch(U){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:Ut(t,e,U,r,i,null)}return;default:if(hc(e)){for(Q in i)i.hasOwnProperty(Q)&&(r=i[Q],r!==void 0&&$u(t,e,Q,r,i,void 0));return}}for(S in i)i.hasOwnProperty(S)&&(r=i[S],r!=null&&Ut(t,e,S,r,i,null))}function MT(t,e,i,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,u=null,y=null,S=null,C=null,U=null,Q=null;for(G in i){var I=i[G];if(i.hasOwnProperty(G)&&I!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":C=I;default:r.hasOwnProperty(G)||Ut(t,e,G,null,r,I)}}for(var H in r){var G=r[H];if(I=i[H],r.hasOwnProperty(H)&&(G!=null||I!=null))switch(H){case"type":u=G;break;case"name":o=G;break;case"checked":U=G;break;case"defaultChecked":Q=G;break;case"value":y=G;break;case"defaultValue":S=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(l(137,e));break;default:G!==I&&Ut(t,e,H,G,r,I)}}fc(t,y,S,C,U,Q,u,o);return;case"select":G=y=S=H=null;for(u in i)if(C=i[u],i.hasOwnProperty(u)&&C!=null)switch(u){case"value":break;case"multiple":G=C;default:r.hasOwnProperty(u)||Ut(t,e,u,null,r,C)}for(o in r)if(u=r[o],C=i[o],r.hasOwnProperty(o)&&(u!=null||C!=null))switch(o){case"value":H=u;break;case"defaultValue":S=u;break;case"multiple":y=u;default:u!==C&&Ut(t,e,o,u,r,C)}e=S,i=y,r=G,H!=null?bi(t,!!i,H,!1):!!r!=!!i&&(e!=null?bi(t,!!i,e,!0):bi(t,!!i,i?[]:"",!1));return;case"textarea":G=H=null;for(S in i)if(o=i[S],i.hasOwnProperty(S)&&o!=null&&!r.hasOwnProperty(S))switch(S){case"value":break;case"children":break;default:Ut(t,e,S,null,r,o)}for(y in r)if(o=r[y],u=i[y],r.hasOwnProperty(y)&&(o!=null||u!=null))switch(y){case"value":H=o;break;case"defaultValue":G=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(l(91));break;default:o!==u&&Ut(t,e,y,o,r,u)}Bh(t,H,G);return;case"option":for(var vt in i)if(H=i[vt],i.hasOwnProperty(vt)&&H!=null&&!r.hasOwnProperty(vt))switch(vt){case"selected":t.selected=!1;break;default:Ut(t,e,vt,null,r,H)}for(C in r)if(H=r[C],G=i[C],r.hasOwnProperty(C)&&H!==G&&(H!=null||G!=null))switch(C){case"selected":t.selected=H&&typeof H!="function"&&typeof H!="symbol";break;default:Ut(t,e,C,H,r,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var pt in i)H=i[pt],i.hasOwnProperty(pt)&&H!=null&&!r.hasOwnProperty(pt)&&Ut(t,e,pt,null,r,H);for(U in r)if(H=r[U],G=i[U],r.hasOwnProperty(U)&&H!==G&&(H!=null||G!=null))switch(U){case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(l(137,e));break;default:Ut(t,e,U,H,r,G)}return;default:if(hc(e)){for(var Ht in i)H=i[Ht],i.hasOwnProperty(Ht)&&H!==void 0&&!r.hasOwnProperty(Ht)&&$u(t,e,Ht,void 0,r,H);for(Q in r)H=r[Q],G=i[Q],!r.hasOwnProperty(Q)||H===G||H===void 0&&G===void 0||$u(t,e,Q,H,r,G);return}}for(var z in i)H=i[z],i.hasOwnProperty(z)&&H!=null&&!r.hasOwnProperty(z)&&Ut(t,e,z,null,r,H);for(I in r)H=r[I],G=i[I],!r.hasOwnProperty(I)||H===G||H==null&&G==null||Ut(t,e,I,H,r,G)}var Wu=null,Ju=null;function to(t){return t.nodeType===9?t:t.ownerDocument}function Ug(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function tf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ef=null;function CT(){var t=window.event;return t&&t.type==="popstate"?t===ef?!1:(ef=t,!0):(ef=null,!1)}var Pg=typeof setTimeout=="function"?setTimeout:void 0,RT=typeof clearTimeout=="function"?clearTimeout:void 0,Gg=typeof Promise=="function"?Promise:void 0,NT=typeof queueMicrotask=="function"?queueMicrotask:typeof Gg!="undefined"?function(t){return Gg.resolve(null).then(t).catch(DT)}:Pg;function DT(t){setTimeout(function(){throw t})}function va(t){return t==="head"}function qg(t,e){var i=e,r=0,o=0;do{var u=i.nextSibling;if(t.removeChild(i),u&&u.nodeType===8)if(i=u.data,i==="/$"){if(0<r&&8>r){i=r;var y=t.ownerDocument;if(i&1&&ur(y.documentElement),i&2&&ur(y.body),i&4)for(i=y.head,ur(i),y=i.firstChild;y;){var S=y.nextSibling,C=y.nodeName;y[Es]||C==="SCRIPT"||C==="STYLE"||C==="LINK"&&y.rel.toLowerCase()==="stylesheet"||i.removeChild(y),y=S}}if(o===0){t.removeChild(u),vr(e);return}o--}else i==="$"||i==="$?"||i==="$!"?o++:r=i.charCodeAt(0)-48;else r=0;i=u}while(i);vr(e)}function nf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":nf(i),lc(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function OT(t,e,i,r){for(;t.nodeType===1;){var o=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[Es])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=fn(t.nextSibling),t===null)break}return null}function jT(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=fn(t.nextSibling),t===null))return null;return t}function af(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function _T(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var r=function(){e(),i.removeEventListener("DOMContentLoaded",r)};i.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function fn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var sf=null;function Yg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function Xg(t,e,i){switch(e=to(i),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function ur(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);lc(t)}var an=new Map,Kg=new Set;function eo(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Zn=X.d;X.d={f:VT,r:zT,D:LT,C:BT,L:kT,m:UT,X:PT,S:HT,M:GT};function VT(){var t=Zn.f(),e=Kl();return t||e}function zT(t){var e=gi(t);e!==null&&e.tag===5&&e.type==="form"?dp(e):Zn.r(t)}var Ii=typeof document=="undefined"?null:document;function Zg(t,e,i){var r=Ii;if(r&&typeof e=="string"&&e){var o=Ie(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof i=="string"&&(o+='[crossorigin="'+i+'"]'),Kg.has(o)||(Kg.add(o),t={rel:t,crossOrigin:i,href:e},r.querySelector(o)===null&&(e=r.createElement("link"),xe(e,"link",t),ue(e),r.head.appendChild(e)))}}function LT(t){Zn.D(t),Zg("dns-prefetch",t,null)}function BT(t,e){Zn.C(t,e),Zg("preconnect",t,e)}function kT(t,e,i){Zn.L(t,e,i);var r=Ii;if(r&&t&&e){var o='link[rel="preload"][as="'+Ie(e)+'"]';e==="image"&&i&&i.imageSrcSet?(o+='[imagesrcset="'+Ie(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(o+='[imagesizes="'+Ie(i.imageSizes)+'"]')):o+='[href="'+Ie(t)+'"]';var u=o;switch(e){case"style":u=$i(t);break;case"script":u=Wi(t)}an.has(u)||(t=g({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),an.set(u,t),r.querySelector(o)!==null||e==="style"&&r.querySelector(fr(u))||e==="script"&&r.querySelector(dr(u))||(e=r.createElement("link"),xe(e,"link",t),ue(e),r.head.appendChild(e)))}}function UT(t,e){Zn.m(t,e);var i=Ii;if(i&&t){var r=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+Ie(r)+'"][href="'+Ie(t)+'"]',u=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Wi(t)}if(!an.has(u)&&(t=g({rel:"modulepreload",href:t},e),an.set(u,t),i.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(dr(u)))return}r=i.createElement("link"),xe(r,"link",t),ue(r),i.head.appendChild(r)}}}function HT(t,e,i){Zn.S(t,e,i);var r=Ii;if(r&&t){var o=yi(r).hoistableStyles,u=$i(t);e=e||"default";var y=o.get(u);if(!y){var S={loading:0,preload:null};if(y=r.querySelector(fr(u)))S.loading=5;else{t=g({rel:"stylesheet",href:t,"data-precedence":e},i),(i=an.get(u))&&rf(t,i);var C=y=r.createElement("link");ue(C),xe(C,"link",t),C._p=new Promise(function(U,Q){C.onload=U,C.onerror=Q}),C.addEventListener("load",function(){S.loading|=1}),C.addEventListener("error",function(){S.loading|=2}),S.loading|=4,no(y,e,r)}y={type:"stylesheet",instance:y,count:1,state:S},o.set(u,y)}}}function PT(t,e){Zn.X(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=Wi(t),u=r.get(o);u||(u=i.querySelector(dr(o)),u||(t=g({src:t,async:!0},e),(e=an.get(o))&&lf(t,e),u=i.createElement("script"),ue(u),xe(u,"link",t),i.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},r.set(o,u))}}function GT(t,e){Zn.M(t,e);var i=Ii;if(i&&t){var r=yi(i).hoistableScripts,o=Wi(t),u=r.get(o);u||(u=i.querySelector(dr(o)),u||(t=g({src:t,async:!0,type:"module"},e),(e=an.get(o))&&lf(t,e),u=i.createElement("script"),ue(u),xe(u,"link",t),i.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},r.set(o,u))}}function Qg(t,e,i,r){var o=(o=mt.current)?eo(o):null;if(!o)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=$i(i.href),i=yi(o).hoistableStyles,r=i.get(e),r||(r={type:"style",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=$i(i.href);var u=yi(o).hoistableStyles,y=u.get(t);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,y),(u=o.querySelector(fr(t)))&&!u._p&&(y.instance=u,y.state.loading=5),an.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},an.set(t,i),u||qT(o,t,i,y.state))),e&&r===null)throw Error(l(528,""));return y}if(e&&r!==null)throw Error(l(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wi(i),i=yi(o).hoistableScripts,r=i.get(e),r||(r={type:"script",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function $i(t){return'href="'+Ie(t)+'"'}function fr(t){return'link[rel="stylesheet"]['+t+"]"}function Fg(t){return g({},t,{"data-precedence":t.precedence,precedence:null})}function qT(t,e,i,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),xe(e,"link",i),ue(e),t.head.appendChild(e))}function Wi(t){return'[src="'+Ie(t)+'"]'}function dr(t){return"script[async]"+t}function Ig(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Ie(i.href)+'"]');if(r)return e.instance=r,ue(r),r;var o=g({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),ue(r),xe(r,"style",o),no(r,i.precedence,t),e.instance=r;case"stylesheet":o=$i(i.href);var u=t.querySelector(fr(o));if(u)return e.state.loading|=4,e.instance=u,ue(u),u;r=Fg(i),(o=an.get(o))&&rf(r,o),u=(t.ownerDocument||t).createElement("link"),ue(u);var y=u;return y._p=new Promise(function(S,C){y.onload=S,y.onerror=C}),xe(u,"link",r),e.state.loading|=4,no(u,i.precedence,t),e.instance=u;case"script":return u=Wi(i.src),(o=t.querySelector(dr(u)))?(e.instance=o,ue(o),o):(r=i,(o=an.get(u))&&(r=g({},i),lf(r,o)),t=t.ownerDocument||t,o=t.createElement("script"),ue(o),xe(o,"link",r),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,no(r,i.precedence,t));return e.instance}function no(t,e,i){for(var r=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,u=o,y=0;y<r.length;y++){var S=r[y];if(S.dataset.precedence===e)u=S;else if(u!==o)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function rf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function lf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ao=null;function $g(t,e,i){if(ao===null){var r=new Map,o=ao=new Map;o.set(i,r)}else o=ao,r=o.get(i),r||(r=new Map,o.set(i,r));if(r.has(t))return r;for(r.set(t,null),i=i.getElementsByTagName(t),o=0;o<i.length;o++){var u=i[o];if(!(u[Es]||u[Se]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var y=u.getAttribute(e)||"";y=t+y;var S=r.get(y);S?S.push(u):r.set(y,[u])}}return r}function Wg(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function YT(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Jg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var hr=null;function XT(){}function KT(t,e,i){if(hr===null)throw Error(l(475));var r=hr;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=$i(i.href),u=t.querySelector(fr(o));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=io.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=u,ue(u);return}u=t.ownerDocument||t,i=Fg(i),(o=an.get(o))&&rf(i,o),u=u.createElement("link"),ue(u);var y=u;y._p=new Promise(function(S,C){y.onload=S,y.onerror=C}),xe(u,"link",i),e.instance=u}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=io.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function ZT(){if(hr===null)throw Error(l(475));var t=hr;return t.stylesheets&&t.count===0&&of(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&of(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function io(){if(this.count--,this.count===0){if(this.stylesheets)of(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var so=null;function of(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,so=new Map,e.forEach(QT,t),so=null,io.call(t))}function QT(t,e){if(!(e.state.loading&4)){var i=so.get(t);if(i)var r=i.get(null);else{i=new Map,so.set(t,i);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<o.length;u++){var y=o[u];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(i.set(y.dataset.precedence,y),r=y)}r&&i.set(null,r)}o=e.instance,y=o.getAttribute("data-precedence"),u=i.get(y)||r,u===r&&i.set(null,o),i.set(y,o),this.count++,r=io.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),u?u.parentNode.insertBefore(o,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var mr={$$typeof:D,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function FT(t,e,i,r,o,u,y,S){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ac(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ac(0),this.hiddenUpdates=ac(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=u,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=S,this.incompleteTransitions=new Map}function ty(t,e,i,r,o,u,y,S,C,U,Q,I){return t=new FT(t,e,i,y,S,C,U,I),e=1,u===!0&&(e|=24),u=He(3,null,null,e),t.current=u,u.stateNode=t,e=Gc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:r,isDehydrated:i,cache:e},Kc(u),t}function ey(t){return t?(t=Ni,t):Ni}function ny(t,e,i,r,o,u){o=ey(o),r.context===null?r.context=o:r.pendingContext=o,r=sa(e),r.payload={element:i},u=u===void 0?null:u,u!==null&&(r.callback=u),i=ra(t,r,e),i!==null&&(Xe(i,t,e),Ys(i,t,e))}function ay(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function cf(t,e){ay(t,e),(t=t.alternate)&&ay(t,e)}function iy(t){if(t.tag===13){var e=Ri(t,67108864);e!==null&&Xe(e,t,67108864),cf(t,67108864)}}var ro=!0;function IT(t,e,i,r){var o=B.T;B.T=null;var u=X.p;try{X.p=2,uf(t,e,i,r)}finally{X.p=u,B.T=o}}function $T(t,e,i,r){var o=B.T;B.T=null;var u=X.p;try{X.p=8,uf(t,e,i,r)}finally{X.p=u,B.T=o}}function uf(t,e,i,r){if(ro){var o=ff(r);if(o===null)Iu(t,e,r,lo,i),ry(t,r);else if(JT(o,t,e,i,r))r.stopPropagation();else if(ry(t,r),e&4&&-1<WT.indexOf(t)){for(;o!==null;){var u=gi(o);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var y=Ba(u.pendingLanes);if(y!==0){var S=u;for(S.pendingLanes|=2,S.entangledLanes|=2;y;){var C=1<<31-ke(y);S.entanglements[1]|=C,y&=~C}En(u),(zt&6)===0&&(Yl=ge()+500,lr(0))}}break;case 13:S=Ri(u,2),S!==null&&Xe(S,u,2),Kl(),cf(u,2)}if(u=ff(r),u===null&&Iu(t,e,r,lo,i),u===o)break;o=u}o!==null&&r.stopPropagation()}else Iu(t,e,r,null,i)}}function ff(t){return t=pc(t),df(t)}var lo=null;function df(t){if(lo=null,t=pi(t),t!==null){var e=d(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=f(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return lo=t,null}function sy(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(La()){case Wr:return 2;case Jr:return 8;case di:case hi:return 32;case Sh:return 268435456;default:return 32}default:return 32}}var hf=!1,xa=null,ba=null,Sa=null,pr=new Map,gr=new Map,Ta=[],WT="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ry(t,e){switch(t){case"focusin":case"focusout":xa=null;break;case"dragenter":case"dragleave":ba=null;break;case"mouseover":case"mouseout":Sa=null;break;case"pointerover":case"pointerout":pr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(e.pointerId)}}function yr(t,e,i,r,o,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:i,eventSystemFlags:r,nativeEvent:u,targetContainers:[o]},e!==null&&(e=gi(e),e!==null&&iy(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function JT(t,e,i,r,o){switch(e){case"focusin":return xa=yr(xa,t,e,i,r,o),!0;case"dragenter":return ba=yr(ba,t,e,i,r,o),!0;case"mouseover":return Sa=yr(Sa,t,e,i,r,o),!0;case"pointerover":var u=o.pointerId;return pr.set(u,yr(pr.get(u)||null,t,e,i,r,o)),!0;case"gotpointercapture":return u=o.pointerId,gr.set(u,yr(gr.get(u)||null,t,e,i,r,o)),!0}return!1}function ly(t){var e=pi(t.target);if(e!==null){var i=d(e);if(i!==null){if(e=i.tag,e===13){if(e=f(i),e!==null){t.blockedOn=e,KS(t.priority,function(){if(i.tag===13){var r=Ye();r=ic(r);var o=Ri(i,r);o!==null&&Xe(o,i,r),cf(i,r)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function oo(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=ff(t.nativeEvent);if(i===null){i=t.nativeEvent;var r=new i.constructor(i.type,i);mc=r,i.target.dispatchEvent(r),mc=null}else return e=gi(i),e!==null&&iy(e),t.blockedOn=i,!1;e.shift()}return!0}function oy(t,e,i){oo(t)&&i.delete(e)}function tw(){hf=!1,xa!==null&&oo(xa)&&(xa=null),ba!==null&&oo(ba)&&(ba=null),Sa!==null&&oo(Sa)&&(Sa=null),pr.forEach(oy),gr.forEach(oy)}function co(t,e){t.blockedOn===e&&(t.blockedOn=null,hf||(hf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,tw)))}var uo=null;function cy(t){uo!==t&&(uo=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){uo===t&&(uo=null);for(var e=0;e<t.length;e+=3){var i=t[e],r=t[e+1],o=t[e+2];if(typeof r!="function"){if(df(r||i)===null)continue;break}var u=gi(i);u!==null&&(t.splice(e,3),e-=3,fu(u,{pending:!0,data:o,method:i.method,action:r},r,o))}}))}function vr(t){function e(C){return co(C,t)}xa!==null&&co(xa,t),ba!==null&&co(ba,t),Sa!==null&&co(Sa,t),pr.forEach(e),gr.forEach(e);for(var i=0;i<Ta.length;i++){var r=Ta[i];r.blockedOn===t&&(r.blockedOn=null)}for(;0<Ta.length&&(i=Ta[0],i.blockedOn===null);)ly(i),i.blockedOn===null&&Ta.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(r=0;r<i.length;r+=3){var o=i[r],u=i[r+1],y=o[Oe]||null;if(typeof u=="function")y||cy(i);else if(y){var S=null;if(u&&u.hasAttribute("formAction")){if(o=u,y=u[Oe]||null)S=y.formAction;else if(df(o)!==null)continue}else S=y.action;typeof S=="function"?i[r+1]=S:(i.splice(r,3),r-=3),cy(i)}}}function mf(t){this._internalRoot=t}fo.prototype.render=mf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var i=e.current,r=Ye();ny(i,r,t,e,null,null)},fo.prototype.unmount=mf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;ny(t.current,2,null,t,null,null),Kl(),e[mi]=null}};function fo(t){this._internalRoot=t}fo.prototype.unstable_scheduleHydration=function(t){if(t){var e=Mh();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Ta.length&&e!==0&&e<Ta[i].priority;i++);Ta.splice(i,0,t),i===0&&ly(t)}};var uy=a.version;if(uy!=="19.1.0")throw Error(l(527,uy,"19.1.0"));X.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=m(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var ew={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ho.isDisabled&&ho.supportsFiber)try{Ts=ho.inject(ew),Be=ho}catch(t){}}return br.createRoot=function(t,e){if(!c(t))throw Error(l(299));var i=!1,r="",o=Mp,u=Cp,y=Rp,S=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(S=e.unstable_transitionCallbacks)),e=ty(t,1,!1,null,null,i,r,o,u,y,S,null),t[mi]=e.current,Fu(t),new mf(e)},br.hydrateRoot=function(t,e,i){if(!c(t))throw Error(l(299));var r=!1,o="",u=Mp,y=Cp,S=Rp,C=null,U=null;return i!=null&&(i.unstable_strictMode===!0&&(r=!0),i.identifierPrefix!==void 0&&(o=i.identifierPrefix),i.onUncaughtError!==void 0&&(u=i.onUncaughtError),i.onCaughtError!==void 0&&(y=i.onCaughtError),i.onRecoverableError!==void 0&&(S=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(C=i.unstable_transitionCallbacks),i.formState!==void 0&&(U=i.formState)),e=ty(t,1,!0,e,i!=null?i:null,r,o,u,y,S,C,U),e.context=ey(null),i=e.current,r=Ye(),r=ic(r),o=sa(r),o.callback=null,ra(i,o,r),i=r,e.current.lanes=i,As(e,i),En(e),t[mi]=e.current,Fu(t),new fo(e)},br.version="19.1.0",br}var wy;function pw(){if(wy)return vf.exports;wy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),vf.exports=mw(),vf.exports}var gw=pw();const y0=w.createContext({});function yw(n){const a=w.useRef(null);return a.current===null&&(a.current=n()),a.current}const Dd=typeof window!="undefined",vw=Dd?w.useLayoutEffect:w.useEffect,Od=w.createContext(null);function jd(n,a){n.indexOf(a)===-1&&n.push(a)}function _d(n,a){const s=n.indexOf(a);s>-1&&n.splice(s,1)}const In=(n,a,s)=>s>a?a:s<n?n:s;let Vd=()=>{};const $n={},v0=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function x0(n){return typeof n=="object"&&n!==null}const b0=n=>/^0[^.\s]+$/u.test(n);function zd(n){let a;return()=>(a===void 0&&(a=n()),a)}const ln=n=>n,xw=(n,a)=>s=>a(n(s)),qr=(...n)=>n.reduce(xw),_r=(n,a,s)=>{const l=a-n;return l===0?1:(s-n)/l};class Ld{constructor(){this.subscriptions=[]}add(a){return jd(this.subscriptions,a),()=>_d(this.subscriptions,a)}notify(a,s,l){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](a,s,l);else for(let d=0;d<c;d++){const f=this.subscriptions[d];f&&f(a,s,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Cn=n=>n*1e3,Rn=n=>n/1e3;function S0(n,a){return a?n*(1e3/a):0}const T0=(n,a,s)=>(((1-3*s+3*a)*n+(3*s-6*a))*n+3*a)*n,bw=1e-7,Sw=12;function Tw(n,a,s,l,c){let d,f,h=0;do f=a+(s-a)/2,d=T0(f,l,c)-n,d>0?s=f:a=f;while(Math.abs(d)>bw&&++h<Sw);return f}function Yr(n,a,s,l){if(n===a&&s===l)return ln;const c=d=>Tw(d,0,1,n,s);return d=>d===0||d===1?d:T0(c(d),a,l)}const w0=n=>a=>a<=.5?n(2*a)/2:(2-n(2*(1-a)))/2,A0=n=>a=>1-n(1-a),E0=Yr(.33,1.53,.69,.99),Bd=A0(E0),M0=w0(Bd),C0=n=>(n*=2)<1?.5*Bd(n):.5*(2-Math.pow(2,-10*(n-1))),kd=n=>1-Math.sin(Math.acos(n)),R0=A0(kd),N0=w0(kd),ww=Yr(.42,0,1,1),Aw=Yr(0,0,.58,1),D0=Yr(.42,0,.58,1),Ew=n=>Array.isArray(n)&&typeof n[0]!="number",O0=n=>Array.isArray(n)&&typeof n[0]=="number",Mw={linear:ln,easeIn:ww,easeInOut:D0,easeOut:Aw,circIn:kd,circInOut:N0,circOut:R0,backIn:Bd,backInOut:M0,backOut:E0,anticipate:C0},Cw=n=>typeof n=="string",Ay=n=>{if(O0(n)){Vd(n.length===4);const[a,s,l,c]=n;return Yr(a,s,l,c)}else if(Cw(n))return Mw[n];return n},po=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Ey={value:null};function Rw(n,a){let s=new Set,l=new Set,c=!1,d=!1;const f=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},m=0;function p(v){f.has(v)&&(g.schedule(v),n()),m++,v(h)}const g={schedule:(v,b=!1,T=!1)=>{const A=T&&c?s:l;return b&&f.add(v),A.has(v)||A.add(v),v},cancel:v=>{l.delete(v),f.delete(v)},process:v=>{if(h=v,c){d=!0;return}c=!0,[s,l]=[l,s],s.forEach(p),a&&Ey.value&&Ey.value.frameloop[a].push(m),m=0,s.clear(),c=!1,d&&(d=!1,g.process(v))}};return g}const Nw=40;function j0(n,a){let s=!1,l=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>s=!0,f=po.reduce((D,L)=>(D[L]=Rw(d,a?L:void 0),D),{}),{setup:h,read:m,resolveKeyframes:p,preUpdate:g,update:v,preRender:b,render:T,postRender:M}=f,A=()=>{const D=$n.useManualTiming?c.timestamp:performance.now();s=!1,$n.useManualTiming||(c.delta=l?1e3/60:Math.max(Math.min(D-c.timestamp,Nw),1)),c.timestamp=D,c.isProcessing=!0,h.process(c),m.process(c),p.process(c),g.process(c),v.process(c),b.process(c),T.process(c),M.process(c),c.isProcessing=!1,s&&a&&(l=!1,n(A))},E=()=>{s=!0,l=!0,c.isProcessing||n(A)};return{schedule:po.reduce((D,L)=>{const j=f[L];return D[L]=($,et=!1,q=!1)=>(s||E(),j.schedule($,et,q)),D},{}),cancel:D=>{for(let L=0;L<po.length;L++)f[po[L]].cancel(D)},state:c,steps:f}}const{schedule:Ft,cancel:Ra,state:be,steps:Tf}=j0(typeof requestAnimationFrame!="undefined"?requestAnimationFrame:ln,!0);let Ro;function Dw(){Ro=void 0}const Le={now:()=>(Ro===void 0&&Le.set(be.isProcessing||$n.useManualTiming?be.timestamp:performance.now()),Ro),set:n=>{Ro=n,queueMicrotask(Dw)}},_0=n=>a=>typeof a=="string"&&a.startsWith(n),Ud=_0("--"),Ow=_0("var(--"),Hd=n=>Ow(n)?jw.test(n.split("/*")[0].trim()):!1,jw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ms={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Vr=Z(N({},ms),{transform:n=>In(0,1,n)}),go=Z(N({},ms),{default:1}),Rr=n=>Math.round(n*1e5)/1e5,Pd=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function _w(n){return n==null}const Vw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Gd=(n,a)=>s=>!!(typeof s=="string"&&Vw.test(s)&&s.startsWith(n)||a&&!_w(s)&&Object.prototype.hasOwnProperty.call(s,a)),V0=(n,a,s)=>l=>{if(typeof l!="string")return l;const[c,d,f,h]=l.match(Pd);return{[n]:parseFloat(c),[a]:parseFloat(d),[s]:parseFloat(f),alpha:h!==void 0?parseFloat(h):1}},zw=n=>In(0,255,n),wf=Z(N({},ms),{transform:n=>Math.round(zw(n))}),si={test:Gd("rgb","red"),parse:V0("red","green","blue"),transform:({red:n,green:a,blue:s,alpha:l=1})=>"rgba("+wf.transform(n)+", "+wf.transform(a)+", "+wf.transform(s)+", "+Rr(Vr.transform(l))+")"};function Lw(n){let a="",s="",l="",c="";return n.length>5?(a=n.substring(1,3),s=n.substring(3,5),l=n.substring(5,7),c=n.substring(7,9)):(a=n.substring(1,2),s=n.substring(2,3),l=n.substring(3,4),c=n.substring(4,5),a+=a,s+=s,l+=l,c+=c),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(l,16),alpha:c?parseInt(c,16)/255:1}}const Ff={test:Gd("#"),parse:Lw,transform:si.transform},Xr=n=>({test:a=>typeof a=="string"&&a.endsWith(n)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${n}`}),Ma=Xr("deg"),Nn=Xr("%"),xt=Xr("px"),Bw=Xr("vh"),kw=Xr("vw"),My=Z(N({},Nn),{parse:n=>Nn.parse(n)/100,transform:n=>Nn.transform(n*100)}),is={test:Gd("hsl","hue"),parse:V0("hue","saturation","lightness"),transform:({hue:n,saturation:a,lightness:s,alpha:l=1})=>"hsla("+Math.round(n)+", "+Nn.transform(Rr(a))+", "+Nn.transform(Rr(s))+", "+Rr(Vr.transform(l))+")"},Ae={test:n=>si.test(n)||Ff.test(n)||is.test(n),parse:n=>si.test(n)?si.parse(n):is.test(n)?is.parse(n):Ff.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?si.transform(n):is.transform(n)},Uw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Hw(n){var a,s;return isNaN(n)&&typeof n=="string"&&(((a=n.match(Pd))==null?void 0:a.length)||0)+(((s=n.match(Uw))==null?void 0:s.length)||0)>0}const z0="number",L0="color",Pw="var",Gw="var(",Cy="${}",qw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function zr(n){const a=n.toString(),s=[],l={color:[],number:[],var:[]},c=[];let d=0;const h=a.replace(qw,m=>(Ae.test(m)?(l.color.push(d),c.push(L0),s.push(Ae.parse(m))):m.startsWith(Gw)?(l.var.push(d),c.push(Pw),s.push(m)):(l.number.push(d),c.push(z0),s.push(parseFloat(m))),++d,Cy)).split(Cy);return{values:s,split:h,indexes:l,types:c}}function B0(n){return zr(n).values}function k0(n){const{split:a,types:s}=zr(n),l=a.length;return c=>{let d="";for(let f=0;f<l;f++)if(d+=a[f],c[f]!==void 0){const h=s[f];h===z0?d+=Rr(c[f]):h===L0?d+=Ae.transform(c[f]):d+=c[f]}return d}}const Yw=n=>typeof n=="number"?0:n;function Xw(n){const a=B0(n);return k0(n)(a.map(Yw))}const Na={test:Hw,parse:B0,createTransformer:k0,getAnimatableNone:Xw};function Af(n,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(a-n)*6*s:s<1/2?a:s<2/3?n+(a-n)*(2/3-s)*6:n}function Kw({hue:n,saturation:a,lightness:s,alpha:l}){n/=360,a/=100,s/=100;let c=0,d=0,f=0;if(!a)c=d=f=s;else{const h=s<.5?s*(1+a):s+a-s*a,m=2*s-h;c=Af(m,h,n+1/3),d=Af(m,h,n),f=Af(m,h,n-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:l}}function zo(n,a){return s=>s>0?a:n}const Qt=(n,a,s)=>n+(a-n)*s,Ef=(n,a,s)=>{const l=n*n,c=s*(a*a-l)+l;return c<0?0:Math.sqrt(c)},Zw=[Ff,si,is],Qw=n=>Zw.find(a=>a.test(n));function Ry(n){const a=Qw(n);if(!a)return!1;let s=a.parse(n);return a===is&&(s=Kw(s)),s}const Ny=(n,a)=>{const s=Ry(n),l=Ry(a);if(!s||!l)return zo(n,a);const c=N({},s);return d=>(c.red=Ef(s.red,l.red,d),c.green=Ef(s.green,l.green,d),c.blue=Ef(s.blue,l.blue,d),c.alpha=Qt(s.alpha,l.alpha,d),si.transform(c))},If=new Set(["none","hidden"]);function Fw(n,a){return If.has(n)?s=>s<=0?n:a:s=>s>=1?a:n}function Iw(n,a){return s=>Qt(n,a,s)}function qd(n){return typeof n=="number"?Iw:typeof n=="string"?Hd(n)?zo:Ae.test(n)?Ny:Jw:Array.isArray(n)?U0:typeof n=="object"?Ae.test(n)?Ny:$w:zo}function U0(n,a){const s=[...n],l=s.length,c=n.map((d,f)=>qd(d)(d,a[f]));return d=>{for(let f=0;f<l;f++)s[f]=c[f](d);return s}}function $w(n,a){const s=N(N({},n),a),l={};for(const c in s)n[c]!==void 0&&a[c]!==void 0&&(l[c]=qd(n[c])(n[c],a[c]));return c=>{for(const d in l)s[d]=l[d](c);return s}}function Ww(n,a){var c;const s=[],l={color:0,var:0,number:0};for(let d=0;d<a.values.length;d++){const f=a.types[d],h=n.indexes[f][l[f]],m=(c=n.values[h])!=null?c:0;s[d]=m,l[f]++}return s}const Jw=(n,a)=>{const s=Na.createTransformer(a),l=zr(n),c=zr(a);return l.indexes.var.length===c.indexes.var.length&&l.indexes.color.length===c.indexes.color.length&&l.indexes.number.length>=c.indexes.number.length?If.has(n)&&!c.values.length||If.has(a)&&!l.values.length?Fw(n,a):qr(U0(Ww(l,c),c.values),s):zo(n,a)};function H0(n,a,s){return typeof n=="number"&&typeof a=="number"&&typeof s=="number"?Qt(n,a,s):qd(n)(n,a)}const tA=n=>{const a=({timestamp:s})=>n(s);return{start:(s=!0)=>Ft.update(a,s),stop:()=>Ra(a),now:()=>be.isProcessing?be.timestamp:Le.now()}},P0=(n,a,s=10)=>{let l="";const c=Math.max(Math.round(a/s),2);for(let d=0;d<c;d++)l+=n(d/(c-1))+", ";return`linear(${l.substring(0,l.length-2)})`},Lo=2e4;function Yd(n){let a=0;const s=50;let l=n.next(a);for(;!l.done&&a<Lo;)a+=s,l=n.next(a);return a>=Lo?1/0:a}function eA(n,a=100,s){const l=s(Z(N({},n),{keyframes:[0,a]})),c=Math.min(Yd(l),Lo);return{type:"keyframes",ease:d=>l.next(c*d).value/a,duration:Rn(c)}}const nA=5;function G0(n,a,s){const l=Math.max(a-nA,0);return S0(s-n(l),a-l)}const Wt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Dy=.001;function aA({duration:n=Wt.duration,bounce:a=Wt.bounce,velocity:s=Wt.velocity,mass:l=Wt.mass}){let c,d,f=1-a;f=In(Wt.minDamping,Wt.maxDamping,f),n=In(Wt.minDuration,Wt.maxDuration,Rn(n)),f<1?(c=p=>{const g=p*f,v=g*n,b=g-s,T=$f(p,f),M=Math.exp(-v);return Dy-b/T*M},d=p=>{const v=p*f*n,b=v*s+s,T=Math.pow(f,2)*Math.pow(p,2)*n,M=Math.exp(-v),A=$f(Math.pow(p,2),f);return(-c(p)+Dy>0?-1:1)*((b-T)*M)/A}):(c=p=>{const g=Math.exp(-p*n),v=(p-s)*n+1;return-.001+g*v},d=p=>{const g=Math.exp(-p*n),v=(s-p)*(n*n);return g*v});const h=5/n,m=sA(c,d,h);if(n=Cn(n),isNaN(m))return{stiffness:Wt.stiffness,damping:Wt.damping,duration:n};{const p=Math.pow(m,2)*l;return{stiffness:p,damping:f*2*Math.sqrt(l*p),duration:n}}}const iA=12;function sA(n,a,s){let l=s;for(let c=1;c<iA;c++)l=l-n(l)/a(l);return l}function $f(n,a){return n*Math.sqrt(1-a*a)}const rA=["duration","bounce"],lA=["stiffness","damping","mass"];function Oy(n,a){return a.some(s=>n[s]!==void 0)}function oA(n){let a=N({velocity:Wt.velocity,stiffness:Wt.stiffness,damping:Wt.damping,mass:Wt.mass,isResolvedFromDuration:!1},n);if(!Oy(n,lA)&&Oy(n,rA))if(n.visualDuration){const s=n.visualDuration,l=2*Math.PI/(s*1.2),c=l*l,d=2*In(.05,1,1-(n.bounce||0))*Math.sqrt(c);a=Z(N({},a),{mass:Wt.mass,stiffness:c,damping:d})}else{const s=aA(n);a=Z(N(N({},a),s),{mass:Wt.mass}),a.isResolvedFromDuration=!0}return a}function Bo(n=Wt.visualDuration,a=Wt.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:a}:n;let{restSpeed:l,restDelta:c}=s;const d=s.keyframes[0],f=s.keyframes[s.keyframes.length-1],h={done:!1,value:d},{stiffness:m,damping:p,mass:g,duration:v,velocity:b,isResolvedFromDuration:T}=oA(Z(N({},s),{velocity:-Rn(s.velocity||0)})),M=b||0,A=p/(2*Math.sqrt(m*g)),E=f-d,O=Rn(Math.sqrt(m/g)),V=Math.abs(E)<5;l||(l=V?Wt.restSpeed.granular:Wt.restSpeed.default),c||(c=V?Wt.restDelta.granular:Wt.restDelta.default);let D;if(A<1){const j=$f(O,A);D=$=>{const et=Math.exp(-A*O*$);return f-et*((M+A*O*E)/j*Math.sin(j*$)+E*Math.cos(j*$))}}else if(A===1)D=j=>f-Math.exp(-O*j)*(E+(M+O*E)*j);else{const j=O*Math.sqrt(A*A-1);D=$=>{const et=Math.exp(-A*O*$),q=Math.min(j*$,300);return f-et*((M+A*O*E)*Math.sinh(q)+j*E*Math.cosh(q))/j}}const L={calculatedDuration:T&&v||null,next:j=>{const $=D(j);if(T)h.done=j>=v;else{let et=j===0?M:0;A<1&&(et=j===0?Cn(M):G0(D,j,$));const q=Math.abs(et)<=l,W=Math.abs(f-$)<=c;h.done=q&&W}return h.value=h.done?f:$,h},toString:()=>{const j=Math.min(Yd(L),Lo),$=P0(et=>L.next(j*et).value,j,30);return j+"ms "+$},toTransition:()=>{}};return L}Bo.applyToOptions=n=>{const a=eA(n,100,Bo);return n.ease=a.ease,n.duration=Cn(a.duration),n.type="keyframes",n};function Wf({keyframes:n,velocity:a=0,power:s=.8,timeConstant:l=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:f,min:h,max:m,restDelta:p=.5,restSpeed:g}){const v=n[0],b={done:!1,value:v},T=q=>h!==void 0&&q<h||m!==void 0&&q>m,M=q=>h===void 0?m:m===void 0||Math.abs(h-q)<Math.abs(m-q)?h:m;let A=s*a;const E=v+A,O=f===void 0?E:f(E);O!==E&&(A=O-v);const V=q=>-A*Math.exp(-q/l),D=q=>O+V(q),L=q=>{const W=V(q),Y=D(q);b.done=Math.abs(W)<=p,b.value=b.done?O:Y};let j,$;const et=q=>{T(b.value)&&(j=q,$=Bo({keyframes:[b.value,M(b.value)],velocity:G0(D,q,b.value),damping:c,stiffness:d,restDelta:p,restSpeed:g}))};return et(0),{calculatedDuration:null,next:q=>{let W=!1;return!$&&j===void 0&&(W=!0,L(q),et(q)),j!==void 0&&q>=j?$.next(q-j):(!W&&L(q),b)}}}function cA(n,a,s){const l=[],c=s||$n.mix||H0,d=n.length-1;for(let f=0;f<d;f++){let h=c(n[f],n[f+1]);if(a){const m=Array.isArray(a)?a[f]||ln:a;h=qr(m,h)}l.push(h)}return l}function uA(n,a,{clamp:s=!0,ease:l,mixer:c}={}){const d=n.length;if(Vd(d===a.length),d===1)return()=>a[0];if(d===2&&a[0]===a[1])return()=>a[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),a=[...a].reverse());const h=cA(a,l,c),m=h.length,p=g=>{if(f&&g<n[0])return a[0];let v=0;if(m>1)for(;v<n.length-2&&!(g<n[v+1]);v++);const b=_r(n[v],n[v+1],g);return h[v](b)};return s?g=>p(In(n[0],n[d-1],g)):p}function fA(n,a){const s=n[n.length-1];for(let l=1;l<=a;l++){const c=_r(0,a,l);n.push(Qt(s,1,c))}}function dA(n){const a=[0];return fA(a,n.length-1),a}function hA(n,a){return n.map(s=>s*a)}function mA(n,a){return n.map(()=>a||D0).splice(0,n.length-1)}function Nr({duration:n=300,keyframes:a,times:s,ease:l="easeInOut"}){const c=Ew(l)?l.map(Ay):Ay(l),d={done:!1,value:a[0]},f=hA(s&&s.length===a.length?s:dA(a),n),h=uA(f,a,{ease:Array.isArray(c)?c:mA(a,c)});return{calculatedDuration:n,next:m=>(d.value=h(m),d.done=m>=n,d)}}const pA=n=>n!==null;function Xd(n,{repeat:a,repeatType:s="loop"},l,c=1){const d=n.filter(pA),h=c<0||a&&s!=="loop"&&a%2===1?0:d.length-1;return!h||l===void 0?d[h]:l}const gA={decay:Wf,inertia:Wf,tween:Nr,keyframes:Nr,spring:Bo};function q0(n){typeof n.type=="string"&&(n.type=gA[n.type])}class Kd{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,s){return this.finished.then(a,s)}}const yA=n=>n/100;class Zd extends Kd{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var l,c;const{motionValue:s}=this.options;s&&s.updatedAt!==Le.now()&&this.tick(Le.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(c=(l=this.options).onStop)==null||c.call(l))},this.options=a,this.initAnimation(),this.play(),a.autoplay===!1&&this.pause()}initAnimation(){const{options:a}=this;q0(a);const{type:s=Nr,repeat:l=0,repeatDelay:c=0,repeatType:d,velocity:f=0}=a;let{keyframes:h}=a;const m=s||Nr;m!==Nr&&typeof h[0]!="number"&&(this.mixKeyframes=qr(yA,H0(h[0],h[1])),h=[0,100]);const p=m(Z(N({},a),{keyframes:h}));d==="mirror"&&(this.mirroredGenerator=m(Z(N({},a),{keyframes:[...h].reverse(),velocity:-f}))),p.calculatedDuration===null&&(p.calculatedDuration=Yd(p));const{calculatedDuration:g}=p;this.calculatedDuration=g,this.resolvedDuration=g+c,this.totalDuration=this.resolvedDuration*(l+1)-c,this.generator=p}updateTime(a){const s=Math.round(a-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(a,s=!1){const{generator:l,totalDuration:c,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:m}=this;if(this.startTime===null)return l.next(0);const{delay:p=0,keyframes:g,repeat:v,repeatType:b,repeatDelay:T,type:M,onUpdate:A,finalKeyframe:E}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-c/this.speed,this.startTime)),s?this.currentTime=a:this.updateTime(a);const O=this.currentTime-p*(this.playbackSpeed>=0?1:-1),V=this.playbackSpeed>=0?O<0:O>c;this.currentTime=Math.max(O,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let D=this.currentTime,L=l;if(v){const q=Math.min(this.currentTime,c)/h;let W=Math.floor(q),Y=q%1;!Y&&q>=1&&(Y=1),Y===1&&W--,W=Math.min(W,v+1),!!(W%2)&&(b==="reverse"?(Y=1-Y,T&&(Y-=T/h)):b==="mirror"&&(L=f)),D=In(0,1,Y)*h}const j=V?{done:!1,value:g[0]}:L.next(D);d&&(j.value=d(j.value));let{done:$}=j;!V&&m!==null&&($=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const et=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&$);return et&&M!==Wf&&(j.value=Xd(g,this.options,E,this.speed)),A&&A(j.value),et&&this.finish(),j}then(a,s){return this.finished.then(a,s)}get duration(){return Rn(this.calculatedDuration)}get time(){return Rn(this.currentTime)}set time(a){var s;a=Cn(a),this.currentTime=a,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(Le.now());const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Rn(this.currentTime))}play(){var c,d;if(this.isStopped)return;const{driver:a=tA,startTime:s}=this.options;this.driver||(this.driver=a(f=>this.tick(f))),(d=(c=this.options).onPlay)==null||d.call(c);const l=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=l):this.holdTime!==null?this.startTime=l-this.holdTime:this.startTime||(this.startTime=s!=null?s:l),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Le.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var a,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(a=this.options).onComplete)==null||s.call(a)}cancel(){var a,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(a=this.options).onCancel)==null||s.call(a)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),a.observe(this)}}function vA(n){var a;for(let s=1;s<n.length;s++)(a=n[s])!=null||(n[s]=n[s-1])}const ri=n=>n*180/Math.PI,Jf=n=>{const a=ri(Math.atan2(n[1],n[0]));return td(a)},xA={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:Jf,rotateZ:Jf,skewX:n=>ri(Math.atan(n[1])),skewY:n=>ri(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},td=n=>(n=n%360,n<0&&(n+=360),n),jy=Jf,_y=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),Vy=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),bA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:_y,scaleY:Vy,scale:n=>(_y(n)+Vy(n))/2,rotateX:n=>td(ri(Math.atan2(n[6],n[5]))),rotateY:n=>td(ri(Math.atan2(-n[2],n[0]))),rotateZ:jy,rotate:jy,skewX:n=>ri(Math.atan(n[4])),skewY:n=>ri(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function ed(n){return n.includes("scale")?1:0}function nd(n,a){if(!n||n==="none")return ed(a);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let l,c;if(s)l=bA,c=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);l=xA,c=h}if(!c)return ed(a);const d=l[a],f=c[1].split(",").map(TA);return typeof d=="function"?d(f):f[d]}const SA=(n,a)=>{const{transform:s="none"}=getComputedStyle(n);return nd(s,a)};function TA(n){return parseFloat(n.trim())}const ps=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gs=new Set(ps),zy=n=>n===ms||n===xt,wA=new Set(["x","y","z"]),AA=ps.filter(n=>!wA.has(n));function EA(n){const a=[];return AA.forEach(s=>{const l=n.getValue(s);l!==void 0&&(a.push([s,l.get()]),l.set(s.startsWith("scale")?1:0))}),a}const li={width:({x:n},{paddingLeft:a="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),height:({y:n},{paddingTop:a="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),top:(n,{top:a})=>parseFloat(a),left:(n,{left:a})=>parseFloat(a),bottom:({y:n},{top:a})=>parseFloat(a)+(n.max-n.min),right:({x:n},{left:a})=>parseFloat(a)+(n.max-n.min),x:(n,{transform:a})=>nd(a,"x"),y:(n,{transform:a})=>nd(a,"y")};li.translateX=li.x;li.translateY=li.y;const oi=new Set;let ad=!1,id=!1,sd=!1;function Y0(){if(id){const n=Array.from(oi).filter(l=>l.needsMeasurement),a=new Set(n.map(l=>l.element)),s=new Map;a.forEach(l=>{const c=EA(l);c.length&&(s.set(l,c),l.render())}),n.forEach(l=>l.measureInitialState()),a.forEach(l=>{l.render();const c=s.get(l);c&&c.forEach(([d,f])=>{var h;(h=l.getValue(d))==null||h.set(f)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}id=!1,ad=!1,oi.forEach(n=>n.complete(sd)),oi.clear()}function X0(){oi.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(id=!0)})}function MA(){sd=!0,X0(),Y0(),sd=!1}class Qd{constructor(a,s,l,c,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=l,this.motionValue=c,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(oi.add(this),ad||(ad=!0,Ft.read(X0),Ft.resolveKeyframes(Y0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:l,motionValue:c}=this;if(a[0]===null){const d=c==null?void 0:c.get(),f=a[a.length-1];if(d!==void 0)a[0]=d;else if(l&&s){const h=l.readValue(s,f);h!=null&&(a[0]=h)}a[0]===void 0&&(a[0]=f),c&&d===void 0&&c.set(a[0])}vA(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),oi.delete(this)}cancel(){this.state==="scheduled"&&(oi.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const CA=n=>n.startsWith("--");function RA(n,a,s){CA(a)?n.style.setProperty(a,s):n.style[a]=s}const NA=zd(()=>window.ScrollTimeline!==void 0),DA={};function OA(n,a){const s=zd(n);return()=>{var l;return(l=DA[a])!=null?l:s()}}const K0=OA(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(n){return!1}return!0},"linearEasing"),Mr=([n,a,s,l])=>`cubic-bezier(${n}, ${a}, ${s}, ${l})`,Ly={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Mr([0,.65,.55,1]),circOut:Mr([.55,0,1,.45]),backIn:Mr([.31,.01,.66,-.59]),backOut:Mr([.33,1.53,.69,.99])};function Z0(n,a){if(n)return typeof n=="function"?K0()?P0(n,a):"ease-out":O0(n)?Mr(n):Array.isArray(n)?n.map(s=>Z0(s,a)||Ly.easeOut):Ly[n]}function jA(n,a,s,{delay:l=0,duration:c=300,repeat:d=0,repeatType:f="loop",ease:h="easeOut",times:m}={},p=void 0){const g={[a]:s};m&&(g.offset=m);const v=Z0(h,c);Array.isArray(v)&&(g.easing=v);const b={delay:l,duration:c,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return p&&(b.pseudoElement=p),n.animate(g,b)}function Q0(n){return typeof n=="function"&&"applyToOptions"in n}function _A(s){var l=s,{type:n}=l,a=it(l,["type"]);var c,d;return Q0(n)&&K0()?n.applyToOptions(a):((c=a.duration)!=null||(a.duration=300),(d=a.ease)!=null||(a.ease="easeOut"),a)}class VA extends Kd{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;const{element:s,name:l,keyframes:c,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:h,onComplete:m}=a;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=a,Vd(typeof a.type!="string");const p=_A(a);this.animation=jA(s,l,c,p,d),p.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const g=Xd(c,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(g):RA(s,l,g),this.animation.cancel()}m==null||m(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var a,s;(s=(a=this.animation).finish)==null||s.call(a)}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:a}=this;a==="idle"||a==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var a,s;this.isPseudoElement||(s=(a=this.animation).commitStyles)==null||s.call(a)}get duration(){var s,l;const a=((l=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:l.call(s).duration)||0;return Rn(Number(a))}get time(){return Rn(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=Cn(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:s}){var l;return this.allowFlatten&&((l=this.animation.effect)==null||l.updateTiming({easing:"linear"})),this.animation.onfinish=null,a&&NA()?(this.animation.timeline=a,ln):s(this)}}const F0={anticipate:C0,backInOut:M0,circInOut:N0};function zA(n){return n in F0}function LA(n){typeof n.ease=="string"&&zA(n.ease)&&(n.ease=F0[n.ease])}const By=10;class BA extends VA{constructor(a){LA(a),q0(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){var g;const p=this.options,{motionValue:s,onUpdate:l,onComplete:c,element:d}=p,f=it(p,["motionValue","onUpdate","onComplete","element"]);if(!s)return;if(a!==void 0){s.set(a);return}const h=new Zd(Z(N({},f),{autoplay:!1})),m=Cn((g=this.finishedTime)!=null?g:this.time);s.setWithVelocity(h.sample(m-By).value,h.sample(m).value,By),h.stop()}}const ky=(n,a)=>a==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Na.test(n)||n==="0")&&!n.startsWith("url("));function kA(n){const a=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==a)return!0}function UA(n,a,s,l){const c=n[0];if(c===null)return!1;if(a==="display"||a==="visibility")return!0;const d=n[n.length-1],f=ky(c,a),h=ky(d,a);return!f||!h?!1:kA(n)||(s==="spring"||Q0(s))&&l}function I0(n){return x0(n)&&"offsetHeight"in n}const HA=new Set(["opacity","clipPath","filter","transform"]),PA=zd(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function GA(n){var p;const{motionValue:a,name:s,repeatDelay:l,repeatType:c,damping:d,type:f}=n;if(!I0((p=a==null?void 0:a.owner)==null?void 0:p.current))return!1;const{onUpdate:h,transformTemplate:m}=a.owner.getProps();return PA()&&s&&HA.has(s)&&(s!=="transform"||!m)&&!h&&!l&&c!=="mirror"&&d!==0&&f!=="inertia"}const qA=40;class YA extends Kd{constructor(b){var T=b,{autoplay:a=!0,delay:s=0,type:l="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:f="loop",keyframes:h,name:m,motionValue:p,element:g}=T,v=it(T,["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"]);var E;super(),this.stop=()=>{var O,V;this._animation&&(this._animation.stop(),(O=this.stopTimeline)==null||O.call(this)),(V=this.keyframeResolver)==null||V.cancel()},this.createdAt=Le.now();const M=N({autoplay:a,delay:s,type:l,repeat:c,repeatDelay:d,repeatType:f,name:m,motionValue:p,element:g},v),A=(g==null?void 0:g.KeyframeResolver)||Qd;this.keyframeResolver=new A(h,(O,V,D)=>this.onKeyframesResolved(O,V,M,!D),m,p,g),(E=this.keyframeResolver)==null||E.scheduleResolve()}onKeyframesResolved(a,s,l,c){this.keyframeResolver=void 0;const{name:d,type:f,velocity:h,delay:m,isHandoff:p,onUpdate:g}=l;this.resolvedAt=Le.now(),UA(a,d,f,h)||(($n.instantAnimations||!m)&&(g==null||g(Xd(a,l,s))),a[0]=a[a.length-1],l.duration=0,l.repeat=0);const v=c?this.resolvedAt?this.resolvedAt-this.createdAt>qA?this.resolvedAt:this.createdAt:this.createdAt:void 0,b=Z(N({startTime:v,finalKeyframe:s},l),{keyframes:a}),T=!p&&GA(b)?new BA(Z(N({},b),{element:b.motionValue.owner.current})):new Zd(b);T.finished.then(()=>this.notifyFinished()).catch(ln),this.pendingTimeline&&(this.stopTimeline=T.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=T}get finished(){return this._animation?this.animation.finished:this._finished}then(a,s){return this.finished.finally(a).then(()=>{})}get animation(){var a;return this._animation||((a=this.keyframeResolver)==null||a.resume(),MA()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var a;this._animation&&this.animation.cancel(),(a=this.keyframeResolver)==null||a.cancel()}}const XA=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function KA(n){const a=XA.exec(n);if(!a)return[,];const[,s,l,c]=a;return[`--${s!=null?s:l}`,c]}function $0(n,a,s=1){const[l,c]=KA(n);if(!l)return;const d=window.getComputedStyle(a).getPropertyValue(l);if(d){const f=d.trim();return v0(f)?parseFloat(f):f}return Hd(c)?$0(c,a,s+1):c}function Fd(n,a){var s,l;return(l=(s=n==null?void 0:n[a])!=null?s:n==null?void 0:n.default)!=null?l:n}const W0=new Set(["width","height","top","left","right","bottom",...ps]),ZA={test:n=>n==="auto",parse:n=>n},J0=n=>a=>a.test(n),tx=[ms,xt,Nn,Ma,kw,Bw,ZA],Uy=n=>tx.find(J0(n));function QA(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||b0(n):!0}const FA=new Set(["brightness","contrast","saturate","opacity"]);function IA(n){const[a,s]=n.slice(0,-1).split("(");if(a==="drop-shadow")return n;const[l]=s.match(Pd)||[];if(!l)return n;const c=s.replace(l,"");let d=FA.has(a)?1:0;return l!==s&&(d*=100),a+"("+d+c+")"}const $A=/\b([a-z-]*)\(.*?\)/gu,rd=Z(N({},Na),{getAnimatableNone:n=>{const a=n.match($A);return a?a.map(IA).join(" "):n}}),Hy=Z(N({},ms),{transform:Math.round}),WA={rotate:Ma,rotateX:Ma,rotateY:Ma,rotateZ:Ma,scale:go,scaleX:go,scaleY:go,scaleZ:go,skew:Ma,skewX:Ma,skewY:Ma,distance:xt,translateX:xt,translateY:xt,translateZ:xt,x:xt,y:xt,z:xt,perspective:xt,transformPerspective:xt,opacity:Vr,originX:My,originY:My,originZ:xt},Id=Z(N({borderWidth:xt,borderTopWidth:xt,borderRightWidth:xt,borderBottomWidth:xt,borderLeftWidth:xt,borderRadius:xt,radius:xt,borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomRightRadius:xt,borderBottomLeftRadius:xt,width:xt,maxWidth:xt,height:xt,maxHeight:xt,top:xt,right:xt,bottom:xt,left:xt,padding:xt,paddingTop:xt,paddingRight:xt,paddingBottom:xt,paddingLeft:xt,margin:xt,marginTop:xt,marginRight:xt,marginBottom:xt,marginLeft:xt,backgroundPositionX:xt,backgroundPositionY:xt},WA),{zIndex:Hy,fillOpacity:Vr,strokeOpacity:Vr,numOctaves:Hy}),JA=Z(N({},Id),{color:Ae,backgroundColor:Ae,outlineColor:Ae,fill:Ae,stroke:Ae,borderColor:Ae,borderTopColor:Ae,borderRightColor:Ae,borderBottomColor:Ae,borderLeftColor:Ae,filter:rd,WebkitFilter:rd}),ex=n=>JA[n];function nx(n,a){let s=ex(n);return s!==rd&&(s=Na),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const tE=new Set(["auto","none","0"]);function eE(n,a,s){let l=0,c;for(;l<n.length&&!c;){const d=n[l];typeof d=="string"&&!tE.has(d)&&zr(d).values.length&&(c=n[l]),l++}if(c&&s)for(const d of a)n[d]=nx(s,c)}class nE extends Qd{constructor(a,s,l,c,d){super(a,s,l,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:l}=this;if(!s||!s.current)return;super.readKeyframes();for(let m=0;m<a.length;m++){let p=a[m];if(typeof p=="string"&&(p=p.trim(),Hd(p))){const g=$0(p,s.current);g!==void 0&&(a[m]=g),m===a.length-1&&(this.finalKeyframe=p)}}if(this.resolveNoneKeyframes(),!W0.has(l)||a.length!==2)return;const[c,d]=a,f=Uy(c),h=Uy(d);if(f!==h)if(zy(f)&&zy(h))for(let m=0;m<a.length;m++){const p=a[m];typeof p=="string"&&(a[m]=parseFloat(p))}else li[l]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,l=[];for(let c=0;c<a.length;c++)(a[c]===null||QA(a[c]))&&l.push(c);l.length&&eE(a,l,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:l}=this;if(!a||!a.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=li[l](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const c=s[s.length-1];c!==void 0&&a.getValue(l,c).jump(c,!1)}measureEndState(){var h;const{element:a,name:s,unresolvedKeyframes:l}=this;if(!a||!a.current)return;const c=a.getValue(s);c&&c.jump(this.measuredOrigin,!1);const d=l.length-1,f=l[d];l[d]=li[s](a.measureViewportBox(),window.getComputedStyle(a.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([m,p])=>{a.getValue(m).set(p)}),this.resolveNoneKeyframes()}}function aE(n,a,s){var l;if(n instanceof EventTarget)return[n];if(typeof n=="string"){let c=document;const d=(l=s==null?void 0:s[n])!=null?l:c.querySelectorAll(n);return d?Array.from(d):[]}return Array.from(n)}const ax=(n,a)=>a&&typeof n=="number"?a.transform(n):n,Py=30,iE=n=>!isNaN(parseFloat(n));class sE{constructor(a,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,c=!0)=>{var f,h;const d=Le.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&((f=this.events.change)==null||f.notify(this.current),this.dependents))for(const m of this.dependents)m.dirty();c&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=Le.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=iE(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new Ld);const l=this.events[a].add(s);return a==="change"?()=>{l(),Ft.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,l){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-l}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var a;(a=this.events.change)==null||a.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=Le.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>Py)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Py);return S0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var a,s;(a=this.dependents)==null||a.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function fs(n,a){return new sE(n,a)}const{schedule:$d}=j0(queueMicrotask,!1),yn={x:!1,y:!1};function ix(){return yn.x||yn.y}function rE(n){return n==="x"||n==="y"?yn[n]?null:(yn[n]=!0,()=>{yn[n]=!1}):yn.x||yn.y?null:(yn.x=yn.y=!0,()=>{yn.x=yn.y=!1})}function sx(n,a){const s=aE(n),l=new AbortController,c=Z(N({passive:!0},a),{signal:l.signal});return[s,c,()=>l.abort()]}function Gy(n){return!(n.pointerType==="touch"||ix())}function lE(n,a,s={}){const[l,c,d]=sx(n,s),f=h=>{if(!Gy(h))return;const{target:m}=h,p=a(m,h);if(typeof p!="function"||!m)return;const g=v=>{Gy(v)&&(p(v),m.removeEventListener("pointerleave",g))};m.addEventListener("pointerleave",g,c)};return l.forEach(h=>{h.addEventListener("pointerenter",f,c)}),d}const rx=(n,a)=>a?n===a?!0:rx(n,a.parentElement):!1,Wd=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,oE=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function cE(n){return oE.has(n.tagName)||n.tabIndex!==-1}const No=new WeakSet;function qy(n){return a=>{a.key==="Enter"&&n(a)}}function Mf(n,a){n.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const uE=(n,a)=>{const s=n.currentTarget;if(!s)return;const l=qy(()=>{if(No.has(s))return;Mf(s,"down");const c=qy(()=>{Mf(s,"up")}),d=()=>Mf(s,"cancel");s.addEventListener("keyup",c,a),s.addEventListener("blur",d,a)});s.addEventListener("keydown",l,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",l),a)};function Yy(n){return Wd(n)&&!ix()}function fE(n,a,s={}){const[l,c,d]=sx(n,s),f=h=>{const m=h.currentTarget;if(!Yy(h))return;No.add(m);const p=a(m,h),g=(T,M)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",b),No.has(m)&&No.delete(m),Yy(T)&&typeof p=="function"&&p(T,{success:M})},v=T=>{g(T,m===window||m===document||s.useGlobalTarget||rx(m,T.target))},b=T=>{g(T,!1)};window.addEventListener("pointerup",v,c),window.addEventListener("pointercancel",b,c)};return l.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",f,c),I0(h)&&(h.addEventListener("focus",p=>uE(p,c)),!cE(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),d}function lx(n){return x0(n)&&"ownerSVGElement"in n}function dE(n){return lx(n)&&n.tagName==="svg"}const Ee=n=>!!(n&&n.getVelocity),hE=[...tx,Ae,Na],mE=n=>hE.find(J0(n)),ox=w.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function pE(n=!0){const a=w.useContext(Od);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:l,register:c}=a,d=w.useId();w.useEffect(()=>{if(n)return c(d)},[n]);const f=w.useCallback(()=>n&&l&&l(d),[d,l,n]);return!s&&l?[!1,f]:[!0]}const cx=w.createContext({strict:!1}),Xy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ds={};for(const n in Xy)ds[n]={isEnabled:a=>Xy[n].some(s=>!!a[s])};function gE(n){for(const a in n)ds[a]=N(N({},ds[a]),n[a])}const yE=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ko(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||yE.has(n)}let ux=n=>!ko(n);function vE(n){n&&(ux=a=>a.startsWith("on")?!ko(a):n(a))}try{vE(require("@emotion/is-prop-valid").default)}catch(n){}function xE(n,a,s){const l={};for(const c in n)c==="values"&&typeof n.values=="object"||(ux(c)||s===!0&&ko(c)||!a&&!ko(c)||n.draggable&&c.startsWith("onDrag"))&&(l[c]=n[c]);return l}function bE(n){if(typeof Proxy=="undefined")return n;const a=new Map,s=(...l)=>n(...l);return new Proxy(s,{get:(l,c)=>c==="create"?n:(a.has(c)||a.set(c,n(c)),a.get(c))})}const Ko=w.createContext({});function Zo(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function Lr(n){return typeof n=="string"||Array.isArray(n)}const Jd=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],th=["initial",...Jd];function Qo(n){return Zo(n.animate)||th.some(a=>Lr(n[a]))}function fx(n){return!!(Qo(n)||n.variants)}function SE(n,a){if(Qo(n)){const{initial:s,animate:l}=n;return{initial:s===!1||Lr(s)?s:void 0,animate:Lr(l)?l:void 0}}return n.inherit!==!1?a:{}}function TE(n){const{initial:a,animate:s}=SE(n,w.useContext(Ko));return w.useMemo(()=>({initial:a,animate:s}),[Ky(a),Ky(s)])}function Ky(n){return Array.isArray(n)?n.join(" "):n}const wE=Symbol.for("motionComponentSymbol");function ss(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function AE(n,a,s){return w.useCallback(l=>{l&&n.onMount&&n.onMount(l),a&&(l?a.mount(l):a.unmount()),s&&(typeof s=="function"?s(l):ss(s)&&(s.current=l))},[a])}const eh=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),EE="framerAppearId",dx="data-"+eh(EE),hx=w.createContext({});function ME(n,a,s,l,c){var A,E;const{visualElement:d}=w.useContext(Ko),f=w.useContext(cx),h=w.useContext(Od),m=w.useContext(ox).reducedMotion,p=w.useRef(null);l=l||f.renderer,!p.current&&l&&(p.current=l(n,{visualState:a,parent:d,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:m}));const g=p.current,v=w.useContext(hx);g&&!g.projection&&c&&(g.type==="html"||g.type==="svg")&&CE(p.current,s,c,v);const b=w.useRef(!1);w.useInsertionEffect(()=>{g&&b.current&&g.update(s,h)});const T=s[dx],M=w.useRef(!!T&&!((A=window.MotionHandoffIsComplete)!=null&&A.call(window,T))&&((E=window.MotionHasOptimisedAnimation)==null?void 0:E.call(window,T)));return vw(()=>{g&&(b.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),$d.render(g.render),M.current&&g.animationState&&g.animationState.animateChanges())}),w.useEffect(()=>{g&&(!M.current&&g.animationState&&g.animationState.animateChanges(),M.current&&(queueMicrotask(()=>{var O;(O=window.MotionHandoffMarkAsComplete)==null||O.call(window,T)}),M.current=!1))}),g}function CE(n,a,s,l){const{layoutId:c,layout:d,drag:f,dragConstraints:h,layoutScroll:m,layoutRoot:p,layoutCrossfade:g}=a;n.projection=new s(n.latestValues,a["data-framer-portal-id"]?void 0:mx(n.parent)),n.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!f||h&&ss(h),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,crossfade:g,layoutScroll:m,layoutRoot:p})}function mx(n){if(n)return n.options.allowProjection!==!1?n.projection:mx(n.parent)}function RE({preloadedFeatures:n,createVisualElement:a,useRender:s,useVisualState:l,Component:c}){var h,m;n&&gE(n);function d(p,g){let v;const b=Z(N(N({},w.useContext(ox)),p),{layoutId:NE(p)}),{isStatic:T}=b,M=TE(p),A=l(p,T);if(!T&&Dd){DE();const E=OE(b);v=E.MeasureLayout,M.visualElement=ME(c,A,b,a,E.ProjectionNode)}return x.jsxs(Ko.Provider,{value:M,children:[v&&M.visualElement?x.jsx(v,N({visualElement:M.visualElement},b)):null,s(c,p,AE(A,M.visualElement,g),A,T,M.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${(m=(h=c.displayName)!=null?h:c.name)!=null?m:""})`}`;const f=w.forwardRef(d);return f[wE]=c,f}function NE({layoutId:n}){const a=w.useContext(y0).id;return a&&n!==void 0?a+"-"+n:n}function DE(n,a){w.useContext(cx).strict}function OE(n){const{drag:a,layout:s}=ds;if(!a&&!s)return{};const l=N(N({},a),s);return{MeasureLayout:a!=null&&a.isEnabled(n)||s!=null&&s.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const Br={};function jE(n){for(const a in n)Br[a]=n[a],Ud(a)&&(Br[a].isCSSVariable=!0)}function px(n,{layout:a,layoutId:s}){return gs.has(n)||n.startsWith("origin")||(a||s!==void 0)&&(!!Br[n]||n==="opacity")}const _E={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},VE=ps.length;function zE(n,a,s){let l="",c=!0;for(let d=0;d<VE;d++){const f=ps[d],h=n[f];if(h===void 0)continue;let m=!0;if(typeof h=="number"?m=h===(f.startsWith("scale")?1:0):m=parseFloat(h)===0,!m||s){const p=ax(h,Id[f]);if(!m){c=!1;const g=_E[f]||f;l+=`${g}(${p}) `}s&&(a[f]=p)}}return l=l.trim(),s?l=s(a,c?"":l):c&&(l="none"),l}function nh(n,a,s){const{style:l,vars:c,transformOrigin:d}=n;let f=!1,h=!1;for(const m in a){const p=a[m];if(gs.has(m)){f=!0;continue}else if(Ud(m)){c[m]=p;continue}else{const g=ax(p,Id[m]);m.startsWith("origin")?(h=!0,d[m]=g):l[m]=g}}if(a.transform||(f||s?l.transform=zE(a,n.transform,s):l.transform&&(l.transform="none")),h){const{originX:m="50%",originY:p="50%",originZ:g=0}=d;l.transformOrigin=`${m} ${p} ${g}`}}const ah=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function gx(n,a,s){for(const l in a)!Ee(a[l])&&!px(l,s)&&(n[l]=a[l])}function LE({transformTemplate:n},a){return w.useMemo(()=>{const s=ah();return nh(s,a,n),Object.assign({},s.vars,s.style)},[a])}function BE(n,a){const s=n.style||{},l={};return gx(l,s,n),Object.assign(l,LE(n,a)),l}function kE(n,a){const s={},l=BE(n,a);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=l,s}const UE={offset:"stroke-dashoffset",array:"stroke-dasharray"},HE={offset:"strokeDashoffset",array:"strokeDasharray"};function PE(n,a,s=1,l=0,c=!0){n.pathLength=1;const d=c?UE:HE;n[d.offset]=xt.transform(-l);const f=xt.transform(a),h=xt.transform(s);n[d.array]=`${f} ${h}`}function yx(n,v,m,p,g){var b=v,{attrX:a,attrY:s,attrScale:l,pathLength:c,pathSpacing:d=1,pathOffset:f=0}=b,h=it(b,["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"]);var A,E;if(nh(n,h,p),m){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:T,style:M}=n;T.transform&&(M.transform=T.transform,delete T.transform),(M.transform||T.transformOrigin)&&(M.transformOrigin=(A=T.transformOrigin)!=null?A:"50% 50%",delete T.transformOrigin),M.transform&&(M.transformBox=(E=g==null?void 0:g.transformBox)!=null?E:"fill-box",delete T.transformBox),a!==void 0&&(T.x=a),s!==void 0&&(T.y=s),l!==void 0&&(T.scale=l),c!==void 0&&PE(T,c,d,f,!1)}const vx=()=>Z(N({},ah()),{attrs:{}}),xx=n=>typeof n=="string"&&n.toLowerCase()==="svg";function GE(n,a,s,l){const c=w.useMemo(()=>{const d=vx();return yx(d,a,xx(l),n.transformTemplate,n.style),Z(N({},d.attrs),{style:N({},d.style)})},[a]);if(n.style){const d={};gx(d,n.style,n),c.style=N(N({},d),c.style)}return c}const qE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ih(n){return typeof n!="string"||n.includes("-")?!1:!!(qE.indexOf(n)>-1||/[A-Z]/u.test(n))}function YE(n=!1){return(s,l,c,{latestValues:d},f)=>{const m=(ih(s)?GE:kE)(l,d,f,s),p=xE(l,typeof s=="string",n),g=s!==w.Fragment?Z(N(N({},p),m),{ref:c}):{},{children:v}=l,b=w.useMemo(()=>Ee(v)?v.get():v,[v]);return w.createElement(s,Z(N({},g),{children:b}))}}function Zy(n){const a=[{},{}];return n==null||n.values.forEach((s,l)=>{a[0][l]=s.get(),a[1][l]=s.getVelocity()}),a}function sh(n,a,s,l){if(typeof a=="function"){const[c,d]=Zy(l);a=a(s!==void 0?s:n.custom,c,d)}if(typeof a=="string"&&(a=n.variants&&n.variants[a]),typeof a=="function"){const[c,d]=Zy(l);a=a(s!==void 0?s:n.custom,c,d)}return a}function Do(n){return Ee(n)?n.get():n}function XE({scrapeMotionValuesFromProps:n,createRenderState:a},s,l,c){return{latestValues:KE(s,l,c,n),renderState:a()}}const bx=n=>(a,s)=>{const l=w.useContext(Ko),c=w.useContext(Od),d=()=>XE(n,a,l,c);return s?d():yw(d)};function KE(n,a,s,l){const c={},d=l(n,{});for(const T in d)c[T]=Do(d[T]);let{initial:f,animate:h}=n;const m=Qo(n),p=fx(n);a&&p&&!m&&n.inherit!==!1&&(f===void 0&&(f=a.initial),h===void 0&&(h=a.animate));let g=s?s.initial===!1:!1;g=g||f===!1;const v=g?h:f;if(v&&typeof v!="boolean"&&!Zo(v)){const T=Array.isArray(v)?v:[v];for(let M=0;M<T.length;M++){const A=sh(n,T[M]);if(A){const b=A,{transitionEnd:E,transition:O}=b,V=it(b,["transitionEnd","transition"]);for(const D in V){let L=V[D];if(Array.isArray(L)){const j=g?L.length-1:0;L=L[j]}L!==null&&(c[D]=L)}for(const D in E)c[D]=E[D]}}}return c}function rh(n,a,s){var d;const{style:l}=n,c={};for(const f in l)(Ee(l[f])||a.style&&Ee(a.style[f])||px(f,n)||((d=s==null?void 0:s.getValue(f))==null?void 0:d.liveStyle)!==void 0)&&(c[f]=l[f]);return c}const ZE={useVisualState:bx({scrapeMotionValuesFromProps:rh,createRenderState:ah})};function Sx(n,a,s){const l=rh(n,a,s);for(const c in n)if(Ee(n[c])||Ee(a[c])){const d=ps.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;l[d]=n[c]}return l}const QE={useVisualState:bx({scrapeMotionValuesFromProps:Sx,createRenderState:vx})};function FE(n,a){return function(l,{forwardMotionProps:c}={forwardMotionProps:!1}){const d=ih(l)?QE:ZE,f=Z(N({},d),{preloadedFeatures:n,useRender:YE(c),createVisualElement:a,Component:l});return RE(f)}}function kr(n,a,s){const l=n.getProps();return sh(l,a,s!==void 0?s:l.custom,n)}const ld=n=>Array.isArray(n);function IE(n,a,s){n.hasValue(a)?n.getValue(a).set(s):n.addValue(a,fs(s))}function $E(n){return ld(n)?n[n.length-1]||0:n}function WE(n,a){let f=kr(n,a)||{},{transitionEnd:l={},transition:c={}}=f,d=it(f,["transitionEnd","transition"]);d=N(N({},d),l);for(const h in d){const m=$E(d[h]);IE(n,h,m)}}function JE(n){return!!(Ee(n)&&n.add)}function od(n,a){const s=n.getValue("willChange");if(JE(s))return s.add(a);if(!s&&$n.WillChange){const l=new $n.WillChange("auto");n.addValue("willChange",l),l.add(a)}}function Tx(n){return n.props[dx]}const t2=n=>n!==null;function e2(n,{repeat:a,repeatType:s="loop"},l){const c=n.filter(t2),d=a&&s!=="loop"&&a%2===1?0:c.length-1;return c[d]}const n2={type:"spring",stiffness:500,damping:25,restSpeed:10},a2=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),i2={type:"keyframes",duration:.8},s2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},r2=(n,{keyframes:a})=>a.length>2?i2:gs.has(n)?n.startsWith("scale")?a2(a[1]):n2:s2;function l2(v){var b=v,{when:n,delay:a,delayChildren:s,staggerChildren:l,staggerDirection:c,repeat:d,repeatType:f,repeatDelay:h,from:m,elapsed:p}=b,g=it(b,["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"]);return!!Object.keys(g).length}const lh=(n,a,s,l={},c,d)=>f=>{const h=Fd(l,n)||{},m=h.delay||l.delay||0;let{elapsed:p=0}=l;p=p-Cn(m);const g=Z(N({keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity()},h),{delay:-p,onUpdate:b=>{a.set(b),h.onUpdate&&h.onUpdate(b)},onComplete:()=>{f(),h.onComplete&&h.onComplete()},name:n,motionValue:a,element:d?void 0:c});l2(h)||Object.assign(g,r2(n,g)),g.duration&&(g.duration=Cn(g.duration)),g.repeatDelay&&(g.repeatDelay=Cn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let v=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(v=!0)),($n.instantAnimations||$n.skipAnimations)&&(v=!0,g.duration=0,g.delay=0),g.allowFlatten=!h.type&&!h.ease,v&&!d&&a.get()!==void 0){const b=e2(g.keyframes,h);if(b!==void 0){Ft.update(()=>{g.onUpdate(b),g.onComplete()});return}}return h.isSync?new Zd(g):new YA(g)};function o2({protectedKeys:n,needsAnimating:a},s){const l=n.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,l}function wx(n,a,{delay:s=0,transitionOverride:l,type:c}={}){var v;let g=a,{transition:d=n.getDefaultTransition(),transitionEnd:f}=g,h=it(g,["transition","transitionEnd"]);l&&(d=l);const m=[],p=c&&n.animationState&&n.animationState.getState()[c];for(const b in h){const T=n.getValue(b,(v=n.latestValues[b])!=null?v:null),M=h[b];if(M===void 0||p&&o2(p,b))continue;const A=N({delay:s},Fd(d||{},b)),E=T.get();if(E!==void 0&&!T.isAnimating&&!Array.isArray(M)&&M===E&&!A.velocity)continue;let O=!1;if(window.MotionHandoffAnimation){const D=Tx(n);if(D){const L=window.MotionHandoffAnimation(D,b,Ft);L!==null&&(A.startTime=L,O=!0)}}od(n,b),T.start(lh(b,T,M,n.shouldReduceMotion&&W0.has(b)?{type:!1}:A,n,O));const V=T.animation;V&&m.push(V)}return f&&Promise.all(m).then(()=>{Ft.update(()=>{f&&WE(n,f)})}),m}function cd(n,a,s={}){var m;const l=kr(n,a,s.type==="exit"?(m=n.presenceContext)==null?void 0:m.custom:void 0);let{transition:c=n.getDefaultTransition()||{}}=l||{};s.transitionOverride&&(c=s.transitionOverride);const d=l?()=>Promise.all(wx(n,l,s)):()=>Promise.resolve(),f=n.variantChildren&&n.variantChildren.size?(p=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:b}=c;return c2(n,a,g+p,v,b,s)}:()=>Promise.resolve(),{when:h}=c;if(h){const[p,g]=h==="beforeChildren"?[d,f]:[f,d];return p().then(()=>g())}else return Promise.all([d(),f(s.delay)])}function c2(n,a,s=0,l=0,c=1,d){const f=[],h=(n.variantChildren.size-1)*l,m=c===1?(p=0)=>p*l:(p=0)=>h-p*l;return Array.from(n.variantChildren).sort(u2).forEach((p,g)=>{p.notify("AnimationStart",a),f.push(cd(p,a,Z(N({},d),{delay:s+m(g)})).then(()=>p.notify("AnimationComplete",a)))}),Promise.all(f)}function u2(n,a){return n.sortNodePosition(a)}function f2(n,a,s={}){n.notify("AnimationStart",a);let l;if(Array.isArray(a)){const c=a.map(d=>cd(n,d,s));l=Promise.all(c)}else if(typeof a=="string")l=cd(n,a,s);else{const c=typeof a=="function"?kr(n,a,s.custom):a;l=Promise.all(wx(n,c,s))}return l.then(()=>{n.notify("AnimationComplete",a)})}function Ax(n,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==n.length)return!1;for(let l=0;l<s;l++)if(a[l]!==n[l])return!1;return!0}const d2=th.length;function Ex(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?Ex(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const a={};for(let s=0;s<d2;s++){const l=th[s],c=n.props[l];(Lr(c)||c===!1)&&(a[l]=c)}return a}const h2=[...Jd].reverse(),m2=Jd.length;function p2(n){return a=>Promise.all(a.map(({animation:s,options:l})=>f2(n,s,l)))}function g2(n){let a=p2(n),s=Qy(),l=!0;const c=m=>(p,g)=>{var b;const v=kr(n,g,m==="exit"?(b=n.presenceContext)==null?void 0:b.custom:void 0);if(v){const T=v,{transition:M,transitionEnd:A}=T,E=it(T,["transition","transitionEnd"]);p=N(N(N({},p),E),A)}return p};function d(m){a=m(n)}function f(m){const{props:p}=n,g=Ex(n.parent)||{},v=[],b=new Set;let T={},M=1/0;for(let E=0;E<m2;E++){const O=h2[E],V=s[O],D=p[O]!==void 0?p[O]:g[O],L=Lr(D),j=O===m?V.isActive:null;j===!1&&(M=E);let $=D===g[O]&&D!==p[O]&&L;if($&&l&&n.manuallyAnimateOnMount&&($=!1),V.protectedKeys=N({},T),!V.isActive&&j===null||!D&&!V.prevProp||Zo(D)||typeof D=="boolean")continue;const et=y2(V.prevProp,D);let q=et||O===m&&V.isActive&&!$&&L||E>M&&L,W=!1;const Y=Array.isArray(D)?D:[D];let J=Y.reduce(c(O),{});j===!1&&(J={});const{prevResolvedValues:nt={}}=V,ut=N(N({},nt),J),yt=X=>{q=!0,b.has(X)&&(W=!0,b.delete(X)),V.needsAnimating[X]=!0;const P=n.getValue(X);P&&(P.liveStyle=!1)};for(const X in ut){const P=J[X],ht=nt[X];if(T.hasOwnProperty(X))continue;let R=!1;ld(P)&&ld(ht)?R=!Ax(P,ht):R=P!==ht,R?P!=null?yt(X):b.add(X):P!==void 0&&b.has(X)?yt(X):V.protectedKeys[X]=!0}V.prevProp=D,V.prevResolvedValues=J,V.isActive&&(T=N(N({},T),J)),l&&n.blockInitialAnimation&&(q=!1),q&&(!($&&et)||W)&&v.push(...Y.map(X=>({animation:X,options:{type:O}})))}if(b.size){const E={};if(typeof p.initial!="boolean"){const O=kr(n,Array.isArray(p.initial)?p.initial[0]:p.initial);O&&O.transition&&(E.transition=O.transition)}b.forEach(O=>{const V=n.getBaseTarget(O),D=n.getValue(O);D&&(D.liveStyle=!0),E[O]=V!=null?V:null}),v.push({animation:E})}let A=!!v.length;return l&&(p.initial===!1||p.initial===p.animate)&&!n.manuallyAnimateOnMount&&(A=!1),l=!1,A?a(v):Promise.resolve()}function h(m,p){var v;if(s[m].isActive===p)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(b=>{var T;return(T=b.animationState)==null?void 0:T.setActive(m,p)}),s[m].isActive=p;const g=f(m);for(const b in s)s[b].protectedKeys={};return g}return{animateChanges:f,setActive:h,setAnimateFunction:d,getState:()=>s,reset:()=>{s=Qy(),l=!0}}}function y2(n,a){return typeof a=="string"?a!==n:Array.isArray(a)?!Ax(a,n):!1}function ei(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Qy(){return{animate:ei(!0),whileInView:ei(),whileHover:ei(),whileTap:ei(),whileDrag:ei(),whileFocus:ei(),exit:ei()}}class _a{constructor(a){this.isMounted=!1,this.node=a}update(){}}class v2 extends _a{constructor(a){super(a),a.animationState||(a.animationState=g2(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();Zo(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)==null||a.call(this)}}let x2=0;class b2 extends _a{constructor(){super(...arguments),this.id=x2++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===l)return;const c=this.node.animationState.setActive("exit",!a);s&&!a&&c.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const S2={animation:{Feature:v2},exit:{Feature:b2}};function Ur(n,a,s,l={passive:!0}){return n.addEventListener(a,s,l),()=>n.removeEventListener(a,s)}function Kr(n){return{point:{x:n.pageX,y:n.pageY}}}const T2=n=>a=>Wd(a)&&n(a,Kr(a));function Dr(n,a,s,l){return Ur(n,a,T2(s),l)}function Mx({top:n,left:a,right:s,bottom:l}){return{x:{min:a,max:s},y:{min:n,max:l}}}function w2({x:n,y:a}){return{top:a.min,right:n.max,bottom:a.max,left:n.min}}function A2(n,a){if(!a)return n;const s=a({x:n.left,y:n.top}),l=a({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:l.y,right:l.x}}const Cx=1e-4,E2=1-Cx,M2=1+Cx,Rx=.01,C2=0-Rx,R2=0+Rx;function Ne(n){return n.max-n.min}function N2(n,a,s){return Math.abs(n-a)<=s}function Fy(n,a,s,l=.5){n.origin=l,n.originPoint=Qt(a.min,a.max,n.origin),n.scale=Ne(s)/Ne(a),n.translate=Qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=E2&&n.scale<=M2||isNaN(n.scale))&&(n.scale=1),(n.translate>=C2&&n.translate<=R2||isNaN(n.translate))&&(n.translate=0)}function Or(n,a,s,l){Fy(n.x,a.x,s.x,l?l.originX:void 0),Fy(n.y,a.y,s.y,l?l.originY:void 0)}function Iy(n,a,s){n.min=s.min+a.min,n.max=n.min+Ne(a)}function D2(n,a,s){Iy(n.x,a.x,s.x),Iy(n.y,a.y,s.y)}function $y(n,a,s){n.min=a.min-s.min,n.max=n.min+Ne(a)}function jr(n,a,s){$y(n.x,a.x,s.x),$y(n.y,a.y,s.y)}const Wy=()=>({translate:0,scale:1,origin:0,originPoint:0}),rs=()=>({x:Wy(),y:Wy()}),Jy=()=>({min:0,max:0}),ee=()=>({x:Jy(),y:Jy()});function rn(n){return[n("x"),n("y")]}function Cf(n){return n===void 0||n===1}function ud({scale:n,scaleX:a,scaleY:s}){return!Cf(n)||!Cf(a)||!Cf(s)}function ii(n){return ud(n)||Nx(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Nx(n){return tv(n.x)||tv(n.y)}function tv(n){return n&&n!=="0%"}function Uo(n,a,s){const l=n-s,c=a*l;return s+c}function ev(n,a,s,l,c){return c!==void 0&&(n=Uo(n,c,l)),Uo(n,s,l)+a}function fd(n,a=0,s=1,l,c){n.min=ev(n.min,a,s,l,c),n.max=ev(n.max,a,s,l,c)}function Dx(n,{x:a,y:s}){fd(n.x,a.translate,a.scale,a.originPoint),fd(n.y,s.translate,s.scale,s.originPoint)}const nv=.999999999999,av=1.0000000000001;function O2(n,a,s,l=!1){const c=s.length;if(!c)return;a.x=a.y=1;let d,f;for(let h=0;h<c;h++){d=s[h],f=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&os(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(a.x*=f.x.scale,a.y*=f.y.scale,Dx(n,f)),l&&ii(d.latestValues)&&os(n,d.latestValues))}a.x<av&&a.x>nv&&(a.x=1),a.y<av&&a.y>nv&&(a.y=1)}function ls(n,a){n.min=n.min+a,n.max=n.max+a}function iv(n,a,s,l,c=.5){const d=Qt(n.min,n.max,c);fd(n,a,s,d,l)}function os(n,a){iv(n.x,a.x,a.scaleX,a.scale,a.originX),iv(n.y,a.y,a.scaleY,a.scale,a.originY)}function Ox(n,a){return Mx(A2(n.getBoundingClientRect(),a))}function j2(n,a,s){const l=Ox(n,s),{scroll:c}=a;return c&&(ls(l.x,c.offset.x),ls(l.y,c.offset.y)),l}const jx=({current:n})=>n?n.ownerDocument.defaultView:null,sv=(n,a)=>Math.abs(n-a);function _2(n,a){const s=sv(n.x,a.x),l=sv(n.y,a.y);return Math.sqrt(pf(s,2)+pf(l,2))}class _x{constructor(a,s,{transformPagePoint:l,contextWindow:c,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=Nf(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,T=_2(v.offset,{x:0,y:0})>=3;if(!b&&!T)return;const{point:M}=v,{timestamp:A}=be;this.history.push(Z(N({},M),{timestamp:A}));const{onStart:E,onMove:O}=this.handlers;b||(E&&E(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),O&&O(this.lastMoveEvent,v)},this.handlePointerMove=(v,b)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=Rf(b,this.transformPagePoint),Ft.update(this.updatePoint,!0)},this.handlePointerUp=(v,b)=>{this.end();const{onEnd:T,onSessionEnd:M,resumeAnimation:A}=this.handlers;if(this.dragSnapToOrigin&&A&&A(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const E=Nf(v.type==="pointercancel"?this.lastMoveEventInfo:Rf(b,this.transformPagePoint),this.history);this.startEvent&&T&&T(v,E),M&&M(v,E)},!Wd(a))return;this.dragSnapToOrigin=d,this.handlers=s,this.transformPagePoint=l,this.contextWindow=c||window;const f=Kr(a),h=Rf(f,this.transformPagePoint),{point:m}=h,{timestamp:p}=be;this.history=[Z(N({},m),{timestamp:p})];const{onSessionStart:g}=s;g&&g(a,Nf(h,this.history)),this.removeListeners=qr(Dr(this.contextWindow,"pointermove",this.handlePointerMove),Dr(this.contextWindow,"pointerup",this.handlePointerUp),Dr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Ra(this.updatePoint)}}function Rf(n,a){return a?{point:a(n.point)}:n}function rv(n,a){return{x:n.x-a.x,y:n.y-a.y}}function Nf({point:n},a){return{point:n,delta:rv(n,Vx(a)),offset:rv(n,V2(a)),velocity:z2(a,.1)}}function V2(n){return n[0]}function Vx(n){return n[n.length-1]}function z2(n,a){if(n.length<2)return{x:0,y:0};let s=n.length-1,l=null;const c=Vx(n);for(;s>=0&&(l=n[s],!(c.timestamp-l.timestamp>Cn(a)));)s--;if(!l)return{x:0,y:0};const d=Rn(c.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const f={x:(c.x-l.x)/d,y:(c.y-l.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function L2(n,{min:a,max:s},l){return a!==void 0&&n<a?n=l?Qt(a,n,l.min):Math.max(n,a):s!==void 0&&n>s&&(n=l?Qt(s,n,l.max):Math.min(n,s)),n}function lv(n,a,s){return{min:a!==void 0?n.min+a:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function B2(n,{top:a,left:s,bottom:l,right:c}){return{x:lv(n.x,s,c),y:lv(n.y,a,l)}}function ov(n,a){let s=a.min-n.min,l=a.max-n.max;return a.max-a.min<n.max-n.min&&([s,l]=[l,s]),{min:s,max:l}}function k2(n,a){return{x:ov(n.x,a.x),y:ov(n.y,a.y)}}function U2(n,a){let s=.5;const l=Ne(n),c=Ne(a);return c>l?s=_r(a.min,a.max-l,n.min):l>c&&(s=_r(n.min,n.max-c,a.min)),In(0,1,s)}function H2(n,a){const s={};return a.min!==void 0&&(s.min=a.min-n.min),a.max!==void 0&&(s.max=a.max-n.min),s}const dd=.35;function P2(n=dd){return n===!1?n=0:n===!0&&(n=dd),{x:cv(n,"left","right"),y:cv(n,"top","bottom")}}function cv(n,a,s){return{min:uv(n,a),max:uv(n,s)}}function uv(n,a){return typeof n=="number"?n:n[a]||0}const G2=new WeakMap;class q2{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const c=g=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Kr(g).point)},d=(g,v)=>{const{drag:b,dragPropagation:T,onDragStart:M}=this.getProps();if(b&&!T&&(this.openDragLock&&this.openDragLock(),this.openDragLock=rE(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rn(E=>{let O=this.getAxisMotionValue(E).get()||0;if(Nn.test(O)){const{projection:V}=this.visualElement;if(V&&V.layout){const D=V.layout.layoutBox[E];D&&(O=Ne(D)*(parseFloat(O)/100))}}this.originPoint[E]=O}),M&&Ft.postRender(()=>M(g,v)),od(this.visualElement,"transform");const{animationState:A}=this.visualElement;A&&A.setActive("whileDrag",!0)},f=(g,v)=>{const{dragPropagation:b,dragDirectionLock:T,onDirectionLock:M,onDrag:A}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:E}=v;if(T&&this.currentDirection===null){this.currentDirection=Y2(E),this.currentDirection!==null&&M&&M(this.currentDirection);return}this.updateAxis("x",v.point,E),this.updateAxis("y",v.point,E),this.visualElement.render(),A&&A(g,v)},h=(g,v)=>this.stop(g,v),m=()=>rn(g=>{var v;return this.getAnimationState(g)==="paused"&&((v=this.getAxisMotionValue(g).animation)==null?void 0:v.play())}),{dragSnapToOrigin:p}=this.getProps();this.panSession=new _x(a,{onSessionStart:c,onStart:d,onMove:f,onSessionEnd:h,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:p,contextWindow:jx(this.visualElement)})}stop(a,s){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:c}=s;this.startAnimation(c);const{onDragEnd:d}=this.getProps();d&&Ft.postRender(()=>d(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,l){const{drag:c}=this.getProps();if(!l||!yo(a,c,this.currentDirection))return;const d=this.getAxisMotionValue(a);let f=this.originPoint[a]+l[a];this.constraints&&this.constraints[a]&&(f=L2(f,this.constraints[a],this.elastic[a])),d.set(f)}resolveConstraints(){var d;const{dragConstraints:a,dragElastic:s}=this.getProps(),l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,c=this.constraints;a&&ss(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&l?this.constraints=B2(l.layoutBox,a):this.constraints=!1,this.elastic=P2(s),c!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&rn(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=H2(l.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!ss(a))return!1;const l=a.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=j2(l,c.root,this.visualElement.getTransformPagePoint());let f=k2(c.layout.layoutBox,d);if(s){const h=s(w2(f));this.hasMutatedConstraints=!!h,h&&(f=Mx(h))}return f}startAnimation(a){const{drag:s,dragMomentum:l,dragElastic:c,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:h}=this.getProps(),m=this.constraints||{},p=rn(g=>{if(!yo(g,s,this.currentDirection))return;let v=m&&m[g]||{};f&&(v={min:0,max:0});const b=c?200:1e6,T=c?40:1e7,M=N(N({type:"inertia",velocity:l?a[g]:0,bounceStiffness:b,bounceDamping:T,timeConstant:750,restDelta:1,restSpeed:10},d),v);return this.startAxisValueAnimation(g,M)});return Promise.all(p).then(h)}startAxisValueAnimation(a,s){const l=this.getAxisMotionValue(a);return od(this.visualElement,a),l.start(lh(a,l,0,s,this.visualElement,!1))}stopAnimation(){rn(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){rn(a=>{var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,l=this.visualElement.getProps(),c=l[s];return c||this.visualElement.getValue(a,(l.initial?l.initial[a]:void 0)||0)}snapToCursor(a){rn(s=>{const{drag:l}=this.getProps();if(!yo(s,l,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(s);if(c&&c.layout){const{min:f,max:h}=c.layout.layoutBox[s];d.set(a[s]-Qt(f,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:l}=this.visualElement;if(!ss(s)||!l||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};rn(f=>{const h=this.getAxisMotionValue(f);if(h&&this.constraints!==!1){const m=h.get();c[f]=U2({min:m,max:m},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),rn(f=>{if(!yo(f,a,null))return;const h=this.getAxisMotionValue(f),{min:m,max:p}=this.constraints[f];h.set(Qt(m,p,c[f]))})}addListeners(){if(!this.visualElement.current)return;G2.set(this.visualElement,this);const a=this.visualElement.current,s=Dr(a,"pointerdown",m=>{const{drag:p,dragListener:g=!0}=this.getProps();p&&g&&this.start(m)}),l=()=>{const{dragConstraints:m}=this.getProps();ss(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",l);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Ft.read(l);const f=Ur(window,"resize",()=>this.scalePositionWithinConstraints()),h=c.addEventListener("didUpdate",({delta:m,hasLayoutChanged:p})=>{this.isDragging&&p&&(rn(g=>{const v=this.getAxisMotionValue(g);v&&(this.originPoint[g]+=m[g].translate,v.set(v.get()+m[g].translate))}),this.visualElement.render())});return()=>{f(),s(),d(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:l=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:f=dd,dragMomentum:h=!0}=a;return Z(N({},a),{drag:s,dragDirectionLock:l,dragPropagation:c,dragConstraints:d,dragElastic:f,dragMomentum:h})}}function yo(n,a,s){return(a===!0||a===n)&&(s===null||s===n)}function Y2(n,a=10){let s=null;return Math.abs(n.y)>a?s="y":Math.abs(n.x)>a&&(s="x"),s}class X2 extends _a{constructor(a){super(a),this.removeGroupControls=ln,this.removeListeners=ln,this.controls=new q2(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ln}unmount(){this.removeGroupControls(),this.removeListeners()}}const fv=n=>(a,s)=>{n&&Ft.postRender(()=>n(a,s))};class K2 extends _a{constructor(){super(...arguments),this.removePointerDownListener=ln}onPointerDown(a){this.session=new _x(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:jx(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:l,onPanEnd:c}=this.node.getProps();return{onSessionStart:fv(a),onStart:fv(s),onMove:l,onEnd:(d,f)=>{delete this.session,c&&Ft.postRender(()=>c(d,f))}}}mount(){this.removePointerDownListener=Dr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Oo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function dv(n,a){return a.max===a.min?0:n/(a.max-a.min)*100}const Sr={correct:(n,a)=>{if(!a.target)return n;if(typeof n=="string")if(xt.test(n))n=parseFloat(n);else return n;const s=dv(n,a.target.x),l=dv(n,a.target.y);return`${s}% ${l}%`}},Z2={correct:(n,{treeScale:a,projectionDelta:s})=>{const l=n,c=Na.parse(n);if(c.length>5)return l;const d=Na.createTransformer(n),f=typeof c[0]!="number"?1:0,h=s.x.scale*a.x,m=s.y.scale*a.y;c[0+f]/=h,c[1+f]/=m;const p=Qt(h,m,.5);return typeof c[2+f]=="number"&&(c[2+f]/=p),typeof c[3+f]=="number"&&(c[3+f]/=p),d(c)}};class Q2 extends w.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l,layoutId:c}=this.props,{projection:d}=a;jE(F2),d&&(s.group&&s.group.add(d),l&&l.register&&c&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions(Z(N({},d.options),{onExitComplete:()=>this.safeToRemove()}))),Oo.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:l,drag:c,isPresent:d}=this.props,{projection:f}=l;return f&&(f.isPresent=d,c||a.layoutDependency!==s||s===void 0||a.isPresent!==d?f.willUpdate():this.safeToRemove(),a.isPresent!==d&&(d?f.promote():f.relegate()||Ft.postRender(()=>{const h=f.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),$d.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:l}=this.props,{projection:c}=a;c&&(c.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(c),l&&l.deregister&&l.deregister(c))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function zx(n){const[a,s]=pE(),l=w.useContext(y0);return x.jsx(Q2,Z(N({},n),{layoutGroup:l,switchLayoutGroup:w.useContext(hx),isPresent:a,safeToRemove:s}))}const F2={borderRadius:Z(N({},Sr),{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Sr,borderTopRightRadius:Sr,borderBottomLeftRadius:Sr,borderBottomRightRadius:Sr,boxShadow:Z2};function I2(n,a,s){const l=Ee(n)?n:fs(n);return l.start(lh("",l,a,s)),l.animation}const $2=(n,a)=>n.depth-a.depth;class W2{constructor(){this.children=[],this.isDirty=!1}add(a){jd(this.children,a),this.isDirty=!0}remove(a){_d(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort($2),this.isDirty=!1,this.children.forEach(a)}}function J2(n,a){const s=Le.now(),l=({timestamp:c})=>{const d=c-s;d>=a&&(Ra(l),n(d-a))};return Ft.setup(l,!0),()=>Ra(l)}const Lx=["TopLeft","TopRight","BottomLeft","BottomRight"],tM=Lx.length,hv=n=>typeof n=="string"?parseFloat(n):n,mv=n=>typeof n=="number"||xt.test(n);function eM(n,a,s,l,c,d){var f,h,m,p;c?(n.opacity=Qt(0,(f=s.opacity)!=null?f:1,nM(l)),n.opacityExit=Qt((h=a.opacity)!=null?h:1,0,aM(l))):d&&(n.opacity=Qt((m=a.opacity)!=null?m:1,(p=s.opacity)!=null?p:1,l));for(let g=0;g<tM;g++){const v=`border${Lx[g]}Radius`;let b=pv(a,v),T=pv(s,v);if(b===void 0&&T===void 0)continue;b||(b=0),T||(T=0),b===0||T===0||mv(b)===mv(T)?(n[v]=Math.max(Qt(hv(b),hv(T),l),0),(Nn.test(T)||Nn.test(b))&&(n[v]+="%")):n[v]=T}(a.rotate||s.rotate)&&(n.rotate=Qt(a.rotate||0,s.rotate||0,l))}function pv(n,a){return n[a]!==void 0?n[a]:n.borderRadius}const nM=Bx(0,.5,R0),aM=Bx(.5,.95,ln);function Bx(n,a,s){return l=>l<n?0:l>a?1:s(_r(n,a,l))}function gv(n,a){n.min=a.min,n.max=a.max}function sn(n,a){gv(n.x,a.x),gv(n.y,a.y)}function yv(n,a){n.translate=a.translate,n.scale=a.scale,n.originPoint=a.originPoint,n.origin=a.origin}function vv(n,a,s,l,c){return n-=a,n=Uo(n,1/s,l),c!==void 0&&(n=Uo(n,1/c,l)),n}function iM(n,a=0,s=1,l=.5,c,d=n,f=n){if(Nn.test(a)&&(a=parseFloat(a),a=Qt(f.min,f.max,a/100)-f.min),typeof a!="number")return;let h=Qt(d.min,d.max,l);n===d&&(h-=a),n.min=vv(n.min,a,s,h,c),n.max=vv(n.max,a,s,h,c)}function xv(n,a,[s,l,c],d,f){iM(n,a[s],a[l],a[c],a.scale,d,f)}const sM=["x","scaleX","originX"],rM=["y","scaleY","originY"];function bv(n,a,s,l){xv(n.x,a,sM,s?s.x:void 0,l?l.x:void 0),xv(n.y,a,rM,s?s.y:void 0,l?l.y:void 0)}function Sv(n){return n.translate===0&&n.scale===1}function kx(n){return Sv(n.x)&&Sv(n.y)}function Tv(n,a){return n.min===a.min&&n.max===a.max}function lM(n,a){return Tv(n.x,a.x)&&Tv(n.y,a.y)}function wv(n,a){return Math.round(n.min)===Math.round(a.min)&&Math.round(n.max)===Math.round(a.max)}function Ux(n,a){return wv(n.x,a.x)&&wv(n.y,a.y)}function Av(n){return Ne(n.x)/Ne(n.y)}function Ev(n,a){return n.translate===a.translate&&n.scale===a.scale&&n.originPoint===a.originPoint}class oM{constructor(){this.members=[]}add(a){jd(this.members,a),a.scheduleRender()}remove(a){if(_d(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(c=>a===c);if(s===0)return!1;let l;for(let c=s;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(a,s){const l=this.lead;if(a!==l&&(this.prevLead=l,this.lead=a,a.show(),l)){l.instance&&l.scheduleRender(),a.scheduleRender(),a.resumeFrom=l,s&&(a.resumeFrom.preserveOpacity=!0),l.snapshot&&(a.snapshot=l.snapshot,a.snapshot.latestValues=l.animationValues||l.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:c}=a.options;c===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:l}=a;s.onExitComplete&&s.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function cM(n,a,s){let l="";const c=n.x.translate/a.x,d=n.y.translate/a.y,f=(s==null?void 0:s.z)||0;if((c||d||f)&&(l=`translate3d(${c}px, ${d}px, ${f}px) `),(a.x!==1||a.y!==1)&&(l+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:p,rotate:g,rotateX:v,rotateY:b,skewX:T,skewY:M}=s;p&&(l=`perspective(${p}px) ${l}`),g&&(l+=`rotate(${g}deg) `),v&&(l+=`rotateX(${v}deg) `),b&&(l+=`rotateY(${b}deg) `),T&&(l+=`skewX(${T}deg) `),M&&(l+=`skewY(${M}deg) `)}const h=n.x.scale*a.x,m=n.y.scale*a.y;return(h!==1||m!==1)&&(l+=`scale(${h}, ${m})`),l||"none"}const Df=["","X","Y","Z"],uM={visibility:"hidden"},fM=1e3;let dM=0;function Of(n,a,s,l){const{latestValues:c}=a;c[n]&&(s[n]=c[n],a.setStaticValue(n,0),l&&(l[n]=0))}function Hx(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:a}=n.options;if(!a)return;const s=Tx(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:c,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Ft,!(c||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&Hx(l)}function Px({attachResizeListener:n,defaultParent:a,measureScroll:s,checkIsScrollRoot:l,resetTransform:c}){return class{constructor(f={},h=a==null?void 0:a()){this.id=dM++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(pM),this.nodes.forEach(bM),this.nodes.forEach(SM),this.nodes.forEach(gM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new W2)}addEventListener(f,h){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new Ld),this.eventHandlers.get(f).add(h)}notifyListeners(f,...h){const m=this.eventHandlers.get(f);m&&m.notify(...h)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=lx(f)&&!dE(f),this.instance=f;const{layoutId:h,layout:m,visualElement:p}=this.options;if(p&&!p.current&&p.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(m||h)&&(this.isLayoutDirty=!0),n){let g;const v=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,g&&g(),g=J2(v,250),Oo.hasAnimatedSinceResize&&(Oo.hasAnimatedSinceResize=!1,this.nodes.forEach(Cv))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&p&&(h||m)&&this.addEventListener("didUpdate",({delta:g,hasLayoutChanged:v,hasRelativeLayoutChanged:b,layout:T})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const M=this.options.transition||p.getDefaultTransition()||MM,{onLayoutAnimationStart:A,onLayoutAnimationComplete:E}=p.getProps(),O=!this.targetLayout||!Ux(this.targetLayout,T),V=!v&&b;if(this.options.layoutRoot||this.resumeFrom||V||v&&(O||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const D=Z(N({},Fd(M,"layout")),{onPlay:A,onComplete:E});(p.shouldReduceMotion||this.options.layoutRoot)&&(D.delay=0,D.type=!1),this.startAnimation(D),this.setAnimationOrigin(g,V)}else v||Cv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=T})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ra(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(TM),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Hx(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const v=this.path[g];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:m}=this.options;if(h===void 0&&!m)return;const p=this.getTransformTemplate();this.prevTransformTemplateValue=p?p(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Mv);return}this.isUpdating||this.nodes.forEach(vM),this.isUpdating=!1,this.nodes.forEach(xM),this.nodes.forEach(hM),this.nodes.forEach(mM),this.clearAllSnapshots();const h=Le.now();be.delta=In(0,1e3/60,h-be.timestamp),be.timestamp=h,be.isProcessing=!0,Tf.update.process(be),Tf.preRender.process(be),Tf.render.process(be),be.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,$d.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yM),this.sharedNodes.forEach(wM)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ft.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ft.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Ne(this.snapshot.measuredBox.x)&&!Ne(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(h=!1),h&&this.instance){const m=l(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:m,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!c)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!kx(this.projectionDelta),m=this.getTransformTemplate(),p=m?m(this.latestValues,""):void 0,g=p!==this.prevTransformTemplateValue;f&&this.instance&&(h||ii(this.latestValues)||g)&&(c(this.instance,p),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const h=this.measurePageBox();let m=this.removeElementScroll(h);return f&&(m=this.removeTransform(m)),CM(m),{animationId:this.root.animationId,measuredBox:h,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var p;const{visualElement:f}=this.options;if(!f)return ee();const h=f.measureViewportBox();if(!(((p=this.scroll)==null?void 0:p.wasRoot)||this.path.some(RM))){const{scroll:g}=this.root;g&&(ls(h.x,g.offset.x),ls(h.y,g.offset.y))}return h}removeElementScroll(f){var m;const h=ee();if(sn(h,f),(m=this.scroll)!=null&&m.wasRoot)return h;for(let p=0;p<this.path.length;p++){const g=this.path[p],{scroll:v,options:b}=g;g!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&sn(h,f),ls(h.x,v.offset.x),ls(h.y,v.offset.y))}return h}applyTransform(f,h=!1){const m=ee();sn(m,f);for(let p=0;p<this.path.length;p++){const g=this.path[p];!h&&g.options.layoutScroll&&g.scroll&&g!==g.root&&os(m,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),ii(g.latestValues)&&os(m,g.latestValues)}return ii(this.latestValues)&&os(m,this.latestValues),m}removeTransform(f){const h=ee();sn(h,f);for(let m=0;m<this.path.length;m++){const p=this.path[m];if(!p.instance||!ii(p.latestValues))continue;ud(p.latestValues)&&p.updateSnapshot();const g=ee(),v=p.measurePageBox();sn(g,v),bv(h,p.latestValues,p.snapshot?p.snapshot.layoutBox:void 0,g)}return ii(this.latestValues)&&bv(h,this.latestValues),h}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options=Z(N(N({},this.options),f),{crossfade:f.crossfade!==void 0?f.crossfade:!0})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==be.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var b;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==h;if(!(f||m&&this.isSharedProjectionDirty||this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:g,layoutId:v}=this.options;if(!(!this.layout||!(g||v))){if(this.resolvedRelativeTargetAt=be.timestamp,!this.targetDelta&&!this.relativeTarget){const T=this.getClosestProjectingParent();T&&T.layout&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),jr(this.relativeTargetOrigin,this.layout.layoutBox,T.layout.layoutBox),sn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),D2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sn(this.target,this.layout.layoutBox),Dx(this.target,this.targetDelta)):sn(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const T=this.getClosestProjectingParent();T&&!!T.resumingFrom==!!this.resumingFrom&&!T.options.layoutScroll&&T.target&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),jr(this.relativeTargetOrigin,this.target,T.target),sn(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||ud(this.parent.latestValues)||Nx(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var M;const f=this.getLead(),h=!!this.resumingFrom||this!==f;let m=!0;if((this.isProjectionDirty||(M=this.parent)!=null&&M.isProjectionDirty)&&(m=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===be.timestamp&&(m=!1),m)return;const{layout:p,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(p||g))return;sn(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,b=this.treeScale.y;O2(this.layoutCorrected,this.treeScale,this.path,h),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=ee());const{target:T}=f;if(!T){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(yv(this.prevProjectionDelta.x,this.projectionDelta.x),yv(this.prevProjectionDelta.y,this.projectionDelta.y)),Or(this.projectionDelta,this.layoutCorrected,T,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==b||!Ev(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ev(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",T))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rs(),this.projectionDelta=rs(),this.projectionDeltaWithTransform=rs()}setAnimationOrigin(f,h=!1){const m=this.snapshot,p=m?m.latestValues:{},g=N({},this.latestValues),v=rs();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const b=ee(),T=m?m.source:void 0,M=this.layout?this.layout.source:void 0,A=T!==M,E=this.getStack(),O=!E||E.members.length<=1,V=!!(A&&!O&&this.options.crossfade===!0&&!this.path.some(EM));this.animationProgress=0;let D;this.mixTargetDelta=L=>{const j=L/1e3;Rv(v.x,f.x,j),Rv(v.y,f.y,j),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(jr(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),AM(this.relativeTarget,this.relativeTargetOrigin,b,j),D&&lM(this.relativeTarget,D)&&(this.isProjectionDirty=!1),D||(D=ee()),sn(D,this.relativeTarget)),A&&(this.animationValues=g,eM(g,p,this.latestValues,j,V,O)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){var h,m,p;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(p=(m=this.resumingFrom)==null?void 0:m.currentAnimation)==null||p.stop(),this.pendingAnimation&&(Ra(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ft.update(()=>{Oo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=fs(0)),this.currentAnimation=I2(this.motionValue,[0,1e3],Z(N({},f),{isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),f.onUpdate&&f.onUpdate(g)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fM),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:h,target:m,layout:p,latestValues:g}=f;if(!(!h||!m||!p)){if(this!==f&&this.layout&&p&&Gx(this.options.animationType,this.layout.layoutBox,p.layoutBox)){m=this.target||ee();const v=Ne(this.layout.layoutBox.x);m.x.min=f.target.x.min,m.x.max=m.x.min+v;const b=Ne(this.layout.layoutBox.y);m.y.min=f.target.y.min,m.y.max=m.y.min+b}sn(h,m),os(h,g),Or(this.projectionDeltaWithTransform,this.layoutCorrected,h,g)}}registerSharedNode(f,h){this.sharedNodes.has(f)||this.sharedNodes.set(f,new oM),this.sharedNodes.get(f).add(h);const p=h.options.initialPromotionConfig;h.promote({transition:p?p.transition:void 0,preserveFollowOpacity:p&&p.shouldPreserveFollowOpacity?p.shouldPreserveFollowOpacity(h):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var h;const{layoutId:f}=this.options;return f?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:f}=this.options;return f?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:h,preserveFollowOpacity:m}={}){const p=this.getStack();p&&p.promote(this,m),f&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let h=!1;const{latestValues:m}=f;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(h=!0),!h)return;const p={};m.z&&Of("z",f,p,this.animationValues);for(let g=0;g<Df.length;g++)Of(`rotate${Df[g]}`,f,p,this.animationValues),Of(`skew${Df[g]}`,f,p,this.animationValues);f.render();for(const g in p)f.setStaticValue(g,p[g]),this.animationValues&&(this.animationValues[g]=p[g]);f.scheduleRender()}getProjectionStyles(f){var T,M;if(!this.instance||this.isSVG)return;if(!this.isVisible)return uM;const h={visibility:""},m=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=Do(f==null?void 0:f.pointerEvents)||"",h.transform=m?m(this.latestValues,""):"none",h;const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){const A={};return this.options.layoutId&&(A.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,A.pointerEvents=Do(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!ii(this.latestValues)&&(A.transform=m?m({},""):"none",this.hasProjected=!1),A}const g=p.animationValues||p.latestValues;this.applyTransformsToTarget(),h.transform=cM(this.projectionDeltaWithTransform,this.treeScale,g),m&&(h.transform=m(g,h.transform));const{x:v,y:b}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${b.origin*100}% 0`,p.animationValues?h.opacity=p===this?(M=(T=g.opacity)!=null?T:this.latestValues.opacity)!=null?M:1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:h.opacity=p===this?g.opacity!==void 0?g.opacity:"":g.opacityExit!==void 0?g.opacityExit:0;for(const A in Br){if(g[A]===void 0)continue;const{correct:E,applyTo:O,isCSSVariable:V}=Br[A],D=h.transform==="none"?g[A]:E(g[A],p);if(O){const L=O.length;for(let j=0;j<L;j++)h[O[j]]=D}else V?this.options.visualElement.renderState.vars[A]=D:h[A]=D}return this.options.layoutId&&(h.pointerEvents=p===this?Do(f==null?void 0:f.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var h;return(h=f.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(Mv),this.root.sharedNodes.clear()}}}function hM(n){n.updateLayout()}function mM(n){var s;const a=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&a&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:c}=n.layout,{animationType:d}=n.options,f=a.source!==n.layout.source;d==="size"?rn(v=>{const b=f?a.measuredBox[v]:a.layoutBox[v],T=Ne(b);b.min=l[v].min,b.max=b.min+T}):Gx(d,a.layoutBox,l)&&rn(v=>{const b=f?a.measuredBox[v]:a.layoutBox[v],T=Ne(l[v]);b.max=b.min+T,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+T)});const h=rs();Or(h,l,a.layoutBox);const m=rs();f?Or(m,n.applyTransform(c,!0),a.measuredBox):Or(m,l,a.layoutBox);const p=!kx(h);let g=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:T}=v;if(b&&T){const M=ee();jr(M,a.layoutBox,b.layoutBox);const A=ee();jr(A,l,T.layoutBox),Ux(M,A)||(g=!0),v.options.layoutRoot&&(n.relativeTarget=A,n.relativeTargetOrigin=M,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:a,delta:m,layoutDelta:h,hasLayoutChanged:p,hasRelativeLayoutChanged:g})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function pM(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function gM(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function yM(n){n.clearSnapshot()}function Mv(n){n.clearMeasurements()}function vM(n){n.isLayoutDirty=!1}function xM(n){const{visualElement:a}=n.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),n.resetTransform()}function Cv(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function bM(n){n.resolveTargetDelta()}function SM(n){n.calcProjection()}function TM(n){n.resetSkewAndRotation()}function wM(n){n.removeLeadSnapshot()}function Rv(n,a,s){n.translate=Qt(a.translate,0,s),n.scale=Qt(a.scale,1,s),n.origin=a.origin,n.originPoint=a.originPoint}function Nv(n,a,s,l){n.min=Qt(a.min,s.min,l),n.max=Qt(a.max,s.max,l)}function AM(n,a,s,l){Nv(n.x,a.x,s.x,l),Nv(n.y,a.y,s.y,l)}function EM(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const MM={duration:.45,ease:[.4,0,.1,1]},Dv=n=>typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Ov=Dv("applewebkit/")&&!Dv("chrome/")?Math.round:ln;function jv(n){n.min=Ov(n.min),n.max=Ov(n.max)}function CM(n){jv(n.x),jv(n.y)}function Gx(n,a,s){return n==="position"||n==="preserve-aspect"&&!N2(Av(a),Av(s),.2)}function RM(n){var a;return n!==n.root&&((a=n.scroll)==null?void 0:a.wasRoot)}const NM=Px({attachResizeListener:(n,a)=>Ur(n,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),jf={current:void 0},qx=Px({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!jf.current){const n=new NM({});n.mount(window),n.setOptions({layoutScroll:!0}),jf.current=n}return jf.current},resetTransform:(n,a)=>{n.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),DM={pan:{Feature:K2},drag:{Feature:X2,ProjectionNode:qx,MeasureLayout:zx}};function _v(n,a,s){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",s==="Start");const c="onHover"+s,d=l[c];d&&Ft.postRender(()=>d(a,Kr(a)))}class OM extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=lE(a,(s,l)=>(_v(this.node,l,"Start"),c=>_v(this.node,c,"End"))))}unmount(){}}class jM extends _a{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(s){a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=qr(Ur(this.node.current,"focus",()=>this.onFocus()),Ur(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Vv(n,a,s){const{props:l}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",s==="Start");const c="onTap"+(s==="End"?"":s),d=l[c];d&&Ft.postRender(()=>d(a,Kr(a)))}class _M extends _a{mount(){const{current:a}=this.node;a&&(this.unmount=fE(a,(s,l)=>(Vv(this.node,l,"Start"),(c,{success:d})=>Vv(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const hd=new WeakMap,_f=new WeakMap,VM=n=>{const a=hd.get(n.target);a&&a(n)},zM=n=>{n.forEach(VM)};function LM(s){var l=s,{root:n}=l,a=it(l,["root"]);const c=n||document;_f.has(c)||_f.set(c,{});const d=_f.get(c),f=JSON.stringify(a);return d[f]||(d[f]=new IntersectionObserver(zM,N({root:n},a))),d[f]}function BM(n,a,s){const l=LM(a);return hd.set(n,s),l.observe(n),()=>{hd.delete(n),l.unobserve(n)}}const kM={some:0,all:1};class UM extends _a{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:l,amount:c="some",once:d}=a,f={root:s?s.current:void 0,rootMargin:l,threshold:typeof c=="number"?c:kM[c]},h=m=>{const{isIntersecting:p}=m;if(this.isInView===p||(this.isInView=p,d&&!p&&this.hasEnteredView))return;p&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",p);const{onViewportEnter:g,onViewportLeave:v}=this.node.getProps(),b=p?g:v;b&&b(m)};return BM(this.node.current,f,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver=="undefined")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(HM(a,s))&&this.startObserver()}unmount(){}}function HM({viewport:n={}},{viewport:a={}}={}){return s=>n[s]!==a[s]}const PM={inView:{Feature:UM},tap:{Feature:_M},focus:{Feature:jM},hover:{Feature:OM}},GM={layout:{ProjectionNode:qx,MeasureLayout:zx}},md={current:null},Yx={current:!1};function qM(){if(Yx.current=!0,!!Dd)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),a=()=>md.current=n.matches;n.addListener(a),a()}else md.current=!1}const YM=new WeakMap;function XM(n,a,s){for(const l in a){const c=a[l],d=s[l];if(Ee(c))n.addValue(l,c);else if(Ee(d))n.addValue(l,fs(c,{owner:n}));else if(d!==c)if(n.hasValue(l)){const f=n.getValue(l);f.liveStyle===!0?f.jump(c):f.hasAnimated||f.set(c)}else{const f=n.getStaticValue(l);n.addValue(l,fs(f!==void 0?f:c,{owner:n}))}}for(const l in s)a[l]===void 0&&n.removeValue(l);return a}const zv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class KM{scrapeMotionValuesFromProps(a,s,l){return{}}constructor({parent:a,props:s,presenceContext:l,reducedMotionConfig:c,blockInitialAnimation:d,visualState:f},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const T=Le.now();this.renderScheduledAt<T&&(this.renderScheduledAt=T,Ft.render(this.render,!1,!0))};const{latestValues:m,renderState:p}=f;this.latestValues=m,this.baseTarget=N({},m),this.initialValues=s.initial?N({},m):{},this.renderState=p,this.parent=a,this.props=s,this.presenceContext=l,this.depth=a?a.depth+1:0,this.reducedMotionConfig=c,this.options=h,this.blockInitialAnimation=!!d,this.isControllingVariants=Qo(s),this.isVariantNode=fx(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const b=this.scrapeMotionValuesFromProps(s,{},this),{willChange:g}=b,v=it(b,["willChange"]);for(const T in v){const M=v[T];m[T]!==void 0&&Ee(M)&&M.set(m[T],!1)}}mount(a){this.current=a,YM.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,l)=>this.bindToMotionValue(l,s)),Yx.current||qM(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:md.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ra(this.notifyUpdate),Ra(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const l=gs.has(a);l&&this.onBindTransform&&this.onBindTransform();const c=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Ft.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=s.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{c(),d(),f&&f(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in ds){const s=ds[a];if(!s)continue;const{isEnabled:l,Feature:c}=s;if(!this.features[a]&&c&&l(this.props)&&(this.features[a]=new c(this)),this.features[a]){const d=this.features[a];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let l=0;l<zv.length;l++){const c=zv[l];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,f=a[d];f&&(this.propEventSubscriptions[c]=this.on(c,f))}this.prevMotionValues=XM(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const l=this.values.get(a);s!==l&&(l&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let l=this.values.get(a);return l===void 0&&s!==void 0&&(l=fs(s===null?void 0:s,{owner:this}),this.addValue(a,l)),l}readValue(a,s){var c;let l=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:(c=this.getBaseTargetFromProps(this.props,a))!=null?c:this.readValueFromInstance(this.current,a,this.options);return l!=null&&(typeof l=="string"&&(v0(l)||b0(l))?l=parseFloat(l):!mE(l)&&Na.test(s)&&(l=nx(a,s)),this.setBaseTarget(a,Ee(l)?l.get():l)),Ee(l)?l.get():l}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var d;const{initial:s}=this.props;let l;if(typeof s=="string"||typeof s=="object"){const f=sh(this.props,s,(d=this.presenceContext)==null?void 0:d.custom);f&&(l=f[a])}if(s&&l!==void 0)return l;const c=this.getBaseTargetFromProps(this.props,a);return c!==void 0&&!Ee(c)?c:this.initialValues[a]!==void 0&&l===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new Ld),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class Xx extends KM{constructor(){super(...arguments),this.KeyframeResolver=nE}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:l}){delete s[a],delete l[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;Ee(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Kx(n,{style:a,vars:s},l,c){Object.assign(n.style,a,c&&c.getProjectionStyles(l));for(const d in s)n.style.setProperty(d,s[d])}function ZM(n){return window.getComputedStyle(n)}class QM extends Xx{constructor(){super(...arguments),this.type="html",this.renderInstance=Kx}readValueFromInstance(a,s){var l;if(gs.has(s))return(l=this.projection)!=null&&l.isProjecting?ed(s):SA(a,s);{const c=ZM(a),d=(Ud(s)?c.getPropertyValue(s):c[s])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:s}){return Ox(a,s)}build(a,s,l){nh(a,s,l.transformTemplate)}scrapeMotionValuesFromProps(a,s,l){return rh(a,s,l)}}const Zx=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function FM(n,a,s,l){Kx(n,a,void 0,l);for(const c in a.attrs)n.setAttribute(Zx.has(c)?c:eh(c),a.attrs[c])}class IM extends Xx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ee}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(gs.has(s)){const l=ex(s);return l&&l.default||0}return s=Zx.has(s)?s:eh(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,l){return Sx(a,s,l)}build(a,s,l){yx(a,s,this.isSVGTag,l.transformTemplate,l.style)}renderInstance(a,s,l,c){FM(a,s,l,c)}mount(a){this.isSVGTag=xx(a.tagName),super.mount(a)}}const $M=(n,a)=>ih(n)?new IM(a):new QM(a,{allowProjection:n!==w.Fragment}),WM=FE(N(N(N(N({},S2),PM),DM),GM),$M),Ke=bE(WM);function Lv(n,a){if(typeof n=="function")return n(a);n!=null&&(n.current=a)}function JM(...n){return a=>{let s=!1;const l=n.map(c=>{const d=Lv(c,a);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let c=0;c<l.length;c++){const d=l[c];typeof d=="function"?d():Lv(n[c],null)}}}}function ie(...n){return w.useCallback(JM(...n),n)}function Hr(n){const a=tC(n),s=w.forwardRef((l,c)=>{const p=l,{children:d}=p,f=it(p,["children"]),h=w.Children.toArray(d),m=h.find(nC);if(m){const g=m.props.children,v=h.map(b=>b===m?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:b);return x.jsx(a,Z(N({},f),{ref:c,children:w.isValidElement(g)?w.cloneElement(g,void 0,v):null}))}return x.jsx(a,Z(N({},f),{ref:c,children:d}))});return s.displayName=`${n}.Slot`,s}var Qx=Hr("Slot");function tC(n){const a=w.forwardRef((s,l)=>{const m=s,{children:c}=m,d=it(m,["children"]),f=w.isValidElement(c)?iC(c):void 0,h=ie(f,l);if(w.isValidElement(c)){const p=aC(d,c.props);return c.type!==w.Fragment&&(p.ref=h),w.cloneElement(c,p)}return w.Children.count(c)>1?w.Children.only(null):null});return a.displayName=`${n}.SlotClone`,a}var eC=Symbol("radix.slottable");function nC(n){return w.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===eC}function aC(n,a){const s=N({},a);for(const l in a){const c=n[l],d=a[l];/^on[A-Z]/.test(l)?c&&d?s[l]=(...h)=>{const m=d(...h);return c(...h),m}:c&&(s[l]=c):l==="style"?s[l]=N(N({},c),d):l==="className"&&(s[l]=[c,d].filter(Boolean).join(" "))}return N(N({},n),s)}function iC(n){var l,c;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Fx(n){var a,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(a=0;a<c;a++)n[a]&&(s=Fx(n[a]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function Ix(){for(var n,a,s=0,l="",c=arguments.length;s<c;s++)(n=arguments[s])&&(a=Fx(n))&&(l&&(l+=" "),l+=a);return l}const Bv=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,kv=Ix,$x=(n,a)=>s=>{var l;if((a==null?void 0:a.variants)==null)return kv(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:c,defaultVariants:d}=a,f=Object.keys(c).map(p=>{const g=s==null?void 0:s[p],v=d==null?void 0:d[p];if(g===null)return null;const b=Bv(g)||Bv(v);return c[p][b]}),h=s&&Object.entries(s).reduce((p,g)=>{let[v,b]=g;return b===void 0||(p[v]=b),p},{}),m=a==null||(l=a.compoundVariants)===null||l===void 0?void 0:l.reduce((p,g)=>{let M=g,{class:v,className:b}=M,T=it(M,["class","className"]);return Object.entries(T).every(A=>{let[E,O]=A;return Array.isArray(O)?O.includes(N(N({},d),h)[E]):N(N({},d),h)[E]===O})?[...p,v,b]:p},[]);return kv(n,f,m,s==null?void 0:s.class,s==null?void 0:s.className)},oh="-",sC=n=>{const a=lC(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const h=f.split(oh);return h[0]===""&&h.length!==1&&h.shift(),Wx(h,a)||rC(f)},getConflictingClassGroupIds:(f,h)=>{const m=s[f]||[];return h&&l[f]?[...m,...l[f]]:m}}},Wx=(n,a)=>{var f;if(n.length===0)return a.classGroupId;const s=n[0],l=a.nextPart.get(s),c=l?Wx(n.slice(1),l):void 0;if(c)return c;if(a.validators.length===0)return;const d=n.join(oh);return(f=a.validators.find(({validator:h})=>h(d)))==null?void 0:f.classGroupId},Uv=/^\[(.+)\]$/,rC=n=>{if(Uv.test(n)){const a=Uv.exec(n)[1],s=a==null?void 0:a.substring(0,a.indexOf(":"));if(s)return"arbitrary.."+s}},lC=n=>{const{theme:a,classGroups:s}=n,l={nextPart:new Map,validators:[]};for(const c in s)pd(s[c],l,c,a);return l},pd=(n,a,s,l)=>{n.forEach(c=>{if(typeof c=="string"){const d=c===""?a:Hv(a,c);d.classGroupId=s;return}if(typeof c=="function"){if(oC(c)){pd(c(l),a,s,l);return}a.validators.push({validator:c,classGroupId:s});return}Object.entries(c).forEach(([d,f])=>{pd(f,Hv(a,d),s,l)})})},Hv=(n,a)=>{let s=n;return a.split(oh).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},oC=n=>n.isThemeGetter,cC=n=>{if(n<1)return{get:()=>{},set:()=>{}};let a=0,s=new Map,l=new Map;const c=(d,f)=>{s.set(d,f),a++,a>n&&(a=0,l=s,s=new Map)};return{get(d){let f=s.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return c(d,f),f},set(d,f){s.has(d)?s.set(d,f):c(d,f)}}},gd="!",yd=":",uC=yd.length,fC=n=>{const{prefix:a,experimentalParseClassName:s}=n;let l=c=>{const d=[];let f=0,h=0,m=0,p;for(let M=0;M<c.length;M++){let A=c[M];if(f===0&&h===0){if(A===yd){d.push(c.slice(m,M)),m=M+uC;continue}if(A==="/"){p=M;continue}}A==="["?f++:A==="]"?f--:A==="("?h++:A===")"&&h--}const g=d.length===0?c:c.substring(m),v=dC(g),b=v!==g,T=p&&p>m?p-m:void 0;return{modifiers:d,hasImportantModifier:b,baseClassName:v,maybePostfixModifierPosition:T}};if(a){const c=a+yd,d=l;l=f=>f.startsWith(c)?d(f.substring(c.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:f,maybePostfixModifierPosition:void 0}}if(s){const c=l;l=d=>s({className:d,parseClassName:c})}return l},dC=n=>n.endsWith(gd)?n.substring(0,n.length-1):n.startsWith(gd)?n.substring(1):n,hC=n=>{const a=Object.fromEntries(n.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;const c=[];let d=[];return l.forEach(f=>{f[0]==="["||a[f]?(c.push(...d.sort(),f),d=[]):d.push(f)}),c.push(...d.sort()),c}},mC=n=>N({cache:cC(n.cacheSize),parseClassName:fC(n),sortModifiers:hC(n)},sC(n)),pC=/\s+/,gC=(n,a)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:c,sortModifiers:d}=a,f=[],h=n.trim().split(pC);let m="";for(let p=h.length-1;p>=0;p-=1){const g=h[p],{isExternal:v,modifiers:b,hasImportantModifier:T,baseClassName:M,maybePostfixModifierPosition:A}=s(g);if(v){m=g+(m.length>0?" "+m:m);continue}let E=!!A,O=l(E?M.substring(0,A):M);if(!O){if(!E){m=g+(m.length>0?" "+m:m);continue}if(O=l(M),!O){m=g+(m.length>0?" "+m:m);continue}E=!1}const V=d(b).join(":"),D=T?V+gd:V,L=D+O;if(f.includes(L))continue;f.push(L);const j=c(O,E);for(let $=0;$<j.length;++$){const et=j[$];f.push(D+et)}m=g+(m.length>0?" "+m:m)}return m};function yC(){let n=0,a,s,l="";for(;n<arguments.length;)(a=arguments[n++])&&(s=Jx(a))&&(l&&(l+=" "),l+=s);return l}const Jx=n=>{if(typeof n=="string")return n;let a,s="";for(let l=0;l<n.length;l++)n[l]&&(a=Jx(n[l]))&&(s&&(s+=" "),s+=a);return s};function vC(n,...a){let s,l,c,d=f;function f(m){const p=a.reduce((g,v)=>v(g),n());return s=mC(p),l=s.cache.get,c=s.cache.set,d=h,h(m)}function h(m){const p=l(m);if(p)return p;const g=gC(m,s);return c(m,g),g}return function(){return d(yC.apply(null,arguments))}}const le=n=>{const a=s=>s[n]||[];return a.isThemeGetter=!0,a},tb=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,eb=/^\((?:(\w[\w-]*):)?(.+)\)$/i,xC=/^\d+\/\d+$/,bC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,SC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,TC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,wC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,AC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ji=n=>xC.test(n),Et=n=>!!n&&!Number.isNaN(Number(n)),Aa=n=>!!n&&Number.isInteger(Number(n)),Vf=n=>n.endsWith("%")&&Et(n.slice(0,-1)),Qn=n=>bC.test(n),EC=()=>!0,MC=n=>SC.test(n)&&!TC.test(n),nb=()=>!1,CC=n=>wC.test(n),RC=n=>AC.test(n),NC=n=>!ot(n)&&!ct(n),DC=n=>ys(n,sb,nb),ot=n=>tb.test(n),ni=n=>ys(n,rb,MC),zf=n=>ys(n,zC,Et),Pv=n=>ys(n,ab,nb),OC=n=>ys(n,ib,RC),vo=n=>ys(n,lb,CC),ct=n=>eb.test(n),Tr=n=>vs(n,rb),jC=n=>vs(n,LC),Gv=n=>vs(n,ab),_C=n=>vs(n,sb),VC=n=>vs(n,ib),xo=n=>vs(n,lb,!0),ys=(n,a,s)=>{const l=tb.exec(n);return l?l[1]?a(l[1]):s(l[2]):!1},vs=(n,a,s=!1)=>{const l=eb.exec(n);return l?l[1]?a(l[1]):s:!1},ab=n=>n==="position"||n==="percentage",ib=n=>n==="image"||n==="url",sb=n=>n==="length"||n==="size"||n==="bg-size",rb=n=>n==="length",zC=n=>n==="number",LC=n=>n==="family-name",lb=n=>n==="shadow",BC=()=>{const n=le("color"),a=le("font"),s=le("text"),l=le("font-weight"),c=le("tracking"),d=le("leading"),f=le("breakpoint"),h=le("container"),m=le("spacing"),p=le("radius"),g=le("shadow"),v=le("inset-shadow"),b=le("text-shadow"),T=le("drop-shadow"),M=le("blur"),A=le("perspective"),E=le("aspect"),O=le("ease"),V=le("animate"),D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...L(),ct,ot],$=()=>["auto","hidden","clip","visible","scroll"],et=()=>["auto","contain","none"],q=()=>[ct,ot,m],W=()=>[Ji,"full","auto",...q()],Y=()=>[Aa,"none","subgrid",ct,ot],J=()=>["auto",{span:["full",Aa,ct,ot]},Aa,ct,ot],nt=()=>[Aa,"auto",ct,ot],ut=()=>["auto","min","max","fr",ct,ot],yt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ft=()=>["start","end","center","stretch","center-safe","end-safe"],B=()=>["auto",...q()],X=()=>[Ji,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],P=()=>[n,ct,ot],ht=()=>[...L(),Gv,Pv,{position:[ct,ot]}],R=()=>["no-repeat",{repeat:["","x","y","space","round"]}],K=()=>["auto","cover","contain",_C,DC,{size:[ct,ot]}],at=()=>[Vf,Tr,ni],tt=()=>["","none","full",p,ct,ot],rt=()=>["",Et,Tr,ni],bt=()=>["solid","dashed","dotted","double"],mt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],St=()=>[Et,Vf,Gv,Pv],st=()=>["","none",M,ct,ot],wt=()=>["none",Et,ct,ot],Yt=()=>["none",Et,ct,ot],Dt=()=>[Et,ct,ot],Ct=()=>[Ji,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qn],breakpoint:[Qn],color:[EC],container:[Qn],"drop-shadow":[Qn],ease:["in","out","in-out"],font:[NC],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qn],shadow:[Qn],spacing:["px",Et],text:[Qn],"text-shadow":[Qn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ji,ot,ct,E]}],container:["container"],columns:[{columns:[Et,ot,ct,h]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:$()}],"overflow-x":[{"overflow-x":$()}],"overflow-y":[{"overflow-y":$()}],overscroll:[{overscroll:et()}],"overscroll-x":[{"overscroll-x":et()}],"overscroll-y":[{"overscroll-y":et()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:W()}],"inset-x":[{"inset-x":W()}],"inset-y":[{"inset-y":W()}],start:[{start:W()}],end:[{end:W()}],top:[{top:W()}],right:[{right:W()}],bottom:[{bottom:W()}],left:[{left:W()}],visibility:["visible","invisible","collapse"],z:[{z:[Aa,"auto",ct,ot]}],basis:[{basis:[Ji,"full","auto",h,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Et,Ji,"auto","initial","none",ot]}],grow:[{grow:["",Et,ct,ot]}],shrink:[{shrink:["",Et,ct,ot]}],order:[{order:[Aa,"first","last","none",ct,ot]}],"grid-cols":[{"grid-cols":Y()}],"col-start-end":[{col:J()}],"col-start":[{"col-start":nt()}],"col-end":[{"col-end":nt()}],"grid-rows":[{"grid-rows":Y()}],"row-start-end":[{row:J()}],"row-start":[{"row-start":nt()}],"row-end":[{"row-end":nt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ut()}],"auto-rows":[{"auto-rows":ut()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...yt(),"normal"]}],"justify-items":[{"justify-items":[...ft(),"normal"]}],"justify-self":[{"justify-self":["auto",...ft()]}],"align-content":[{content:["normal",...yt()]}],"align-items":[{items:[...ft(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ft(),{baseline:["","last"]}]}],"place-content":[{"place-content":yt()}],"place-items":[{"place-items":[...ft(),"baseline"]}],"place-self":[{"place-self":["auto",...ft()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:B()}],mx:[{mx:B()}],my:[{my:B()}],ms:[{ms:B()}],me:[{me:B()}],mt:[{mt:B()}],mr:[{mr:B()}],mb:[{mb:B()}],ml:[{ml:B()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:X()}],w:[{w:[h,"screen",...X()]}],"min-w":[{"min-w":[h,"screen","none",...X()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[f]},...X()]}],h:[{h:["screen","lh",...X()]}],"min-h":[{"min-h":["screen","lh","none",...X()]}],"max-h":[{"max-h":["screen","lh",...X()]}],"font-size":[{text:["base",s,Tr,ni]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,ct,zf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Vf,ot]}],"font-family":[{font:[jC,ot,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[c,ct,ot]}],"line-clamp":[{"line-clamp":[Et,"none",ct,zf]}],leading:[{leading:[d,...q()]}],"list-image":[{"list-image":["none",ct,ot]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ct,ot]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...bt(),"wavy"]}],"text-decoration-thickness":[{decoration:[Et,"from-font","auto",ct,ni]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[Et,"auto",ct,ot]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ct,ot]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ct,ot]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ht()}],"bg-repeat":[{bg:R()}],"bg-size":[{bg:K()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Aa,ct,ot],radial:["",ct,ot],conic:[Aa,ct,ot]},VC,OC]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:at()}],"gradient-via-pos":[{via:at()}],"gradient-to-pos":[{to:at()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:tt()}],"rounded-s":[{"rounded-s":tt()}],"rounded-e":[{"rounded-e":tt()}],"rounded-t":[{"rounded-t":tt()}],"rounded-r":[{"rounded-r":tt()}],"rounded-b":[{"rounded-b":tt()}],"rounded-l":[{"rounded-l":tt()}],"rounded-ss":[{"rounded-ss":tt()}],"rounded-se":[{"rounded-se":tt()}],"rounded-ee":[{"rounded-ee":tt()}],"rounded-es":[{"rounded-es":tt()}],"rounded-tl":[{"rounded-tl":tt()}],"rounded-tr":[{"rounded-tr":tt()}],"rounded-br":[{"rounded-br":tt()}],"rounded-bl":[{"rounded-bl":tt()}],"border-w":[{border:rt()}],"border-w-x":[{"border-x":rt()}],"border-w-y":[{"border-y":rt()}],"border-w-s":[{"border-s":rt()}],"border-w-e":[{"border-e":rt()}],"border-w-t":[{"border-t":rt()}],"border-w-r":[{"border-r":rt()}],"border-w-b":[{"border-b":rt()}],"border-w-l":[{"border-l":rt()}],"divide-x":[{"divide-x":rt()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":rt()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...bt(),"hidden","none"]}],"divide-style":[{divide:[...bt(),"hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:[...bt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Et,ct,ot]}],"outline-w":[{outline:["",Et,Tr,ni]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",g,xo,vo]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",v,xo,vo]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:rt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[Et,ni]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":rt()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",b,xo,vo]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[Et,ct,ot]}],"mix-blend":[{"mix-blend":[...mt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":mt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Et]}],"mask-image-linear-from-pos":[{"mask-linear-from":St()}],"mask-image-linear-to-pos":[{"mask-linear-to":St()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":St()}],"mask-image-t-to-pos":[{"mask-t-to":St()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":St()}],"mask-image-r-to-pos":[{"mask-r-to":St()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":St()}],"mask-image-b-to-pos":[{"mask-b-to":St()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":St()}],"mask-image-l-to-pos":[{"mask-l-to":St()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":St()}],"mask-image-x-to-pos":[{"mask-x-to":St()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":St()}],"mask-image-y-to-pos":[{"mask-y-to":St()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[ct,ot]}],"mask-image-radial-from-pos":[{"mask-radial-from":St()}],"mask-image-radial-to-pos":[{"mask-radial-to":St()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":L()}],"mask-image-conic-pos":[{"mask-conic":[Et]}],"mask-image-conic-from-pos":[{"mask-conic-from":St()}],"mask-image-conic-to-pos":[{"mask-conic-to":St()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ht()}],"mask-repeat":[{mask:R()}],"mask-size":[{mask:K()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ct,ot]}],filter:[{filter:["","none",ct,ot]}],blur:[{blur:st()}],brightness:[{brightness:[Et,ct,ot]}],contrast:[{contrast:[Et,ct,ot]}],"drop-shadow":[{"drop-shadow":["","none",T,xo,vo]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",Et,ct,ot]}],"hue-rotate":[{"hue-rotate":[Et,ct,ot]}],invert:[{invert:["",Et,ct,ot]}],saturate:[{saturate:[Et,ct,ot]}],sepia:[{sepia:["",Et,ct,ot]}],"backdrop-filter":[{"backdrop-filter":["","none",ct,ot]}],"backdrop-blur":[{"backdrop-blur":st()}],"backdrop-brightness":[{"backdrop-brightness":[Et,ct,ot]}],"backdrop-contrast":[{"backdrop-contrast":[Et,ct,ot]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Et,ct,ot]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Et,ct,ot]}],"backdrop-invert":[{"backdrop-invert":["",Et,ct,ot]}],"backdrop-opacity":[{"backdrop-opacity":[Et,ct,ot]}],"backdrop-saturate":[{"backdrop-saturate":[Et,ct,ot]}],"backdrop-sepia":[{"backdrop-sepia":["",Et,ct,ot]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ct,ot]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Et,"initial",ct,ot]}],ease:[{ease:["linear","initial",O,ct,ot]}],delay:[{delay:[Et,ct,ot]}],animate:[{animate:["none",V,ct,ot]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[A,ct,ot]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:wt()}],"rotate-x":[{"rotate-x":wt()}],"rotate-y":[{"rotate-y":wt()}],"rotate-z":[{"rotate-z":wt()}],scale:[{scale:Yt()}],"scale-x":[{"scale-x":Yt()}],"scale-y":[{"scale-y":Yt()}],"scale-z":[{"scale-z":Yt()}],"scale-3d":["scale-3d"],skew:[{skew:Dt()}],"skew-x":[{"skew-x":Dt()}],"skew-y":[{"skew-y":Dt()}],transform:[{transform:[ct,ot,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ct()}],"translate-x":[{"translate-x":Ct()}],"translate-y":[{"translate-y":Ct()}],"translate-z":[{"translate-z":Ct()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ct,ot]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ct,ot]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[Et,Tr,ni,zf]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},kC=vC(BC);function Kt(...n){return kC(Ix(n))}const UC=$x("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function me(d){var f=d,{className:n,variant:a,size:s,asChild:l=!1}=f,c=it(f,["className","variant","size","asChild"]);const h=l?Qx:"button";return x.jsx(h,N({"data-slot":"button",className:Kt(UC({variant:a,size:s,className:n}))},c))}function hn(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("div",N({"data-slot":"card",className:Kt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n)},a))}function mn(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("div",N({"data-slot":"card-header",className:Kt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n)},a))}function pn(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("div",N({"data-slot":"card-title",className:Kt("leading-none font-semibold",n)},a))}function gn(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("div",N({"data-slot":"card-content",className:Kt("px-6",n)},a))}function ts(l){var c=l,{className:n,type:a}=c,s=it(c,["className","type"]);return x.jsx("input",N({type:a,"data-slot":"input",className:Kt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n)},s))}var Zr=g0();const HC=m0(Zr);var PC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pt=PC.reduce((n,a)=>{const s=Hr(`Primitive.${a}`),l=w.forwardRef((c,d)=>{const p=c,{asChild:f}=p,h=it(p,["asChild"]),m=f?s:a;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(m,Z(N({},h),{ref:d}))});return l.displayName=`Primitive.${a}`,Z(N({},n),{[a]:l})},{});function GC(n,a){n&&Zr.flushSync(()=>n.dispatchEvent(a))}var qC="Label",ob=w.forwardRef((n,a)=>x.jsx(Pt.label,Z(N({},n),{ref:a,onMouseDown:s=>{var c;s.target.closest("button, input, select, textarea")||((c=n.onMouseDown)==null||c.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}})));ob.displayName=qC;var YC=ob;function ai(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(YC,N({"data-slot":"label",className:Kt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",n)},a))}function qv(n,[a,s]){return Math.min(s,Math.max(a,n))}function Bt(n,a,{checkForDefaultPrevented:s=!0}={}){return function(c){if(n==null||n(c),s===!1||!c.defaultPrevented)return a==null?void 0:a(c)}}function Qr(n,a=[]){let s=[];function l(d,f){const h=w.createContext(f),m=s.length;s=[...s,f];const p=v=>{var V;const O=v,{scope:b,children:T}=O,M=it(O,["scope","children"]),A=((V=b==null?void 0:b[n])==null?void 0:V[m])||h,E=w.useMemo(()=>M,Object.values(M));return x.jsx(A.Provider,{value:E,children:T})};p.displayName=d+"Provider";function g(v,b){var A;const T=((A=b==null?void 0:b[n])==null?void 0:A[m])||h,M=w.useContext(T);if(M)return M;if(f!==void 0)return f;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[p,g]}const c=()=>{const d=s.map(f=>w.createContext(f));return function(h){const m=(h==null?void 0:h[n])||d;return w.useMemo(()=>({[`__scope${n}`]:Z(N({},h),{[n]:m})}),[h,m])}};return c.scopeName=n,[l,XC(c,...a)]}function XC(...n){const a=n[0];if(n.length===1)return a;const s=()=>{const l=n.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(d){const f=l.reduce((h,{useScope:m,scopeName:p})=>{const v=m(d)[`__scope${p}`];return N(N({},h),v)},{});return w.useMemo(()=>({[`__scope${a.scopeName}`]:f}),[f])}};return s.scopeName=a.scopeName,s}function cb(n){const a=n+"CollectionProvider",[s,l]=Qr(a),[c,d]=s(a,{collectionRef:{current:null},itemMap:new Map}),f=A=>{const{scope:E,children:O}=A,V=Ea.useRef(null),D=Ea.useRef(new Map).current;return x.jsx(c,{scope:E,itemMap:D,collectionRef:V,children:O})};f.displayName=a;const h=n+"CollectionSlot",m=Hr(h),p=Ea.forwardRef((A,E)=>{const{scope:O,children:V}=A,D=d(h,O),L=ie(E,D.collectionRef);return x.jsx(m,{ref:L,children:V})});p.displayName=h;const g=n+"CollectionItemSlot",v="data-radix-collection-item",b=Hr(g),T=Ea.forwardRef((A,E)=>{const et=A,{scope:O,children:V}=et,D=it(et,["scope","children"]),L=Ea.useRef(null),j=ie(E,L),$=d(g,O);return Ea.useEffect(()=>($.itemMap.set(L,N({ref:L},D)),()=>void $.itemMap.delete(L))),x.jsx(b,{[v]:"",ref:j,children:V})});T.displayName=g;function M(A){const E=d(n+"CollectionConsumer",A);return Ea.useCallback(()=>{const V=E.collectionRef.current;if(!V)return[];const D=Array.from(V.querySelectorAll(`[${v}]`));return Array.from(E.itemMap.values()).sort(($,et)=>D.indexOf($.ref.current)-D.indexOf(et.ref.current))},[E.collectionRef,E.itemMap])}return[{Provider:f,Slot:p,ItemSlot:T},M,l]}var KC=w.createContext(void 0);function ch(n){const a=w.useContext(KC);return n||a||"ltr"}function Da(n){const a=w.useRef(n);return w.useEffect(()=>{a.current=n}),w.useMemo(()=>(...s)=>{var l;return(l=a.current)==null?void 0:l.call(a,...s)},[])}function ZC(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n);w.useEffect(()=>{const l=c=>{c.key==="Escape"&&s(c)};return a.addEventListener("keydown",l,{capture:!0}),()=>a.removeEventListener("keydown",l,{capture:!0})},[s,a])}var QC="DismissableLayer",vd="dismissableLayer.update",FC="dismissableLayer.pointerDownOutside",IC="dismissableLayer.focusOutside",Yv,ub=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),fb=w.forwardRef((n,a)=>{var q;const et=n,{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:f,onDismiss:h}=et,m=it(et,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),p=w.useContext(ub),[g,v]=w.useState(null),b=(q=g==null?void 0:g.ownerDocument)!=null?q:globalThis==null?void 0:globalThis.document,[,T]=w.useState({}),M=ie(a,W=>v(W)),A=Array.from(p.layers),[E]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),O=A.indexOf(E),V=g?A.indexOf(g):-1,D=p.layersWithOutsidePointerEventsDisabled.size>0,L=V>=O,j=JC(W=>{const Y=W.target,J=[...p.branches].some(nt=>nt.contains(Y));!L||J||(c==null||c(W),f==null||f(W),W.defaultPrevented||h==null||h())},b),$=tR(W=>{const Y=W.target;[...p.branches].some(nt=>nt.contains(Y))||(d==null||d(W),f==null||f(W),W.defaultPrevented||h==null||h())},b);return ZC(W=>{V===p.layers.size-1&&(l==null||l(W),!W.defaultPrevented&&h&&(W.preventDefault(),h()))},b),w.useEffect(()=>{if(g)return s&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(Yv=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(g)),p.layers.add(g),Xv(),()=>{s&&p.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=Yv)}},[g,b,s,p]),w.useEffect(()=>()=>{g&&(p.layers.delete(g),p.layersWithOutsidePointerEventsDisabled.delete(g),Xv())},[g,p]),w.useEffect(()=>{const W=()=>T({});return document.addEventListener(vd,W),()=>document.removeEventListener(vd,W)},[]),x.jsx(Pt.div,Z(N({},m),{ref:M,style:N({pointerEvents:D?L?"auto":"none":void 0},n.style),onFocusCapture:Bt(n.onFocusCapture,$.onFocusCapture),onBlurCapture:Bt(n.onBlurCapture,$.onBlurCapture),onPointerDownCapture:Bt(n.onPointerDownCapture,j.onPointerDownCapture)}))});fb.displayName=QC;var $C="DismissableLayerBranch",WC=w.forwardRef((n,a)=>{const s=w.useContext(ub),l=w.useRef(null),c=ie(a,l);return w.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),x.jsx(Pt.div,Z(N({},n),{ref:c}))});WC.displayName=$C;function JC(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n),l=w.useRef(!1),c=w.useRef(()=>{});return w.useEffect(()=>{const d=h=>{if(h.target&&!l.current){let m=function(){db(FC,s,p,{discrete:!0})};const p={originalEvent:h};h.pointerType==="touch"?(a.removeEventListener("click",c.current),c.current=m,a.addEventListener("click",c.current,{once:!0})):m()}else a.removeEventListener("click",c.current);l.current=!1},f=window.setTimeout(()=>{a.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(f),a.removeEventListener("pointerdown",d),a.removeEventListener("click",c.current)}},[a,s]),{onPointerDownCapture:()=>l.current=!0}}function tR(n,a=globalThis==null?void 0:globalThis.document){const s=Da(n),l=w.useRef(!1);return w.useEffect(()=>{const c=d=>{d.target&&!l.current&&db(IC,s,{originalEvent:d},{discrete:!1})};return a.addEventListener("focusin",c),()=>a.removeEventListener("focusin",c)},[a,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Xv(){const n=new CustomEvent(vd);document.dispatchEvent(n)}function db(n,a,s,{discrete:l}){const c=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});a&&c.addEventListener(n,a,{once:!0}),l?GC(c,d):c.dispatchEvent(d)}var Lf=0;function eR(){w.useEffect(()=>{var a,s;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(a=n[0])!=null?a:Kv()),document.body.insertAdjacentElement("beforeend",(s=n[1])!=null?s:Kv()),Lf++,()=>{Lf===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),Lf--}},[])}function Kv(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Bf="focusScope.autoFocusOnMount",kf="focusScope.autoFocusOnUnmount",Zv={bubbles:!1,cancelable:!0},nR="FocusScope",hb=w.forwardRef((n,a)=>{const A=n,{loop:s=!1,trapped:l=!1,onMountAutoFocus:c,onUnmountAutoFocus:d}=A,f=it(A,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[h,m]=w.useState(null),p=Da(c),g=Da(d),v=w.useRef(null),b=ie(a,E=>m(E)),T=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(l){let E=function(L){if(T.paused||!h)return;const j=L.target;h.contains(j)?v.current=j:Ca(v.current,{select:!0})},O=function(L){if(T.paused||!h)return;const j=L.relatedTarget;j!==null&&(h.contains(j)||Ca(v.current,{select:!0}))},V=function(L){if(document.activeElement===document.body)for(const $ of L)$.removedNodes.length>0&&Ca(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",O);const D=new MutationObserver(V);return h&&D.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",O),D.disconnect()}}},[l,h,T.paused]),w.useEffect(()=>{if(h){Fv.add(T);const E=document.activeElement;if(!h.contains(E)){const V=new CustomEvent(Bf,Zv);h.addEventListener(Bf,p),h.dispatchEvent(V),V.defaultPrevented||(aR(oR(mb(h)),{select:!0}),document.activeElement===E&&Ca(h))}return()=>{h.removeEventListener(Bf,p),setTimeout(()=>{const V=new CustomEvent(kf,Zv);h.addEventListener(kf,g),h.dispatchEvent(V),V.defaultPrevented||Ca(E!=null?E:document.body,{select:!0}),h.removeEventListener(kf,g),Fv.remove(T)},0)}}},[h,p,g,T]);const M=w.useCallback(E=>{if(!s&&!l||T.paused)return;const O=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,V=document.activeElement;if(O&&V){const D=E.currentTarget,[L,j]=iR(D);L&&j?!E.shiftKey&&V===j?(E.preventDefault(),s&&Ca(L,{select:!0})):E.shiftKey&&V===L&&(E.preventDefault(),s&&Ca(j,{select:!0})):V===D&&E.preventDefault()}},[s,l,T.paused]);return x.jsx(Pt.div,Z(N({tabIndex:-1},f),{ref:b,onKeyDown:M}))});hb.displayName=nR;function aR(n,{select:a=!1}={}){const s=document.activeElement;for(const l of n)if(Ca(l,{select:a}),document.activeElement!==s)return}function iR(n){const a=mb(n),s=Qv(a,n),l=Qv(a.reverse(),n);return[s,l]}function mb(n){const a=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const c=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||c?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)a.push(s.currentNode);return a}function Qv(n,a){for(const s of n)if(!sR(s,{upTo:a}))return s}function sR(n,{upTo:a}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(a!==void 0&&n===a)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function rR(n){return n instanceof HTMLInputElement&&"select"in n}function Ca(n,{select:a=!1}={}){if(n&&n.focus){const s=document.activeElement;n.focus({preventScroll:!0}),n!==s&&rR(n)&&a&&n.select()}}var Fv=lR();function lR(){let n=[];return{add(a){const s=n[0];a!==s&&(s==null||s.pause()),n=Iv(n,a),n.unshift(a)},remove(a){var s;n=Iv(n,a),(s=n[0])==null||s.resume()}}}function Iv(n,a){const s=[...n],l=s.indexOf(a);return l!==-1&&s.splice(l,1),s}function oR(n){return n.filter(a=>a.tagName!=="A")}var Me=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},cR=p0[" useId ".trim().toString()]||(()=>{}),uR=0;function Fr(n){const[a,s]=w.useState(cR());return Me(()=>{s(l=>l!=null?l:String(uR++))},[n]),n||(a?`radix-${a}`:"")}const fR=["top","right","bottom","left"],Oa=Math.min,Ze=Math.max,Ho=Math.round,bo=Math.floor,Dn=n=>({x:n,y:n}),dR={left:"right",right:"left",bottom:"top",top:"bottom"},hR={start:"end",end:"start"};function xd(n,a,s){return Ze(n,Oa(a,s))}function Wn(n,a){return typeof n=="function"?n(a):n}function Jn(n){return n.split("-")[0]}function xs(n){return n.split("-")[1]}function uh(n){return n==="x"?"y":"x"}function fh(n){return n==="y"?"height":"width"}function Fn(n){return["top","bottom"].includes(Jn(n))?"y":"x"}function dh(n){return uh(Fn(n))}function mR(n,a,s){s===void 0&&(s=!1);const l=xs(n),c=dh(n),d=fh(c);let f=c==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return a.reference[d]>a.floating[d]&&(f=Po(f)),[f,Po(f)]}function pR(n){const a=Po(n);return[bd(n),a,bd(a)]}function bd(n){return n.replace(/start|end/g,a=>hR[a])}function gR(n,a,s){const l=["left","right"],c=["right","left"],d=["top","bottom"],f=["bottom","top"];switch(n){case"top":case"bottom":return s?a?c:l:a?l:c;case"left":case"right":return a?d:f;default:return[]}}function yR(n,a,s,l){const c=xs(n);let d=gR(Jn(n),s==="start",l);return c&&(d=d.map(f=>f+"-"+c),a&&(d=d.concat(d.map(bd)))),d}function Po(n){return n.replace(/left|right|bottom|top/g,a=>dR[a])}function vR(n){return N({top:0,right:0,bottom:0,left:0},n)}function pb(n){return typeof n!="number"?vR(n):{top:n,right:n,bottom:n,left:n}}function Go(n){const{x:a,y:s,width:l,height:c}=n;return{width:l,height:c,top:s,left:a,right:a+l,bottom:s+c,x:a,y:s}}function $v(n,a,s){let{reference:l,floating:c}=n;const d=Fn(a),f=dh(a),h=fh(f),m=Jn(a),p=d==="y",g=l.x+l.width/2-c.width/2,v=l.y+l.height/2-c.height/2,b=l[h]/2-c[h]/2;let T;switch(m){case"top":T={x:g,y:l.y-c.height};break;case"bottom":T={x:g,y:l.y+l.height};break;case"right":T={x:l.x+l.width,y:v};break;case"left":T={x:l.x-c.width,y:v};break;default:T={x:l.x,y:l.y}}switch(xs(a)){case"start":T[f]-=b*(s&&p?-1:1);break;case"end":T[f]+=b*(s&&p?-1:1);break}return T}const xR=(n,a,s)=>dn(null,null,function*(){const{placement:l="bottom",strategy:c="absolute",middleware:d=[],platform:f}=s,h=d.filter(Boolean),m=yield f.isRTL==null?void 0:f.isRTL(a);let p=yield f.getElementRects({reference:n,floating:a,strategy:c}),{x:g,y:v}=$v(p,l,m),b=l,T={},M=0;for(let A=0;A<h.length;A++){const{name:E,fn:O}=h[A],{x:V,y:D,data:L,reset:j}=yield O({x:g,y:v,initialPlacement:l,placement:b,strategy:c,middlewareData:T,rects:p,platform:f,elements:{reference:n,floating:a}});g=V!=null?V:g,v=D!=null?D:v,T=Z(N({},T),{[E]:N(N({},T[E]),L)}),j&&M<=50&&(M++,typeof j=="object"&&(j.placement&&(b=j.placement),j.rects&&(p=j.rects===!0?yield f.getElementRects({reference:n,floating:a,strategy:c}):j.rects),{x:g,y:v}=$v(p,b,m)),A=-1)}return{x:g,y:v,placement:b,strategy:c,middlewareData:T}});function Pr(n,a){return dn(this,null,function*(){var s;a===void 0&&(a={});const{x:l,y:c,platform:d,rects:f,elements:h,strategy:m}=n,{boundary:p="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:b=!1,padding:T=0}=Wn(a,n),M=pb(T),E=h[b?v==="floating"?"reference":"floating":v],O=Go(yield d.getClippingRect({element:(s=yield d.isElement==null?void 0:d.isElement(E))==null||s?E:E.contextElement||(yield d.getDocumentElement==null?void 0:d.getDocumentElement(h.floating)),boundary:p,rootBoundary:g,strategy:m})),V=v==="floating"?{x:l,y:c,width:f.floating.width,height:f.floating.height}:f.reference,D=yield d.getOffsetParent==null?void 0:d.getOffsetParent(h.floating),L=(yield d.isElement==null?void 0:d.isElement(D))?(yield d.getScale==null?void 0:d.getScale(D))||{x:1,y:1}:{x:1,y:1},j=Go(d.convertOffsetParentRelativeRectToViewportRelativeRect?yield d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:V,offsetParent:D,strategy:m}):V);return{top:(O.top-j.top+M.top)/L.y,bottom:(j.bottom-O.bottom+M.bottom)/L.y,left:(O.left-j.left+M.left)/L.x,right:(j.right-O.right+M.right)/L.x}})}const bR=n=>({name:"arrow",options:n,fn(s){return dn(this,null,function*(){const{x:l,y:c,placement:d,rects:f,platform:h,elements:m,middlewareData:p}=s,{element:g,padding:v=0}=Wn(n,s)||{};if(g==null)return{};const b=pb(v),T={x:l,y:c},M=dh(d),A=fh(M),E=yield h.getDimensions(g),O=M==="y",V=O?"top":"left",D=O?"bottom":"right",L=O?"clientHeight":"clientWidth",j=f.reference[A]+f.reference[M]-T[M]-f.floating[A],$=T[M]-f.reference[M],et=yield h.getOffsetParent==null?void 0:h.getOffsetParent(g);let q=et?et[L]:0;(!q||!(yield h.isElement==null?void 0:h.isElement(et)))&&(q=m.floating[L]||f.floating[A]);const W=j/2-$/2,Y=q/2-E[A]/2-1,J=Oa(b[V],Y),nt=Oa(b[D],Y),ut=J,yt=q-E[A]-nt,ft=q/2-E[A]/2+W,B=xd(ut,ft,yt),X=!p.arrow&&xs(d)!=null&&ft!==B&&f.reference[A]/2-(ft<ut?J:nt)-E[A]/2<0,P=X?ft<ut?ft-ut:ft-yt:0;return{[M]:T[M]+P,data:N({[M]:B,centerOffset:ft-B-P},X&&{alignmentOffset:P}),reset:X}})}}),SR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,fn(s){return dn(this,null,function*(){var l,c;const{placement:d,middlewareData:f,rects:h,initialPlacement:m,platform:p,elements:g}=s,B=Wn(n,s),{mainAxis:v=!0,crossAxis:b=!0,fallbackPlacements:T,fallbackStrategy:M="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:E=!0}=B,O=it(B,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((l=f.arrow)!=null&&l.alignmentOffset)return{};const V=Jn(d),D=Fn(m),L=Jn(m)===m,j=yield p.isRTL==null?void 0:p.isRTL(g.floating),$=T||(L||!E?[Po(m)]:pR(m)),et=A!=="none";!T&&et&&$.push(...yR(m,E,A,j));const q=[m,...$],W=yield Pr(s,O),Y=[];let J=((c=f.flip)==null?void 0:c.overflows)||[];if(v&&Y.push(W[V]),b){const X=mR(d,h,j);Y.push(W[X[0]],W[X[1]])}if(J=[...J,{placement:d,overflows:Y}],!Y.every(X=>X<=0)){var nt,ut;const X=(((nt=f.flip)==null?void 0:nt.index)||0)+1,P=q[X];if(P){var yt;const R=b==="alignment"?D!==Fn(P):!1,K=((yt=J[0])==null?void 0:yt.overflows[0])>0;if(!R||K)return{data:{index:X,overflows:J},reset:{placement:P}}}let ht=(ut=J.filter(R=>R.overflows[0]<=0).sort((R,K)=>R.overflows[1]-K.overflows[1])[0])==null?void 0:ut.placement;if(!ht)switch(M){case"bestFit":{var ft;const R=(ft=J.filter(K=>{if(et){const at=Fn(K.placement);return at===D||at==="y"}return!0}).map(K=>[K.placement,K.overflows.filter(at=>at>0).reduce((at,tt)=>at+tt,0)]).sort((K,at)=>K[1]-at[1])[0])==null?void 0:ft[0];R&&(ht=R);break}case"initialPlacement":ht=m;break}if(d!==ht)return{reset:{placement:ht}}}return{}})}}};function Wv(n,a){return{top:n.top-a.height,right:n.right-a.width,bottom:n.bottom-a.height,left:n.left-a.width}}function Jv(n){return fR.some(a=>n[a]>=0)}const TR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,fn(s){return dn(this,null,function*(){const{rects:l}=s,f=Wn(n,s),{strategy:c="referenceHidden"}=f,d=it(f,["strategy"]);switch(c){case"referenceHidden":{const h=yield Pr(s,Z(N({},d),{elementContext:"reference"})),m=Wv(h,l.reference);return{data:{referenceHiddenOffsets:m,referenceHidden:Jv(m)}}}case"escaped":{const h=yield Pr(s,Z(N({},d),{altBoundary:!0})),m=Wv(h,l.floating);return{data:{escapedOffsets:m,escaped:Jv(m)}}}default:return{}}})}}};function wR(n,a){return dn(this,null,function*(){const{placement:s,platform:l,elements:c}=n,d=yield l.isRTL==null?void 0:l.isRTL(c.floating),f=Jn(s),h=xs(s),m=Fn(s)==="y",p=["left","top"].includes(f)?-1:1,g=d&&m?-1:1,v=Wn(a,n);let{mainAxis:b,crossAxis:T,alignmentAxis:M}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return h&&typeof M=="number"&&(T=h==="end"?M*-1:M),m?{x:T*g,y:b*p}:{x:b*p,y:T*g}})}const AR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,fn(s){return dn(this,null,function*(){var l,c;const{x:d,y:f,placement:h,middlewareData:m}=s,p=yield wR(s,n);return h===((l=m.offset)==null?void 0:l.placement)&&(c=m.arrow)!=null&&c.alignmentOffset?{}:{x:d+p.x,y:f+p.y,data:Z(N({},p),{placement:h})}})}}},ER=function(n){return n===void 0&&(n={}),{name:"shift",options:n,fn(s){return dn(this,null,function*(){const{x:l,y:c,placement:d}=s,O=Wn(n,s),{mainAxis:f=!0,crossAxis:h=!1,limiter:m={fn:V=>{let{x:D,y:L}=V;return{x:D,y:L}}}}=O,p=it(O,["mainAxis","crossAxis","limiter"]),g={x:l,y:c},v=yield Pr(s,p),b=Fn(Jn(d)),T=uh(b);let M=g[T],A=g[b];if(f){const V=T==="y"?"top":"left",D=T==="y"?"bottom":"right",L=M+v[V],j=M-v[D];M=xd(L,M,j)}if(h){const V=b==="y"?"top":"left",D=b==="y"?"bottom":"right",L=A+v[V],j=A-v[D];A=xd(L,A,j)}const E=m.fn(Z(N({},s),{[T]:M,[b]:A}));return Z(N({},E),{data:{x:E.x-l,y:E.y-c,enabled:{[T]:f,[b]:h}}})})}}},MR=function(n){return n===void 0&&(n={}),{options:n,fn(a){const{x:s,y:l,placement:c,rects:d,middlewareData:f}=a,{offset:h=0,mainAxis:m=!0,crossAxis:p=!0}=Wn(n,a),g={x:s,y:l},v=Fn(c),b=uh(v);let T=g[b],M=g[v];const A=Wn(h,a),E=typeof A=="number"?{mainAxis:A,crossAxis:0}:N({mainAxis:0,crossAxis:0},A);if(m){const D=b==="y"?"height":"width",L=d.reference[b]-d.floating[D]+E.mainAxis,j=d.reference[b]+d.reference[D]-E.mainAxis;T<L?T=L:T>j&&(T=j)}if(p){var O,V;const D=b==="y"?"width":"height",L=["top","left"].includes(Jn(c)),j=d.reference[v]-d.floating[D]+(L&&((O=f.offset)==null?void 0:O[v])||0)+(L?0:E.crossAxis),$=d.reference[v]+d.reference[D]+(L?0:((V=f.offset)==null?void 0:V[v])||0)-(L?E.crossAxis:0);M<j?M=j:M>$&&(M=$)}return{[b]:T,[v]:M}}}},CR=function(n){return n===void 0&&(n={}),{name:"size",options:n,fn(s){return dn(this,null,function*(){var l,c;const{placement:d,rects:f,platform:h,elements:m}=s,J=Wn(n,s),{apply:p=()=>{}}=J,g=it(J,["apply"]),v=yield Pr(s,g),b=Jn(d),T=xs(d),M=Fn(d)==="y",{width:A,height:E}=f.floating;let O,V;b==="top"||b==="bottom"?(O=b,V=T===((yield h.isRTL==null?void 0:h.isRTL(m.floating))?"start":"end")?"left":"right"):(V=b,O=T==="end"?"top":"bottom");const D=E-v.top-v.bottom,L=A-v.left-v.right,j=Oa(E-v[O],D),$=Oa(A-v[V],L),et=!s.middlewareData.shift;let q=j,W=$;if((l=s.middlewareData.shift)!=null&&l.enabled.x&&(W=L),(c=s.middlewareData.shift)!=null&&c.enabled.y&&(q=D),et&&!T){const nt=Ze(v.left,0),ut=Ze(v.right,0),yt=Ze(v.top,0),ft=Ze(v.bottom,0);M?W=A-2*(nt!==0||ut!==0?nt+ut:Ze(v.left,v.right)):q=E-2*(yt!==0||ft!==0?yt+ft:Ze(v.top,v.bottom))}yield p(Z(N({},s),{availableWidth:W,availableHeight:q}));const Y=yield h.getDimensions(m.floating);return A!==Y.width||E!==Y.height?{reset:{rects:!0}}:{}})}}};function Fo(){return typeof window!="undefined"}function bs(n){return gb(n)?(n.nodeName||"").toLowerCase():"#document"}function Qe(n){var a;return(n==null||(a=n.ownerDocument)==null?void 0:a.defaultView)||window}function jn(n){var a;return(a=(gb(n)?n.ownerDocument:n.document)||window.document)==null?void 0:a.documentElement}function gb(n){return Fo()?n instanceof Node||n instanceof Qe(n).Node:!1}function xn(n){return Fo()?n instanceof Element||n instanceof Qe(n).Element:!1}function On(n){return Fo()?n instanceof HTMLElement||n instanceof Qe(n).HTMLElement:!1}function t0(n){return!Fo()||typeof ShadowRoot=="undefined"?!1:n instanceof ShadowRoot||n instanceof Qe(n).ShadowRoot}function Ir(n){const{overflow:a,overflowX:s,overflowY:l,display:c}=bn(n);return/auto|scroll|overlay|hidden|clip/.test(a+l+s)&&!["inline","contents"].includes(c)}function RR(n){return["table","td","th"].includes(bs(n))}function Io(n){return[":popover-open",":modal"].some(a=>{try{return n.matches(a)}catch(s){return!1}})}function hh(n){const a=mh(),s=xn(n)?bn(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!a&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!a&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function NR(n){let a=ja(n);for(;On(a)&&!hs(a);){if(hh(a))return a;if(Io(a))return null;a=ja(a)}return null}function mh(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function hs(n){return["html","body","#document"].includes(bs(n))}function bn(n){return Qe(n).getComputedStyle(n)}function $o(n){return xn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function ja(n){if(bs(n)==="html")return n;const a=n.assignedSlot||n.parentNode||t0(n)&&n.host||jn(n);return t0(a)?a.host:a}function yb(n){const a=ja(n);return hs(a)?n.ownerDocument?n.ownerDocument.body:n.body:On(a)&&Ir(a)?a:yb(a)}function Gr(n,a,s){var l;a===void 0&&(a=[]),s===void 0&&(s=!0);const c=yb(n),d=c===((l=n.ownerDocument)==null?void 0:l.body),f=Qe(c);if(d){const h=Sd(f);return a.concat(f,f.visualViewport||[],Ir(c)?c:[],h&&s?Gr(h):[])}return a.concat(c,Gr(c,[],s))}function Sd(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function vb(n){const a=bn(n);let s=parseFloat(a.width)||0,l=parseFloat(a.height)||0;const c=On(n),d=c?n.offsetWidth:s,f=c?n.offsetHeight:l,h=Ho(s)!==d||Ho(l)!==f;return h&&(s=d,l=f),{width:s,height:l,$:h}}function ph(n){return xn(n)?n:n.contextElement}function cs(n){const a=ph(n);if(!On(a))return Dn(1);const s=a.getBoundingClientRect(),{width:l,height:c,$:d}=vb(a);let f=(d?Ho(s.width):s.width)/l,h=(d?Ho(s.height):s.height)/c;return(!f||!Number.isFinite(f))&&(f=1),(!h||!Number.isFinite(h))&&(h=1),{x:f,y:h}}const DR=Dn(0);function xb(n){const a=Qe(n);return!mh()||!a.visualViewport?DR:{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}}function OR(n,a,s){return a===void 0&&(a=!1),!s||a&&s!==Qe(n)?!1:a}function ci(n,a,s,l){a===void 0&&(a=!1),s===void 0&&(s=!1);const c=n.getBoundingClientRect(),d=ph(n);let f=Dn(1);a&&(l?xn(l)&&(f=cs(l)):f=cs(n));const h=OR(d,s,l)?xb(d):Dn(0);let m=(c.left+h.x)/f.x,p=(c.top+h.y)/f.y,g=c.width/f.x,v=c.height/f.y;if(d){const b=Qe(d),T=l&&xn(l)?Qe(l):l;let M=b,A=Sd(M);for(;A&&l&&T!==M;){const E=cs(A),O=A.getBoundingClientRect(),V=bn(A),D=O.left+(A.clientLeft+parseFloat(V.paddingLeft))*E.x,L=O.top+(A.clientTop+parseFloat(V.paddingTop))*E.y;m*=E.x,p*=E.y,g*=E.x,v*=E.y,m+=D,p+=L,M=Qe(A),A=Sd(M)}}return Go({width:g,height:v,x:m,y:p})}function gh(n,a){const s=$o(n).scrollLeft;return a?a.left+s:ci(jn(n)).left+s}function bb(n,a,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),c=l.left+a.scrollLeft-(s?0:gh(n,l)),d=l.top+a.scrollTop;return{x:c,y:d}}function jR(n){let{elements:a,rect:s,offsetParent:l,strategy:c}=n;const d=c==="fixed",f=jn(l),h=a?Io(a.floating):!1;if(l===f||h&&d)return s;let m={scrollLeft:0,scrollTop:0},p=Dn(1);const g=Dn(0),v=On(l);if((v||!v&&!d)&&((bs(l)!=="body"||Ir(f))&&(m=$o(l)),On(l))){const T=ci(l);p=cs(l),g.x=T.x+l.clientLeft,g.y=T.y+l.clientTop}const b=f&&!v&&!d?bb(f,m,!0):Dn(0);return{width:s.width*p.x,height:s.height*p.y,x:s.x*p.x-m.scrollLeft*p.x+g.x+b.x,y:s.y*p.y-m.scrollTop*p.y+g.y+b.y}}function _R(n){return Array.from(n.getClientRects())}function VR(n){const a=jn(n),s=$o(n),l=n.ownerDocument.body,c=Ze(a.scrollWidth,a.clientWidth,l.scrollWidth,l.clientWidth),d=Ze(a.scrollHeight,a.clientHeight,l.scrollHeight,l.clientHeight);let f=-s.scrollLeft+gh(n);const h=-s.scrollTop;return bn(l).direction==="rtl"&&(f+=Ze(a.clientWidth,l.clientWidth)-c),{width:c,height:d,x:f,y:h}}function zR(n,a){const s=Qe(n),l=jn(n),c=s.visualViewport;let d=l.clientWidth,f=l.clientHeight,h=0,m=0;if(c){d=c.width,f=c.height;const p=mh();(!p||p&&a==="fixed")&&(h=c.offsetLeft,m=c.offsetTop)}return{width:d,height:f,x:h,y:m}}function LR(n,a){const s=ci(n,!0,a==="fixed"),l=s.top+n.clientTop,c=s.left+n.clientLeft,d=On(n)?cs(n):Dn(1),f=n.clientWidth*d.x,h=n.clientHeight*d.y,m=c*d.x,p=l*d.y;return{width:f,height:h,x:m,y:p}}function e0(n,a,s){let l;if(a==="viewport")l=zR(n,s);else if(a==="document")l=VR(jn(n));else if(xn(a))l=LR(a,s);else{const c=xb(n);l={x:a.x-c.x,y:a.y-c.y,width:a.width,height:a.height}}return Go(l)}function Sb(n,a){const s=ja(n);return s===a||!xn(s)||hs(s)?!1:bn(s).position==="fixed"||Sb(s,a)}function BR(n,a){const s=a.get(n);if(s)return s;let l=Gr(n,[],!1).filter(h=>xn(h)&&bs(h)!=="body"),c=null;const d=bn(n).position==="fixed";let f=d?ja(n):n;for(;xn(f)&&!hs(f);){const h=bn(f),m=hh(f);!m&&h.position==="fixed"&&(c=null),(d?!m&&!c:!m&&h.position==="static"&&!!c&&["absolute","fixed"].includes(c.position)||Ir(f)&&!m&&Sb(n,f))?l=l.filter(g=>g!==f):c=h,f=ja(f)}return a.set(n,l),l}function kR(n){let{element:a,boundary:s,rootBoundary:l,strategy:c}=n;const f=[...s==="clippingAncestors"?Io(a)?[]:BR(a,this._c):[].concat(s),l],h=f[0],m=f.reduce((p,g)=>{const v=e0(a,g,c);return p.top=Ze(v.top,p.top),p.right=Oa(v.right,p.right),p.bottom=Oa(v.bottom,p.bottom),p.left=Ze(v.left,p.left),p},e0(a,h,c));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function UR(n){const{width:a,height:s}=vb(n);return{width:a,height:s}}function HR(n,a,s){const l=On(a),c=jn(a),d=s==="fixed",f=ci(n,!0,d,a);let h={scrollLeft:0,scrollTop:0};const m=Dn(0);function p(){m.x=gh(c)}if(l||!l&&!d)if((bs(a)!=="body"||Ir(c))&&(h=$o(a)),l){const T=ci(a,!0,d,a);m.x=T.x+a.clientLeft,m.y=T.y+a.clientTop}else c&&p();d&&!l&&c&&p();const g=c&&!l&&!d?bb(c,h):Dn(0),v=f.left+h.scrollLeft-m.x-g.x,b=f.top+h.scrollTop-m.y-g.y;return{x:v,y:b,width:f.width,height:f.height}}function Uf(n){return bn(n).position==="static"}function n0(n,a){if(!On(n)||bn(n).position==="fixed")return null;if(a)return a(n);let s=n.offsetParent;return jn(n)===s&&(s=s.ownerDocument.body),s}function Tb(n,a){const s=Qe(n);if(Io(n))return s;if(!On(n)){let c=ja(n);for(;c&&!hs(c);){if(xn(c)&&!Uf(c))return c;c=ja(c)}return s}let l=n0(n,a);for(;l&&RR(l)&&Uf(l);)l=n0(l,a);return l&&hs(l)&&Uf(l)&&!hh(l)?s:l||NR(n)||s}const PR=function(n){return dn(this,null,function*(){const a=this.getOffsetParent||Tb,s=this.getDimensions,l=yield s(n.floating);return{reference:HR(n.reference,yield a(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}})};function GR(n){return bn(n).direction==="rtl"}const qR={convertOffsetParentRelativeRectToViewportRelativeRect:jR,getDocumentElement:jn,getClippingRect:kR,getOffsetParent:Tb,getElementRects:PR,getClientRects:_R,getDimensions:UR,getScale:cs,isElement:xn,isRTL:GR};function wb(n,a){return n.x===a.x&&n.y===a.y&&n.width===a.width&&n.height===a.height}function YR(n,a){let s=null,l;const c=jn(n);function d(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function f(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),d();const p=n.getBoundingClientRect(),{left:g,top:v,width:b,height:T}=p;if(h||a(),!b||!T)return;const M=bo(v),A=bo(c.clientWidth-(g+b)),E=bo(c.clientHeight-(v+T)),O=bo(g),D={rootMargin:-M+"px "+-A+"px "+-E+"px "+-O+"px",threshold:Ze(0,Oa(1,m))||1};let L=!0;function j($){const et=$[0].intersectionRatio;if(et!==m){if(!L)return f();et?f(!1,et):l=setTimeout(()=>{f(!1,1e-7)},1e3)}et===1&&!wb(p,n.getBoundingClientRect())&&f(),L=!1}try{s=new IntersectionObserver(j,Z(N({},D),{root:c.ownerDocument}))}catch($){s=new IntersectionObserver(j,D)}s.observe(n)}return f(!0),d}function XR(n,a,s,l){l===void 0&&(l={});const{ancestorScroll:c=!0,ancestorResize:d=!0,elementResize:f=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=l,p=ph(n),g=c||d?[...p?Gr(p):[],...Gr(a)]:[];g.forEach(O=>{c&&O.addEventListener("scroll",s,{passive:!0}),d&&O.addEventListener("resize",s)});const v=p&&h?YR(p,s):null;let b=-1,T=null;f&&(T=new ResizeObserver(O=>{let[V]=O;V&&V.target===p&&T&&(T.unobserve(a),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var D;(D=T)==null||D.observe(a)})),s()}),p&&!m&&T.observe(p),T.observe(a));let M,A=m?ci(n):null;m&&E();function E(){const O=ci(n);A&&!wb(A,O)&&s(),A=O,M=requestAnimationFrame(E)}return s(),()=>{var O;g.forEach(V=>{c&&V.removeEventListener("scroll",s),d&&V.removeEventListener("resize",s)}),v==null||v(),(O=T)==null||O.disconnect(),T=null,m&&cancelAnimationFrame(M)}}const KR=AR,ZR=ER,QR=SR,FR=CR,IR=TR,a0=bR,$R=MR,WR=(n,a,s)=>{const l=new Map,c=N({platform:qR},s),d=Z(N({},c.platform),{_c:l});return xR(n,a,Z(N({},c),{platform:d}))};var jo=typeof document!="undefined"?w.useLayoutEffect:w.useEffect;function qo(n,a){if(n===a)return!0;if(typeof n!=typeof a)return!1;if(typeof n=="function"&&n.toString()===a.toString())return!0;let s,l,c;if(n&&a&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==a.length)return!1;for(l=s;l--!==0;)if(!qo(n[l],a[l]))return!1;return!0}if(c=Object.keys(n),s=c.length,s!==Object.keys(a).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(a,c[l]))return!1;for(l=s;l--!==0;){const d=c[l];if(!(d==="_owner"&&n.$$typeof)&&!qo(n[d],a[d]))return!1}return!0}return n!==n&&a!==a}function Ab(n){return typeof window=="undefined"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function i0(n,a){const s=Ab(n);return Math.round(a*s)/s}function Hf(n){const a=w.useRef(n);return jo(()=>{a.current=n}),a}function JR(n){n===void 0&&(n={});const{placement:a="bottom",strategy:s="absolute",middleware:l=[],platform:c,elements:{reference:d,floating:f}={},transform:h=!0,whileElementsMounted:m,open:p}=n,[g,v]=w.useState({x:0,y:0,strategy:s,placement:a,middlewareData:{},isPositioned:!1}),[b,T]=w.useState(l);qo(b,l)||T(l);const[M,A]=w.useState(null),[E,O]=w.useState(null),V=w.useCallback(P=>{P!==$.current&&($.current=P,A(P))},[]),D=w.useCallback(P=>{P!==et.current&&(et.current=P,O(P))},[]),L=d||M,j=f||E,$=w.useRef(null),et=w.useRef(null),q=w.useRef(g),W=m!=null,Y=Hf(m),J=Hf(c),nt=Hf(p),ut=w.useCallback(()=>{if(!$.current||!et.current)return;const P={placement:a,strategy:s,middleware:b};J.current&&(P.platform=J.current),WR($.current,et.current,P).then(ht=>{const R=Z(N({},ht),{isPositioned:nt.current!==!1});yt.current&&!qo(q.current,R)&&(q.current=R,Zr.flushSync(()=>{v(R)}))})},[b,a,s,J,nt]);jo(()=>{p===!1&&q.current.isPositioned&&(q.current.isPositioned=!1,v(P=>Z(N({},P),{isPositioned:!1})))},[p]);const yt=w.useRef(!1);jo(()=>(yt.current=!0,()=>{yt.current=!1}),[]),jo(()=>{if(L&&($.current=L),j&&(et.current=j),L&&j){if(Y.current)return Y.current(L,j,ut);ut()}},[L,j,ut,Y,W]);const ft=w.useMemo(()=>({reference:$,floating:et,setReference:V,setFloating:D}),[V,D]),B=w.useMemo(()=>({reference:L,floating:j}),[L,j]),X=w.useMemo(()=>{const P={position:s,left:0,top:0};if(!B.floating)return P;const ht=i0(B.floating,g.x),R=i0(B.floating,g.y);return h?N(Z(N({},P),{transform:"translate("+ht+"px, "+R+"px)"}),Ab(B.floating)>=1.5&&{willChange:"transform"}):{position:s,left:ht,top:R}},[s,h,B.floating,g.x,g.y]);return w.useMemo(()=>Z(N({},g),{update:ut,refs:ft,elements:B,floatingStyles:X}),[g,ut,ft,B,X])}const tN=n=>{function a(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:c}=typeof n=="function"?n(s):n;return l&&a(l)?l.current!=null?a0({element:l.current,padding:c}).fn(s):{}:l?a0({element:l,padding:c}).fn(s):{}}}},eN=(n,a)=>Z(N({},KR(n)),{options:[n,a]}),nN=(n,a)=>Z(N({},ZR(n)),{options:[n,a]}),aN=(n,a)=>Z(N({},$R(n)),{options:[n,a]}),iN=(n,a)=>Z(N({},QR(n)),{options:[n,a]}),sN=(n,a)=>Z(N({},FR(n)),{options:[n,a]}),rN=(n,a)=>Z(N({},IR(n)),{options:[n,a]}),lN=(n,a)=>Z(N({},tN(n)),{options:[n,a]});var oN="Arrow",Eb=w.forwardRef((n,a)=>{const f=n,{children:s,width:l=10,height:c=5}=f,d=it(f,["children","width","height"]);return x.jsx(Pt.svg,Z(N({},d),{ref:a,width:l,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:x.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Eb.displayName=oN;var cN=Eb;function uN(n){const[a,s]=w.useState(void 0);return Me(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const d=c[0];let f,h;if("borderBoxSize"in d){const m=d.borderBoxSize,p=Array.isArray(m)?m[0]:m;f=p.inlineSize,h=p.blockSize}else f=n.offsetWidth,h=n.offsetHeight;s({width:f,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),a}var yh="Popper",[Mb,Cb]=Qr(yh),[fN,Rb]=Mb(yh),Nb=n=>{const{__scopePopper:a,children:s}=n,[l,c]=w.useState(null);return x.jsx(fN,{scope:a,anchor:l,onAnchorChange:c,children:s})};Nb.displayName=yh;var Db="PopperAnchor",Ob=w.forwardRef((n,a)=>{const m=n,{__scopePopper:s,virtualRef:l}=m,c=it(m,["__scopePopper","virtualRef"]),d=Rb(Db,s),f=w.useRef(null),h=ie(a,f);return w.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||f.current)}),l?null:x.jsx(Pt.div,Z(N({},c),{ref:h}))});Ob.displayName=Db;var vh="PopperContent",[dN,hN]=Mb(vh),jb=w.forwardRef((n,a)=>{var st,wt,Yt,Dt,Ct,jt,pe,Ce;const St=n,{__scopePopper:s,side:l="bottom",sideOffset:c=0,align:d="center",alignOffset:f=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:b=!1,updatePositionStrategy:T="optimized",onPlaced:M}=St,A=it(St,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),E=Rb(vh,s),[O,V]=w.useState(null),D=ie(a,on=>V(on)),[L,j]=w.useState(null),$=uN(L),et=(st=$==null?void 0:$.width)!=null?st:0,q=(wt=$==null?void 0:$.height)!=null?wt:0,W=l+(d!=="center"?"-"+d:""),Y=typeof g=="number"?g:N({top:0,right:0,bottom:0,left:0},g),J=Array.isArray(p)?p:[p],nt=J.length>0,ut={padding:Y,boundary:J.filter(pN),altBoundary:nt},{refs:yt,floatingStyles:ft,placement:B,isPositioned:X,middlewareData:P}=JR({strategy:"fixed",placement:W,whileElementsMounted:(...on)=>XR(...on,{animationFrame:T==="always"}),elements:{reference:E.anchor},middleware:[eN({mainAxis:c+q,alignmentAxis:f}),m&&nN(N({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?aN():void 0},ut)),m&&iN(N({},ut)),sN(Z(N({},ut),{apply:({elements:on,rects:ge,availableWidth:La,availableHeight:Wr})=>{const{width:Jr,height:di}=ge.reference,hi=on.floating.style;hi.setProperty("--radix-popper-available-width",`${La}px`),hi.setProperty("--radix-popper-available-height",`${Wr}px`),hi.setProperty("--radix-popper-anchor-width",`${Jr}px`),hi.setProperty("--radix-popper-anchor-height",`${di}px`)}})),L&&lN({element:L,padding:h}),gN({arrowWidth:et,arrowHeight:q}),b&&rN(N({strategy:"referenceHidden"},ut))]}),[ht,R]=zb(B),K=Da(M);Me(()=>{X&&(K==null||K())},[X,K]);const at=(Yt=P.arrow)==null?void 0:Yt.x,tt=(Dt=P.arrow)==null?void 0:Dt.y,rt=((Ct=P.arrow)==null?void 0:Ct.centerOffset)!==0,[bt,mt]=w.useState();return Me(()=>{O&&mt(window.getComputedStyle(O).zIndex)},[O]),x.jsx("div",{ref:yt.setFloating,"data-radix-popper-content-wrapper":"",style:N(Z(N({},ft),{transform:X?ft.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:bt,"--radix-popper-transform-origin":[(jt=P.transformOrigin)==null?void 0:jt.x,(pe=P.transformOrigin)==null?void 0:pe.y].join(" ")}),((Ce=P.hide)==null?void 0:Ce.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:n.dir,children:x.jsx(dN,{scope:s,placedSide:ht,onArrowChange:j,arrowX:at,arrowY:tt,shouldHideArrow:rt,children:x.jsx(Pt.div,Z(N({"data-side":ht,"data-align":R},A),{ref:D,style:Z(N({},A.style),{animation:X?void 0:"none"})}))})})});jb.displayName=vh;var _b="PopperArrow",mN={top:"bottom",right:"left",bottom:"top",left:"right"},Vb=w.forwardRef(function(a,s){const h=a,{__scopePopper:l}=h,c=it(h,["__scopePopper"]),d=hN(_b,l),f=mN[d.placedSide];return x.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:x.jsx(cN,Z(N({},c),{ref:s,style:Z(N({},c.style),{display:"block"})}))})});Vb.displayName=_b;function pN(n){return n!==null}var gN=n=>({name:"transformOrigin",options:n,fn(a){var E,O,V,D,L;const{placement:s,rects:l,middlewareData:c}=a,f=((E=c.arrow)==null?void 0:E.centerOffset)!==0,h=f?0:n.arrowWidth,m=f?0:n.arrowHeight,[p,g]=zb(s),v={start:"0%",center:"50%",end:"100%"}[g],b=((V=(O=c.arrow)==null?void 0:O.x)!=null?V:0)+h/2,T=((L=(D=c.arrow)==null?void 0:D.y)!=null?L:0)+m/2;let M="",A="";return p==="bottom"?(M=f?v:`${b}px`,A=`${-m}px`):p==="top"?(M=f?v:`${b}px`,A=`${l.floating.height+m}px`):p==="right"?(M=`${-m}px`,A=f?v:`${T}px`):p==="left"&&(M=`${l.floating.width+m}px`,A=f?v:`${T}px`),{data:{x:M,y:A}}}});function zb(n){const[a,s="center"]=n.split("-");return[a,s]}var yN=Nb,vN=Ob,xN=jb,bN=Vb,SN="Portal",Lb=w.forwardRef((n,a)=>{var m;const h=n,{container:s}=h,l=it(h,["container"]),[c,d]=w.useState(!1);Me(()=>d(!0),[]);const f=s||c&&((m=globalThis==null?void 0:globalThis.document)==null?void 0:m.body);return f?HC.createPortal(x.jsx(Pt.div,Z(N({},l),{ref:a})),f):null});Lb.displayName=SN;var TN=p0[" useInsertionEffect ".trim().toString()]||Me;function Yo({prop:n,defaultProp:a,onChange:s=()=>{},caller:l}){const[c,d,f]=wN({defaultProp:a,onChange:s}),h=n!==void 0,m=h?n:c;{const g=w.useRef(n!==void 0);w.useEffect(()=>{const v=g.current;v!==h&&console.warn(`${l} is changing from ${v?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=h},[h,l])}const p=w.useCallback(g=>{var v;if(h){const b=AN(g)?g(n):g;b!==n&&((v=f.current)==null||v.call(f,b))}else d(g)},[h,n,d,f]);return[m,p]}function wN({defaultProp:n,onChange:a}){const[s,l]=w.useState(n),c=w.useRef(s),d=w.useRef(a);return TN(()=>{d.current=a},[a]),w.useEffect(()=>{var f;c.current!==s&&((f=d.current)==null||f.call(d,s),c.current=s)},[s,c]),[s,l,d]}function AN(n){return typeof n=="function"}function EN(n){const a=w.useRef({value:n,previous:n});return w.useMemo(()=>(a.current.value!==n&&(a.current.previous=a.current.value,a.current.value=n),a.current.previous),[n])}var Bb=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),MN="VisuallyHidden",CN=w.forwardRef((n,a)=>x.jsx(Pt.span,Z(N({},n),{ref:a,style:N(N({},Bb),n.style)})));CN.displayName=MN;var RN=function(n){if(typeof document=="undefined")return null;var a=Array.isArray(n)?n[0]:n;return a.ownerDocument.body},es=new WeakMap,So=new WeakMap,To={},Pf=0,kb=function(n){return n&&(n.host||kb(n.parentNode))},NN=function(n,a){return a.map(function(s){if(n.contains(s))return s;var l=kb(s);return l&&n.contains(l)?l:(console.error("aria-hidden",s,"in not contained inside",n,". Doing nothing"),null)}).filter(function(s){return!!s})},DN=function(n,a,s,l){var c=NN(a,Array.isArray(n)?n:[n]);To[s]||(To[s]=new WeakMap);var d=To[s],f=[],h=new Set,m=new Set(c),p=function(v){!v||h.has(v)||(h.add(v),p(v.parentNode))};c.forEach(p);var g=function(v){!v||m.has(v)||Array.prototype.forEach.call(v.children,function(b){if(h.has(b))g(b);else try{var T=b.getAttribute(l),M=T!==null&&T!=="false",A=(es.get(b)||0)+1,E=(d.get(b)||0)+1;es.set(b,A),d.set(b,E),f.push(b),A===1&&M&&So.set(b,!0),E===1&&b.setAttribute(s,"true"),M||b.setAttribute(l,"true")}catch(O){console.error("aria-hidden: cannot operate on ",b,O)}})};return g(a),h.clear(),Pf++,function(){f.forEach(function(v){var b=es.get(v)-1,T=d.get(v)-1;es.set(v,b),d.set(v,T),b||(So.has(v)||v.removeAttribute(l),So.delete(v)),T||v.removeAttribute(s)}),Pf--,Pf||(es=new WeakMap,es=new WeakMap,So=new WeakMap,To={})}},ON=function(n,a,s){s===void 0&&(s="data-aria-hidden");var l=Array.from(Array.isArray(n)?n:[n]),c=RN(n);return c?(l.push.apply(l,Array.from(c.querySelectorAll("[aria-live]"))),DN(l,c,s,"aria-hidden")):function(){return null}},Mn=function(){return Mn=Object.assign||function(a){for(var s,l=1,c=arguments.length;l<c;l++){s=arguments[l];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(a[d]=s[d])}return a},Mn.apply(this,arguments)};function Ub(n,a){var s={};for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&a.indexOf(l)<0&&(s[l]=n[l]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,l=Object.getOwnPropertySymbols(n);c<l.length;c++)a.indexOf(l[c])<0&&Object.prototype.propertyIsEnumerable.call(n,l[c])&&(s[l[c]]=n[l[c]]);return s}function jN(n,a,s){if(s||arguments.length===2)for(var l=0,c=a.length,d;l<c;l++)(d||!(l in a))&&(d||(d=Array.prototype.slice.call(a,0,l)),d[l]=a[l]);return n.concat(d||Array.prototype.slice.call(a))}var _o="right-scroll-bar-position",Vo="width-before-scroll-bar",_N="with-scroll-bars-hidden",VN="--removed-body-scroll-bar-size";function Gf(n,a){return typeof n=="function"?n(a):n&&(n.current=a),n}function zN(n,a){var s=w.useState(function(){return{value:n,callback:a,facade:{get current(){return s.value},set current(l){var c=s.value;c!==l&&(s.value=l,s.callback(l,c))}}}})[0];return s.callback=a,s.facade}var LN=typeof window!="undefined"?w.useLayoutEffect:w.useEffect,s0=new WeakMap;function BN(n,a){var s=zN(null,function(l){return n.forEach(function(c){return Gf(c,l)})});return LN(function(){var l=s0.get(s);if(l){var c=new Set(l),d=new Set(n),f=s.current;c.forEach(function(h){d.has(h)||Gf(h,null)}),d.forEach(function(h){c.has(h)||Gf(h,f)})}s0.set(s,n)},[n]),s}function kN(n){return n}function UN(n,a){a===void 0&&(a=kN);var s=[],l=!1,c={read:function(){if(l)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:n},useMedium:function(d){var f=a(d,l);return s.push(f),function(){s=s.filter(function(h){return h!==f})}},assignSyncMedium:function(d){for(l=!0;s.length;){var f=s;s=[],f.forEach(d)}s={push:function(h){return d(h)},filter:function(){return s}}},assignMedium:function(d){l=!0;var f=[];if(s.length){var h=s;s=[],h.forEach(d),f=s}var m=function(){var g=f;f=[],g.forEach(d)},p=function(){return Promise.resolve().then(m)};p(),s={push:function(g){f.push(g),p()},filter:function(g){return f=f.filter(g),s}}}};return c}function HN(n){n===void 0&&(n={});var a=UN(null);return a.options=Mn({async:!0,ssr:!1},n),a}var Hb=function(n){var a=n.sideCar,s=Ub(n,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var l=a.read();if(!l)throw new Error("Sidecar medium not found");return w.createElement(l,Mn({},s))};Hb.isSideCarExport=!0;function PN(n,a){return n.useMedium(a),Hb}var Pb=HN(),qf=function(){},Wo=w.forwardRef(function(n,a){var s=w.useRef(null),l=w.useState({onScrollCapture:qf,onWheelCapture:qf,onTouchMoveCapture:qf}),c=l[0],d=l[1],f=n.forwardProps,h=n.children,m=n.className,p=n.removeScrollBar,g=n.enabled,v=n.shards,b=n.sideCar,T=n.noIsolation,M=n.inert,A=n.allowPinchZoom,E=n.as,O=E===void 0?"div":E,V=n.gapMode,D=Ub(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=b,j=BN([s,a]),$=Mn(Mn({},D),c);return w.createElement(w.Fragment,null,g&&w.createElement(L,{sideCar:Pb,removeScrollBar:p,shards:v,noIsolation:T,inert:M,setCallbacks:d,allowPinchZoom:!!A,lockRef:s,gapMode:V}),f?w.cloneElement(w.Children.only(h),Mn(Mn({},$),{ref:j})):w.createElement(O,Mn({},$,{className:m,ref:j}),h))});Wo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Wo.classNames={fullWidth:Vo,zeroRight:_o};var GN=function(){if(typeof __webpack_nonce__!="undefined")return __webpack_nonce__};function qN(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var a=GN();return a&&n.setAttribute("nonce",a),n}function YN(n,a){n.styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a))}function XN(n){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(n)}var KN=function(){var n=0,a=null;return{add:function(s){n==0&&(a=qN())&&(YN(a,s),XN(a)),n++},remove:function(){n--,!n&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},ZN=function(){var n=KN();return function(a,s){w.useEffect(function(){return n.add(a),function(){n.remove()}},[a&&s])}},Gb=function(){var n=ZN(),a=function(s){var l=s.styles,c=s.dynamic;return n(l,c),null};return a},QN={left:0,top:0,right:0,gap:0},Yf=function(n){return parseInt(n||"",10)||0},FN=function(n){var a=window.getComputedStyle(document.body),s=a[n==="padding"?"paddingLeft":"marginLeft"],l=a[n==="padding"?"paddingTop":"marginTop"],c=a[n==="padding"?"paddingRight":"marginRight"];return[Yf(s),Yf(l),Yf(c)]},IN=function(n){if(n===void 0&&(n="margin"),typeof window=="undefined")return QN;var a=FN(n),s=document.documentElement.clientWidth,l=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,l-s+a[2]-a[0])}},$N=Gb(),us="data-scroll-locked",WN=function(n,a,s,l){var c=n.left,d=n.top,f=n.right,h=n.gap;return s===void 0&&(s="margin"),`
  .`.concat(_N,` {
   overflow: hidden `).concat(l,`;
   padding-right: `).concat(h,"px ").concat(l,`;
  }
  body[`).concat(us,`] {
    overflow: hidden `).concat(l,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(l,";"),s==="margin"&&`
    padding-left: `.concat(c,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(f,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(l,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(l,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(_o,` {
    right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(Vo,` {
    margin-right: `).concat(h,"px ").concat(l,`;
  }
  
  .`).concat(_o," .").concat(_o,` {
    right: 0 `).concat(l,`;
  }
  
  .`).concat(Vo," .").concat(Vo,` {
    margin-right: 0 `).concat(l,`;
  }
  
  body[`).concat(us,`] {
    `).concat(VN,": ").concat(h,`px;
  }
`)},r0=function(){var n=parseInt(document.body.getAttribute(us)||"0",10);return isFinite(n)?n:0},JN=function(){w.useEffect(function(){return document.body.setAttribute(us,(r0()+1).toString()),function(){var n=r0()-1;n<=0?document.body.removeAttribute(us):document.body.setAttribute(us,n.toString())}},[])},tD=function(n){var a=n.noRelative,s=n.noImportant,l=n.gapMode,c=l===void 0?"margin":l;JN();var d=w.useMemo(function(){return IN(c)},[c]);return w.createElement($N,{styles:WN(d,!a,c,s?"":"!important")})},Td=!1;if(typeof window!="undefined")try{var wo=Object.defineProperty({},"passive",{get:function(){return Td=!0,!0}});window.addEventListener("test",wo,wo),window.removeEventListener("test",wo,wo)}catch(n){Td=!1}var ns=Td?{passive:!1}:!1,eD=function(n){return n.tagName==="TEXTAREA"},qb=function(n,a){if(!(n instanceof Element))return!1;var s=window.getComputedStyle(n);return s[a]!=="hidden"&&!(s.overflowY===s.overflowX&&!eD(n)&&s[a]==="visible")},nD=function(n){return qb(n,"overflowY")},aD=function(n){return qb(n,"overflowX")},l0=function(n,a){var s=a.ownerDocument,l=a;do{typeof ShadowRoot!="undefined"&&l instanceof ShadowRoot&&(l=l.host);var c=Yb(n,l);if(c){var d=Xb(n,l),f=d[1],h=d[2];if(f>h)return!0}l=l.parentNode}while(l&&l!==s.body);return!1},iD=function(n){var a=n.scrollTop,s=n.scrollHeight,l=n.clientHeight;return[a,s,l]},sD=function(n){var a=n.scrollLeft,s=n.scrollWidth,l=n.clientWidth;return[a,s,l]},Yb=function(n,a){return n==="v"?nD(a):aD(a)},Xb=function(n,a){return n==="v"?iD(a):sD(a)},rD=function(n,a){return n==="h"&&a==="rtl"?-1:1},lD=function(n,a,s,l,c){var d=rD(n,window.getComputedStyle(a).direction),f=d*l,h=s.target,m=a.contains(h),p=!1,g=f>0,v=0,b=0;do{var T=Xb(n,h),M=T[0],A=T[1],E=T[2],O=A-E-d*M;(M||O)&&Yb(n,h)&&(v+=O,b+=M),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!m&&h!==document.body||m&&(a.contains(h)||a===h));return(g&&Math.abs(v)<1||!g&&Math.abs(b)<1)&&(p=!0),p},Ao=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},o0=function(n){return[n.deltaX,n.deltaY]},c0=function(n){return n&&"current"in n?n.current:n},oD=function(n,a){return n[0]===a[0]&&n[1]===a[1]},cD=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},uD=0,as=[];function fD(n){var a=w.useRef([]),s=w.useRef([0,0]),l=w.useRef(),c=w.useState(uD++)[0],d=w.useState(Gb)[0],f=w.useRef(n);w.useEffect(function(){f.current=n},[n]),w.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(c));var A=jN([n.lockRef.current],(n.shards||[]).map(c0),!0).filter(Boolean);return A.forEach(function(E){return E.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),A.forEach(function(E){return E.classList.remove("allow-interactivity-".concat(c))})}}},[n.inert,n.lockRef.current,n.shards]);var h=w.useCallback(function(A,E){if("touches"in A&&A.touches.length===2||A.type==="wheel"&&A.ctrlKey)return!f.current.allowPinchZoom;var O=Ao(A),V=s.current,D="deltaX"in A?A.deltaX:V[0]-O[0],L="deltaY"in A?A.deltaY:V[1]-O[1],j,$=A.target,et=Math.abs(D)>Math.abs(L)?"h":"v";if("touches"in A&&et==="h"&&$.type==="range")return!1;var q=l0(et,$);if(!q)return!0;if(q?j=et:(j=et==="v"?"h":"v",q=l0(et,$)),!q)return!1;if(!l.current&&"changedTouches"in A&&(D||L)&&(l.current=j),!j)return!0;var W=l.current||j;return lD(W,E,A,W==="h"?D:L)},[]),m=w.useCallback(function(A){var E=A;if(!(!as.length||as[as.length-1]!==d)){var O="deltaY"in E?o0(E):Ao(E),V=a.current.filter(function(j){return j.name===E.type&&(j.target===E.target||E.target===j.shadowParent)&&oD(j.delta,O)})[0];if(V&&V.should){E.cancelable&&E.preventDefault();return}if(!V){var D=(f.current.shards||[]).map(c0).filter(Boolean).filter(function(j){return j.contains(E.target)}),L=D.length>0?h(E,D[0]):!f.current.noIsolation;L&&E.cancelable&&E.preventDefault()}}},[]),p=w.useCallback(function(A,E,O,V){var D={name:A,delta:E,target:O,should:V,shadowParent:dD(O)};a.current.push(D),setTimeout(function(){a.current=a.current.filter(function(L){return L!==D})},1)},[]),g=w.useCallback(function(A){s.current=Ao(A),l.current=void 0},[]),v=w.useCallback(function(A){p(A.type,o0(A),A.target,h(A,n.lockRef.current))},[]),b=w.useCallback(function(A){p(A.type,Ao(A),A.target,h(A,n.lockRef.current))},[]);w.useEffect(function(){return as.push(d),n.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:b}),document.addEventListener("wheel",m,ns),document.addEventListener("touchmove",m,ns),document.addEventListener("touchstart",g,ns),function(){as=as.filter(function(A){return A!==d}),document.removeEventListener("wheel",m,ns),document.removeEventListener("touchmove",m,ns),document.removeEventListener("touchstart",g,ns)}},[]);var T=n.removeScrollBar,M=n.inert;return w.createElement(w.Fragment,null,M?w.createElement(d,{styles:cD(c)}):null,T?w.createElement(tD,{gapMode:n.gapMode}):null)}function dD(n){for(var a=null;n!==null;)n instanceof ShadowRoot&&(a=n.host,n=n.host),n=n.parentNode;return a}const hD=PN(Pb,fD);var Kb=w.forwardRef(function(n,a){return w.createElement(Wo,Mn({},n,{ref:a,sideCar:hD}))});Kb.classNames=Wo.classNames;var mD=[" ","Enter","ArrowUp","ArrowDown"],pD=[" ","Enter"],ui="Select",[Jo,tc,gD]=cb(ui),[Ss,lj]=Qr(ui,[gD,Cb]),ec=Cb(),[yD,Va]=Ss(ui),[vD,xD]=Ss(ui),Zb=n=>{const{__scopeSelect:a,children:s,open:l,defaultOpen:c,onOpenChange:d,value:f,defaultValue:h,onValueChange:m,dir:p,name:g,autoComplete:v,disabled:b,required:T,form:M}=n,A=ec(a),[E,O]=w.useState(null),[V,D]=w.useState(null),[L,j]=w.useState(!1),$=ch(p),[et,q]=Yo({prop:l,defaultProp:c!=null?c:!1,onChange:d,caller:ui}),[W,Y]=Yo({prop:f,defaultProp:h,onChange:m,caller:ui}),J=w.useRef(null),nt=E?M||!!E.closest("form"):!0,[ut,yt]=w.useState(new Set),ft=Array.from(ut).map(B=>B.props.value).join(";");return x.jsx(yN,Z(N({},A),{children:x.jsxs(yD,{required:T,scope:a,trigger:E,onTriggerChange:O,valueNode:V,onValueNodeChange:D,valueNodeHasChildren:L,onValueNodeHasChildrenChange:j,contentId:Fr(),value:W,onValueChange:Y,open:et,onOpenChange:q,dir:$,triggerPointerDownPosRef:J,disabled:b,children:[x.jsx(Jo.Provider,{scope:a,children:x.jsx(vD,{scope:n.__scopeSelect,onNativeOptionAdd:w.useCallback(B=>{yt(X=>new Set(X).add(B))},[]),onNativeOptionRemove:w.useCallback(B=>{yt(X=>{const P=new Set(X);return P.delete(B),P})},[]),children:s})}),nt?x.jsxs(pS,{"aria-hidden":!0,required:T,tabIndex:-1,name:g,autoComplete:v,value:W,onChange:B=>Y(B.target.value),disabled:b,form:M,children:[W===void 0?x.jsx("option",{value:""}):null,Array.from(ut)]},ft):null]})}))};Zb.displayName=ui;var Qb="SelectTrigger",Fb=w.forwardRef((n,a)=>{const A=n,{__scopeSelect:s,disabled:l=!1}=A,c=it(A,["__scopeSelect","disabled"]),d=ec(s),f=Va(Qb,s),h=f.disabled||l,m=ie(a,f.onTriggerChange),p=tc(s),g=w.useRef("touch"),[v,b,T]=yS(E=>{const O=p().filter(L=>!L.disabled),V=O.find(L=>L.value===f.value),D=vS(O,E,V);D!==void 0&&f.onValueChange(D.value)}),M=E=>{h||(f.onOpenChange(!0),T()),E&&(f.triggerPointerDownPosRef.current={x:Math.round(E.pageX),y:Math.round(E.pageY)})};return x.jsx(vN,Z(N({asChild:!0},d),{children:x.jsx(Pt.button,Z(N({type:"button",role:"combobox","aria-controls":f.contentId,"aria-expanded":f.open,"aria-required":f.required,"aria-autocomplete":"none",dir:f.dir,"data-state":f.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":gS(f.value)?"":void 0},c),{ref:m,onClick:Bt(c.onClick,E=>{E.currentTarget.focus(),g.current!=="mouse"&&M(E)}),onPointerDown:Bt(c.onPointerDown,E=>{g.current=E.pointerType;const O=E.target;O.hasPointerCapture(E.pointerId)&&O.releasePointerCapture(E.pointerId),E.button===0&&E.ctrlKey===!1&&E.pointerType==="mouse"&&(M(E),E.preventDefault())}),onKeyDown:Bt(c.onKeyDown,E=>{const O=v.current!=="";!(E.ctrlKey||E.altKey||E.metaKey)&&E.key.length===1&&b(E.key),!(O&&E.key===" ")&&mD.includes(E.key)&&(M(),E.preventDefault())})}))}))});Fb.displayName=Qb;var Ib="SelectValue",$b=w.forwardRef((n,a)=>{const b=n,{__scopeSelect:s,className:l,style:c,children:d,placeholder:f=""}=b,h=it(b,["__scopeSelect","className","style","children","placeholder"]),m=Va(Ib,s),{onValueNodeHasChildrenChange:p}=m,g=d!==void 0,v=ie(a,m.onValueNodeChange);return Me(()=>{p(g)},[p,g]),x.jsx(Pt.span,Z(N({},h),{ref:v,style:{pointerEvents:"none"},children:gS(m.value)?x.jsx(x.Fragment,{children:f}):d}))});$b.displayName=Ib;var bD="SelectIcon",Wb=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s,children:l}=d,c=it(d,["__scopeSelect","children"]);return x.jsx(Pt.span,Z(N({"aria-hidden":!0},c),{ref:a,children:l||"▼"}))});Wb.displayName=bD;var SD="SelectPortal",Jb=n=>x.jsx(Lb,N({asChild:!0},n));Jb.displayName=SD;var fi="SelectContent",tS=w.forwardRef((n,a)=>{const s=Va(fi,n.__scopeSelect),[l,c]=w.useState();if(Me(()=>{c(new DocumentFragment)},[]),!s.open){const d=l;return d?Zr.createPortal(x.jsx(eS,{scope:n.__scopeSelect,children:x.jsx(Jo.Slot,{scope:n.__scopeSelect,children:x.jsx("div",{children:n.children})})}),d):null}return x.jsx(nS,Z(N({},n),{ref:a}))});tS.displayName=fi;var vn=10,[eS,za]=Ss(fi),TD="SelectContentImpl",wD=Hr("SelectContent.RemoveScroll"),nS=w.forwardRef((n,a)=>{const St=n,{__scopeSelect:s,position:l="item-aligned",onCloseAutoFocus:c,onEscapeKeyDown:d,onPointerDownOutside:f,side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:M,hideWhenDetached:A,avoidCollisions:E}=St,O=it(St,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),V=Va(fi,s),[D,L]=w.useState(null),[j,$]=w.useState(null),et=ie(a,st=>L(st)),[q,W]=w.useState(null),[Y,J]=w.useState(null),nt=tc(s),[ut,yt]=w.useState(!1),ft=w.useRef(!1);w.useEffect(()=>{if(D)return ON(D)},[D]),eR();const B=w.useCallback(st=>{const[wt,...Yt]=nt().map(jt=>jt.ref.current),[Dt]=Yt.slice(-1),Ct=document.activeElement;for(const jt of st)if(jt===Ct||(jt==null||jt.scrollIntoView({block:"nearest"}),jt===wt&&j&&(j.scrollTop=0),jt===Dt&&j&&(j.scrollTop=j.scrollHeight),jt==null||jt.focus(),document.activeElement!==Ct))return},[nt,j]),X=w.useCallback(()=>B([q,D]),[B,q,D]);w.useEffect(()=>{ut&&X()},[ut,X]);const{onOpenChange:P,triggerPointerDownPosRef:ht}=V;w.useEffect(()=>{if(D){let st={x:0,y:0};const wt=Dt=>{var Ct,jt,pe,Ce;st={x:Math.abs(Math.round(Dt.pageX)-((jt=(Ct=ht.current)==null?void 0:Ct.x)!=null?jt:0)),y:Math.abs(Math.round(Dt.pageY)-((Ce=(pe=ht.current)==null?void 0:pe.y)!=null?Ce:0))}},Yt=Dt=>{st.x<=10&&st.y<=10?Dt.preventDefault():D.contains(Dt.target)||P(!1),document.removeEventListener("pointermove",wt),ht.current=null};return ht.current!==null&&(document.addEventListener("pointermove",wt),document.addEventListener("pointerup",Yt,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",wt),document.removeEventListener("pointerup",Yt,{capture:!0})}}},[D,P,ht]),w.useEffect(()=>{const st=()=>P(!1);return window.addEventListener("blur",st),window.addEventListener("resize",st),()=>{window.removeEventListener("blur",st),window.removeEventListener("resize",st)}},[P]);const[R,K]=yS(st=>{const wt=nt().filter(Ct=>!Ct.disabled),Yt=wt.find(Ct=>Ct.ref.current===document.activeElement),Dt=vS(wt,st,Yt);Dt&&setTimeout(()=>Dt.ref.current.focus())}),at=w.useCallback((st,wt,Yt)=>{const Dt=!ft.current&&!Yt;(V.value!==void 0&&V.value===wt||Dt)&&(W(st),Dt&&(ft.current=!0))},[V.value]),tt=w.useCallback(()=>D==null?void 0:D.focus(),[D]),rt=w.useCallback((st,wt,Yt)=>{const Dt=!ft.current&&!Yt;(V.value!==void 0&&V.value===wt||Dt)&&J(st)},[V.value]),bt=l==="popper"?wd:aS,mt=bt===wd?{side:h,sideOffset:m,align:p,alignOffset:g,arrowPadding:v,collisionBoundary:b,collisionPadding:T,sticky:M,hideWhenDetached:A,avoidCollisions:E}:{};return x.jsx(eS,{scope:s,content:D,viewport:j,onViewportChange:$,itemRefCallback:at,selectedItem:q,onItemLeave:tt,itemTextRefCallback:rt,focusSelectedItem:X,selectedItemText:Y,position:l,isPositioned:ut,searchRef:R,children:x.jsx(Kb,{as:wD,allowPinchZoom:!0,children:x.jsx(hb,{asChild:!0,trapped:V.open,onMountAutoFocus:st=>{st.preventDefault()},onUnmountAutoFocus:Bt(c,st=>{var wt;(wt=V.trigger)==null||wt.focus({preventScroll:!0}),st.preventDefault()}),children:x.jsx(fb,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:st=>st.preventDefault(),onDismiss:()=>V.onOpenChange(!1),children:x.jsx(bt,Z(N(N({role:"listbox",id:V.contentId,"data-state":V.open?"open":"closed",dir:V.dir,onContextMenu:st=>st.preventDefault()},O),mt),{onPlaced:()=>yt(!0),ref:et,style:N({display:"flex",flexDirection:"column",outline:"none"},O.style),onKeyDown:Bt(O.onKeyDown,st=>{const wt=st.ctrlKey||st.altKey||st.metaKey;if(st.key==="Tab"&&st.preventDefault(),!wt&&st.key.length===1&&K(st.key),["ArrowUp","ArrowDown","Home","End"].includes(st.key)){let Dt=nt().filter(Ct=>!Ct.disabled).map(Ct=>Ct.ref.current);if(["ArrowUp","End"].includes(st.key)&&(Dt=Dt.slice().reverse()),["ArrowUp","ArrowDown"].includes(st.key)){const Ct=st.target,jt=Dt.indexOf(Ct);Dt=Dt.slice(jt+1)}setTimeout(()=>B(Dt)),st.preventDefault()}})}))})})})})});nS.displayName=TD;var AD="SelectItemAlignedPosition",aS=w.forwardRef((n,a)=>{const et=n,{__scopeSelect:s,onPlaced:l}=et,c=it(et,["__scopeSelect","onPlaced"]),d=Va(fi,s),f=za(fi,s),[h,m]=w.useState(null),[p,g]=w.useState(null),v=ie(a,q=>g(q)),b=tc(s),T=w.useRef(!1),M=w.useRef(!0),{viewport:A,selectedItem:E,selectedItemText:O,focusSelectedItem:V}=f,D=w.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&p&&A&&E&&O){const q=d.trigger.getBoundingClientRect(),W=p.getBoundingClientRect(),Y=d.valueNode.getBoundingClientRect(),J=O.getBoundingClientRect();if(d.dir!=="rtl"){const Ct=J.left-W.left,jt=Y.left-Ct,pe=q.left-jt,Ce=q.width+pe,on=Math.max(Ce,W.width),ge=window.innerWidth-vn,La=qv(jt,[vn,Math.max(vn,ge-on)]);h.style.minWidth=Ce+"px",h.style.left=La+"px"}else{const Ct=W.right-J.right,jt=window.innerWidth-Y.right-Ct,pe=window.innerWidth-q.right-jt,Ce=q.width+pe,on=Math.max(Ce,W.width),ge=window.innerWidth-vn,La=qv(jt,[vn,Math.max(vn,ge-on)]);h.style.minWidth=Ce+"px",h.style.right=La+"px"}const nt=b(),ut=window.innerHeight-vn*2,yt=A.scrollHeight,ft=window.getComputedStyle(p),B=parseInt(ft.borderTopWidth,10),X=parseInt(ft.paddingTop,10),P=parseInt(ft.borderBottomWidth,10),ht=parseInt(ft.paddingBottom,10),R=B+X+yt+ht+P,K=Math.min(E.offsetHeight*5,R),at=window.getComputedStyle(A),tt=parseInt(at.paddingTop,10),rt=parseInt(at.paddingBottom,10),bt=q.top+q.height/2-vn,mt=ut-bt,St=E.offsetHeight/2,st=E.offsetTop+St,wt=B+X+st,Yt=R-wt;if(wt<=bt){const Ct=nt.length>0&&E===nt[nt.length-1].ref.current;h.style.bottom="0px";const jt=p.clientHeight-A.offsetTop-A.offsetHeight,pe=Math.max(mt,St+(Ct?rt:0)+jt+P),Ce=wt+pe;h.style.height=Ce+"px"}else{const Ct=nt.length>0&&E===nt[0].ref.current;h.style.top="0px";const pe=Math.max(bt,B+A.offsetTop+(Ct?tt:0)+St)+Yt;h.style.height=pe+"px",A.scrollTop=wt-bt+A.offsetTop}h.style.margin=`${vn}px 0`,h.style.minHeight=K+"px",h.style.maxHeight=ut+"px",l==null||l(),requestAnimationFrame(()=>T.current=!0)}},[b,d.trigger,d.valueNode,h,p,A,E,O,d.dir,l]);Me(()=>D(),[D]);const[L,j]=w.useState();Me(()=>{p&&j(window.getComputedStyle(p).zIndex)},[p]);const $=w.useCallback(q=>{q&&M.current===!0&&(D(),V==null||V(),M.current=!1)},[D,V]);return x.jsx(MD,{scope:s,contentWrapper:h,shouldExpandOnScrollRef:T,onScrollButtonChange:$,children:x.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:L},children:x.jsx(Pt.div,Z(N({},c),{ref:v,style:N({boxSizing:"border-box",maxHeight:"100%"},c.style)}))})})});aS.displayName=AD;var ED="SelectPopperPosition",wd=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s,align:l="start",collisionPadding:c=vn}=h,d=it(h,["__scopeSelect","align","collisionPadding"]),f=ec(s);return x.jsx(xN,Z(N(N({},f),d),{ref:a,align:l,collisionPadding:c,style:Z(N({boxSizing:"border-box"},d.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});wd.displayName=ED;var[MD,xh]=Ss(fi,{}),Ad="SelectViewport",iS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,nonce:l}=p,c=it(p,["__scopeSelect","nonce"]),d=za(Ad,s),f=xh(Ad,s),h=ie(a,d.onViewportChange),m=w.useRef(0);return x.jsxs(x.Fragment,{children:[x.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),x.jsx(Jo.Slot,{scope:s,children:x.jsx(Pt.div,Z(N({"data-radix-select-viewport":"",role:"presentation"},c),{ref:h,style:N({position:"relative",flex:1,overflow:"hidden auto"},c.style),onScroll:Bt(c.onScroll,g=>{const v=g.currentTarget,{contentWrapper:b,shouldExpandOnScrollRef:T}=f;if(T!=null&&T.current&&b){const M=Math.abs(m.current-v.scrollTop);if(M>0){const A=window.innerHeight-vn*2,E=parseFloat(b.style.minHeight),O=parseFloat(b.style.height),V=Math.max(E,O);if(V<A){const D=V+M,L=Math.min(A,D),j=D-L;b.style.height=L+"px",b.style.bottom==="0px"&&(v.scrollTop=j>0?j:0,b.style.justifyContent="flex-end")}}}m.current=v.scrollTop})}))})]})});iS.displayName=Ad;var sS="SelectGroup",[CD,RD]=Ss(sS),ND=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=it(d,["__scopeSelect"]),c=Fr();return x.jsx(CD,{scope:s,id:c,children:x.jsx(Pt.div,Z(N({role:"group","aria-labelledby":c},l),{ref:a}))})});ND.displayName=sS;var rS="SelectLabel",DD=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=it(d,["__scopeSelect"]),c=RD(rS,s);return x.jsx(Pt.div,Z(N({id:c.id},l),{ref:a}))});DD.displayName=rS;var Xo="SelectItem",[OD,lS]=Ss(Xo),oS=w.forwardRef((n,a)=>{const V=n,{__scopeSelect:s,value:l,disabled:c=!1,textValue:d}=V,f=it(V,["__scopeSelect","value","disabled","textValue"]),h=Va(Xo,s),m=za(Xo,s),p=h.value===l,[g,v]=w.useState(d!=null?d:""),[b,T]=w.useState(!1),M=ie(a,D=>{var L;return(L=m.itemRefCallback)==null?void 0:L.call(m,D,l,c)}),A=Fr(),E=w.useRef("touch"),O=()=>{c||(h.onValueChange(l),h.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return x.jsx(OD,{scope:s,value:l,disabled:c,textId:A,isSelected:p,onItemTextChange:w.useCallback(D=>{v(L=>{var j;return L||((j=D==null?void 0:D.textContent)!=null?j:"").trim()})},[]),children:x.jsx(Jo.ItemSlot,{scope:s,value:l,disabled:c,textValue:g,children:x.jsx(Pt.div,Z(N({role:"option","aria-labelledby":A,"data-highlighted":b?"":void 0,"aria-selected":p&&b,"data-state":p?"checked":"unchecked","aria-disabled":c||void 0,"data-disabled":c?"":void 0,tabIndex:c?void 0:-1},f),{ref:M,onFocus:Bt(f.onFocus,()=>T(!0)),onBlur:Bt(f.onBlur,()=>T(!1)),onClick:Bt(f.onClick,()=>{E.current!=="mouse"&&O()}),onPointerUp:Bt(f.onPointerUp,()=>{E.current==="mouse"&&O()}),onPointerDown:Bt(f.onPointerDown,D=>{E.current=D.pointerType}),onPointerMove:Bt(f.onPointerMove,D=>{var L;E.current=D.pointerType,c?(L=m.onItemLeave)==null||L.call(m):E.current==="mouse"&&D.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Bt(f.onPointerLeave,D=>{var L;D.currentTarget===document.activeElement&&((L=m.onItemLeave)==null||L.call(m))}),onKeyDown:Bt(f.onKeyDown,D=>{var j;((j=m.searchRef)==null?void 0:j.current)!==""&&D.key===" "||(pD.includes(D.key)&&O(),D.key===" "&&D.preventDefault())})}))})})});oS.displayName=Xo;var Cr="SelectItemText",cS=w.forwardRef((n,a)=>{const O=n,{__scopeSelect:s,className:l,style:c}=O,d=it(O,["__scopeSelect","className","style"]),f=Va(Cr,s),h=za(Cr,s),m=lS(Cr,s),p=xD(Cr,s),[g,v]=w.useState(null),b=ie(a,V=>v(V),m.onItemTextChange,V=>{var D;return(D=h.itemTextRefCallback)==null?void 0:D.call(h,V,m.value,m.disabled)}),T=g==null?void 0:g.textContent,M=w.useMemo(()=>x.jsx("option",{value:m.value,disabled:m.disabled,children:T},m.value),[m.disabled,m.value,T]),{onNativeOptionAdd:A,onNativeOptionRemove:E}=p;return Me(()=>(A(M),()=>E(M)),[A,E,M]),x.jsxs(x.Fragment,{children:[x.jsx(Pt.span,Z(N({id:m.textId},d),{ref:b})),m.isSelected&&f.valueNode&&!f.valueNodeHasChildren?Zr.createPortal(d.children,f.valueNode):null]})});cS.displayName=Cr;var uS="SelectItemIndicator",fS=w.forwardRef((n,a)=>{const d=n,{__scopeSelect:s}=d,l=it(d,["__scopeSelect"]);return lS(uS,s).isSelected?x.jsx(Pt.span,Z(N({"aria-hidden":!0},l),{ref:a})):null});fS.displayName=uS;var Ed="SelectScrollUpButton",dS=w.forwardRef((n,a)=>{const s=za(Ed,n.__scopeSelect),l=xh(Ed,n.__scopeSelect),[c,d]=w.useState(!1),f=ie(a,l.onScrollButtonChange);return Me(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollTop>0;d(p)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),c?x.jsx(mS,Z(N({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop-m.offsetHeight)}})):null});dS.displayName=Ed;var Md="SelectScrollDownButton",hS=w.forwardRef((n,a)=>{const s=za(Md,n.__scopeSelect),l=xh(Md,n.__scopeSelect),[c,d]=w.useState(!1),f=ie(a,l.onScrollButtonChange);return Me(()=>{if(s.viewport&&s.isPositioned){let h=function(){const p=m.scrollHeight-m.clientHeight,g=Math.ceil(m.scrollTop)<p;d(g)};const m=s.viewport;return h(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[s.viewport,s.isPositioned]),c?x.jsx(mS,Z(N({},n),{ref:f,onAutoScroll:()=>{const{viewport:h,selectedItem:m}=s;h&&m&&(h.scrollTop=h.scrollTop+m.offsetHeight)}})):null});hS.displayName=Md;var mS=w.forwardRef((n,a)=>{const p=n,{__scopeSelect:s,onAutoScroll:l}=p,c=it(p,["__scopeSelect","onAutoScroll"]),d=za("SelectScrollButton",s),f=w.useRef(null),h=tc(s),m=w.useCallback(()=>{f.current!==null&&(window.clearInterval(f.current),f.current=null)},[]);return w.useEffect(()=>()=>m(),[m]),Me(()=>{var v;const g=h().find(b=>b.ref.current===document.activeElement);(v=g==null?void 0:g.ref.current)==null||v.scrollIntoView({block:"nearest"})},[h]),x.jsx(Pt.div,Z(N({"aria-hidden":!0},c),{ref:a,style:N({flexShrink:0},c.style),onPointerDown:Bt(c.onPointerDown,()=>{f.current===null&&(f.current=window.setInterval(l,50))}),onPointerMove:Bt(c.onPointerMove,()=>{var g;(g=d.onItemLeave)==null||g.call(d),f.current===null&&(f.current=window.setInterval(l,50))}),onPointerLeave:Bt(c.onPointerLeave,()=>{m()})}))}),jD="SelectSeparator",_D=w.forwardRef((n,a)=>{const c=n,{__scopeSelect:s}=c,l=it(c,["__scopeSelect"]);return x.jsx(Pt.div,Z(N({"aria-hidden":!0},l),{ref:a}))});_D.displayName=jD;var Cd="SelectArrow",VD=w.forwardRef((n,a)=>{const h=n,{__scopeSelect:s}=h,l=it(h,["__scopeSelect"]),c=ec(s),d=Va(Cd,s),f=za(Cd,s);return d.open&&f.position==="popper"?x.jsx(bN,Z(N(N({},c),l),{ref:a})):null});VD.displayName=Cd;var zD="SelectBubbleInput",pS=w.forwardRef((c,l)=>{var d=c,{__scopeSelect:n,value:a}=d,s=it(d,["__scopeSelect","value"]);const f=w.useRef(null),h=ie(l,f),m=EN(a);return w.useEffect(()=>{const p=f.current;if(!p)return;const g=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(g,"value").set;if(m!==a&&b){const T=new Event("change",{bubbles:!0});b.call(p,a),p.dispatchEvent(T)}},[m,a]),x.jsx(Pt.select,Z(N({},s),{style:N(N({},Bb),s.style),ref:h,defaultValue:a}))});pS.displayName=zD;function gS(n){return n===""||n===void 0}function yS(n){const a=Da(n),s=w.useRef(""),l=w.useRef(0),c=w.useCallback(f=>{const h=s.current+f;a(h),function m(p){s.current=p,window.clearTimeout(l.current),p!==""&&(l.current=window.setTimeout(()=>m(""),1e3))}(h)},[a]),d=w.useCallback(()=>{s.current="",window.clearTimeout(l.current)},[]);return w.useEffect(()=>()=>window.clearTimeout(l.current),[]),[s,c,d]}function vS(n,a,s){const c=a.length>1&&Array.from(a).every(p=>p===a[0])?a[0]:a,d=s?n.indexOf(s):-1;let f=LD(n,Math.max(d,0));c.length===1&&(f=f.filter(p=>p!==s));const m=f.find(p=>p.textValue.toLowerCase().startsWith(c.toLowerCase()));return m!==s?m:void 0}function LD(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var BD=Zb,kD=Fb,UD=$b,HD=Wb,PD=Jb,GD=tS,qD=iS,YD=oS,XD=cS,KD=fS,ZD=dS,QD=hS;/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FD=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ID=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,s,l)=>l?l.toUpperCase():s.toLowerCase()),u0=n=>{const a=ID(n);return a.charAt(0).toUpperCase()+a.slice(1)},xS=(...n)=>n.filter((a,s,l)=>!!a&&a.trim()!==""&&l.indexOf(a)===s).join(" ").trim(),$D=n=>{for(const a in n)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var WD={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JD=w.forwardRef((p,m)=>{var g=p,{color:n="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:f}=g,h=it(g,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return w.createElement("svg",N(N(Z(N({ref:m},WD),{width:a,height:a,stroke:n,strokeWidth:l?Number(s)*24/Number(a):s,className:xS("lucide",c)}),!d&&!$D(h)&&{"aria-hidden":"true"}),h),[...f.map(([v,b])=>w.createElement(v,b)),...Array.isArray(d)?d:[d]])});/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=(n,a)=>{const s=w.forwardRef((f,d)=>{var h=f,{className:l}=h,c=it(h,["className"]);return w.createElement(JD,N({ref:d,iconNode:a,className:xS(`lucide-${FD(u0(n))}`,`lucide-${n}`,l)},c))});return s.displayName=u0(n),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tO=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],eO=De("building",tO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nO=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],aO=De("calendar",nO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iO=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],sO=De("check",iO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rO=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],bS=De("chevron-down",rO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lO=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],oO=De("chevron-up",lO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cO=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Eo=De("circle-alert",cO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uO=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],f0=De("circle-check-big",uO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fO=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],d0=De("log-out",fO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dO=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],hO=De("refresh-cw",dO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mO=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],pO=De("search",mO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gO=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],yO=De("settings",gO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vO=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],xO=De("sparkles",vO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bO=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],SO=De("user-plus",bO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TO=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],wO=De("user",TO);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AO=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],EO=De("users",AO);function MO(a){var n=it(a,[]);return x.jsx(BD,N({"data-slot":"select"},n))}function CO(a){var n=it(a,[]);return x.jsx(UD,N({"data-slot":"select-value"},n))}function RO(c){var d=c,{className:n,size:a="default",children:s}=d,l=it(d,["className","size","children"]);return x.jsxs(kD,Z(N({"data-slot":"select-trigger","data-size":a,className:Kt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},l),{children:[s,x.jsx(HD,{asChild:!0,children:x.jsx(bS,{className:"size-4 opacity-50"})})]}))}function NO(c){var d=c,{className:n,children:a,position:s="popper"}=d,l=it(d,["className","children","position"]);return x.jsx(PD,{children:x.jsxs(GD,Z(N({"data-slot":"select-content",className:Kt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:s},l),{children:[x.jsx(OO,{}),x.jsx(qD,{className:Kt("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),x.jsx(jO,{})]}))})}function DO(l){var c=l,{className:n,children:a}=c,s=it(c,["className","children"]);return x.jsxs(YD,Z(N({"data-slot":"select-item",className:Kt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n)},s),{children:[x.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:x.jsx(KD,{children:x.jsx(sO,{className:"size-4"})})}),x.jsx(XD,{children:a})]}))}function OO(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(ZD,Z(N({"data-slot":"select-scroll-up-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:x.jsx(oO,{className:"size-4"})}))}function jO(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(QD,Z(N({"data-slot":"select-scroll-down-button",className:Kt("flex cursor-default items-center justify-center py-1",n)},a),{children:x.jsx(bS,{className:"size-4"})}))}function Xf(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:x.jsx("table",N({"data-slot":"table",className:Kt("w-full caption-bottom text-sm",n)},a))})}function Kf(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("thead",N({"data-slot":"table-header",className:Kt("[&_tr]:border-b",n)},a))}function Zf(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("tbody",N({"data-slot":"table-body",className:Kt("[&_tr:last-child]:border-0",n)},a))}function Mo(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("tr",N({"data-slot":"table-row",className:Kt("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",n)},a))}function oe(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("th",N({"data-slot":"table-head",className:Kt("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}function ce(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx("td",N({"data-slot":"table-cell",className:Kt("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",n)},a))}const _O=$x("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function wr(c){var d=c,{className:n,variant:a,asChild:s=!1}=d,l=it(d,["className","variant","asChild"]);const f=s?Qx:"span";return x.jsx(f,N({"data-slot":"badge",className:Kt(_O({variant:a}),n)},l))}var Qf="rovingFocusGroup.onEntryFocus",VO={bubbles:!1,cancelable:!0},$r="RovingFocusGroup",[Rd,SS,zO]=cb($r),[LO,TS]=Qr($r,[zO]),[BO,kO]=LO($r),wS=w.forwardRef((n,a)=>x.jsx(Rd.Provider,{scope:n.__scopeRovingFocusGroup,children:x.jsx(Rd.Slot,{scope:n.__scopeRovingFocusGroup,children:x.jsx(UO,Z(N({},n),{ref:a}))})}));wS.displayName=$r;var UO=w.forwardRef((n,a)=>{const q=n,{__scopeRovingFocusGroup:s,orientation:l,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:g=!1}=q,v=it(q,["__scopeRovingFocusGroup","orientation","loop","dir","currentTabStopId","defaultCurrentTabStopId","onCurrentTabStopIdChange","onEntryFocus","preventScrollOnEntryFocus"]),b=w.useRef(null),T=ie(a,b),M=ch(d),[A,E]=Yo({prop:f,defaultProp:h!=null?h:null,onChange:m,caller:$r}),[O,V]=w.useState(!1),D=Da(p),L=SS(s),j=w.useRef(!1),[$,et]=w.useState(0);return w.useEffect(()=>{const W=b.current;if(W)return W.addEventListener(Qf,D),()=>W.removeEventListener(Qf,D)},[D]),x.jsx(BO,{scope:s,orientation:l,dir:M,loop:c,currentTabStopId:A,onItemFocus:w.useCallback(W=>E(W),[E]),onItemShiftTab:w.useCallback(()=>V(!0),[]),onFocusableItemAdd:w.useCallback(()=>et(W=>W+1),[]),onFocusableItemRemove:w.useCallback(()=>et(W=>W-1),[]),children:x.jsx(Pt.div,Z(N({tabIndex:O||$===0?-1:0,"data-orientation":l},v),{ref:T,style:N({outline:"none"},n.style),onMouseDown:Bt(n.onMouseDown,()=>{j.current=!0}),onFocus:Bt(n.onFocus,W=>{const Y=!j.current;if(W.target===W.currentTarget&&Y&&!O){const J=new CustomEvent(Qf,VO);if(W.currentTarget.dispatchEvent(J),!J.defaultPrevented){const nt=L().filter(X=>X.focusable),ut=nt.find(X=>X.active),yt=nt.find(X=>X.id===A),B=[ut,yt,...nt].filter(Boolean).map(X=>X.ref.current);MS(B,g)}}j.current=!1}),onBlur:Bt(n.onBlur,()=>V(!1))}))})}),AS="RovingFocusGroupItem",ES=w.forwardRef((n,a)=>{const E=n,{__scopeRovingFocusGroup:s,focusable:l=!0,active:c=!1,tabStopId:d,children:f}=E,h=it(E,["__scopeRovingFocusGroup","focusable","active","tabStopId","children"]),m=Fr(),p=d||m,g=kO(AS,s),v=g.currentTabStopId===p,b=SS(s),{onFocusableItemAdd:T,onFocusableItemRemove:M,currentTabStopId:A}=g;return w.useEffect(()=>{if(l)return T(),()=>M()},[l,T,M]),x.jsx(Rd.ItemSlot,{scope:s,id:p,focusable:l,active:c,children:x.jsx(Pt.span,Z(N({tabIndex:v?0:-1,"data-orientation":g.orientation},h),{ref:a,onMouseDown:Bt(n.onMouseDown,O=>{l?g.onItemFocus(p):O.preventDefault()}),onFocus:Bt(n.onFocus,()=>g.onItemFocus(p)),onKeyDown:Bt(n.onKeyDown,O=>{if(O.key==="Tab"&&O.shiftKey){g.onItemShiftTab();return}if(O.target!==O.currentTarget)return;const V=GO(O,g.orientation,g.dir);if(V!==void 0){if(O.metaKey||O.ctrlKey||O.altKey||O.shiftKey)return;O.preventDefault();let L=b().filter(j=>j.focusable).map(j=>j.ref.current);if(V==="last")L.reverse();else if(V==="prev"||V==="next"){V==="prev"&&L.reverse();const j=L.indexOf(O.currentTarget);L=g.loop?qO(L,j+1):L.slice(j+1)}setTimeout(()=>MS(L))}}),children:typeof f=="function"?f({isCurrentTabStop:v,hasTabStop:A!=null}):f}))})});ES.displayName=AS;var HO={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function PO(n,a){return a!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function GO(n,a,s){const l=PO(n.key,s);if(!(a==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(a==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return HO[l]}function MS(n,a=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:a}),document.activeElement!==s))return}function qO(n,a){return n.map((s,l)=>n[(a+l)%n.length])}var YO=wS,XO=ES;function KO(n,a){return w.useReducer((s,l)=>{const c=a[s][l];return c!=null?c:s},n)}var CS=n=>{const{present:a,children:s}=n,l=ZO(a),c=typeof s=="function"?s({present:l.isPresent}):w.Children.only(s),d=ie(l.ref,QO(c));return typeof s=="function"||l.isPresent?w.cloneElement(c,{ref:d}):null};CS.displayName="Presence";function ZO(n){const[a,s]=w.useState(),l=w.useRef(null),c=w.useRef(n),d=w.useRef("none"),f=n?"mounted":"unmounted",[h,m]=KO(f,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const p=Co(l.current);d.current=h==="mounted"?p:"none"},[h]),Me(()=>{const p=l.current,g=c.current;if(g!==n){const b=d.current,T=Co(p);n?m("MOUNT"):T==="none"||(p==null?void 0:p.display)==="none"?m("UNMOUNT"):m(g&&b!==T?"ANIMATION_OUT":"UNMOUNT"),c.current=n}},[n,m]),Me(()=>{var p;if(a){let g;const v=(p=a.ownerDocument.defaultView)!=null?p:window,b=M=>{const E=Co(l.current).includes(M.animationName);if(M.target===a&&E&&(m("ANIMATION_END"),!c.current)){const O=a.style.animationFillMode;a.style.animationFillMode="forwards",g=v.setTimeout(()=>{a.style.animationFillMode==="forwards"&&(a.style.animationFillMode=O)})}},T=M=>{M.target===a&&(d.current=Co(l.current))};return a.addEventListener("animationstart",T),a.addEventListener("animationcancel",b),a.addEventListener("animationend",b),()=>{v.clearTimeout(g),a.removeEventListener("animationstart",T),a.removeEventListener("animationcancel",b),a.removeEventListener("animationend",b)}}else m("ANIMATION_END")},[a,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(p=>{l.current=p?getComputedStyle(p):null,s(p)},[])}}function Co(n){return(n==null?void 0:n.animationName)||"none"}function QO(n){var l,c;let a=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?n.ref:(a=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var nc="Tabs",[FO,oj]=Qr(nc,[TS]),RS=TS(),[IO,bh]=FO(nc),NS=w.forwardRef((n,a)=>{const T=n,{__scopeTabs:s,value:l,onValueChange:c,defaultValue:d,orientation:f="horizontal",dir:h,activationMode:m="automatic"}=T,p=it(T,["__scopeTabs","value","onValueChange","defaultValue","orientation","dir","activationMode"]),g=ch(h),[v,b]=Yo({prop:l,onChange:c,defaultProp:d!=null?d:"",caller:nc});return x.jsx(IO,{scope:s,baseId:Fr(),value:v,onValueChange:b,orientation:f,dir:g,activationMode:m,children:x.jsx(Pt.div,Z(N({dir:g,"data-orientation":f},p),{ref:a}))})});NS.displayName=nc;var DS="TabsList",OS=w.forwardRef((n,a)=>{const h=n,{__scopeTabs:s,loop:l=!0}=h,c=it(h,["__scopeTabs","loop"]),d=bh(DS,s),f=RS(s);return x.jsx(YO,Z(N({asChild:!0},f),{orientation:d.orientation,dir:d.dir,loop:l,children:x.jsx(Pt.div,Z(N({role:"tablist","aria-orientation":d.orientation},c),{ref:a}))}))});OS.displayName=DS;var jS="TabsTrigger",_S=w.forwardRef((n,a)=>{const v=n,{__scopeTabs:s,value:l,disabled:c=!1}=v,d=it(v,["__scopeTabs","value","disabled"]),f=bh(jS,s),h=RS(s),m=LS(f.baseId,l),p=BS(f.baseId,l),g=l===f.value;return x.jsx(XO,Z(N({asChild:!0},h),{focusable:!c,active:g,children:x.jsx(Pt.button,Z(N({type:"button",role:"tab","aria-selected":g,"aria-controls":p,"data-state":g?"active":"inactive","data-disabled":c?"":void 0,disabled:c,id:m},d),{ref:a,onMouseDown:Bt(n.onMouseDown,b=>{!c&&b.button===0&&b.ctrlKey===!1?f.onValueChange(l):b.preventDefault()}),onKeyDown:Bt(n.onKeyDown,b=>{[" ","Enter"].includes(b.key)&&f.onValueChange(l)}),onFocus:Bt(n.onFocus,()=>{const b=f.activationMode!=="manual";!g&&!c&&b&&f.onValueChange(l)})}))}))});_S.displayName=jS;var VS="TabsContent",zS=w.forwardRef((n,a)=>{const b=n,{__scopeTabs:s,value:l,forceMount:c,children:d}=b,f=it(b,["__scopeTabs","value","forceMount","children"]),h=bh(VS,s),m=LS(h.baseId,l),p=BS(h.baseId,l),g=l===h.value,v=w.useRef(g);return w.useEffect(()=>{const T=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(T)},[]),x.jsx(CS,{present:c||g,children:({present:T})=>x.jsx(Pt.div,Z(N({"data-state":g?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":m,hidden:!T,id:p,tabIndex:0},f),{ref:a,style:Z(N({},n.style),{animationDuration:v.current?"0s":void 0}),children:T&&d}))})});zS.displayName=VS;function LS(n,a){return`${n}-trigger-${a}`}function BS(n,a){return`${n}-content-${a}`}var $O=NS,WO=OS,JO=_S,tj=zS;function ej(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx($O,N({"data-slot":"tabs",className:Kt("flex flex-col gap-2",n)},a))}function nj(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(WO,N({"data-slot":"tabs-list",className:Kt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n)},a))}function Ar(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(JO,N({"data-slot":"tabs-trigger",className:Kt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n)},a))}function Er(s){var l=s,{className:n}=l,a=it(l,["className"]);return x.jsx(tj,N({"data-slot":"tabs-content",className:Kt("flex-1 outline-none",n)},a))}function h0(n,a){const[s,l]=w.useState(()=>{try{const d=window.localStorage.getItem(n);if(d){const f=JSON.parse(d);return Array.isArray(f)&&n.includes("employees")?f.filter(h=>h.id&&h.name&&h.department&&h.startMonth):f}return a}catch(d){console.error(`خطأ في قراءة ${n} من localStorage:`,d);try{const f=`${n}_backup`,h=window.localStorage.getItem(f);if(h)return console.log(`استرداد البيانات من النسخة الاحتياطية: ${f}`),JSON.parse(h)}catch(f){console.error("خطأ في استرداد النسخة الاحتياطية:",f)}return a}});return[s,d=>{try{const f=d instanceof Function?d(s):d;if(s&&JSON.stringify(s)!==JSON.stringify(a)){const h=`${n}_backup`;window.localStorage.setItem(h,JSON.stringify(s))}l(f),window.localStorage.setItem(n,JSON.stringify(f)),window.localStorage.setItem(`${n}_lastUpdate`,new Date().toISOString())}catch(f){if(console.error(`خطأ في حفظ ${n} في localStorage:`,f),f.name==="QuotaExceededError"){console.log("محاولة تنظيف localStorage...");try{Object.keys(localStorage).forEach(h=>{h.includes("_backup")&&!h.includes(n)&&localStorage.removeItem(h)}),window.localStorage.setItem(n,JSON.stringify(valueToStore)),l(valueToStore)}catch(h){console.error("فشل في الحفظ حتى بعد التنظيف:",h)}}}}]}function aj(){const[n,a]=w.useState(!0),[s,l]=w.useState(!1),[c,d]=h0("work-attendance-employees",[]),[f,h]=w.useState({name:"",department:"",startMonth:""}),[m,p]=h0("work-attendance-search-filters",{name:"",department:"",startMonth:"",status:""}),[g,v]=w.useState("input"),b=["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"];w.useEffect(()=>{const Y=setTimeout(()=>{a(!1),setTimeout(()=>{A()>0&&l(!0)},500)},4e3);return()=>clearTimeout(Y)},[c]),w.useEffect(()=>{const Y=setInterval(()=>{if(c.length>0)try{const J=localStorage.getItem("work-attendance-employees_lastUpdate"),nt=new Date().toISOString();(!J||new Date(nt)-new Date(J)>3e4)&&(localStorage.setItem("work-attendance-employees",JSON.stringify(c)),localStorage.setItem("work-attendance-employees_lastUpdate",nt),console.log("تم الحفظ التلقائي للبيانات"))}catch(J){console.error("خطأ في الحفظ التلقائي:",J)}},3e4);return()=>clearInterval(Y)},[c]),w.useEffect(()=>{const Y=J=>{try{localStorage.setItem("work-attendance-employees",JSON.stringify(c)),localStorage.setItem("work-attendance-search-filters",JSON.stringify(m)),console.log("تم حفظ البيانات قبل إغلاق النافذة")}catch(nt){console.error("خطأ في حفظ البيانات قبل الإغلاق:",nt)}};return window.addEventListener("beforeunload",Y),()=>window.removeEventListener("beforeunload",Y)},[c,m]),w.useEffect(()=>{const Y=J=>{(J.ctrlKey&&J.key==="q"||J.altKey&&J.key==="F4")&&(J.preventDefault(),q())};return document.addEventListener("keydown",Y),()=>document.removeEventListener("keydown",Y)},[]);const T=Y=>{const J=new Date().getMonth(),nt=b.indexOf(Y);return(J-nt+12)%12>=3},M=()=>{const Y=new Date().getMonth();return c.filter(J=>{const nt=b.indexOf(J.startMonth);return(Y-nt+12)%12===2&&!J.status})},A=()=>M().length,E=Y=>{if(Y.preventDefault(),f.name&&f.department&&f.startMonth){const J=Z(N({id:Date.now()},f),{status:T(f.startMonth),createdAt:new Date().toISOString(),lastModified:new Date().toISOString()});d([...c,J]),h({name:"",department:"",startMonth:""}),j(`✅ تم حفظ بيانات الموظف "${J.name}" بنجاح!`,"success"),setTimeout(()=>{const nt=localStorage.getItem("work-attendance-employees");nt&&JSON.parse(nt).find(ut=>ut.id===J.id)&&console.log("تأكيد: تم حفظ البيانات في التخزين المحلي")},100)}},O=()=>{d(c.map(Y=>Z(N({},Y),{status:T(Y.startMonth)})))},V=()=>{const Y={employees:c,exportDate:new Date().toISOString(),version:"1.0"},J=JSON.stringify(Y,null,2),nt=new Blob([J],{type:"application/json"}),ut=document.createElement("a");ut.href=URL.createObjectURL(nt),ut.download=`work-attendance-backup-${new Date().toISOString().split("T")[0]}.json`,ut.click(),j("📁 تم تصدير البيانات بنجاح!","success")},D=(Y=null)=>{const J=Y||c;if(J.length===0){j("❌ لا توجد بيانات للتصدير!","error");return}const nt=`
      <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <!--[if gte mso 9]>
        <xml>
          <x:ExcelWorkbook>
            <x:ExcelWorksheets>
              <x:ExcelWorksheet>
                <x:Name>كشف الحضور</x:Name>
                <x:WorksheetOptions>
                  <x:DisplayGridlines/>
                </x:WorksheetOptions>
              </x:ExcelWorksheet>
            </x:ExcelWorksheets>
          </x:ExcelWorkbook>
        </xml>
        <![endif]-->
        <style>
          table {
            border-collapse: collapse;
            width: 100%;
            direction: rtl;
            font-size: 14px;
            table-layout: auto;
          }
          th, td {
            border: 1px solid #000;
            padding: 6px 8px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
          }
          th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
            font-size: 15px;
          }
          .status-required {
            background-color: #ffebee;
            color: #c62828;
            font-weight: bold;
          }
          .status-completed {
            background-color: #e8f5e8;
            color: #2e7d32;
            font-weight: bold;
          }
          /* تحسين عرض الأعمدة */
          th:nth-child(1), td:nth-child(1) { width: 60px; } /* الرقم */
          th:nth-child(2), td:nth-child(2) { width: 150px; } /* اسم الموظف */
          th:nth-child(3), td:nth-child(3) { width: 120px; } /* القسم */
          th:nth-child(4), td:nth-child(4) { width: 100px; } /* أول شهر */
          th:nth-child(5), td:nth-child(5) { width: 130px; } /* الحالة */
          th:nth-child(6), td:nth-child(6) { width: 110px; } /* التاريخ */
        </style>
      </head>
      <body>
        <table>
          <thead>
            <tr>
              <th>الرقم</th>
              <th>اسم الموظف</th>
              <th>القسم</th>
              <th>أول شهر عمل</th>
              <th>حالة الاستحقاق</th>
              <th>تاريخ التصدير</th>
            </tr>
          </thead>
          <tbody>
            ${J.map((ft,B)=>`
              <tr>
                <td>${B+1}</td>
                <td>${ft.name}</td>
                <td>${ft.department}</td>
                <td>${ft.startMonth}</td>
                <td class="${ft.status?"status-required":"status-completed"}">
                  ${ft.status?"مطلوب كشف":"تم استلام الكشف"}
                </td>
                <td>${new Date().toLocaleDateString("en-GB")}</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </body>
      </html>
    `,ut=new Blob(["\uFEFF",nt],{type:"application/vnd.ms-excel;charset=utf-8"}),yt=document.createElement("a");yt.href=URL.createObjectURL(ut),yt.download=`كشف-الحضور-${new Date().toISOString().split("T")[0]}.xls`,yt.click(),URL.revokeObjectURL(yt.href),j(`📊 تم تصدير ${J.length} موظف إلى Excel بنجاح!`,"success")},L=Y=>{const J=Y.target.files[0];if(!J)return;const nt=new FileReader;nt.onload=ut=>{try{const yt=JSON.parse(ut.target.result);yt.employees&&Array.isArray(yt.employees)?(d(yt.employees),j("📥 تم استيراد البيانات بنجاح!","success")):j("❌ ملف غير صالح!","error")}catch(yt){console.error("خطأ في استيراد البيانات:",yt),j("❌ خطأ في قراءة الملف!","error")}},nt.readAsText(J),Y.target.value=""},j=(Y,J="success")=>{const nt=document.createElement("div"),ut=J==="success"?"bg-green-500":"bg-red-500";nt.className=`fixed top-4 right-4 ${ut} text-white px-6 py-3 rounded-lg shadow-lg z-50`,nt.textContent=Y,document.body.appendChild(nt),setTimeout(()=>{document.body.contains(nt)&&document.body.removeChild(nt)},3e3)},$=()=>{window.confirm("هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!")&&(d([]),p({name:"",department:"",startMonth:"",status:""}),j("🗑️ تم مسح جميع البيانات!","success"))},et=Y=>{const J=c.find(nt=>nt.id===Y);J&&window.confirm(`هل أنت متأكد من حذف الموظف "${J.name}"؟`)&&(d(c.filter(nt=>nt.id!==Y)),j(`🗑️ تم حذف الموظف "${J.name}" بنجاح!`,"success"))},q=()=>{window.confirm(`هل تريد إغلاق البرنامج؟

سيتم حفظ جميع البيانات تلقائياً.`)&&(j("👋 شكراً لاستخدام البرنامج!","success"),setTimeout(()=>{try{window.close()}catch(Y){try{self.close()}catch(J){window.location.href="about:blank"}}},1e3))},W=c.filter(Y=>(!m.name||Y.name.includes(m.name))&&(!m.department||Y.department.includes(m.department))&&(!m.startMonth||Y.startMonth.includes(m.startMonth))&&(!m.status||m.status.includes("مطلوب")&&Y.status||m.status.includes("تم")&&!Y.status));return n?x.jsx("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:x.jsxs(Ke.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1},className:"text-center",children:[x.jsx(Ke.h1,{className:"text-6xl font-bold text-white mb-8 typewriter",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"🌟 برنامج استحقاق كشف عمل 🌟"}),x.jsx(Ke.p,{className:"text-2xl text-white mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:"👨‍💻 المبرمج: علي عاجل خشان المحنّة"}),x.jsx(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:x.jsx(me,{onClick:()=>a(!1),className:"pulse-glow bg-white text-purple-600 hover:bg-gray-100 text-xl px-8 py-4",children:"ابدأ الآن"})})]})}):x.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900",children:[x.jsxs("div",{className:"flex",children:[x.jsxs(Ke.div,{initial:{x:-300},animate:{x:0},className:"w-80 min-h-screen glassmorphism p-6",children:[x.jsxs("div",{className:"mb-8",children:[x.jsx("h2",{className:"text-2xl font-bold gradient-text mb-2",children:"برنامج استحقاق كشف عمل"}),x.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"👨‍💻 تصميم و برمجة: علي عاجل خشان المحنّة"})]}),x.jsxs(me,{variant:"destructive",className:"w-full mb-6 hover-lift",onClick:q,children:[x.jsx(d0,{className:"mr-2 h-4 w-4"}),"خروج"]})]}),x.jsx("div",{className:"flex-1 p-8",children:x.jsxs(ej,{value:g,onValueChange:v,className:"w-full",children:[x.jsxs(nj,{className:"grid w-full grid-cols-5 mb-8",children:[x.jsxs(Ar,{value:"input",className:"flex items-center gap-2",children:[x.jsx(SO,{className:"h-4 w-4"}),"إدخال البيانات"]}),x.jsxs(Ar,{value:"employees",className:"flex items-center gap-2",children:[x.jsx(EO,{className:"h-4 w-4"}),"المستحقين للكشوفات"]}),x.jsxs(Ar,{value:"search",className:"flex items-center gap-2",children:[x.jsx(pO,{className:"h-4 w-4"}),"البحث المتقدم"]}),x.jsxs(Ar,{value:"upcoming",className:"flex items-center gap-2 relative",children:[x.jsx(Eo,{className:"h-4 w-4"}),"إشعارات الاستحقاق",A()>0&&x.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:A()})]}),x.jsxs(Ar,{value:"settings",className:"flex items-center gap-2",children:[x.jsx(yO,{className:"h-4 w-4"}),"الإعدادات"]})]}),x.jsx(Er,{value:"input",children:x.jsx(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:x.jsxs(hn,{className:"max-w-2xl mx-auto neomorphism hover-lift",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-center gradient-text text-2xl",children:"📥 إدخال بيانات موظف جديد"})}),x.jsx(gn,{children:x.jsxs("form",{onSubmit:E,className:"space-y-6",children:[x.jsxs("div",{className:"space-y-2",children:[x.jsxs(ai,{htmlFor:"name",className:"flex items-center gap-2",children:[x.jsx(wO,{className:"h-4 w-4"}),"اسم الموظف"]}),x.jsx(ts,{id:"name",value:f.name,onChange:Y=>h(Z(N({},f),{name:Y.target.value})),placeholder:"أدخل اسم الموظف",className:"text-right",required:!0})]}),x.jsxs("div",{className:"space-y-2",children:[x.jsxs(ai,{htmlFor:"department",className:"flex items-center gap-2",children:[x.jsx(eO,{className:"h-4 w-4"}),"القسم"]}),x.jsx(ts,{id:"department",value:f.department,onChange:Y=>h(Z(N({},f),{department:Y.target.value})),placeholder:"أدخل اسم القسم",className:"text-right",required:!0})]}),x.jsxs("div",{className:"space-y-2",children:[x.jsxs(ai,{htmlFor:"startMonth",className:"flex items-center gap-2",children:[x.jsx(aO,{className:"h-4 w-4"}),"أول شهر للكشف 🌙"]}),x.jsxs(MO,{value:f.startMonth,onValueChange:Y=>h(Z(N({},f),{startMonth:Y})),required:!0,children:[x.jsx(RO,{children:x.jsx(CO,{placeholder:"اختر الشهر"})}),x.jsx(NO,{children:b.map(Y=>x.jsx(DO,{value:Y,children:Y},Y))})]})]}),x.jsx(me,{type:"submit",className:"w-full bg-gradient-to-r from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 pulse-glow text-lg py-6",children:"➕ حفظ البيانات"})]})})]})})}),x.jsx(Er,{value:"employees",children:x.jsx(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:x.jsxs(hn,{className:"neomorphism",children:[x.jsxs(mn,{className:"flex flex-row items-center justify-between",children:[x.jsx(pn,{className:"gradient-text text-2xl",children:"📄 المستحقين للكشوفات"}),x.jsxs(me,{onClick:O,className:"bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500",children:[x.jsx(hO,{className:"mr-2 h-4 w-4"}),"🔁 تحديث الكشفات"]})]}),x.jsx(gn,{children:c.length===0?x.jsxs("div",{className:"text-center py-12",children:[x.jsx(xO,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),x.jsx("p",{className:"text-gray-500 text-lg",children:"لا توجد بيانات موظفين حتى الآن"})]}):x.jsxs(Xf,{children:[x.jsx(Kf,{children:x.jsxs(Mo,{children:[x.jsx(oe,{className:"text-right",children:"#"}),x.jsx(oe,{className:"text-right",children:"اسم الموظف"}),x.jsx(oe,{className:"text-right",children:"القسم"}),x.jsx(oe,{className:"text-right",children:"أول شهر"}),x.jsx(oe,{className:"text-right",children:"الحالة"}),x.jsx(oe,{className:"text-right",children:"إجراءات"})]})}),x.jsx(Zf,{children:c.map((Y,J)=>x.jsxs(Ke.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:J*.1},className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[x.jsx(ce,{children:J+1}),x.jsx(ce,{className:"font-medium",children:Y.name}),x.jsx(ce,{children:Y.department}),x.jsx(ce,{children:Y.startMonth}),x.jsx(ce,{children:x.jsx(wr,{className:Y.status?"status-required":"status-completed",children:Y.status?x.jsxs(x.Fragment,{children:[x.jsx(Eo,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):x.jsxs(x.Fragment,{children:[x.jsx(f0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),x.jsx(ce,{children:x.jsx(me,{variant:"destructive",size:"sm",onClick:()=>et(Y.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},Y.id))})]})})]})})}),x.jsx(Er,{value:"search",children:x.jsxs(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:[x.jsxs(hn,{className:"neomorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"gradient-text text-2xl",children:"🔍 البحث المتقدم"})}),x.jsxs(gn,{children:[x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[x.jsxs("div",{children:[x.jsx(ai,{children:"البحث بالاسم"}),x.jsx(ts,{placeholder:"🔎 اسم الموظف",value:m.name,onChange:Y=>p(Z(N({},m),{name:Y.target.value})),className:"text-right"})]}),x.jsxs("div",{children:[x.jsx(ai,{children:"القسم"}),x.jsx(ts,{placeholder:"🏢 اسم القسم",value:m.department,onChange:Y=>p(Z(N({},m),{department:Y.target.value})),className:"text-right"})]}),x.jsxs("div",{children:[x.jsx(ai,{children:"أول شهر"}),x.jsx(ts,{placeholder:"🗓️ اسم الشهر",value:m.startMonth,onChange:Y=>p(Z(N({},m),{startMonth:Y.target.value})),className:"text-right"})]}),x.jsxs("div",{children:[x.jsx(ai,{children:"الحالة"}),x.jsx(ts,{placeholder:"🔍 الحالة (مطلوب/تم)",value:m.status,onChange:Y=>p(Z(N({},m),{status:Y.target.value})),className:"text-right"})]})]}),x.jsxs("div",{className:"flex flex-wrap gap-4 mt-6 pt-4 border-t",children:[x.jsx(me,{onClick:()=>D(W),className:"bg-green-500 hover:bg-green-600 text-white flex items-center gap-2",disabled:W.length===0,children:"📊 تصدير إلى Excel"}),x.jsx(me,{onClick:()=>D(),className:"bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2",children:"📋 تصدير جميع البيانات"}),x.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:["📈 عدد النتائج: ",W.length," من أصل ",c.length]})]})]})]}),x.jsxs(hn,{className:"neomorphism",children:[x.jsx(mn,{children:x.jsx(pn,{children:"نتائج البحث"})}),x.jsx(gn,{children:W.length===0?x.jsx("div",{className:"text-center py-8",children:x.jsx("p",{className:"text-gray-500",children:"🚫 لا توجد بيانات مطابقة!"})}):x.jsxs(Xf,{children:[x.jsx(Kf,{children:x.jsxs(Mo,{children:[x.jsx(oe,{className:"text-right",children:"#"}),x.jsx(oe,{className:"text-right",children:"اسم الموظف"}),x.jsx(oe,{className:"text-right",children:"القسم"}),x.jsx(oe,{className:"text-right",children:"أول شهر"}),x.jsx(oe,{className:"text-right",children:"الحالة"}),x.jsx(oe,{className:"text-right",children:"إجراءات"})]})}),x.jsx(Zf,{children:W.map((Y,J)=>x.jsxs(Mo,{children:[x.jsx(ce,{children:J+1}),x.jsx(ce,{className:"font-medium",children:Y.name}),x.jsx(ce,{children:Y.department}),x.jsx(ce,{children:Y.startMonth}),x.jsx(ce,{children:x.jsx(wr,{className:Y.status?"status-required":"status-completed",children:Y.status?x.jsxs(x.Fragment,{children:[x.jsx(Eo,{className:"mr-1 h-3 w-3"}),"🔴 مطلوب كشف"]}):x.jsxs(x.Fragment,{children:[x.jsx(f0,{className:"mr-1 h-3 w-3"}),"🟢 تم التسليم"]})})}),x.jsx(ce,{children:x.jsx(me,{variant:"destructive",size:"sm",onClick:()=>et(Y.id),className:"h-8 px-3",children:"🗑️ حذف"})})]},Y.id))})]})})]})]})}),x.jsx(Er,{value:"upcoming",children:x.jsx(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"space-y-6",children:x.jsxs(hn,{className:"neomorphism",children:[x.jsxs(mn,{children:[x.jsxs(pn,{className:"gradient-text text-2xl flex items-center gap-2",children:["🔔 إشعارات قرب الاستحقاق",A()>0&&x.jsxs(wr,{className:"bg-orange-500 text-white",children:[A()," موظف"]})]}),x.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"الموظفون الذين سيستحقون كشف العمل خلال شهر واحد"})]}),x.jsx(gn,{children:M().length===0?x.jsxs("div",{className:"text-center py-12",children:[x.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),x.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"لا توجد إشعارات جديدة!"}),x.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"جميع الموظفين إما لم يحن موعد استحقاقهم بعد أو تم تسليم كشوفاتهم"})]}):x.jsxs(x.Fragment,{children:[x.jsxs("div",{className:"mb-6 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800",children:[x.jsxs("div",{className:"flex items-center gap-2 text-orange-700 dark:text-orange-300",children:[x.jsx(Eo,{className:"h-5 w-5"}),x.jsx("span",{className:"font-semibold",children:"تنبيه مهم:"})]}),x.jsx("p",{className:"text-orange-600 dark:text-orange-400 mt-1",children:"الموظفون التالون سيستحقون كشف العمل خلال شهر واحد. يُنصح بالتحضير مسبقاً."})]}),x.jsxs(Xf,{children:[x.jsx(Kf,{children:x.jsxs(Mo,{children:[x.jsx(oe,{className:"text-right",children:"#"}),x.jsx(oe,{className:"text-right",children:"اسم الموظف"}),x.jsx(oe,{className:"text-right",children:"القسم"}),x.jsx(oe,{className:"text-right",children:"أول شهر عمل"}),x.jsx(oe,{className:"text-right",children:"الأشهر المكتملة"}),x.jsx(oe,{className:"text-right",children:"موعد الاستحقاق المتوقع"}),x.jsx(oe,{className:"text-right",children:"إجراءات"})]})}),x.jsx(Zf,{children:M().map((Y,J)=>{const nt=b.indexOf(Y.startMonth),ut=b[(nt+3)%12];return x.jsxs(Ke.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:J*.1},className:"hover:bg-orange-50 dark:hover:bg-orange-900/10",children:[x.jsx(ce,{children:J+1}),x.jsx(ce,{className:"font-medium",children:Y.name}),x.jsx(ce,{children:Y.department}),x.jsx(ce,{children:Y.startMonth}),x.jsx(ce,{children:x.jsx(wr,{className:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:"شهرين مكتملين"})}),x.jsx(ce,{children:x.jsx(wr,{className:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",children:ut})}),x.jsx(ce,{children:x.jsxs("div",{className:"flex gap-2",children:[x.jsx(me,{variant:"outline",size:"sm",onClick:()=>{v("employees")},className:"h-8 px-3",children:"👁️ عرض"}),x.jsx(me,{variant:"destructive",size:"sm",onClick:()=>et(Y.id),className:"h-8 px-3",children:"🗑️ حذف"})]})})]},Y.id)})})]}),x.jsxs("div",{className:"mt-6 flex flex-wrap gap-4",children:[x.jsx(me,{onClick:()=>D(M()),className:"bg-orange-500 hover:bg-orange-600 text-white flex items-center gap-2",children:"📊 تصدير قائمة الإشعارات"}),x.jsx(me,{onClick:()=>v("input"),className:"bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2",children:"➕ إضافة موظف جديد"})]})]})})]})})}),x.jsx(Er,{value:"settings",children:x.jsx(Ke.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto space-y-6",children:x.jsxs(hn,{className:"neomorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"gradient-text text-2xl",children:"⚙️ الإعدادات"})}),x.jsx(gn,{className:"space-y-6",children:x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[x.jsxs(hn,{className:"glassmorphism md:col-span-2",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-lg",children:"💾 إدارة البيانات"})}),x.jsxs(gn,{children:[x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[x.jsx(me,{onClick:V,className:"bg-blue-500 hover:bg-blue-600 text-white",children:"📁 تصدير البيانات"}),x.jsxs("div",{children:[x.jsx("input",{type:"file",accept:".json",onChange:L,style:{display:"none"},id:"import-file"}),x.jsx(me,{onClick:()=>document.getElementById("import-file").click(),className:"bg-green-500 hover:bg-green-600 text-white w-full",children:"📥 استيراد البيانات"})]}),x.jsx(me,{onClick:$,className:"bg-red-500 hover:bg-red-600 text-white",children:"🗑️ مسح جميع البيانات"})]}),x.jsxs("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[x.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 ",x.jsx("strong",{children:"نصائح:"})]}),x.jsxs("ul",{className:"text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1",children:[x.jsx("li",{children:"• يتم حفظ البيانات تلقائياً في متصفحك"}),x.jsx("li",{children:"• استخدم التصدير لإنشاء نسخة احتياطية"}),x.jsx("li",{children:"• يمكنك استيراد البيانات من ملف JSON"}),x.jsx("li",{children:"• البيانات محفوظة حتى لو أغلقت المتصفح"}),x.jsx("li",{children:"• يتم إنشاء نسخة احتياطية تلقائياً عند كل تحديث"})]})]}),x.jsxs("div",{className:"mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[x.jsx("p",{className:"text-sm text-green-700 dark:text-green-300 font-semibold mb-2",children:"📊 معلومات التخزين:"}),x.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[x.jsx("div",{children:x.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["👥 عدد الموظفين المحفوظين: ",x.jsx("strong",{children:c.length})]})}),x.jsx("div",{children:x.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["💾 حجم البيانات: ",x.jsxs("strong",{children:[Math.round(JSON.stringify(c).length/1024)," KB"]})]})}),x.jsx("div",{children:x.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["🕒 آخر تحديث: ",x.jsx("strong",{children:localStorage.getItem("work-attendance-employees_lastUpdate")?new Date(localStorage.getItem("work-attendance-employees_lastUpdate")).toLocaleString("en-GB"):"غير محدد"})]})}),x.jsx("div",{children:x.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["🔄 الحفظ التلقائي: ",x.jsx("strong",{children:"مفعل"})]})})]})]})]})]}),x.jsxs(hn,{className:"glassmorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-lg",children:"📝 نبذة عن البرنامج"})}),x.jsx(gn,{children:x.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"برنامج احترافي لإدارة كشوفات العمل وتحليل استحقاقها بطريقة ذكية وآلية مع حفظ دائم للبيانات."})})]}),x.jsxs(hn,{className:"glassmorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-lg",children:"👨‍💻 عن المبرمج"})}),x.jsx(gn,{children:x.jsxs("div",{className:"space-y-2",children:[x.jsx("p",{className:"font-semibold",children:"علي عاجل خشان المحنّة"}),x.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"مبرمج محترف بخبرة واسعة في:"}),x.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[x.jsx("li",{children:"• VB.NET / C# / Java"}),x.jsx("li",{children:"• JavaScript / React / Node.js"}),x.jsx("li",{children:"• Microsoft Access و Excel VBA"}),x.jsx("li",{children:"• أنظمة الأرشفة الذكية والتحكم الآلي"})]})]})})]}),x.jsxs(hn,{className:"glassmorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-lg",children:"🆔 إصدار البرنامج"})}),x.jsx(gn,{children:x.jsxs("div",{className:"space-y-2",children:[x.jsx("p",{className:"font-semibold",children:"v2.0.0"}),x.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"تم تطويره سنة 2025 لتسهيل تتبع كشوفات العمل بدقة وأناقة مع حفظ دائم للبيانات."})]})})]}),x.jsxs(hn,{className:"glassmorphism",children:[x.jsx(mn,{children:x.jsx(pn,{className:"text-lg",children:"🎖️ حقوق التصميم"})}),x.jsx(gn,{children:x.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"جميع الحقوق محفوظة © 2025"})})]})]})})]})})})]})})]}),x.jsx(me,{variant:"destructive",className:"fixed bottom-5 left-5 z-50 rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all duration-300",onClick:q,title:"إغلاق البرنامج (Ctrl+Q)",children:x.jsx(d0,{className:"h-6 w-6"})}),s&&A()>0&&x.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:x.jsx(Ke.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-md w-full mx-4 p-6",children:x.jsxs("div",{className:"text-center",children:[x.jsx("div",{className:"text-6xl mb-4",children:"🔔"}),x.jsx("h3",{className:"text-xl font-bold text-gray-800 dark:text-gray-200 mb-2",children:"تنبيه: موظفون قريبون من الاستحقاق!"}),x.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:["يوجد ",x.jsx("strong",{className:"text-orange-600",children:A()})," موظف سيستحق كشف العمل خلال شهر واحد"]}),x.jsx("div",{className:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 mb-6",children:x.jsxs("div",{className:"text-sm text-orange-700 dark:text-orange-300",children:[x.jsx("strong",{children:"الموظفون:"}),x.jsxs("ul",{className:"mt-2 space-y-1",children:[M().slice(0,3).map((Y,J)=>x.jsxs("li",{className:"flex justify-between",children:[x.jsx("span",{children:Y.name}),x.jsx("span",{className:"text-xs",children:Y.department})]},Y.id)),A()>3&&x.jsxs("li",{className:"text-xs text-orange-500",children:["و ",A()-3," موظف آخر..."]})]})]})}),x.jsxs("div",{className:"flex gap-3",children:[x.jsx(me,{onClick:()=>{l(!1),v("upcoming")},className:"flex-1 bg-orange-500 hover:bg-orange-600 text-white",children:"📋 عرض التفاصيل"}),x.jsx(me,{variant:"outline",onClick:()=>l(!1),className:"flex-1",children:"❌ إغلاق"})]})]})})})]})}gw.createRoot(document.getElementById("root")).render(x.jsx(w.StrictMode,{children:x.jsx(aj,{})}))});export default ij();
